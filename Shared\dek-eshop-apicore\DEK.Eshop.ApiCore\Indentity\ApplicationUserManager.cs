using Microsoft.AspNetCore.Identity;

namespace DEK.Eshop.ApiCore.Indentity;

public class ApplicationUserManager
{
    private readonly IUserStore<ApplicationUser> userStore;

    public ApplicationUserManager(IUserStore<ApplicationUser> userStore)
    {
        this.userStore = userStore;
    }

    public async Task<ApplicationUser> GetCurrentUser()
    {
        var user = await this.userStore.FindByIdAsync("http_user", default(CancellationToken));
        if (user == null) {
            throw new NullReferenceException("This should never happend.");
        }
        return user;
    }

    /// <summary>
    /// Throws an exception if the user is not logged in.
    /// If not catched, result in 403.
    /// </summary>
    /// <exception cref="ForbidenException"></exception>
    public async Task<ApplicationUser> GetCurrentUserThrowIfNotLogged()
    {
        var user = await this.GetCurrentUser();
        if (user.IsGuest) {
            throw new ForbidenException("The user is not logged.");
        }
        return user;
    }
}
