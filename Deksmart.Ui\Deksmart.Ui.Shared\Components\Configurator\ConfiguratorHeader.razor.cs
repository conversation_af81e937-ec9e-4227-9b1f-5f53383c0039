using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Deksmart.Ui.Shared.Services;
using Deksmart.Ui.Shared.Resources;

namespace Deksmart.Ui.Shared.Components.Configurator
{
    public partial class ConfiguratorHeader : ComponentBase, IDisposable
    {
        [Parameter]
        public IConfiguratorGridService ConfiguratorGridService { get; set; } = default!;

        [Parameter]
        public bool ShowSaveButton { get; set; } = true;

        [Parameter]
        public bool ShowPageTitle { get; set; } = true;

        [Inject]
        protected NavigationManager NavigationManager { get; set; } = default!;

        [Inject]
        protected IJSRuntime JSRuntime { get; set; } = default!;

        [Inject]
        protected HeaderStateService HeaderState { get; set; } = default!;

        [Inject]
        protected MetaDescriptionService MetaDescriptionService { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            UpdateHeaderState();
            var service = ConfiguratorGridService?.MainConfiguratorGridService ?? ConfiguratorGridService;
            if (service?.ConfiguratorWrapper != null)
            {
                try
                {
                    var metaDescription = MetaDescriptionService.GetMetaDescription(service.ConfiguratorWrapper);
                    await JSRuntime.InvokeVoidAsync("setMetaDescription", metaDescription);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting meta description in OnInitializedAsync: {ex.Message}");
                }
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();
            UpdateHeaderState();
            var service = ConfiguratorGridService?.MainConfiguratorGridService ?? ConfiguratorGridService;
            if (service?.ConfiguratorWrapper != null)
            {
                try
                {
                    var metaDescription = MetaDescriptionService.GetMetaDescription(service.ConfiguratorWrapper);
                    await JSRuntime.InvokeVoidAsync("setMetaDescription", metaDescription);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting meta description in OnParametersSetAsync: {ex.Message}");
                }
            }
        }

        private void UpdateHeaderState()
        {
            if (ConfiguratorGridService?.ConfiguratorWrapper != null)
            {
                // Check if this is a child configurator in a tabbed configurator
                var isChildConfigurator = ConfiguratorGridService.MainConfiguratorGridService != null;

                // If this is a child configurator, use the main configurator's title
                var service = isChildConfigurator ? ConfiguratorGridService.MainConfiguratorGridService : ConfiguratorGridService;

                if (service?.ConfiguratorWrapper != null)
                {
                    var title = service.ConfiguratorWrapper.Title;
                    var saveCallback = EventCallback.Factory.Create(this, SaveLayout);

                    // If this is a child configurator, always show the save button for the main configurator
                    bool shouldShowSaveButton = isChildConfigurator || ShowSaveButton;

                    HeaderState.UpdateHeader(title, shouldShowSaveButton, false, saveCallback);

                }
                else
                {
                    HeaderState.ClearHeader();
                }
            }
            else
            {
                HeaderState.ClearHeader();
            }
        }

        protected async Task SaveLayout()
        {
            // Check if this is a child configurator in a tabbed configurator
            var isChildConfigurator = ConfiguratorGridService.MainConfiguratorGridService != null;

            // If this is a child configurator, save the main configurator
            var service = isChildConfigurator ? ConfiguratorGridService.MainConfiguratorGridService : ConfiguratorGridService;

            if (service != null)
            {

                var presetId = await service.SavePreset();

                if (presetId != null)
                {
                    var configId = service.ConfiguratorWrapper?.Id ?? 0;
                    var newUrl = UiSharedResource.Deksmart + "/" + string.Format(UiSharedResource.LoadPresetUrl, configId, presetId);
                    var completeUrl = NavigationManager.Uri + newUrl;
                    // Copy newUrl to clipboard
                    await JSRuntime.InvokeVoidAsync("copyToClipboard", completeUrl);
                    NavigationManager.NavigateTo(newUrl);
                }
            }
        }

        void IDisposable.Dispose()
        {
            // Clear the header state when this component is disposed
            HeaderState.ClearHeader();
        }
    }
}