using Microsoft.Extensions.Logging;
using Deksmart.Ui.Shared.Resources;
using Deksmart.Ui.Model;
using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Factory;

namespace Deksmart.Ui.Shared.Services
{
    public class ConfiguratorGridService : IConfiguratorGridService
    {
        private readonly IConfiguratorApiService _apiService;
        private readonly IConfiguratorStateService _stateService;
        private readonly IConfiguratorValidationService _validationService;
        private readonly IChildServiceManager _childServiceManager;
        private readonly ILogger<ConfiguratorGridService> _logger;
        private readonly NotificationService _notificationService;
        private readonly IConfiguratorGridServiceFactory _serviceFactory;
        private readonly ConfiguratorNavigationService _navigationService;
        
        private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
        private bool _disposed = false;
        private bool _isLoading = false;
        
        public ConfiguratorWrapper? ConfiguratorWrapper { get; set; }
        public bool IsLoading => _isLoading || ChildServices.Any(child => child.IsLoading);
        public List<IConfiguratorGridService> ChildServices { get; } = new();
        public IConfiguratorGridService? MainConfiguratorGridService { get; set; }

        /// <summary>
        /// Tracks the currently active child configurator service in tabbed configurators.
        /// This is used for scrolling logic when validation errors are present.
        /// </summary>
        public IConfiguratorGridService? ActiveChildConfiguratorService { get; set; }

        /// <summary>
        /// Indicates whether validation checkboxes should be shown on category headers.
        /// This is set to true after clicking on a button from configurator actions.
        /// </summary>
        public bool ShowValidationCheckboxes { get; set; } = false;

        public event EventHandler? ValidationStateChanged;
        public event EventHandler? LoadingStateChanged;

        public ConfiguratorGridService(
            IConfiguratorApiService apiService,
            IConfiguratorStateService stateService,
            IConfiguratorValidationService validationService,
            IChildServiceManager childServiceManager,
            ILogger<ConfiguratorGridService> logger,
            NotificationService notificationService,
            IConfiguratorGridServiceFactory serviceFactory,
            ConfiguratorNavigationService navigationService)
        {
            _apiService = apiService;
            _stateService = stateService;
            _validationService = validationService;
            _childServiceManager = childServiceManager;
            _logger = logger;
            _notificationService = notificationService;
            _serviceFactory = serviceFactory;
            _navigationService = navigationService;
        }

        public void NotifyValidationStateChanged()
        {
            OnValidationStateChanged();
        }

        public void NotifyLoadingStateChanged()
        {
            OnLoadingStateChanged();
        }

        public async Task LoadDataAsync(int id)
        {
            await ExecuteWithLoadingState(async (cancellationToken) =>
            {
                // Save field states before reloading
                var fieldStates = _stateService.SaveFieldStates(ConfiguratorWrapper);

                ConfiguratorDto? result;
                
                if (ConfiguratorWrapper is null || ConfiguratorWrapper.Id != id)
                {
                    // Fresh load - use regular endpoint
                    result = await _apiService.GetConfiguratorAsync(id, cancellationToken);
                }
                else
                {
                    // Use composite endpoint if this is a child configurator
                    if (MainConfiguratorGridService != null)
                    {
                        // This is a child configurator - create composite state with this child as active
                        var compositeState = _stateService.GetCompositeConfiguratorState(ConfiguratorWrapper, MainConfiguratorGridService);
                        var compositeResponse = await _apiService.ProcessCompositeConfiguratorAsync(MainConfiguratorGridService.ConfiguratorWrapper!.Id, compositeState, cancellationToken);
                        
                        // Use the processed active child as the main result for this child configurator
                        result = compositeResponse?.ProcessedActiveChild;
                        
                        // Update the main configurator with the composite summary
                        if (compositeResponse?.CompositeConfiguratorSummary != null && MainConfiguratorGridService.ConfiguratorWrapper != null)
                        {
                            var compositeSummaryWrapper = ConfiguratorWrapperFactory.Create(compositeResponse.CompositeConfiguratorSummary);
                            
                            // Update main configurator's compositions with aggregated products
                            MainConfiguratorGridService.ConfiguratorWrapper.UpdateCompositions(compositeSummaryWrapper.ConfiguratorCompositions);
                            
                            // Update main configurator's pricing
                            MainConfiguratorGridService.ConfiguratorWrapper.UpdateTotalPrices(
                                compositeSummaryWrapper.TotalPrice,
                                compositeSummaryWrapper.TotalPriceNoVat,
                                compositeSummaryWrapper.TotalVat);
                        }
                    }
                    else
                    {
                        // Use regular endpoint for single configurators
                        var configuratorState = _stateService.GetConfiguratorState<ConfiguratorStateDto>(ConfiguratorWrapper, ChildServices);
                        result = await _apiService.GetFilteredConfiguratorAsync(id, configuratorState, cancellationToken);
                    }
                }

                cancellationToken.ThrowIfCancellationRequested();

                if (result != null)
                {
                    ConfiguratorWrapper = ConfiguratorWrapperFactory.Create(result);
                    cancellationToken.ThrowIfCancellationRequested();

                    // Restore field states after creating new wrappers
                    _stateService.RestoreFieldStates(ConfiguratorWrapper, fieldStates);
                    cancellationToken.ThrowIfCancellationRequested();

                    // Initialize child services if this is a collection
                    if (ConfiguratorWrapper.IsComposite)
                    {
                        _childServiceManager.InitializeChildServices(result, ChildServices, _serviceFactory, this, ValidationStateChanged, _logger);
                        cancellationToken.ThrowIfCancellationRequested();
                    }

                    // Trigger validation state changed event
                    HandleValidationStateChange();

                    // Handle navigation for preset views
                    await HandleNavigationIfNeeded(id);
                }
            });
        }

        public async Task GetDataForPresetAsync(int id, string preset)
        {
            await ExecuteWithLoadingState(async (cancellationToken) =>
            {
                // Save field states before reloading
                var fieldStates = _stateService.SaveFieldStates(ConfiguratorWrapper);

                var result = await _apiService.GetConfiguratorForPresetAsync(id, preset, cancellationToken);
                cancellationToken.ThrowIfCancellationRequested();

                if (result != null)
                {
                    ConfiguratorWrapper = ConfiguratorWrapperFactory.Create(result);
                    cancellationToken.ThrowIfCancellationRequested();

                    // Restore field states after creating new wrappers
                    _stateService.RestoreFieldStates(ConfiguratorWrapper, fieldStates);
                    cancellationToken.ThrowIfCancellationRequested();

                    // Initialize child services if this is a collection
                    if (ConfiguratorWrapper.IsComposite)
                    {
                        _childServiceManager.InitializeChildServices(result, ChildServices, _serviceFactory, this, ValidationStateChanged, _logger);
                    }
                }
            });
        }

        public async Task<Guid?> SavePreset()
        {
            // If this is a child configurator service, delegate to the main configurator
            if (MainConfiguratorGridService != null)
            {
                return await MainConfiguratorGridService.SavePreset();
            }

            var cancellationToken = GetCancelationToken();

            if (ConfiguratorWrapper == null)
            {
                _notificationService.ShowError(UiSharedResource.NoConfiguratorSelected);
                return null;
            }

            return await _apiService.SavePresetAsync(ConfiguratorWrapper.Id, _stateService.GetConfiguratorState<ConfiguratorStateDto>(ConfiguratorWrapper, ChildServices), cancellationToken);
        }

        public async Task ChangeIdWrapperValue(IdFieldWrapper idField, int id)
        {
            if (ConfiguratorWrapper == null) return;
            
            await ExecuteWithLoadingState(async (cancellationToken) =>
            {
                var allFieldsWithSelectedIdent = ConfiguratorWrapper.FieldCategories
                    .SelectMany(d => d.AllFields.OfType<IdFieldWrapper>())
                    .Where(d => d.Ident == idField.Ident);

                _logger.LogInformation($"ChangeIdWrapperValue: {idField.Ident} {allFieldsWithSelectedIdent.Count()} {ConfiguratorWrapper.FieldCategories
                    .SelectMany(d => d.AllFields.OfType<IdFieldWrapper>()).First().Ident}");

                foreach (var field in allFieldsWithSelectedIdent)
                {
                    field.Value = id;
                }

                await LoadDataAsync(ConfiguratorWrapper.Id);
                HandleValidationStateChange();
            });
        }

        public async Task LoadProductDetail(ConfiguratorProductWrapper product)
        {
            var cancellationToken = GetCancelationToken();

            var productDetail = await _apiService.LoadProductDetailAsync(product.ProductCode, cancellationToken);
            cancellationToken.ThrowIfCancellationRequested();

            if (productDetail != null)
            {
                product.Detail = new ConfiguratorProductDetailWrapper(productDetail);
            }
        }

        public async Task CalculateProductPrices(ConfiguratorProductWrapper product)
        {
            if (ConfiguratorWrapper == null) return;

            var cancellationToken = GetCancelationToken();

            var selectedProducts = ConfiguratorWrapper.ConfiguratorCompositions?
                .SelectMany(c => c.SelectedProducts)
                .Select(c => new SelectedProductDto
                {
                    ProductCode = c.ProductCode,
                    Amount = c.PackageQuantity,
                    Unit = c.PackageUnit
                })
                .ToList();

            if (selectedProducts == null || !selectedProducts.Any()) return;

            var request = new ProductPriceCalculationRequestDto
            {
                ConfiguratorId = ConfiguratorWrapper.Id,
                SelectedProducts = selectedProducts,
                CurrentProductCode = product.ProductCode
            };

            cancellationToken.ThrowIfCancellationRequested();

            var result = await _apiService.CalculateProductPricesAsync(request, cancellationToken);
            cancellationToken.ThrowIfCancellationRequested();

            if (result != null)
            {
                // Update total prices in the wrapper
                ConfiguratorWrapper.UpdateTotalPrices(result.TotalPriceVat, result.TotalPriceNoVat, result.TotalVat);
                cancellationToken.ThrowIfCancellationRequested();

                product.UpdatePrices(result.PriceVat);
                cancellationToken.ThrowIfCancellationRequested();

                // Handle navigation if needed
                await HandleNavigationIfNeeded(ConfiguratorWrapper.Id);
            }
        }

        public async Task<(byte[]? pdfBytes, string fileName)> GenerateConfigurationPdf()
        {
            // If this is a child configurator service, delegate to the main configurator
            if (MainConfiguratorGridService != null)
            {
                return await MainConfiguratorGridService.GenerateConfigurationPdf();
            }

            var cancellationToken = GetCancelationToken();

            if (ConfiguratorWrapper == null)
            {
                _notificationService.ShowError(UiSharedResource.NoConfiguratorSelected);
                return (null, "configuration.pdf");
            }

            var configuratorState = _stateService.GetConfiguratorState<ConfiguratorStateDto>(ConfiguratorWrapper, ChildServices);
            cancellationToken.ThrowIfCancellationRequested();

            var (pdfBytes, fileName) = await _apiService.GenerateConfigurationPdfAsync(ConfiguratorWrapper.Id, configuratorState, cancellationToken);
            cancellationToken.ThrowIfCancellationRequested();

            // Use the filename from the server if available, otherwise use a default name
            return (pdfBytes, fileName ?? $"{ConfiguratorWrapper.Title?.Replace(" ", "_") ?? "configuration"}.pdf");
        }

        public void AddProductIntoComposition(int compositionId)
        {
            var composition = ConfiguratorWrapper?.ConfiguratorCompositions?.FirstOrDefault(c => c.Id == compositionId);
            if (composition != null)
            {
                var firstUnselectedProduct = composition.Products.FirstOrDefault(p => !composition.SelectedProducts.Any(sp => sp.Id == p.Id));
                if (firstUnselectedProduct != null)
                {
                    composition.SelectedProducts.Add(firstUnselectedProduct);
                }
            }
        }

        public void RemoveProductFromComposition(int compositionId, int index)
        {
            var composition = ConfiguratorWrapper?.ConfiguratorCompositions?.FirstOrDefault(c => c.Id == compositionId);
            if (composition != null)
            {
                composition.SelectedProducts.RemoveAt(index);
            }
        }

        public async Task<bool> SendInquiry(string? name, string? email, string? phone)
        {
            // If this is a child configurator service, delegate to the main configurator
            if (MainConfiguratorGridService != null)
            {
                return await MainConfiguratorGridService.SendInquiry(name, email, phone);
            }

            var cancellationToken = GetCancelationToken();

            if (ConfiguratorWrapper == null)
            {
                _notificationService.ShowError(UiSharedResource.NoConfiguratorSelected);
                return false;
            }

            var emailState = _stateService.GetConfiguratorState<EmailConfiguratorStateDto>(ConfiguratorWrapper, ChildServices);
            emailState.ContactInfo = new ContactInfoDto
            {
                Name = name,
                Email = email,
                Phone = phone
            };

            cancellationToken.ThrowIfCancellationRequested();

            return await _apiService.SendInquiryAsync(ConfiguratorWrapper.Id, emailState, cancellationToken);
        }

        /// <summary>
        /// Sets all fields in the configurator as dirty to trigger validation display
        /// </summary>
        /// <returns>True if the configurator is valid after setting all fields as dirty</returns>
        public bool SetAllFieldsDirty()
        {
            // Set ShowValidationCheckboxes to true for this service
            ShowValidationCheckboxes = true;

            var isValid = _validationService.SetAllFieldsDirty(ConfiguratorWrapper, ChildServices);

            // Notify that validation state might have visually changed
            OnValidationStateChanged();

            return isValid;
        }

        /// <summary>
        /// Finds the first category with validation errors
        /// </summary>
        /// <returns>The ID of the first category with validation errors, or null if no errors found</returns>
        public string? FindFirstInvalidCategoryId()
        {
            return _validationService.FindFirstInvalidCategoryId(ConfiguratorWrapper, ChildServices);
        }

        private void HandleValidationStateChange()
        {
            if (MainConfiguratorGridService != null)
            {
                // If this is a child service, update the parent's validation state
                _validationService.UpdateParentValidationState(MainConfiguratorGridService.ConfiguratorWrapper, MainConfiguratorGridService.ChildServices);
                MainConfiguratorGridService.NotifyValidationStateChanged();
            }
            else
            {
                // If this is the main service, notify itself
                OnValidationStateChanged();
            }
        }

        private async Task HandleNavigationIfNeeded(int configuratorId)
        {
            // If we have an active service in navigation service, it means we're in preset view and should navigate
            if (_navigationService.GetActiveService() != null)
            {
                // If this is a child service in a collection, use the parent's ID for navigation
                var idToNavigate = MainConfiguratorGridService?.ConfiguratorWrapper?.Id ?? configuratorId;
                await _navigationService.NavigateToConfigurator(idToNavigate);
            }
        }

        private void OnValidationStateChanged()
        {
            ValidationStateChanged?.Invoke(this, EventArgs.Empty);
        }

        private void OnLoadingStateChanged()
        {
            LoadingStateChanged?.Invoke(this, EventArgs.Empty);
            
            // If this is a child service, also notify the parent that loading state changed
            if (MainConfiguratorGridService != null)
            {
                MainConfiguratorGridService.NotifyLoadingStateChanged();
            }
        }

        private async Task ExecuteWithLoadingState(Func<CancellationToken, Task> operation)
        {
            var cancellationToken = GetCancelationToken();
            
            _isLoading = true;
            OnLoadingStateChanged();
            try
            {
                await operation(cancellationToken);
            }
            finally
            {
                // Only set loading to false if this operation wasn't cancelled
                if (!cancellationToken.IsCancellationRequested)
                {
                    _isLoading = false;
                    OnLoadingStateChanged();
                }
            }
        }

        private CancellationToken GetCancelationToken()
        {
            if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
            }
            _cancellationTokenSource = new CancellationTokenSource();
            return _cancellationTokenSource.Token;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _cancellationTokenSource?.Cancel();
                    _cancellationTokenSource?.Dispose();
                    _childServiceManager.DisposeChildServices(ChildServices, ValidationStateChanged);
                    _logger.LogDebug("Disposed ConfiguratorGridService instance for Configurator ID: {ConfiguratorId}", ConfiguratorWrapper?.Id);
                }

                _disposed = true;
            }
        }

        ~ConfiguratorGridService()
        {
            Dispose(false);
        }
    }
}