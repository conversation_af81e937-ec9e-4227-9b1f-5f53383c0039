﻿using Deksmart.Ui.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;

namespace Deksmart.Ui.Shared.Components.Field
{
    public partial class FieldSubComponent : ComponentBase
    {
        [Parameter]
        public ConfiguratorFieldWrapper Field { get; set; } = default!;

        [Parameter]
        public EventCallback OnDataLoaded { get; set; }

        [Parameter]
        public EventCallback<(int, IdFieldWrapper)> OnIdWrapperValueChange { get; set; }

        [Inject]
        private ILogger<FieldSubComponent> Logger { get; set; } = default!;

        [Inject]
        private IJSRuntime JSRuntime { get; set; } = default!;

        public virtual async Task LoadDataAsync()
        {
            Logger.LogInformation("FieldComponent - LoadDataAsync");
            await OnBlur();
            await OnDataLoaded.InvokeAsync();
        }

        protected async Task OnNumericInputChanged(decimal value, NumericFieldWrapper numericField)
        {
            Logger.LogInformation($"OnNumericInputChanged method called: {value}.");
            numericField.Value = value;
            numericField.IsPendingValidation = true;
            await LoadDataAsync();
            StateHasChanged();
        }

        protected void OnInputSlider(decimal value, NumericFieldWrapper numericField)
        {
            numericField.Value = value;
            numericField.IsPendingValidation = true;
        }

        protected async Task OnCheckBoxValueChanged(bool value, CheckboxFieldWrapper checkboxField)
        {
            Logger.LogInformation($"OnCheckBoxValueChanged method called: {value}.");
            checkboxField.Value = value;
            checkboxField.IsPendingValidation = true;
            await LoadDataAsync();
            StateHasChanged();
        }

        protected async Task OnSelectBoxValueChanged(int? id, SelectBoxFieldWrapper selectBoxField)
        {
            Logger.LogInformation($"OnSelectBoxValueChanged method called: {id}.");
            await OnBlur();

            // We don't allow selecting the empty option, it's just a visual placeholder
            // when no value is selected initially
            if (id == null)
            {
                // If somehow the empty option is selected, revert to the previous value
                // or do nothing if there was no previous value
                StateHasChanged();
                return;
            }

            selectBoxField.IsPendingValidation = true;
            await OnIdWrapperValueChange.InvokeAsync((id.Value, selectBoxField));
            StateHasChanged();
        }

        //force re-rendering of the select box when the number of items changes
        protected string GetUniqueKey(SelectBoxFieldWrapper selectBoxField)
        {
            return $"{selectBoxField.GetHashCode()}_{selectBoxField.FieldValues.Count}";
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && !string.IsNullOrEmpty(Field.JavaScriptAfterRender))
            {
                await JSRuntime.InvokeVoidAsync("eval", Field.JavaScriptAfterRender);
            }
        }

        protected async Task OnFocused()
        {
            if (!string.IsNullOrEmpty(Field.JavaScriptOnFocus))
                await JSRuntime.InvokeVoidAsync("eval", Field.JavaScriptOnFocus);
        }

        protected async Task OnBlur()
        {
            if (!string.IsNullOrEmpty(Field.JavaScriptOnBlur))
                await JSRuntime.InvokeVoidAsync("eval", Field.JavaScriptOnBlur);
        }
    }
}
