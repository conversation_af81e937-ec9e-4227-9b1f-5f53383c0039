using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using System.Data.Common;
using System.Data;
using Microsoft.Extensions.Hosting;
using DEK.Eshop.ApiCore.Config.Dto;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Indentity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DEK.Eshop.ApiCore.Database;

public class PgsqlContextFactory<TContext> : IDisposable where TContext : DbContext
{
    private PooledDbContextFactory<TContext> _factory;

    private DbCommand? _cmd;

    [ActivatorUtilitiesConstructor]
    public PgsqlContextFactory(IConfiguration config, IHostEnvironment env, IConfigService configService)
    {
        var eshopId = configService.GetEshopId();
        var configPgsql = ConfigFactory.Create<Pgsql>(config, "Pgsql:" + eshopId);

        _factory = CreateFactory(configPgsql);
    }

    public PgsqlContextFactory(Pgsql configPgsql)
    {
        _factory = CreateFactory(configPgsql);
    }

    public static PooledDbContextFactory<TContext> CreateFactory(Pgsql pgsql)
    {
        var conStr = new NpgsqlConnectionStringBuilder
        {
            Host = pgsql.Host,
            Port = pgsql.Port,
            Database = pgsql.Database,
            Username = pgsql.Username,
            Password = pgsql.Password,
            Pooling = pgsql.Pooling,
            IncludeErrorDetail = true,
        };

        var options = new DbContextOptionsBuilder<TContext>()
            .UseNpgsql(conStr.ConnectionString)
            .UseSnakeCaseNamingConvention()
            .Options;

        return new PooledDbContextFactory<TContext>(options);
    }

    public TContext CreateContext()
    {
        return _factory.CreateDbContext();
    }

    public DbCommand CreateCommand()
    {
        _cmd ??= CreateContext().Database.GetDbConnection().CreateCommand();

        if (_cmd.Connection?.State != ConnectionState.Open)
            _cmd.Connection?.Open();

        return _cmd;
    }

    public void Dispose()
    {
        if (_cmd?.Connection?.State == ConnectionState.Open)
            _cmd.Connection?.Close();
    }
}
