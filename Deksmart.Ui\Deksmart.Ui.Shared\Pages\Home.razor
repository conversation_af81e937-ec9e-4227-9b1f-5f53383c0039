﻿@page "/deksmart"
@using Deksmart.Ui.Shared.Resources
@using Microsoft.AspNetCore.Components
@using Deksmart.Ui.Shared.Components

<PageTitle>@UiSharedResource.Configurators</PageTitle>

<span>@UiSharedResource.EnterCompositionNumber</span>
<input @bind="inputText"/>
<button class="button" @onclick="GetPresetForCatalog">@UiSharedResource.Find</button>

<h1>@UiSharedResource.Configurators</h1>

@if (configurators == null)
{
	<LoadingSpinner />
}
else
{
	<ul>
		@foreach (var configurator in configurators)
		{
			<button class="button" @onclick="() => LoadConfigurator(configurator.Id)">@configurator.Title</button>
		}
	</ul>
}
