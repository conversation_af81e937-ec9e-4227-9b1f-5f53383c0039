using Microsoft.AspNetCore.Components;

namespace Deksmart.Ui.Shared.Services
{
    public class HeaderStateService
    {
        // Event that will be triggered when the header state changes
        public event Action? OnChange;

        // Properties for the header content
        public string? Title { get; private set; }
        public bool ShowSaveButton { get; private set; }
        public bool SaveButtonDisabled { get; private set; }
        public EventCallback SaveCallback { get; private set; }

        // Method to update the header state
        public void UpdateHeader(string? title, bool showSaveButton = false, bool saveButtonDisabled = false, EventCallback saveCallback = default, [System.Runtime.CompilerServices.CallerMemberName] string callerMethod = "", [System.Runtime.CompilerServices.CallerFilePath] string callerFile = "")
        {
            // Extract just the filename from the path
            string fileName = System.IO.Path.GetFileName(callerFile);

            Console.WriteLine($"HeaderState updated by {fileName}:{callerMethod} - Title: {title}, ShowSaveButton: {showSaveButton}, SaveButtonDisabled: {saveButtonDisabled}");

            Title = title;
            ShowSaveButton = showSaveButton;
            SaveButtonDisabled = saveButtonDisabled;
            SaveCallback = saveCallback;

            // Notify subscribers that the state has changed
            NotifyStateChanged();
        }

        // Method to clear the header state (e.g., when navigating away)
        public void ClearHeader([System.Runtime.CompilerServices.CallerMemberName] string callerMethod = "", [System.Runtime.CompilerServices.CallerFilePath] string callerFile = "")
        {
            // Extract just the filename from the path
            string fileName = System.IO.Path.GetFileName(callerFile);

            Console.WriteLine($"HeaderState cleared by {fileName}:{callerMethod}");

            Title = null;
            ShowSaveButton = false;
            SaveButtonDisabled = false;
            SaveCallback = default;

            // Notify subscribers that the state has changed
            NotifyStateChanged();
        }

        // Method to notify subscribers of state changes
        private void NotifyStateChanged() => OnChange?.Invoke();
    }
}
