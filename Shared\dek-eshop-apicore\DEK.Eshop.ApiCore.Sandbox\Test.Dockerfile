﻿FROM docker-registry.dek.cz/dotnet/sdk:7.0 AS build
WORKDIR /src
COPY ["DEK.Eshop.ApiCore.Sandbox/DEK.Eshop.ApiCore.Sandbox.csproj", "DEK.Eshop.ApiCore.Sandbox/"]
RUN dotnet nuget add source "https://gitlab.dek.cz/api/v4/groups/23/-/packages/nuget/index.json" -n "gitlab.dek.cz" -u "nuget-restore" -p "********************" --store-password-in-clear-text
RUN dotnet restore "DEK.Eshop.ApiCore.Sandbox/DEK.Eshop.ApiCore.Sandbox.csproj"
COPY . .
WORKDIR "/src/DEK.Eshop.ApiCore.Sandbox"
RUN dotnet build "DEK.Eshop.ApiCore.Sandbox.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "DEK.Eshop.ApiCore.Sandbox.csproj" -c Release -o /app/publish /p:UseAppHost=false


FROM docker-registry.dek.cz/dotnet/aspnet:7.0-bullseye-slim-amd64 AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "DEK.Eshop.ApiCore.Sandbox.dll"]

#docker build -f .\DEK.Eshop.ApiCore.Sandbox\Test.Dockerfile -t apicore:debian . --no-cache --progress=plain
#docker run -it --rm -e "ASPNETCORE_ENVIRONMENT=Development" -p 5000:80 --name apicore apicore:debian
#remove app.UseHttpsRedirection(); from Program.cs
#http://localhost:5000/article/doc