@using Deksmart.Shared.Dto
@using Deksmart.Ui.Shared.Resources

<div class="configurator-selection-dialog @(IsVisible ? "visible" : "")">
    <div class="dialog-content">
        <h3>@UiSharedResource.SelectConfigurator</h3>
        <div class="configurator-list">
            @foreach (var configurator in Configurators)
            {
                <div class="configurator-item" @onclick="() => OnConfiguratorSelected(configurator)">
                    <div class="configurator-title">@configurator.Title</div>
                </div>
            }
        </div>
        <button class="cancel-button" @onclick="OnCancel">@UiSharedResource.CancelButtonText</button>
    </div>
</div>
