using System.Net.Http.Json;
using System.Web;
using Deksmart.Application.Resource;
using Deksmart.Domain.Entity.Business;

namespace Deksmart.Application.Service.Http
{
    public abstract class BaseHttpService
    {
        protected readonly HttpClient _httpClient;
        protected const string BaseEndpoint = "api";

        protected BaseHttpService(IHttpClientFactory httpClientFactory, string clientName)
        {
            _httpClient = httpClientFactory.CreateClient(clientName);
        }

        protected async Task<ApiResult<List<T>>> GetDataAsync<T>(string endpoint, List<string> codes)
        {
            try
            {
                if (codes.Count == 0)
                {
                    return ApiResult<List<T>>.Error(DeksmartApplicationResource.NoProductCodesProvided);
                }

                var queryString = string.Join("&", codes.Select(code => $"codes={HttpUtility.UrlEncode(code)}"));
                var uriBuilder = new UriBuilder(new Uri(_httpClient.BaseAddress!, $"{BaseEndpoint}/{endpoint}"))
                {
                    Query = queryString
                };

                var response = await _httpClient.GetAsync(uriBuilder.Path + uriBuilder.Query);

                if (!response.IsSuccessStatusCode)
                {
                    var error = await response.Content.ReadAsStringAsync();
                    return ApiResult<List<T>>.Error(string.Format(DeksmartApplicationResource.ApiReturnedStatusCodeError, response.StatusCode, error));
                }

                var data = await response.Content.ReadFromJsonAsync<List<T>>();
                if (data == null || data.Count != codes.Count)
                {
                    return ApiResult<List<T>>.Error(string.Format(DeksmartApplicationResource.ApiReturnedItemCountMismatch, data?.Count ?? 0, codes.Count));
                }

                return ApiResult<List<T>>.Success(data);
            }
            catch (HttpRequestException ex)
            {
                return ApiResult<List<T>>.Error(string.Format(DeksmartApplicationResource.NetworkErrorWhileFetchingData, endpoint, ex.Message));
            }
            catch (Exception ex)
            {
                return ApiResult<List<T>>.Error(string.Format(DeksmartApplicationResource.UnexpectedErrorWhileFetchingData, endpoint, ex.Message));
            }
        }
    }
}