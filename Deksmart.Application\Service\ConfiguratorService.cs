using Deksmart.Infrastructure.Cache;
using Deksmart.Domain.Service;
using Deksmart.Shared.Dto;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Application.Mapping;
using Deksmart.Infrastructure.Repository.Interface;

namespace Deksmart.Application.Service
{
    public interface IConfiguratorService
    {
        /// <summary>
        /// Retrieves a configurator DTO filtered according to the provided configurator state, including any child configurator states.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator to retrieve.</param>
        /// <param name="configuratorState">The state used to filter the configurator, including field values, selected products, and child configurator states.</param>
        /// <returns>
        /// A tuple containing the filtered <see cref="ConfiguratorDto"/> (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(ConfiguratorDto? configurator, ValidationResult validation)> GetFilteredConfiguratorAsync(int configuratorId, ConfiguratorStateDto configuratorState);

        /// <summary>
        /// Processes a composite configurator with active child processing and product aggregation.
        /// This method processes the active child configurator through normal route and combines
        /// with other children to provide both individual child updates and composite summary.
        /// </summary>
        /// <param name="configuratorId">The composite configurator ID.</param>
        /// <param name="configuratorState">The composite configurator state with active child and other products.</param>
        /// <returns>
        /// A tuple containing the <see cref="CompositeConfiguratorResponseDto"/> with both processed active child and composite summary (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(CompositeConfiguratorResponseDto? response, ValidationResult validation)> ProcessCompositeConfiguratorAsync(int configuratorId, CompositeConfiguratorStateDto configuratorState);

        /// <summary>
        /// Retrieves all primary configurators as DTOs.
        /// </summary>
        /// <returns>
        /// A collection of <see cref="ConfiguratorDto"/> representing all primary configurators.
        /// </returns>
        Task<IEnumerable<ConfiguratorDto>> GetAllConfiguratorsAsync();

        /// <summary>
        /// Retrieves a configurator DTO by its ID, including its child configurators if present.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator to retrieve.</param>
        /// <returns>
        /// A tuple containing the <see cref="ConfiguratorDto"/> (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(ConfiguratorDto? configurator, ValidationResult validation)> GetConfiguratorsAsync(int configuratorId);

        /// <summary>
        /// Calculates prices for the selected products within the specified configurator, using current product codes and external pricing data.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="selectedProducts">The list of selected products for which to calculate prices.</param>
        /// <param name="currentProductCode">The current product code for context.</param>
        /// <returns>
        /// A tuple containing the <see cref="ProductPriceCalculationResponseDto"/> result (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(ProductPriceCalculationResponseDto? result, ValidationResult validation)> CalculateProductPricesAsync(
            int configuratorId,
            List<SelectedProductDto> selectedProducts,
            string currentProductCode);

        /// <summary>
        /// Retrieves a configurator entity filtered according to the provided configurator state, including any child configurator states.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator to retrieve.</param>
        /// <param name="configuratorState">The state used to filter the configurator, including field values, selected products, and child configurator states.</param>
        /// <returns>
        /// A tuple containing the filtered <see cref="Configurator"/> entity (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(Configurator? configurator, ValidationResult validation)> GetFilteredConfiguratorEntityAsync(int configuratorId, ConfiguratorStateDto configuratorState);
    }

    public class ConfiguratorService : IConfiguratorService
    {
        private readonly IConfiguratorProcessingService _processingService;
        private readonly IConfiguratorRepository _configuratorDao;
        private readonly IConfiguratorCacheManager _cacheManager;
        private readonly IConfiguratorMappingService _mappingService;
        private readonly IConfiguratorValidationService _validationService;
        private readonly IChildConfiguratorRepository _childConfiguratorDao;
        private readonly IProductProcessingService _productProcessingService;

        public ConfiguratorService(
            IConfiguratorProcessingService processingService,
            IConfiguratorRepository configuratorDao,
            IConfiguratorCacheManager cacheManager,
            IConfiguratorMappingService mappingService,
            IConfiguratorValidationService validationService,
            IChildConfiguratorRepository childConfiguratorDao,
            IProductProcessingService productProcessingService)
        {
            _processingService = processingService;
            _configuratorDao = configuratorDao;
            _cacheManager = cacheManager;
            _mappingService = mappingService;
            _validationService = validationService;
            _childConfiguratorDao = childConfiguratorDao;
            _productProcessingService = productProcessingService;
        }
        
        public async Task<(Configurator? configurator, ValidationResult validation)> GetFilteredConfiguratorEntityAsync(int configuratorId, ConfiguratorStateDto configuratorState)
        {
            var validation = new ValidationResult();

            await _validationService.ValidateConfiguratorExistsAsync(configuratorId, validation);

            if (validation.HasErrors)
                return (null, validation);

            // Load original configurator and set HasProducts property once
            var originalConfigurator = await _cacheManager.GetOrAddAsync(configuratorId, _configuratorDao.GetCompleteConfiguratorNoTrackingAsync);
            if (originalConfigurator != null)
            {
                await SetHasProductsPropertyAsync(originalConfigurator);
            }


            if(configuratorState.ChildConfiguratorStates.Count > 0){
                // Process all child configurators in parallel to avoid sequential processing bottleneck
                var childProcessingTasks = configuratorState.ChildConfiguratorStates.Select(async childConfiguratorState =>
                {
                    var childConfiguratorResult = await _processingService.ProcessConfiguratorAsync(
                        childConfiguratorState.ConfiguratorId,
                        childConfiguratorState.FieldValues,
                        childConfiguratorState.SelectedProducts,
                        childConfiguratorState.CategoryStates);
                    
                    return new { 
                        State = childConfiguratorState, 
                        Result = childConfiguratorResult 
                    };
                }).ToList();

                var childResults = await Task.WhenAll(childProcessingTasks);

                // Check for validation errors and apply results
                foreach(var childResult in childResults)
                {
                    if(childResult.Result.validation.HasErrors)
                        return (null, childResult.Result.validation);

                    childResult.Result.configurator!.TabTitle = childResult.State.TabTitle;
                    childResult.Result.configurator.TabOrder = childResult.State.TabOrder;

                    originalConfigurator?.ChildUserConfigurators.Add(childResult.Result.configurator);
                }

                if (originalConfigurator != null)
                {
                    originalConfigurator.IsValid = originalConfigurator.ChildUserConfigurators.All(c => c.IsValid);
                    originalConfigurator.CanAddToCart = originalConfigurator.ChildUserConfigurators.All(c => c.CanAddToCart);
                    
                    // Aggregate products from all child configurators for composite configurators
                    await _productProcessingService.AggregateAndCalculateProductsAsync(originalConfigurator, validation);
                }

                return (originalConfigurator, validation);
            }

            var (processedConfigurator, processValidation) = await _processingService.ProcessConfiguratorAsync(
                configuratorId,
                configuratorState.FieldValues,
                configuratorState.SelectedProducts,
                configuratorState.CategoryStates);

            if (!processValidation.HasErrors && processedConfigurator != null)
            {
                processedConfigurator.TabTitle = configuratorState.TabTitle;
                processedConfigurator.TabOrder = configuratorState.TabOrder;
                processedConfigurator.HasProducts = originalConfigurator?.HasProducts ?? false;
            }

            return (processedConfigurator, processValidation);
        }

        public async Task<(ConfiguratorDto? configurator, ValidationResult validation)> GetFilteredConfiguratorAsync(int configuratorId, ConfiguratorStateDto configuratorState)
        {
            var (configurator, validation) = await GetFilteredConfiguratorEntityAsync(configuratorId, configuratorState);

            if (validation.HasErrors)
                return (null, validation);

            return (_mappingService.MapToDto(configurator!), validation);
        }

        public async Task<IEnumerable<ConfiguratorDto>> GetAllConfiguratorsAsync()
        {
            var configurators = await _configuratorDao.GetMenuConfiguratorsAsync();
            return configurators.Select(_mappingService.MapToDto);
        }

        public async Task<(ConfiguratorDto? configurator, ValidationResult validation)> GetConfiguratorsAsync(int configuratorId)
        {
            var childConfigurators = await _childConfiguratorDao.GetChildConfiguratorsByConfiguratorId(configuratorId);
            var configuratorState = new ConfiguratorStateDto();

            if(childConfigurators.Count > 0)
                configuratorState.ChildConfiguratorStates.Add(new ChildConfiguratorStateDto() {ConfiguratorId = childConfigurators.OrderBy(c => c.DisplayOrder).First().ConfiguratorId});

            return await GetFilteredConfiguratorAsync(configuratorId, configuratorState);
        }

        public async Task<(ProductPriceCalculationResponseDto? result, ValidationResult validation)> CalculateProductPricesAsync(
            int configuratorId,
            List<SelectedProductDto> selectedProducts,
            string currentProductCode)
        {
            var (priceResult, validation) = await _productProcessingService.CalculateSelectedProductPricesAsync(selectedProducts, currentProductCode);

            if (validation.HasErrors || priceResult == null)
            {
                return (null, validation);
            }

            return (_mappingService.MapToDto(priceResult), validation);
        }

        private async Task SetHasProductsPropertyAsync(Configurator configurator)
        {
            if (configurator == null) return;
            
            // First, check if this configurator directly has products
            configurator.HasProducts = configurator.ConfiguratorCompositions?.Any(c => c.ConfiguratorProducts?.Any() == true) == true;

            // For composite configurators, also check child configurators
            if (configurator.IsComposite && configurator.ChildConfigurators?.Any() == true)
            {
                // Batch load all child configurators to avoid N+1 cache/DB calls
                var childConfiguratorIds = configurator.ChildConfigurators.Select(c => c.ConfiguratorId).ToList();
                var childConfigurators = new List<Configurator>();
                
                // Load all children in parallel to reduce total time
                var childLoadTasks = childConfiguratorIds.Select(async id =>
                {
                    var child = await _cacheManager.GetOrAddAsync(id, _configuratorDao.GetCompleteConfiguratorNoTrackingAsync);
                    return child;
                }).ToList();
                
                var loadedChildren = await Task.WhenAll(childLoadTasks);
                childConfigurators.AddRange(loadedChildren.Where(c => c != null)!);

                // Process all children in parallel - child configurators are simple (non-composite)
                // so we can directly check their products without recursion
                var productCheckTasks = childConfigurators.Select(async child =>
                {
                    // Set HasProducts for each child based on their direct products
                    child.HasProducts = child.ConfiguratorCompositions?.Any(c => c.ConfiguratorProducts?.Any() == true) == true;
                    return child.HasProducts;
                }).ToList();
                
                var hasProductsResults = await Task.WhenAll(productCheckTasks);
                
                // If any child has products, the composite should also have products
                if (hasProductsResults.Any(hasProducts => hasProducts))
                {
                    configurator.HasProducts = true;
                }
            }
        }

        public async Task<(CompositeConfiguratorResponseDto? response, ValidationResult validation)> ProcessCompositeConfiguratorAsync(int configuratorId, CompositeConfiguratorStateDto configuratorState)
        {
            var validation = new ValidationResult();

            await _validationService.ValidateConfiguratorExistsAsync(configuratorId, validation);

            if (validation.HasErrors)
                return (null, validation);

            // Validate that this is indeed a composite configurator
            var configurator = await _cacheManager.GetOrAddAsync(configuratorId, _configuratorDao.GetCompleteConfiguratorNoTrackingAsync);
            if (configurator == null)
            {
                validation.AddError($"Configurator {configuratorId} not found");
                return (null, validation);
            }

            if (!configurator.IsComposite)
            {
                validation.AddError($"Configurator {configuratorId} is not a composite configurator");
                return (null, validation);
            }

            await SetHasProductsPropertyAsync(configurator);

            ConfiguratorDto? processedActiveChild = null;

            // Step 1: Process the active child configurator if provided
            if (configuratorState.ActiveChildState != null)
            {
                var (activeChildResult, activeChildValidation) = await GetFilteredConfiguratorAsync(
                    configuratorState.ActiveChildState.ConfiguratorId, 
                    configuratorState.ActiveChildState);

                if (activeChildValidation.HasErrors)
                    return (null, activeChildValidation);

                processedActiveChild = activeChildResult;
            }

            // Step 2: Build composite configurator with aggregation
            // Use the new architecture: combine active child products with other child products
            var allChildProducts = new List<ChildProductStateDto>();
            
            // Add processed active child products if available
            if (processedActiveChild != null)
            {
                var activeChildProductsForAggregation = _mappingService.MapConfiguratorProductsToAggregationDtos(processedActiveChild);
                
                if (activeChildProductsForAggregation != null && activeChildProductsForAggregation.Any())
                {
                    allChildProducts.Add(new ChildProductStateDto
                    {
                        ConfiguratorId = processedActiveChild.Id,
                        TabOrder = processedActiveChild.TabOrder,
                        SelectedProducts = activeChildProductsForAggregation
                    });
                }
            }
            
            // Add other child products
            if (configuratorState.OtherChildProducts?.Any() == true)
            {
                allChildProducts.AddRange(configuratorState.OtherChildProducts);
            }
            
            // Aggregate all products if we have any
            if (allChildProducts.Any())
            {
                await _productProcessingService.AggregateFromChildProductStatesAsync(
                    configurator, 
                    allChildProducts, 
                    validation);
            }

            if (validation.HasErrors)
                return (null, validation);

            var compositeConfiguratorSummary = _mappingService.MapToDto(configurator);

            return (new CompositeConfiguratorResponseDto
            {
                ProcessedActiveChild = processedActiveChild,
                CompositeConfiguratorSummary = compositeConfiguratorSummary
            }, validation);
        }
    }
}
