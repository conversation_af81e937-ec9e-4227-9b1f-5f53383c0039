using StackExchange.Profiling.Internal;
using System.Security.Principal;

namespace DEK.Eshop.ApiCore.Indentity;

public record ApplicationUser : HttpUser
{

    // required from db
    public string? BranchHomeCode { get; init; } = null;

    public string PriceLevelEshop { get; init; } = "ESHOP";

    public int PriceLevelRental { get; init; } = -1;

    public bool HasRentalBan { get; init; } = false;

    public int CompanyId { get; init; } = -1;
    
    public bool IsB2B { get; init; } = false;

    public bool IsCompanyAdmin { get; init; } = false;

    public string? SeasonId { get; init; } = null;

    // Compouted
    public bool IsGuest { get; init; } = true;

    public ApplicationUser(Dictionary<string, string?> claims)
    {
        EshopId = claims["eshopId"];
        CartId = claims["cartId"] ?? string.Empty;
        UserEmail = claims["userEmail"];
        BranchCode = claims["branchCode"];
        BranchHomeCode = claims["branchHomeCode"];
        PriceLevelEshop = claims["priceLevelEshop"] ?? PriceLevelEshop;
        PriceLevelRental = int.TryParse(claims["priceLevelRental"], out int priceLevelRental) ? priceLevelRental : PriceLevelRental;
        HasRentalBan = bool.TryParse(claims["hasRentalBan"], out bool hasRentalBan) ? hasRentalBan : HasRentalBan;
        SeasonId = claims["seasonId"];
        CompanyId = int.TryParse(claims["firmId"], out int firmId) ? firmId : CompanyId;
        IsCompanyAdmin = bool.TryParse(claims["isFirmAdmin"], out bool isAdmin) ? isAdmin : IsCompanyAdmin;
        Database = claims["instance"];
        IsGuest = UserEmail.IsNullOrWhiteSpace() ? true : false;

        IsAuthenticated = true;
    }
}
