using DEK.Eshop.ApiCore.Database;
using DEK.Eshop.ApiCore.Extension;

namespace DEK.Eshop.ApiCore.Indentity;

public class DbUserRepository
{
    private readonly MssqlContextFactory<DbUserContext> factory;

    public DbUserRepository(MssqlContextFactory<DbUserContext> factory)
    {
        this.factory = factory;
    }

    public virtual async Task<DbUser?> GetUser(string userEmail)
    {
        var context = factory.CreateContext();

        var builder = new ProcedureQueryBuilder("[proc].czES_Web_Uzivatel");
        builder.AddParam("@IN_LOGIN_EMAIL", userEmail);

        return (await context.DbUser!.GetListAsync(builder)).FirstOrDefault();
    }
}
