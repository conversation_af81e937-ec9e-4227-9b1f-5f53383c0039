{"version": "2.0.0", "tasks": [{"label": "build-api", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Deksmart.Api/Deksmart.Api.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-ui-server", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Deksmart.Ui.Server/Deksmart.Ui.Server.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "build-web", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web/Deksmart.Ui.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web"}}, {"label": "build-maui-windows", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui/Deksmart.Ui.csproj", "-f", "net9.0-windows10.0.19041.0", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "run-blazor-wasm", "command": "dotnet", "type": "process", "args": ["run", "--project", "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web/Deksmart.Ui.Web.csproj"], "options": {"cwd": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web"}, "problemMatcher": "$msCompile"}, {"label": "run-blazor-wasm-background", "command": "dotnet", "type": "process", "isBackground": true, "args": ["run", "--project", "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web/Deksmart.Ui.Web.csproj", "--urls=https://localhost:7209;http://localhost:5213"], "options": {"cwd": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "problemMatcher": [{"pattern": [{"regexp": ".", "file": 1, "location": 2, "message": 3}], "background": {"activeOnStart": true, "beginsPattern": ".*", "endsPattern": "Now listening on:.*"}}], "presentation": {"reveal": "always", "panel": "dedicated"}, "dependsOn": ["build-web"]}, {"label": "run-ui-server-background", "command": "dotnet", "type": "process", "isBackground": true, "args": ["run", "--project", "${workspaceFolder}/Deksmart.Ui.Server/Deksmart.Ui.Server.csproj", "--urls=https://localhost:7209;http://localhost:5213"], "options": {"cwd": "${workspaceFolder}/Deksmart.Ui.Server", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "problemMatcher": [{"pattern": [{"regexp": ".", "file": 1, "location": 2, "message": 3}], "background": {"activeOnStart": true, "beginsPattern": ".*", "endsPattern": "Now listening on:.*"}}], "presentation": {"reveal": "always", "panel": "dedicated"}, "dependsOn": ["build-ui-server"]}, {"label": "kill-dotnet-processes", "command": "powershell", "type": "process", "args": ["-Command", "Get-Process -Name dotnet -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue"], "presentation": {"reveal": "never", "panel": "shared"}, "problemMatcher": []}]}