.trim-input-number {
    width: 80px;
    padding: 2px 6px;
    font-size: 1rem;
    height: 24px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f9f9f9;
    transition: all 0.2s ease;
}

.bold-value {
    font-weight: bold;
}

.trim-input-number:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 1px rgba(192, 12, 24, 0.1);
    background-color: white;
}

.trim-input-container {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    width: 100%;
}

.input-suffix {
    margin-left: 8px;
    color: var(--secondary-color);
    font-size: 1rem;
}

@media (max-width: 768px) {
    .trim-input-number {
        width: 80px;
        padding: 2px 6px;
        font-size: 1rem;
        height: 24px;
    }
}