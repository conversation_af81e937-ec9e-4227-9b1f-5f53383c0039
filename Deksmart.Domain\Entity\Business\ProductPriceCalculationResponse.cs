﻿namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Serves as a DTO for transferring the results of a price calculation, including VAT and total prices for the current product and the overall selection.
    /// Used in API responses and UI to communicate calculated pricing outcomes for selected products.
    /// Populated by business logic that aggregates and calculates prices for all selected products.
    /// </summary>
    public class ProductPriceCalculationResponse
    {
        public decimal PriceVat { get; set; }
        public decimal TotalPriceNoVat { get; set; }
        public decimal TotalPriceVat { get; set; }
        public decimal TotalVat { get; set; }
    }
}
