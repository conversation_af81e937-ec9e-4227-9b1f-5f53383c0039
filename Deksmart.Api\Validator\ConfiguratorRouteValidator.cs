using Deksmart.Api.Resource;
using FluentValidation;
using FluentValidation.Results;
using Deksmart.Shared.Dto;

namespace Deksmart.Api.Validator
{
    public interface IConfiguratorRouteValidator
    {
        Task<ValidationResult> ValidateAsync(string id, CancellationToken cancellationToken = default);
    }

    public interface IPresetRouteValidator
    {
        Task<ValidationResult> ValidateAsync(string id, CancellationToken cancellationToken = default);
    }

    public interface IEmailConfiguratorStateValidator
    {
        Task<ValidationResult> ValidateAsync(EmailConfiguratorStateDto state, CancellationToken cancellationToken = default);
    }

    public class ConfiguratorRouteValidator : AbstractValidator<string>, IConfiguratorRouteValidator
    {
        public ConfiguratorRouteValidator()
        {
            RuleFor(x => x)
                .Must(x => int.TryParse(x, out _))
                .WithMessage(DeksmartApiResource.Validation_InvalidConfiguratorId);
        }
    }

    public class PresetRouteValidator : AbstractValidator<string>, IPresetRouteValidator
    {
        public PresetRouteValidator()
        {
            RuleFor(x => x)
                .Must(x => Guid.TryParse(x, out _))
                .WithMessage(DeksmartApiResource.Validation_InvalidPresetId);
        }
    }

    public class EmailConfiguratorStateValidator : AbstractValidator<EmailConfiguratorStateDto>, IEmailConfiguratorStateValidator
    {
        public EmailConfiguratorStateValidator()
        {
            RuleFor(x => x.ContactInfo)
                .NotNull()
                .WithMessage(DeksmartApiResource.Validation_ContactInfoRequired);

            When(x => x.ContactInfo != null, () =>
            {
                RuleFor(x => x.ContactInfo!.Email)
                    .NotEmpty()
                    .WithMessage(DeksmartApiResource.Validation_EmailRequired)
                    .EmailAddress()
                    .WithMessage(DeksmartApiResource.Validation_InvalidEmailFormat);

                RuleFor(x => x.ContactInfo!.Name)
                    .NotEmpty()
                    .WithMessage(DeksmartApiResource.Validation_NameRequired)
                    .MaximumLength(100)
                    .WithMessage(DeksmartApiResource.Validation_NameTooLong);

                RuleFor(x => x.ContactInfo!.Phone)
                    .MaximumLength(20)
                    .WithMessage(DeksmartApiResource.Validation_PhoneTooLong)
                    .Matches(@"^\+?[\d\s-()]+$")
                    .When(x => !string.IsNullOrEmpty(x.ContactInfo!.Phone))
                    .WithMessage(DeksmartApiResource.Validation_InvalidPhoneFormat);
            });
        }
    }
} 
