using DEK.Eshop.ApiCore.Config.Dto;
using DEK.Eshop.ApiCore.Config;
using Microsoft.AspNetCore.Http;
using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace DEK.Eshop.ApiCore.Indentity;

public class HttpUserFactory
{
    private HttpContext httpContext;
    private readonly IConfiguration configuration;
    private readonly IHostEnvironment environment;

    public HttpUserFactory(IHttpContextAccessor contextAccessor, IConfiguration configuration, IHostEnvironment environment)
    {
        if (contextAccessor.HttpContext is null) {
            throw new ArgumentNullException("Missing HttpContext.");
        }

        this.httpContext = contextAccessor.HttpContext;
        this.configuration = configuration;
        this.environment = environment;
    }

    /// <summary>
    /// Interal useage only. If you need eshopId as soon as possible use ConfigService
    /// Use ApplicationUserManager to get current user.
    /// </summary>
    /// <returns></returns>
    public ApplicationUser Create()
    {
        var claims = new Dictionary<string, string?>() {
            ["iss"] = null,
            ["eshopId"] = null,
            ["cartId"] = null,
            ["userEmail"] = null,
            ["branchCode"] = null,
            ["branchHomeCode"] = null,
            ["priceLevelEshop"] = null,
            ["priceLevelRental"] = null,
            ["hasRentalBan"] = null,
            ["firmId"] = null,
            ["isFirmAdmin"] = null,
            ["seasonId"] = null,
            ["instance"] = null,
        };

        foreach (var claim in this.httpContext.User.Claims) {
            claims[claim.Type] = claim.Value;
        }

        return new ApplicationUser(claims);
    }

}

