namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Represents the product state of a child configurator for composite aggregation.
    /// Contains only product selection data needed for aggregating products across child configurators.
    /// </summary>
    [Serializable]
    public class ChildProductStateDto
    {
        /// <summary>
        /// The ID of the child configurator
        /// </summary>
        public int ConfiguratorId { get; set; }

        /// <summary>
        /// The tab order of the child configurator, used for composition ordering
        /// </summary>
        public int? TabOrder { get; set; }

        /// <summary>
        /// The selected products with their details for this child configurator.
        /// Contains sufficient product information for aggregation without requiring additional API calls.
        /// </summary>
        public List<ProductForAggregationDto> SelectedProducts { get; set; } = [];
    }

    /// <summary>
    /// Represents a product with its essential information for aggregation purposes.
    /// Contains only the necessary data to perform product aggregation across child configurators.
    /// </summary>
    [Serializable]
    public class ProductForAggregationDto
    {
        /// <summary>
        /// The product code used for grouping products across child configurators
        /// </summary>
        public string ProductCode { get; set; } = null!;

        /// <summary>
        /// The calculated amount of this product
        /// </summary>
        public decimal CalculatedAmount { get; set; }

        /// <summary>
        /// The user-specified amount, if any
        /// </summary>
        public decimal? UserAmount { get; set; }

        /// <summary>
        /// The unit of measurement for this product
        /// </summary>
        public string ProductUnit { get; set; } = null!;

        /// <summary>
        /// The product title
        /// </summary>
        public string Title { get; set; } = null!;

        /// <summary>
        /// The composition title from which this product comes
        /// </summary>
        public string CompositionTitle { get; set; } = null!;

        /// <summary>
        /// The order of the composition within the configurator
        /// </summary>
        public int CompositionOrder { get; set; }

        /// <summary>
        /// The order of the product within the composition
        /// </summary>
        public int ProductOrder { get; set; }
    }
}