using System.Data;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Moq;
using Deksmart.Domain.Enum;

namespace Deksmart.Domain.Tests
{
    public class ConfiguratorImportParserTest
    {
        private readonly Mock<IImportValidationService> _importValidationServiceMock;
        private readonly Mock<IMarkdownService> _markdownServiceMock;
        private readonly Mock<IValidationService> _validationServiceMock;
        private readonly ConfiguratorImportParser _parser;

        public ConfiguratorImportParserTest()
        {
            _importValidationServiceMock = new Mock<IImportValidationService>();
            _markdownServiceMock = new Mock<IMarkdownService>();
            _validationServiceMock = new Mock<IValidationService>();
            _validationServiceMock.Setup(x => x.CreateValidation()).Returns(new ValidationResult());

            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>()))
                .Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellNumeric(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellBool(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, true, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellImage(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, "image.jpg", new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<ComponentType>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, ComponentType.Numeric, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<FieldPosition>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, FieldPosition.Left, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateExpressionField(It.IsAny<string>(), It.IsAny<ConfiguratorField>(), It.IsAny<int>()))
                .Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateIdentRequiredWhenTitlePresent(It.IsAny<string>(), It.IsAny<string>())).Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateIdentMaxLength(It.IsAny<string>())).Returns(new ValidationResult());

            _markdownServiceMock.Setup(x => x.ConvertToHtml(It.IsAny<string>(), It.IsAny<int>()))
                .Returns(("SomeHtml", new ValidationResult()));

            _parser = new ConfiguratorImportParser(
                _importValidationServiceMock.Object,
                _markdownServiceMock.Object,
                _validationServiceMock.Object);
        }

        [Fact]
        public void ParseConfigurator_WithNewConfigurator_CreatesNewConfigurator()
        {
            // Arrange
            var configuratorId = 1;
            Configurator? existingConfigurator = null;
            var configuratorRow = CreateDataRow(new object[] { configuratorId, "Test Title", false, "Test Description", "Test End Description" });
            var validation = new ValidationResult();

            _markdownServiceMock.Setup(x => x.ConvertToHtml("Test Description", 2))
                .Returns(("Test Description", validation));
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Test End Description", 2))
                .Returns(("Test End Description", validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellBool("False", 2))
                .Returns((true, false, validation));

            // Act
            var (configurator, isUpdated) = _parser.ParseConfigurator(configuratorId, existingConfigurator, configuratorRow, validation);

            // Assert
            Assert.False(isUpdated);
            Assert.Equal(configuratorId, configurator.Id);
            Assert.Equal("Test Title", configurator.Title);
            Assert.Equal("Test Description", configurator.Description);
            Assert.Equal("Test End Description", configurator.EndDescription);
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ParseConfigurator_WithExistingConfigurator_UpdatesConfigurator()
        {
            // Arrange
            var configuratorId = 1;
            var existingConfigurator = new Configurator { Id = configuratorId };
            var configuratorRow = CreateDataRow(new object[] { configuratorId, "Updated Title", true, "Updated Description", "Updated End Description" });
            var validation = new ValidationResult();

            _markdownServiceMock.Setup(x => x.ConvertToHtml("Updated Description", 2))
                .Returns(("Updated Description", validation));
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Updated End Description", 2))
                .Returns(("Updated End Description", validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellBool("True", 2))
                .Returns((true, true, validation));

            // Act
            var (configurator, isUpdated) = _parser.ParseConfigurator(configuratorId, existingConfigurator, configuratorRow, validation);

            // Assert
            Assert.True(isUpdated);
            Assert.Equal(configuratorId, configurator.Id);
            Assert.Equal("Updated Title", configurator.Title);
            Assert.Equal("Updated Description", configurator.Description);
            Assert.Equal("Updated End Description", configurator.EndDescription);
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ParseConfigurator_WithEmptyTitle_AddsValidationError()
        {
            // Arrange
            var configuratorId = 1;
            Configurator? existingConfigurator = null;
            var configuratorRow = CreateDataRow(new object[] { configuratorId, "", false, "Desc", "EndDesc" });
            var validation = new ValidationResult();

            var errorValidation = new ValidationResult();
            errorValidation.AddError("Title is required");

            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>()))
                .Returns(errorValidation);

            // Act
            var (configurator, isUpdated) = _parser.ParseConfigurator(configuratorId, existingConfigurator, configuratorRow, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains("Title is required", validation.GetErrors());
        }

        [Fact]
        public void MapFields_WithValidData_CreatesFieldStructure()
        {
            // Arrange
            var configurator = new Configurator { Id = 1 };
            var result = new DataSet();

            // Add empty first table for ParseConfigurator
            result.Tables.Add("Configurator");

            // Add fields table as second table with updated column names
            var fieldTable = result.Tables.Add("Fields");
            // Updated column names to match current implementation
            fieldTable.Columns.Add("category");                 // 0
            fieldTable.Columns.Add("category_order");           // 1
            fieldTable.Columns.Add("category_visibility");      // 2
            fieldTable.Columns.Add("isTreeView");               // 3
            fieldTable.Columns.Add("ident");                    // 4
            fieldTable.Columns.Add("field");                    // 5
            fieldTable.Columns.Add("field_type");               // 6
            fieldTable.Columns.Add("position");                 // 7
            fieldTable.Columns.Add("field_order");              // 8
            fieldTable.Columns.Add("field_description");        // 9
            fieldTable.Columns.Add("suffix");                   // 10
            fieldTable.Columns.Add("min");                      // 11
            fieldTable.Columns.Add("max");                      // 12
            fieldTable.Columns.Add("composition_order");      // 13
            fieldTable.Columns.Add("expression");               // 14
            fieldTable.Columns.Add("field_visibility");         // 15
            fieldTable.Columns.Add("default");                  // 16
            fieldTable.Columns.Add("validation_expression");    // 17
            fieldTable.Columns.Add("validation_message");       // 18
            fieldTable.Columns.Add("filter_value_title");       // 19
            fieldTable.Columns.Add("picture");                  // 20
            fieldTable.Columns.Add("filter_value");             // 21
            fieldTable.Columns.Add("filter_value_order");       // 22
            fieldTable.Columns.Add("value_visibility");         // 23

            // Add header row
            fieldTable.Rows.Add(new object[24]);

            // Add category with field and value
            var row = fieldTable.NewRow();
            row[0] = "Test Category";       // category
            row[1] = 1;                     // category_order
            row[2] = "1";                   // category_visibility
            row[3] = true;                  // isTreeView
            row[4] = "F1";                  // ident
            row[5] = "Test Field";          // field
            row[6] = "Numeric";             // field_type
            row[7] = "Left";                // position
            row[8] = 1;                     // field_order
            row[9] = "Field Description";   // field_description
            row[10] = "Field Suffix";       // suffix
            row[11] = 0;                    // min
            row[12] = 100;                  // max
            row[13] = 50;                   // composition_order
            row[14] = "1";                  // expression
            row[15] = "1";                  // field_visibility
            row[16] = 1;                    // default
            row[17] = "";                   // validation_expression
            row[18] = "";                   // validation_message
            row[19] = "Test Value";         // filter_value_title
            row[20] = "image.jpg";          // picture
            row[21] = 42;                   // filter_value
            row[22] = 1;                    // filter_value_order
            row[23] = "1";                  // value_visibility
            fieldTable.Rows.Add(row);

            var presentIds = new PresentIds();
            var expressionCache = new Dictionary<string, ConfiguratorExpression>();
            var validation = new ValidationResult();

            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>()))
                .Returns(validation);
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger("1", 2))
                .Returns((true, 1, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger("0", 2))
                .Returns((true, 0, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger("100", 2))
                .Returns((true, 100, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger("50", 2))
                .Returns((true, 50, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellNumeric("1", 2))
                .Returns((true, 1, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellNumeric("42", 2))
                .Returns((true, 42, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellBool("True", 2))
                .Returns((true, true, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellImage("image.jpg", 2))
                .Returns((true, "image.jpg", validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<Enum.ComponentType>("Numeric", It.IsAny<string>(), 2))
                .Returns((true, Enum.ComponentType.Numeric, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<Enum.FieldPosition>("Left", It.IsAny<string>(), 2))
                .Returns((true, Enum.FieldPosition.Left, validation));
            _importValidationServiceMock.Setup(x => x.ValidateExpressionField(It.IsAny<string>(), It.IsAny<ConfiguratorField>(), 2))
                .Returns(validation);
            _importValidationServiceMock.Setup(x => x.ValidateIdentRequiredWhenTitlePresent("F1", "Test Field")).Returns(validation);
            _importValidationServiceMock.Setup(x => x.ValidateIdentMaxLength("F1")).Returns(validation);
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Test Field", 2))
                .Returns(("Field Title", validation));
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Field Description", 2))
                .Returns(("Field Description", validation));
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Field Suffix", 2))
                .Returns(("Field Suffix", validation));
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Test Value", 2))
                .Returns(("Test Value", validation));
            _markdownServiceMock.Setup(x => x.ConvertToHtml("Test Value", 2))
                .Returns(("Test Field", validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<CategoryCollapseState>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, CategoryCollapseState.Expanded, new ValidationResult()));

            // Act
            _parser.MapFields(configurator, result, presentIds, expressionCache, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(configurator.ConfiguratorFieldCategories);
            var category = configurator.ConfiguratorFieldCategories.First();
            Assert.Equal("Test Category", category.Title);
            Assert.Equal(1, category.Order);
            Assert.Equal(CategoryCollapseState.Expanded, category.CollapseState);
            Assert.NotNull(category.Visibility);

            Assert.Single(category.ConfiguratorFields);
            var field = category.ConfiguratorFields.First();
            Assert.Equal("Field Title", field.Title);
            Assert.Equal("Field Description", field.Description);
            Assert.Equal("Field Suffix", field.Suffix);
            Assert.Equal(0, field.MinValue);
            Assert.Equal(100, field.MaxValue);
            Assert.Equal(50, field.CompositionOrder);
            Assert.Equal(1, field.DefaultValue);
            Assert.Equal(1, field.Order);
            Assert.Equal("F1", field.Ident);
            Assert.NotNull(field.Visibility);

            Assert.Single(field.ConfiguratorFieldValues);
            var value = field.ConfiguratorFieldValues.First();
            Assert.Equal("Test Value", value.Title);
            Assert.Equal("image.jpg", value.Image);
            Assert.Equal(42, value.NumericValue);
            Assert.Equal(1, value.Order);
            Assert.NotNull(value.Visibility);
        }

        [Fact]
        public void MapFields_WithInvalidField_AddsValidationError()
        {
            // Arrange
            var configurator = new Configurator { Id = 1 };
            var result = new DataSet();
            result.Tables.Add("Configurator");
            var fieldTable = result.Tables.Add("Fields");
            for (int i = 0; i < 24; i++) fieldTable.Columns.Add($"Col{i}");
            fieldTable.Rows.Add(new object[24]);
            var row = fieldTable.NewRow();
            row[0] = "Category";
            row[1] = 1;
            row[4] = "F1";
            row[5] = ""; // Field title is empty (invalid)
            row[6] = "Numeric";
            row[8] = 1;
            fieldTable.Rows.Add(row);

            var presentIds = new PresentIds();
            var expressionCache = new Dictionary<string, ConfiguratorExpression>();
            var validation = new ValidationResult();

            var errorValidation = new ValidationResult();
            errorValidation.AddError("Field title required");

            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(errorValidation);
            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.Is<object>(o => o != null && o.ToString() != ""), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<ComponentType>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, ComponentType.Numeric, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateIdentMaxLength(It.IsAny<string>())).Returns(new ValidationResult());
            _markdownServiceMock.Setup(x => x.ConvertToHtml(It.IsAny<string>(), It.IsAny<int>())).Returns(("", new ValidationResult()));

            // Act
            _parser.MapFields(configurator, result, presentIds, expressionCache, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains("Field title required", validation.GetErrors());
        }

        [Fact]
        public void MapProducts_WithValidData_CreatesProductStructure()
        {
            // Arrange
            var configurator = new Configurator { Id = 1 };
            var result = new DataSet();

            // Add empty first table for ParseConfigurator
            result.Tables.Add("Configurator");

            // Add empty second table for MapFields
            result.Tables.Add("Fields");

            // Add products table as third table with updated column names
            var productsTable = result.Tables.Add("Products");
            productsTable.Columns.Add("composition");              // 0
            productsTable.Columns.Add("composition_order");        // 1
            productsTable.Columns.Add("composition_visibility");   // 2
            productsTable.Columns.Add("multiple_products");        // 3
            productsTable.Columns.Add("product");                  // 4
            productsTable.Columns.Add("product_id");               // 5
            productsTable.Columns.Add("product_order");            // 6
            productsTable.Columns.Add("product_expression");       // 7
            productsTable.Columns.Add("product_visibility");       // 8
            productsTable.Columns.Add("product_volume");           // 9
            productsTable.Columns.Add("product_measurement_unit"); // 10

            // Add header row
            productsTable.Rows.Add(new object[11]);

            // Add composition with product
            var row = productsTable.NewRow();
            row[0] = "Test Composition";    // composition
            row[1] = 1;                     // composition_order
            row[2] = "1";                   // composition_visibility
            row[3] = "true";                // multiple_products
            row[4] = "Test Product";        // product
            row[5] = "ProductCode";         // product_id
            row[6] = 2;                     // product_order
            row[7] = "1";                   // product_expression
            row[8] = "1";                   // product_visibility
            row[9] = 3;                     // product_volume
            row[10] = "pcs";                // product_measurement_unit
            productsTable.Rows.Add(row);

            var presentIds = new PresentIds();
            var expressionCache = new Dictionary<string, ConfiguratorExpression>();
            var validation = new ValidationResult();

            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>()))
                .Returns(validation);
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger("1", 2))
                .Returns((true, 1, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger("2", 2))
                .Returns((true, 2, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellNumeric("3", 2))
                .Returns((true, 3, validation));
            _importValidationServiceMock.Setup(x => x.ValidateCellBool("true", 2))
                .Returns((true, true, validation));

            // Act
            _parser.MapProducts(configurator, result, presentIds, expressionCache, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(configurator.ConfiguratorCompositions);
            var composition = configurator.ConfiguratorCompositions.First();
            Assert.Equal("Test Composition", composition.Title);
            Assert.Equal(1, composition.Order);
            Assert.NotNull(composition.Visibility);

            Assert.Single(composition.ConfiguratorProducts);
            var product = composition.ConfiguratorProducts.First();
            Assert.Equal("Test Product", product.Title);
            Assert.Equal("ProductCode", product.ProductCode);
            Assert.NotNull(product.Quantity);
            Assert.NotNull(product.Visibility);
            Assert.Equal(3, product.ProductVolume);
            Assert.Equal("pcs", product.ProductUnit);
            Assert.Equal(2, product.Order);
        }

        [Fact]
        public void MapProducts_WithInvalidProduct_AddsValidationError()
        {
            // Arrange
            var configurator = new Configurator { Id = 1 };
            var result = new DataSet();
            result.Tables.Add("Configurator");
            result.Tables.Add("Fields");
            var productsTable = result.Tables.Add("Products");
            for (int i = 0; i < 11; i++) productsTable.Columns.Add($"Col{i}");
            productsTable.Rows.Add(new object[11]);
            var row = productsTable.NewRow();
            row[0] = "Composition";
            row[1] = 1;
            row[4] = ""; // Product name is empty (invalid)
            row[5] = "ProductCode";
            row[6] = 2;
            row[7] = "1";
            row[8] = "1";
            row[9] = 3;
            row[10] = "pcs";
            productsTable.Rows.Add(row);

            var presentIds = new PresentIds();
            var expressionCache = new Dictionary<string, ConfiguratorExpression>();
            var validation = new ValidationResult();

            var errorValidation = new ValidationResult();
            errorValidation.AddError("Product name required");

            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(errorValidation);
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellNumeric(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellBool(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, true, new ValidationResult()));

            // Act
            _parser.MapProducts(configurator, result, presentIds, expressionCache, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains("Product name required", validation.GetErrors());
        }

        private static DataRow CreateDataRow(object[] values)
        {
            var table = new DataTable();
            for (int i = 0; i < values.Length; i++)
            {
                table.Columns.Add($"Column{i}");
            }
            return table.Rows.Add(values);
        }
    }
}
