using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Entity.Business;
using Deksmart.Shared.Dto;

namespace Deksmart.Application.Mapping
{
    public interface IEshopMappingService
    {
        EshopProductDto MapToProductDto(EshopProduct product);
        EshopProductSpecItemDto MapToSpecItemDto(EshopProductSpecItem spec);
        ProductDetailsDto? MapProductDetailsToDto(ProductDetails? productDetails);
    }

    public class EshopMappingService : IEshopMappingService
    {
        public EshopProductDto MapToProductDto(EshopProduct product)
        {
            return new EshopProductDto
            {
                Code = product.Code,
                Title = product.Title,
                Description = product.Description,
                Image = product.Image
            };
        }

        public EshopProductSpecItemDto MapToSpecItemDto(EshopProductSpecItem spec)
        {
            return new EshopProductSpecItemDto
            {
                Title = spec.Title,
                Value = spec.Value
            };
        }

        public ProductDetailsDto? MapProductDetailsToDto(ProductDetails? productDetails)
        {
            if (productDetails == null)
                return null;

            return new ProductDetailsDto
            {
                Product = MapToProductDto(productDetails.Product),
                Specifications = productDetails.Specifications.Select(MapToSpecItemDto).ToList()
            };
        }
    }
} 