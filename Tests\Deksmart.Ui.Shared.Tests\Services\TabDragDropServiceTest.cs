using Microsoft.Extensions.Logging;
using Moq;
using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components.Web;
using Deksmart.Ui.Model;
using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class TabDragDropServiceTest
    {
        private readonly Mock<ILogger<TabDragDropService>> _mockLogger;
        private readonly TabDragDropService _service;
        private readonly List<IConfiguratorGridService> _testServices;

        public TabDragDropServiceTest()
        {
            _mockLogger = new Mock<ILogger<TabDragDropService>>();
            _service = new TabDragDropService(_mockLogger.Object);
            _testServices = CreateTestServices();
        }

        private List<IConfiguratorGridService> CreateTestServices()
        {
            var services = new List<IConfiguratorGridService>();
            for (int i = 0; i < 3; i++)
            {
                var configuratorDto = new ConfiguratorDto
                {
                    Id = i + 1,
                    Title = $"Configurator {i + 1}",
                    Description = $"Description {i + 1}",
                    IsValid = true
                };
                var wrapper = new ConfiguratorWrapper(configuratorDto);
                
                var mockService = new Mock<IConfiguratorGridService>();
                mockService.Setup(s => s.ConfiguratorWrapper).Returns(wrapper);
                services.Add(mockService.Object);
            }
            return services;
        }

        [Fact]
        public void StartDrag_SetsCorrectDraggedService()
        {
            var service = _testServices[0];

            _service.StartDrag(service);

            Assert.Equal(service, _service.DraggedService);
        }

        [Fact]
        public void StartDrag_WhenEditModeActive_DoesNotSetDraggedService()
        {
            _service.SetEditMode(true);
            var service = _testServices[0];

            _service.StartDrag(service);

            Assert.Null(_service.DraggedService);
        }

        [Fact]
        public void EndDrag_ClearsAllDragState()
        {
            var service = _testServices[0];
            _service.StartDrag(service);
            _service.HandleDropZoneOver(1, _testServices);

            _service.EndDrag();

            Assert.Null(_service.DraggedService);
            Assert.Null(_service.DragOverService);
            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void EndDrag_RaisesDragEndedEvent()
        {
            bool eventRaised = false;
            _service.DragEnded += (sender, e) => eventRaised = true;

            _service.EndDrag();

            Assert.True(eventRaised);
        }

        [Fact]
        public void HandleDropZoneOver_WhenEditModeActive_DoesNotSetDropZone()
        {
            _service.SetEditMode(true);
            _service.StartDrag(_testServices[0]);

            _service.HandleDropZoneOver(1, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneOver_WhenNoDraggedService_DoesNotSetDropZone()
        {
            _service.HandleDropZoneOver(1, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneOver_WhenWouldResultInNoMovement_ClearsIndicators()
        {
            _service.StartDrag(_testServices[0]);
            _service.HandleDropZoneOver(2, _testServices);
            Assert.Equal(2, _service.DropZoneIndex);

            _service.HandleDropZoneOver(0, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneOver_WhenWouldResultInNoMovement_NextPosition_ClearsIndicators()
        {
            _service.StartDrag(_testServices[0]);
            _service.HandleDropZoneOver(2, _testServices);
            Assert.Equal(2, _service.DropZoneIndex);

            _service.HandleDropZoneOver(1, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneOver_ValidPosition_SetsDropZoneIndex()
        {
            _service.StartDrag(_testServices[0]);

            _service.HandleDropZoneOver(2, _testServices);

            Assert.Equal(2, _service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneOver_RaisesIndicatorChangedEvent()
        {
            DragIndicatorEventArgs? receivedArgs = null;
            _service.IndicatorChanged += (sender, e) => receivedArgs = e;
            _service.StartDrag(_testServices[0]);

            _service.HandleDropZoneOver(2, _testServices);

            Assert.NotNull(receivedArgs);
            Assert.Equal(2, receivedArgs.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneLeave_ClearsCorrectDropZone()
        {
            _service.StartDrag(_testServices[0]);
            _service.HandleDropZoneOver(2, _testServices);
            Assert.Equal(2, _service.DropZoneIndex);

            _service.HandleDropZoneLeave(2);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneLeave_WrongIndex_DoesNotClearDropZone()
        {
            _service.StartDrag(_testServices[0]);
            _service.HandleDropZoneOver(2, _testServices);

            _service.HandleDropZoneLeave(1);

            Assert.Equal(2, _service.DropZoneIndex);
        }

        [Fact]
        public void HandleDropZoneDrop_WhenEditModeActive_DoesNotTriggerReorder()
        {
            bool eventRaised = false;
            _service.TabReordered += (sender, e) => eventRaised = true;
            _service.SetEditMode(true);
            _service.StartDrag(_testServices[0]);

            _service.HandleDropZoneDrop(2);

            Assert.False(eventRaised);
        }

        [Fact]
        public void HandleDropZoneDrop_WhenNoDraggedService_DoesNotTriggerReorder()
        {
            bool eventRaised = false;
            _service.TabReordered += (sender, e) => eventRaised = true;

            _service.HandleDropZoneDrop(2);

            Assert.False(eventRaised);
        }

        [Fact]
        public void HandleDropZoneDrop_ValidDrop_RaisesTabReorderedEvent()
        {
            TabReorderEventArgs? receivedArgs = null;
            _service.TabReordered += (sender, e) => receivedArgs = e;
            _service.StartDrag(_testServices[0]);

            _service.HandleDropZoneDrop(2);

            Assert.NotNull(receivedArgs);
            Assert.Equal(_testServices[0], receivedArgs.DraggedService);
            Assert.Equal(2, receivedArgs.TargetIndex);
        }

        [Fact]
        public void HandleTabDragOver_WhenEditModeActive_DoesNotSetIndicators()
        {
            _service.SetEditMode(true);
            _service.StartDrag(_testServices[0]);
            var dragArgs = new DragEventArgs { OffsetX = 30 };

            _service.HandleTabDragOver(_testServices[1], 1, dragArgs, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleTabDragOver_DraggingOnSelf_DoesNotSetIndicators()
        {
            _service.StartDrag(_testServices[0]);
            var dragArgs = new DragEventArgs { OffsetX = 30 };

            _service.HandleTabDragOver(_testServices[0], 0, dragArgs, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleTabDragOver_LeftSide_WouldResultInNoMovement_ClearsIndicators()
        {
            _service.StartDrag(_testServices[0]);
            var dragArgs = new DragEventArgs { OffsetX = 30 };

            _service.HandleTabDragOver(_testServices[1], 1, dragArgs, _testServices);

            // Dragging service[0] to left side of service[1] (index 1) would result in no movement
            // because the visual order would remain the same: service[0], service[1], service[2]
            Assert.Null(_service.DropZoneIndex);
            Assert.Null(_service.DragOverService);
        }

        [Fact]
        public void HandleTabDragOver_RightSide_WouldResultInNoMovement_ClearsIndicators()
        {
            _service.StartDrag(_testServices[1]);
            var dragArgs = new DragEventArgs { OffsetX = 80 };

            _service.HandleTabDragOver(_testServices[2], 2, dragArgs, _testServices);

            // Dragging service[1] to right side of service[2] would result in no movement
            // because service[1] is already immediately before service[2]
            Assert.Null(_service.DropZoneIndex);
            Assert.Null(_service.DragOverService);
        }

        [Fact]
        public void HandleTabDragOver_FromMiddleToStart_ValidMovement_SetsIndicator()
        {
            _service.StartDrag(_testServices[1]);
            var dragArgs = new DragEventArgs { OffsetX = 30 };

            _service.HandleTabDragOver(_testServices[0], 0, dragArgs, _testServices);

            // Dragging service[1] to left side of service[0] (index 0) is valid movement
            Assert.Equal(0, _service.DropZoneIndex);
            Assert.Equal(_testServices[0], _service.DragOverService);
        }

        [Fact]
        public void HandleTabDragLeave_ClearsDragOverService()
        {
            _service.StartDrag(_testServices[0]);
            var dragArgs = new DragEventArgs { OffsetX = 80 };
            _service.HandleTabDragOver(_testServices[2], 2, dragArgs, _testServices);
            Assert.Equal(_testServices[2], _service.DragOverService);

            _service.HandleTabDragLeave(_testServices[2]);

            Assert.Null(_service.DragOverService);
            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleTabDragLeave_WrongService_DoesNotClearDragOverService()
        {
            _service.StartDrag(_testServices[0]);
            var dragArgs = new DragEventArgs { OffsetX = 80 };
            _service.HandleTabDragOver(_testServices[2], 2, dragArgs, _testServices);

            _service.HandleTabDragLeave(_testServices[1]);

            Assert.Equal(_testServices[2], _service.DragOverService);
        }

        [Fact]
        public void HandleAddButtonDragOver_WhenNoDraggedService_DoesNotSetDropZone()
        {
            _service.HandleAddButtonDragOver(_testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleAddButtonDragOver_WouldResultInNoMovement_ClearsIndicators()
        {
            _service.StartDrag(_testServices[2]);
            _service.HandleDropZoneOver(1, _testServices);
            Assert.Equal(1, _service.DropZoneIndex);

            _service.HandleAddButtonDragOver(_testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleAddButtonDragOver_ValidDrop_SetsEndPosition()
        {
            _service.StartDrag(_testServices[0]);

            _service.HandleAddButtonDragOver(_testServices);

            Assert.Equal(_testServices.Count, _service.DropZoneIndex);
        }

        [Fact]
        public void HandleAddButtonDragLeave_ClearsEndPosition()
        {
            _service.StartDrag(_testServices[0]);
            _service.HandleAddButtonDragOver(_testServices);
            Assert.Equal(3, _service.DropZoneIndex);

            _service.HandleAddButtonDragLeave(_testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void HandleAddButtonDrop_ValidDrop_TriggersReorderToEnd()
        {
            TabReorderEventArgs? receivedArgs = null;
            _service.TabReordered += (sender, e) => receivedArgs = e;
            _service.StartDrag(_testServices[0]);

            _service.HandleAddButtonDrop(_testServices);

            Assert.NotNull(receivedArgs);
            Assert.Equal(_testServices[0], receivedArgs.DraggedService);
            Assert.Equal(_testServices.Count, receivedArgs.TargetIndex);
        }

        [Fact]
        public void HandleTabDrop_WithValidDropZone_TriggersReorder()
        {
            TabReorderEventArgs? receivedArgs = null;
            _service.TabReordered += (sender, e) => receivedArgs = e;
            _service.StartDrag(_testServices[0]);
            _service.HandleDropZoneOver(2, _testServices);

            _service.HandleTabDrop();

            Assert.NotNull(receivedArgs);
            Assert.Equal(_testServices[0], receivedArgs.DraggedService);
            Assert.Equal(2, receivedArgs.TargetIndex);
        }

        [Fact]
        public void WouldResultInNoMovement_CurrentPosition_ReturnsTrue()
        {
            _service.StartDrag(_testServices[1]);

            _service.HandleDropZoneOver(1, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void WouldResultInNoMovement_NextPosition_ReturnsTrue()
        {
            _service.StartDrag(_testServices[1]);

            _service.HandleDropZoneOver(2, _testServices);

            Assert.Null(_service.DropZoneIndex);
        }

        [Fact]
        public void WouldResultInNoMovement_DifferentPosition_ReturnsFalse()
        {
            _service.StartDrag(_testServices[1]);

            _service.HandleDropZoneOver(0, _testServices);

            Assert.Equal(0, _service.DropZoneIndex);
        }

        [Fact]
        public void EventArgs_TabReorderEventArgs_StoresCorrectValues()
        {
            var service = _testServices[0];
            var targetIndex = 2;

            var eventArgs = new TabReorderEventArgs(service, targetIndex);

            Assert.Equal(service, eventArgs.DraggedService);
            Assert.Equal(targetIndex, eventArgs.TargetIndex);
        }

        [Fact]
        public void EventArgs_DragIndicatorEventArgs_StoresCorrectValues()
        {
            var dropZoneIndex = 1;
            var dragOverService = _testServices[0];

            var eventArgs = new DragIndicatorEventArgs(dropZoneIndex, dragOverService);

            Assert.Equal(dropZoneIndex, eventArgs.DropZoneIndex);
            Assert.Equal(dragOverService, eventArgs.DragOverService);
        }

        [Fact]
        public void EventArgs_DragIndicatorEventArgs_WithNullValues_StoresCorrectly()
        {
            var eventArgs = new DragIndicatorEventArgs(null, null);

            Assert.Null(eventArgs.DropZoneIndex);
            Assert.Null(eventArgs.DragOverService);
        }
    }
}