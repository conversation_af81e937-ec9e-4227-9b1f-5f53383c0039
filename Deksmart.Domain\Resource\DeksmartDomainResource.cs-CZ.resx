﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ComponentTypeDoesNotExist" xml:space="preserve">
    <value>Řádek {0}: Neplatný typ komponenty: {1}</value>
  </data>
  <data name="CollapseStateDoesNotExist" xml:space="preserve">
    <value>Řádek {0}: Neplatný stav sbalení: {1}</value>
  </data>
  <data name="FieldPositionDoesNotExist" xml:space="preserve">
    <value>Řádek {0}: Neplatná pozice pole: {1}</value>
  </data>
  <data name="ExpressionDoesNotMatch" xml:space="preserve">
    <value>Výraz {0} nelze analyzovat, možná chybí ident nebo je nesprávná syntaxe.</value>
  </data>
  <data name="ExpressionValueCannotBeEmpty" xml:space="preserve">
    <value>Řádek {0}: Výraz nemůže být ve výrazovém poli {1} prázdný.</value>
  </data>
  <data name="ImportConfiguratorError" xml:space="preserve">
    <value>První list definující kalkulačku má nesprávný formát</value>
  </data>
  <data name="ImportProductsError" xml:space="preserve">
    <value>Třetí list definující produkty je nesprávně naformátovaný nebo chybí.</value>
  </data>
  <data name="IsNotBoolean" xml:space="preserve">
    <value>Řádek {0}: Hodnota {1} by měla být booleovská.</value>
  </data>
  <data name="IsNotImageUrl" xml:space="preserve">
    <value>Řádek {0}: Hodnota {1} by měla být adresa URL obrázku.</value>
  </data>
  <data name="IsNotInteger" xml:space="preserve">
    <value>Řádek {0}: Hodnota {1} by měla být celé číslo.</value>
  </data>
  <data name="IsNotNumeric" xml:space="preserve">
    <value>Řádek {0}: Hodnota {1} by měla být číselná.</value>
  </data>
  <data name="MarkdownConversionError" xml:space="preserve">
    <value>Řádek {0}: Markdown {1} nelze převést, ujistěte se, že má správnou syntaxi.</value>
  </data>
  <data name="MissingAmountForProduct" xml:space="preserve">
    <value>Řádek {0}: Částka pro produkt chybí a nelze ji nastavit z předchozího produktu.</value>
  </data>
  <data name="MissingVisibilityForProduct" xml:space="preserve">
    <value>Řádek {0}: Viditelnost produktu chybí a nelze ji nastavit z předchozího produktu.</value>
  </data>
  <data name="FieldValueDoesNotExist" xml:space="preserve">
    <value>FieldId: '{0}' nemá žádnou hodnotu: {1}</value>
  </data>
  <data name="FieldExpressionDoesNotMatch" xml:space="preserve">
    <value>Expression {0} nelze analyzovat, může chybět ident, nesprávná syntaxe nebo odkazuje na jiný výrazové pole, není povoleno žádné vnoření.</value>
  </data>
  <data name="ImportFieldsError" xml:space="preserve">
    <value>Druhý list definující pole má nesprávný formát nebo chybí</value>
  </data>
  <data name="MissingFieldForCategory" xml:space="preserve">
    <value>Řádek {0}: Kategorie musí mít alespoň jedno pole.</value>
  </data>
  <data name="FieldValueOutOfRange" xml:space="preserve">
    <value>Pole id {0} hodnota {1} je mimo rozsah {2}-{3}</value>
  </data>
  <data name="ConfiguratorDoesNotExist" xml:space="preserve">
    <value>Kalkulátor s id {0} nenalezen.</value>
  </data>
  <data name="InvalidProductUnit" xml:space="preserve">
    <value>Produkt {0} má neplatnou jednotku '{1}'. Očekává se buď '{2}' nebo '{3}'</value>
  </data>
  <data name="NoEnrichmentDataFound" xml:space="preserve">
    <value>Pro produkt {0} nebyla nalezena žádná obohacující data</value>
  </data>
  <data name="ProductNotFoundInSelection" xml:space="preserve">
    <value>Produkt {0} nebyl nalezen ve vybraných produktech</value>
  </data>
  <data name="UnitsError" xml:space="preserve">
    <value>Chyba jednotek: {0}</value>
  </data>
  <data name="InvalidProductAmount" xml:space="preserve">
    <value>Produkt {0} má neplatné množství: {1}</value>
  </data>
  <data name="CheckboxYesValue" xml:space="preserve">
    <value>Ano</value>
  </data>
  <data name="CheckboxNoValue" xml:space="preserve">
    <value>Ne</value>
  </data>
  <data name="IdentTooLong" xml:space="preserve">
    <value>Identifikátor '{0}' je příliš dlouhý. Maximální délka je 10 znaků.</value>
  </data>
  <data name="IdentRequiredWhenTitlePresent" xml:space="preserve">
    <value>Pole s názvem '{0}' postrádá identifikátor.</value>
  </data>
  <data name="ConfiguratorTitleRequired" xml:space="preserve">
    <value>Název konfigurátoru je povinný.</value>
  </data>
  <data name="ImportFieldsFirstCellError" xml:space="preserve">
    <value>První buňka v prvním datovém řádku (za hlavičkou) listu polí musí být vyplněna.</value>
  </data>
  <data name="ImportProductsFirstCellError" xml:space="preserve">
    <value>První buňka v prvním datovém řádku (za hlavičkou) listu produktů musí být vyplněna.</value>
  </data>
  <data name="ImportFieldsTitleRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je vyplněn identifikátor, musí být vyplněn také název pole.</value>
  </data>
  <data name="ImportFieldsComponentTypeRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je vyplněn identifikátor, musí být vyplněn také typ komponenty.</value>
  </data>
  <data name="ImportFieldsValueRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je typ pole SingleChoice, Tile nebo Selectbox, musí být definována alespoň jedna hodnota.</value>
  </data>
  <data name="ImportFieldsExpressionRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je typ pole Expression, musí být definován výraz.</value>
  </data>
  <data name="ImportFieldsProductRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je typ pole Product, musí být definován produkt.</value>
  </data>
  <data name="ImportFieldsNumericValueRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je vyplněn název hodnoty, musí být vyplněna také číselná hodnota.</value>
  </data>
  <data name="ImportFieldsValueOrderRequired" xml:space="preserve">
    <value>Řádek {0}:  Pokud je vyplněn název hodnoty, musí být vyplněno také pořadí hodnoty.</value>
  </data>
  <data name="MissingProductForComposition" xml:space="preserve">
    <value>Řádek {0}: Skladba musí mít alespoň jeden produkt.</value>
  </data>
  <data name="ImportCompositionsOrderRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je definována skladba, musí být vyplněno pořadí.</value>
  </data>
  <data name="ImportProductNameRequired" xml:space="preserve">
    <value>Řádek {0}: Název produktu musí být vyplněn.</value>
  </data>
  <data name="ImportProductCodeRequired" xml:space="preserve">
    <value>Řádek {0}: Kód produktu musí být vyplněn.</value>
  </data>
  <data name="ImportProductOrderRequired" xml:space="preserve">
    <value>Řádek {0}: Pořadí produktu musí být vyplněno.</value>
  </data>
  <data name="ImportProductVolumeRequired" xml:space="preserve">
    <value>Řádek {0}: Objem produktu musí být vyplněn.</value>
  </data>
  <data name="ImportProductUnitRequired" xml:space="preserve">
    <value>Řádek {0}: Jednotka produktu musí být vyplněna.</value>
  </data>
  <data name="ImportCategoryOrderRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je vyplněna kategorie, musí být vyplněno také pořadí.</value>
  </data>
  <data name="ImportFieldOrderRequired" xml:space="preserve">
    <value>Řádek {0}: Pokud je vyplněno pole, musí být vyplněno také pořadí.</value>
  </data>
  <data name="CalculatorExpressionEmpty" xml:space="preserve">
    <value>Výraz nemůže být prázdný nebo null</value>
  </data>
  <data name="CalculatorExpressionEvaluatedNull" xml:space="preserve">
    <value>Výraz '{0}' se vyhodnotil jako null</value>
  </data>
  <data name="CalculatorResultParseError" xml:space="preserve">
    <value>Nelze převést výsledek výrazu '{0}' na číslo</value>
  </data>
  <data name="CalculatorEvaluationError" xml:space="preserve">
    <value>Chyba při vyhodnocování výrazu: {0}</value>
  </data>
  <data name="CalculatorInvalidArgument" xml:space="preserve">
    <value>Neplatný výraz nebo parametry: {0}</value>
  </data>
  <data name="CalculatorUnexpectedError" xml:space="preserve">
    <value>Neočekávaná chyba při výpočtu: {0}</value>
  </data>
</root>