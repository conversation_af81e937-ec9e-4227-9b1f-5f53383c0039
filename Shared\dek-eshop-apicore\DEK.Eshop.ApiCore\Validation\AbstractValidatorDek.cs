using System.Runtime.CompilerServices;
using FluentValidation;

namespace DEK.Eshop.ApiCore.Validation;

public abstract class AbstractValidatorDek<T> : AbstractValidator<T> where T : class
{ 
    public async Task<ValidationResult> Dek_Validate(T instance, CancellationToken cancellation = new())
    {
        var fluentResult = await this.ValidateAsync(instance, cancellation);

        var errorList = fluentResult.Errors.Select(failure => {
            return new Error {
                Code = failure.ErrorCode,
                Property = failure.PropertyName,
                Message = failure.ErrorMessage,
            };
        });

        return new ValidationResult(errorList.Any(), errorList);
    }

    public string Dek_Translate(string key)
    {
        if (key == null) {
            throw new ArgumentNullException("Key is null. MethodBase.GetCurrentMethod() probably returned null.");
        }
        return ValidatorOptions.Global.LanguageManager.GetString(key);
    }

    public string Dek_GetMethodName([CallerMemberName] string methodName = "")
    {
        return methodName;
    }
}
