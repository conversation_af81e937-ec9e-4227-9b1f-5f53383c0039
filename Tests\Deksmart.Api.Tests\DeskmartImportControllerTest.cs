using Moq;
using Deksmart.Api.Controllers;
using Microsoft.AspNetCore.Mvc;
using Deksmart.Application.Service;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Api.Resource;
using Xunit;
using Microsoft.AspNetCore.Http;
using Deksmart.Domain.Entity.Business;
using Deksmart.Shared.Dto;

namespace Deksmart.Api.Tests
{
    public class DeskmartImportControllerTest
    {
        private readonly Mock<IConfiguratorImportService> _importServiceMock;
        private readonly Mock<ICompositeImportService> _collectionImportServiceMock;
        private readonly Mock<IConfiguratorPresetRepository> _configuratorPresetDaoMock;
        private readonly DeskmartImportController _controller;

        public DeskmartImportControllerTest()
        {
            _importServiceMock = new Mock<IConfiguratorImportService>();
            _collectionImportServiceMock = new Mock<ICompositeImportService>();
            _configuratorPresetDaoMock = new Mock<IConfiguratorPresetRepository>();
            _controller = new DeskmartImportController(
                _importServiceMock.Object,
                _collectionImportServiceMock.Object,
                _configuratorPresetDaoMock.Object);
        }

        [Fact]
        public async Task ImportConfigurator_WithValidFile_ReturnsOk()
        {
            // Arrange
            var file = CreateMockFormFile("test.xlsx", "test content");
            var validation = new ValidationResult();
            _importServiceMock.Setup(x => x.ParseConfiguratorImportsAsync(file))
                .ReturnsAsync((true, validation));

            // Act
            var result = await _controller.ImportConfigurator(file);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<string>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("Import completed successfully.", apiResponse.Data);
        }

        [Fact]
        public async Task ImportConfigurator_WithEmptyFile_ReturnsBadRequest()
        {
            // Arrange
            var file = CreateMockFormFile("test.xlsx", "");

            // Act
            var result = await _controller.ImportConfigurator(file);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(DeksmartApiResource.FileIsEmpty, apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task ImportConfigurator_WithInvalidFileType_ReturnsBadRequest()
        {
            // Arrange
            var file = CreateMockFormFile("test.txt", "test content");

            // Act
            var result = await _controller.ImportConfigurator(file);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(DeksmartApiResource.NotXlsx, apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task ImportConfigurator_WithImportError_ReturnsUnprocessableEntity()
        {
            // Arrange
            var file = CreateMockFormFile("test.xlsx", "test content");
            var errorMessage = "Import failed";
            var validation = new ValidationResult();
            validation.AddError(errorMessage);
            _importServiceMock.Setup(x => x.ParseConfiguratorImportsAsync(file))
                .ReturnsAsync((false, validation));

            // Act
            var result = await _controller.ImportConfigurator(file);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, statusCodeResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(statusCodeResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains(errorMessage, apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task ImportCollection_WithValidFile_ReturnsOk()
        {
            // Arrange
            var file = CreateMockFormFile("test.xlsx", "test content");
            var validation = new ValidationResult();
            _collectionImportServiceMock.Setup(x => x.ImportCompositeAsync(file))
                .ReturnsAsync((true, validation));

            // Act
            var result = await _controller.ImportComposite(file);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<string>>(okResult.Value);
            Assert.True(apiResponse.Success);
        }

        [Fact]
        public async Task ImportCollection_WithEmptyFile_ReturnsBadRequest()
        {
            // Arrange
            var file = CreateMockFormFile("test.xlsx", "");

            // Act
            var result = await _controller.ImportComposite(file);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(DeksmartApiResource.FileIsEmpty, apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task ImportCollection_WithInvalidFileType_ReturnsBadRequest()
        {
            // Arrange
            var file = CreateMockFormFile("test.txt", "test content");

            // Act
            var result = await _controller.ImportComposite(file);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(DeksmartApiResource.NotXlsx, apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task ImportCollection_WithImportError_ReturnsInternalServerError()
        {
            // Arrange
            var file = CreateMockFormFile("test.xlsx", "test content");
            var validation = new ValidationResult();
            validation.AddError("Import failed");
            _collectionImportServiceMock.Setup(x => x.ImportCompositeAsync(file))
                .ReturnsAsync((false, validation));

            // Act
            var result = await _controller.ImportComposite(file);

            // Assert
            var objectResult = Assert.IsAssignableFrom<ObjectResult>(result);
            Assert.Equal(422, objectResult.StatusCode);
            var apiResponse = Assert.IsType<ApiResponse<object>>(objectResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Contains("Import failed", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task SetCatalogIdToPreset_WithValidIds_ReturnsOk()
        {
            // Arrange
            var presetId = "123e4567-e89b-12d3-a456-************";
            var catalogId = "CAT123";
            _configuratorPresetDaoMock.Setup(x => x.SetCatalogIdToPresetAsync(Guid.Parse(presetId), catalogId))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.SetCatalogIdToPreset(presetId, catalogId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<bool>>(okResult.Value);
            Assert.True(apiResponse.Success);
        }

        [Fact]
        public async Task SetCatalogIdToPreset_WithInvalidPresetId_ReturnsNotFound()
        {
            // Arrange
            var presetId = "invalid-guid";
            var catalogId = "CAT123";

            // Act
            var result = await _controller.SetCatalogIdToPreset(presetId, catalogId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(string.Format(DeksmartApiResource.PresetDoesNotExist, presetId), apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task SetCatalogIdToPreset_WithNonExistentPreset_ReturnsNotFound()
        {
            // Arrange
            var presetId = "123e4567-e89b-12d3-a456-************";
            var catalogId = "CAT123";
            _configuratorPresetDaoMock.Setup(x => x.SetCatalogIdToPresetAsync(Guid.Parse(presetId), catalogId))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.SetCatalogIdToPreset(presetId, catalogId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(string.Format(DeksmartApiResource.PresetDoesNotExist, presetId), apiResponse.ErrorMessage);
        }

        private static IFormFile CreateMockFormFile(string fileName, string content)
        {
            var file = new Mock<IFormFile>();
            var ms = new MemoryStream();
            var writer = new StreamWriter(ms);
            writer.Write(content);
            writer.Flush();
            ms.Position = 0;

            file.Setup(f => f.FileName).Returns(fileName);
            file.Setup(f => f.Length).Returns(ms.Length);
            file.Setup(f => f.OpenReadStream()).Returns(ms);
            file.Setup(f => f.ContentType).Returns("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return file.Object;
        }
    }
}