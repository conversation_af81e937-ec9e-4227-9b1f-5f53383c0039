using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository
{
    public class PresetConfiguratorCategoryStateRepository : IPresetConfiguratorCategoryStateRepository
    {
        private readonly ConfiguratorContext _context;

        public PresetConfiguratorCategoryStateRepository(ConfiguratorContext context)
        {
            _context = context;
        }

        /// <inheritdoc/>
        public async Task<List<PresetConfiguratorCategoryState>> GetCategoryStatesForPresetAsync(Guid presetId)
        {
            return await _context.PresetConfiguratorCategoryStates
                .Where(cs => cs.PresetId == presetId)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task InsertAsync(PresetConfiguratorCategoryState categoryState)
        {
            await _context.PresetConfiguratorCategoryStates.AddAsync(categoryState);
            await _context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task DeleteAllForPresetAsync(Guid presetId)
        {
            var categoryStates = await _context.PresetConfiguratorCategoryStates
                .Where(cs => cs.PresetId == presetId)
                .ToListAsync();

            _context.PresetConfiguratorCategoryStates.RemoveRange(categoryStates);
            await _context.SaveChangesAsync();
        }
    }
}
