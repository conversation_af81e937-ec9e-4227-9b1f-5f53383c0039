using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Defines the relationship between a parent preset and its child presets within composite configurators.
    /// Enables saving and organizing the state of individual child configurators as part of a composite configurator preset, mirroring the structure of ChildConfigurator but for persisted configurator states.
    /// </summary>
    [Table("child_preset", Schema = "dbo")]
    public class ChildPreset
    {
        /// <summary>
        /// Foreign key to the parent composite preset.
        /// </summary>
        [Key]
        [Column("composite_id")]
        public Guid CompositeId { get; set; }
        
        /// <summary>
        /// The ID of the child preset.
        /// </summary>
        [Key]
        [Column("preset_id")]
        public Guid PresetId { get; set; }

        [Column("display_order")]
        public int Order { get; set; }

        [Column("tab_title")]
        [StringLength(200)]
        public string? TabTitle { get; set; }

        [ForeignKey("PresetId")]
        public virtual ConfiguratorPreset Preset { get; set; } = null!;
    }
}