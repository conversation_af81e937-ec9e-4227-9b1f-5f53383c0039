@using Deksmart.Ui.Shared.Resources

<div class="modal @(IsVisible ? "show" : "")" style="display: @(IsVisible ? "block" : "none")">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">@UiSharedResource.SendInquiryButtonText</h5>
                <button type="button" class="btn-close" @onclick="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">@UiSharedResource.NameLabel</label>
                    <input type="text" class="form-control" id="name" @bind="Name" @oninput="OnInputChanged" />
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">@UiSharedResource.EmailLabel</label>
                    <input type="email" class="form-control" id="email" @bind="Email" @oninput="OnInputChanged" />
                </div>
                <div class="mb-3">
                    <label for="phone" class="form-label">@UiSharedResource.PhoneLabel</label>
                    <input type="tel" class="form-control" id="phone" @bind="Phone" @oninput="OnInputChanged" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="Close" disabled="@IsSubmitting">@UiSharedResource.CancelButtonText</button>
                <button type="button" class="btn btn-primary" @onclick="Submit" disabled="@(!CanSubmit || IsSubmitting)">
                    @if (IsSubmitting)
                    {
                        <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    }
                    @UiSharedResource.SendInquiryButtonText
                </button>
            </div>
        </div>
    </div>
</div>