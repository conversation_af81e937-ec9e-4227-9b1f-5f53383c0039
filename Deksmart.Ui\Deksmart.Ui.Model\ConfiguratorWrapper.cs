using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Model
{
    public class ConfiguratorWrapper
    {
        private readonly ConfiguratorDto _configurator;

        public ConfiguratorWrapper(ConfiguratorDto configurator)
        {
            _configurator = configurator;
        }

        public int Id => _configurator.Id;
        public string Title => _configurator.Title;
        public string? Description => _configurator.Description;
        public string? EndDescription => _configurator.EndDescription;
        public decimal TotalPrice => _configurator.TotalPriceVat;
        public decimal TotalPriceNoVat => _configurator.TotalPriceNoVat;
        public decimal TotalVat => _configurator.TotalVat;
        public bool IsComposite => _configurator.IsComposite;
        public bool IsValid => _configurator.IsValid;
        public bool CanAddToCart => _configurator.CanAddToCart;
        public int? TabOrder 
        { 
            get => _configurator.TabOrder; 
            set => _configurator.TabOrder = value; 
        }
        public string? TabTitle 
        { 
            get => _configurator.TabTitle; 
            set => _configurator.TabTitle = value; 
        }
        public string DisplayTabTitle => TabTitle ?? Title;
        public string? MetaDescription => _configurator.MetaDescription;

        public List<ConfiguratorFieldCategoryWrapper> FieldCategories { get; set; } = [];

        public List<ConfiguratorCompositionWrapper>? ConfiguratorCompositions { get; set; }

        public List<ChildConfiguratorDto>? ChildConfigurators => _configurator.ChildConfigurators;

        public bool HasProducts => _configurator.HasProducts;

        public void UpdateTotalPrices(decimal totalPriceVat, decimal totalPriceNoVat, decimal totalVat)
        {
            _configurator.TotalPriceVat = totalPriceVat;
            _configurator.TotalPriceNoVat = totalPriceNoVat;
            _configurator.TotalVat = totalVat;
        }

        public void UpdateIsValid(bool isValid)
        {
            _configurator.IsValid = isValid;
        }

        public void UpdateCanAddToCart(bool canAddToCart)
        {
            _configurator.CanAddToCart = canAddToCart;
        }

        public void UpdateCompositions(List<ConfiguratorCompositionWrapper>? compositions)
        {
            ConfiguratorCompositions = compositions;
        }
    }
}
