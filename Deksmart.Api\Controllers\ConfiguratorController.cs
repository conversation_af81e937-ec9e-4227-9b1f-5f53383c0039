using Deksmart.Api.Resource;
using Microsoft.AspNetCore.Mvc;
using Deksmart.Application.Service;
using Deksmart.Application.Service.Http;
using Deksmart.Shared.Dto;

namespace Deksmart.Api.Controllers
{
    /// <summary>
    /// API controller for managing configurator operations, including retrieval, filtering, preset management, and product price calculations.
    /// Handles requests related to configurator entities, their states, and associated product details.
    /// Exposes endpoints for fetching configurators, saving/loading presets, filtering configurator data, and calculating product prices.
    /// </summary>

    [ApiController]
    [Route("/api")]
    public class ConfiguratorController : DeksmartBaseController
    {
        private readonly IConfiguratorService _configuratorService;
        private readonly IConfiguratorPresetService _presetSaverService;
        private readonly ILogger<ConfiguratorController> _logger;
        private readonly IEshopApiService _eshopApiService;

        public ConfiguratorController(IConfiguratorService compositionService, ILogger<ConfiguratorController> logger,
            IConfiguratorPresetService presetSaverService, IEshopApiService eshopApiService)
        {
            _configuratorService = compositionService;
            _logger = logger;
            _presetSaverService = presetSaverService;
            _eshopApiService = eshopApiService;
        }

        /// <summary>
        /// Retrieves all configurators.
        /// </summary>
        /// <returns>A list of all configurators.</returns>
        [HttpGet("configurators")]
        public async Task<ActionResult<List<ConfiguratorDto>>> GetConfigurators()
        {
            var configurators = await _configuratorService.GetAllConfiguratorsAsync();

            return CreateSuccessResponse(configurators);
        }

        /// <summary>
        /// Retrieves a configurator by its ID, including all related data.
        /// </summary>
        /// <param name="id">The configurator ID.</param>
        /// <returns>The configurator with the specified ID, or a validation error.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetConfigurator(int id)
        {
            var (configurator, validation) = await _configuratorService.GetConfiguratorsAsync(id);

            if (validation.HasErrors)
                return CreateValidationErrorResponse(validation);

            return CreateSuccessResponse(configurator);
        }

        /// <summary>
        /// Retrieves a configurator with fields and products filtered by the provided state.
        /// </summary>
        /// <param name="id">The configurator ID.</param>
        /// <param name="configuratorState">The current state of the configurator.</param>
        /// <returns>The filtered configurator, or a validation error.</returns>
        [HttpPost("{id}/configurator")]
        public async Task<IActionResult> GetFilteredConfigurator(int id, [FromBody] ConfiguratorStateDto configuratorState)
        {
            _logger.LogInformation("GetUpdatedFields for id: {ConfiguratorId}", id);

            var (configurator, validation) = await _configuratorService.GetFilteredConfiguratorAsync(id, configuratorState);

            if (validation.HasErrors)
                return CreateValidationErrorResponse(validation);

            return CreateSuccessResponse(configurator);
        }

        /// <summary>
        /// Processes a composite configurator with active child processing and product aggregation.
        /// This endpoint processes the active child configurator through normal route and combines
        /// with other children to provide both individual child updates and composite summary.
        /// </summary>
        /// <param name="id">The composite configurator ID.</param>
        /// <param name="configuratorState">The composite configurator state with active child and other products.</param>
        /// <returns>Both the processed active child and the composite summary with aggregated products.</returns>
        [HttpPost("{id}/composite")]
        public async Task<IActionResult> ProcessCompositeConfigurator(int id, [FromBody] CompositeConfiguratorStateDto configuratorState)
        {
            _logger.LogInformation("ProcessCompositeConfigurator for id: {ConfiguratorId} with ActiveChild: {HasActiveChild}, OtherProducts: {OtherProductCount}", 
                id, configuratorState.ActiveChildState != null, configuratorState.OtherChildProducts?.Count ?? 0);

            var (response, validation) = await _configuratorService.ProcessCompositeConfiguratorAsync(id, configuratorState);

            if (validation.HasErrors)
                return CreateValidationErrorResponse(validation);

            return CreateSuccessResponse(response);
        }

        /// <summary>
        /// Saves the current state of the configurator as a preset.
        /// </summary>
        /// <param name="id">The configurator ID.</param>
        /// <param name="preset">The configurator state to save.</param>
        /// <returns>The GUID of the saved preset, or a validation error.</returns>
        [HttpPost("{id}/save")]
        public async Task<IActionResult> SaveConfiguratorPreset(int id, [FromBody] ConfiguratorStateDto preset)
        {
            _logger.LogInformation("SaveConfiguratorPreset for id: {ConfiguratorId}", id);

            var (guid, validation) = await _presetSaverService.SavePresetAsync(id, preset);

            if (validation.HasErrors)
                return CreateValidationErrorResponse(validation);

            return CreateSuccessResponse(guid);
        }

        /// <summary>
        /// Loads a configurator state from a saved preset.
        /// </summary>
        /// <param name="id">The configurator ID.</param>
        /// <param name="presetId">The preset GUID.</param>
        /// <returns>The configurator state from the preset, or a validation error.</returns>
        [HttpGet("{id}/configurator/{presetId}")]
        public async Task<IActionResult> GetConfiguratorByPreset(int id, string presetId)
        {
            _logger.LogInformation("GetConfiguratorByPreset for presetId: {PresetId}", presetId);

            if(Guid.TryParse(presetId, out var parsedPresetId))
            {
                var state = await _presetSaverService.GetConfiguratorStateAsync(parsedPresetId);

                if (state == null)
                    return NotFound(ApiResponse<object>.CreateError(string.Format(DeksmartApiResource.PresetDoesNotExist, presetId)));

                var (configurator, validation) = await _configuratorService.GetFilteredConfiguratorAsync(id, state);

                if (validation.HasErrors)
                    return CreateValidationErrorResponse(validation);

                return CreateSuccessResponse(configurator);
            }

            return NotFound(ApiResponse<object>.CreateError(string.Format(DeksmartApiResource.PresetDoesNotExist, presetId)));
        }

        /// <summary>
        /// Retrieves a preset ID by catalog ID.
        /// </summary>
        /// <param name="catalogId">The catalog identifier.</param>
        /// <returns>The preset ID associated with the catalog, or not found.</returns>
        [HttpGet("catalog/{catalogId}")]
        public async Task<IActionResult> GetPresetByCatalogId(string catalogId)
        {
            _logger.LogInformation("GetPresetByCatalogId for catalogId: {CatalogId}", catalogId);

            var nullableFieldValues = await _presetSaverService.GetPresetIdByCatalogId(catalogId);

            if (nullableFieldValues == null)
                return NotFound(ApiResponse<object>.CreateError(string.Format(DeksmartApiResource.PresetDoesNotExist, catalogId)));

            return CreateSuccessResponse(nullableFieldValues);
        }

        /// <summary>
        /// Retrieves product details by product code.
        /// </summary>
        /// <param name="code">The product code.</param>
        /// <returns>Product details, or a validation error.</returns>
        [HttpGet("product-detail/{code}")]
        public async Task<IActionResult> GetProductDetails(string code)
        {
            var result = await _eshopApiService.GetProductDetailsAsync(code);

            if (result.Validation.HasErrors)
            {
                return CreateValidationErrorResponse(result.Validation);
            }

            return CreateSuccessResponse(result.Data);
        }

        /// <summary>
        /// Calculates prices for selected products in a configurator.
        /// </summary>
        /// <param name="request">The price calculation request, including configurator ID, selected products, and current product code.</param>
        /// <returns>Calculated product prices, or a validation error.</returns>
        [HttpPost("calculate-product-prices")]
        public async Task<IActionResult> CalculateProductPrices(
            [FromBody] ProductPriceCalculationRequestDto request)
        {
            var (result, validation) = await _configuratorService.CalculateProductPricesAsync(
                request.ConfiguratorId,
                request.SelectedProducts,
                request.CurrentProductCode);

            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            return CreateSuccessResponse(result);
        }
    }
}
