﻿namespace DEK.Eshop.ApiCore.Sandbox.Entity;

using System.ComponentModel.DataAnnotations;

public enum ProductType
{
    [Display(Name = "ProductType1", Description = "ProductType1Description")]
    Eshop = 1,

    [Display(Name = "ProductType2", Description = "ProductType2Description")]
    Rental = 2,

    [Display(Name = "ProductType3", Description = "ProductType3Description")]
    Season = 3
}
public class Product
{
    [Required]
    public virtual int Id { get; set; }

    [Required]
    public string Title { get; set; } = null!;

    [Required]
    public string Description { get; set; } = null!;

    [Required]
    public ProductType ProductType { get; set; }
}
