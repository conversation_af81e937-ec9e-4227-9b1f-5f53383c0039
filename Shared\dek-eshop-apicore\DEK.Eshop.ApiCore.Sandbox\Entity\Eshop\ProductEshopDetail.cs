namespace DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[Keyless]
public class ProductEshopDetail
{
    [Column("POLOZKA")]
    public string Code { get; set; } = string.Empty;

    [Column("NAZEV")]
    public string Title { get; set; } = string.Empty;

    [Column("POPIS_URL")]
    public string Slug { get; set; } = string.Empty;

    [Column("CRC_OBRAZKU")]
    public int? Image { get; set; } = null;

    [Column("VYROBCE_NAZEV")]
    public string? ManufactureTitle {
        get => _manufactureTitle;
        set => _manufactureTitle = string.IsNullOrWhiteSpace(value) ? null : value;
    }
    private string? _manufactureTitle;

    [Column("VYROBCE_URL")]
    public string? ManufactureSlug {
        get => _manufactureSlug;
        set => _manufactureSlug = string.IsNullOrWhiteSpace(value) ? null : value;
    }
    private string? _manufactureSlug;

    [Column("VYROBCE_ID")]
    public int? ManufactureId {
        get => _manufactureId;
        set => _manufactureId = value == -1 ? null : value;
    }
    private int? _manufactureId;
}
