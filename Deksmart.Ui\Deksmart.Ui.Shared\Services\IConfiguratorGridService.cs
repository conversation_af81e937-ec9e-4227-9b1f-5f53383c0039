using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Services
{
    /// <summary>
    /// Central service for managing configurator data, validation, and operations.
    /// <PERSON>les loading configurator data, managing child services for composite configurators,
    /// product calculations, validation, and PDF generation.
    /// </summary>
    public interface IConfiguratorGridService : IDisposable
    {
        /// <summary>
        /// The main configurator data wrapper containing all fields, categories, and products.
        /// Null when no configurator is loaded.
        /// </summary>
        ConfiguratorWrapper? ConfiguratorWrapper { get; set; }

        /// <summary>
        /// Child configurator services for composite configurators (tabbed configurators).
        /// Each child represents a separate configurator instance within the main configurator.
        /// </summary>
        List<IConfiguratorGridService> ChildServices { get; }

        /// <summary>
        /// Reference to the parent configurator service when this is a child service.
        /// Used for navigation and state management in tabbed scenarios.
        /// </summary>
        IConfiguratorGridService? MainConfiguratorGridService { get; set; }

        /// <summary>
        /// The currently active child configurator service in tabbed configurators.
        /// Used for scrolling logic when validation errors are present and determining
        /// which child configurator should receive focus.
        /// </summary>
        IConfiguratorGridService? ActiveChildConfiguratorService { get; set; }

        /// <summary>
        /// Indicates whether validation checkboxes should be shown on category headers.
        /// Set to true after user interaction with configurator actions to show validation state.
        /// </summary>
        bool ShowValidationCheckboxes { get; set; }

        /// <summary>
        /// Indicates whether the configurator is currently loading or processing data.
        /// True during major operations like LoadDataAsync, GetDataForPresetAsync, ChangeIdWrapperValue.
        /// </summary>
        bool IsLoading { get; }

        /// <summary>
        /// Raised when the validation state of the configurator changes.
        /// Used to notify parent components and update UI validation indicators.
        /// </summary>
        event EventHandler? ValidationStateChanged;

        /// <summary>
        /// Raised when the loading state of the configurator changes.
        /// Used to notify parent components and update UI loading indicators.
        /// </summary>
        event EventHandler? LoadingStateChanged;

        /// <summary>
        /// Loads configurator data by ID. For existing configurators, applies current field states
        /// as filters to get updated product calculations and validation results.
        /// </summary>
        /// <param name="id">The configurator ID to load</param>
        /// <returns>Task representing the async loading operation</returns>
        Task LoadDataAsync(int id);

        /// <summary>
        /// Loads configurator data for a specific preset configuration.
        /// Used when loading saved customer configurations or predefined templates.
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="preset">The preset identifier or configuration data</param>
        /// <returns>Task representing the async loading operation</returns>
        Task GetDataForPresetAsync(int id, string preset);

        /// <summary>
        /// Saves the current configurator state as a preset for later retrieval.
        /// Captures all field values, selected products, and configuration state.
        /// </summary>
        /// <returns>The unique identifier of the saved preset, or null if save failed</returns>
        Task<Guid?> SavePreset();

        /// <summary>
        /// Changes the value of an ID field and reloads the configurator to reflect
        /// the impact of this change on available products and validation.
        /// </summary>
        /// <param name="idField">The ID field wrapper to update</param>
        /// <param name="id">The new ID value to set</param>
        /// <returns>Task representing the async operation</returns>
        Task ChangeIdWrapperValue(IdFieldWrapper idField, int id);

        /// <summary>
        /// Loads detailed product information including specifications, images, and descriptions.
        /// Used when user requests to see full product details in the configurator.
        /// </summary>
        /// <param name="product">The product wrapper to load details for</param>
        /// <returns>Task representing the async loading operation</returns>
        Task LoadProductDetail(ConfiguratorProductWrapper product);

        /// <summary>
        /// Calculates current pricing for products based on selected configuration.
        /// Updates product pricing information including VAT, bulk discounts, and total costs.
        /// </summary>
        /// <param name="product">The product to calculate pricing for</param>
        /// <returns>Task representing the async calculation operation</returns>
        Task CalculateProductPrices(ConfiguratorProductWrapper product);

        /// <summary>
        /// Generates a PDF document containing the complete configurator results,
        /// including selected products, quantities, pricing, and configuration details.
        /// </summary>
        /// <returns>Tuple containing PDF bytes and suggested filename, or null if generation failed</returns>
        Task<(byte[]? pdfBytes, string fileName)> GenerateConfigurationPdf();

        /// <summary>
        /// Adds a product to the specified composition section of the configurator.
        /// Used when user selects products from the product grid or search results.
        /// </summary>
        /// <param name="compositionId">The composition section ID to add the product to</param>
        void AddProductIntoComposition(int compositionId);

        /// <summary>
        /// Removes a selected product from the specified composition section.
        /// Used when user deselects products or removes them from their selection.
        /// </summary>
        /// <param name="compositionId">The composition section ID</param>
        /// <param name="index">The index of the product to remove within that composition</param>
        void RemoveProductFromComposition(int compositionId, int index);

        /// <summary>
        /// Sends an inquiry email with the current configurator results to the specified contact.
        /// Includes product selection, pricing, and customer contact information.
        /// </summary>
        /// <param name="name">Customer name</param>
        /// <param name="email">Customer email address</param>
        /// <param name="phone">Customer phone number</param>
        /// <returns>True if inquiry was sent successfully, false otherwise</returns>
        Task<bool> SendInquiry(string? name, string? email, string? phone);

        /// <summary>
        /// Marks all configurator fields as dirty to force validation and recalculation.
        /// Used when global changes occur that affect all field validation states.
        /// </summary>
        /// <returns>True if any fields were marked dirty, false if no fields exist</returns>
        bool SetAllFieldsDirty();

        /// <summary>
        /// Finds the first category that contains validation errors.
        /// Used for navigation to scroll to the first problematic section when validation fails.
        /// </summary>
        /// <returns>The category ID of the first invalid category, or null if all are valid</returns>
        string? FindFirstInvalidCategoryId();

        /// <summary>
        /// Manually triggers the validation state changed event.
        /// Used when external changes require UI validation indicators to be updated.
        /// </summary>
        void NotifyValidationStateChanged();

        /// <summary>
        /// Manually triggers the loading state changed event.
        /// Used when external changes require UI loading indicators to be updated.
        /// </summary>
        void NotifyLoadingStateChanged();
    }
}