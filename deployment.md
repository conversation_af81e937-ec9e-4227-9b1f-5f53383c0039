# Deksmart Deployment Guide

This document provides instructions for deploying the Deksmart application to a production environment.

## Prerequisites

- .NET 9.0 SDK and Runtime
- Docker and Docker Compose (if using containerized deployment)
- PostgreSQL database server
- MS SQL Server database
- Redis server (for distributed caching)
- SSL certificate for HTTPS

## Configuration Files

The application uses the following configuration files:

- `appsettings.json`: Base configuration
- `appsettings.Development.json`: Development environment configuration
- `appsettings.Staging.json`: Staging environment configuration
- `appsettings.Production.json`: Production environment configuration

## Environment Variables

For security reasons, sensitive information should be provided via environment variables rather than being stored in configuration files. The following environment variables should be set in the production environment:

### Database Connection

```
Mssql__User
Mssql__Password
Mssql__Database
Mssql__Host
Pgsql__dek_cz__Host
Pgsql__dek_cz__Port
Pgsql__dek_cz__Database
Pgsql__dek_cz__Username
Pgsql__dek_cz__Password
```

### Redis Cache

```
ApiCore__Cache__Redis__Host
ApiCore__Cache__Redis__Enabled
ApiCore__Cache__Redis__ConnectTimeout
```

### JWT Authentication

```
Jwt__AsymmetricKeys__0
```

### Mail Settings

```
ApiCore__Mail__Host
ApiCore__Mail__Port
ApiCore__Mail__MailFrom
ApiCore__Mail__Password
ApiCore__Mail__NameFrom
ApiCore__Mail__MailTo
```

## Deployment Options

### 1. Docker Deployment

The application can be deployed using Docker and Docker Compose:

1. Build the Docker image:
   ```
   docker-compose build
   ```

2. Start the containers:
   ```
   docker-compose up -d
   ```

3. Check the logs:
   ```
   docker-compose logs -f
   ```

### 2. Manual Deployment

1. Publish the application:
   ```
   dotnet publish Deksmart.Api/Deksmart.Api.csproj -c Release -o ./publish
   ```

2. Copy the published files to the server

3. Set up the environment variables

4. Run the application:
   ```
   dotnet Deksmart.Api.dll
   ```

### 3. IIS Deployment

1. Publish the application:
   ```
   dotnet publish Deksmart.Api/Deksmart.Api.csproj -c Release -o ./publish
   ```

2. Install the ASP.NET Core Hosting Bundle on the server

3. Create a new IIS website pointing to the published folder

4. Configure the application pool to use No Managed Code

5. Set up the environment variables in the web.config file

## Health Checks

The application provides a health check endpoint at `/health` that can be used to monitor the application's health. This endpoint returns:

- HTTP 200 OK if the application is healthy
- HTTP 503 Service Unavailable if the application is unhealthy

## SSL Configuration

In production, the application should be configured to use HTTPS:

1. Obtain an SSL certificate
2. Configure the certificate in the web server or reverse proxy
3. Ensure that HTTPS redirection is enabled

## Monitoring and Logging

The application logs to the console by default. In production, consider using a logging provider like Serilog to log to a file or a centralized logging system.

## Troubleshooting

### Common Issues

1. **Database Connection Issues**:
   - Check the database connection strings
   - Ensure the database server is accessible from the application server
   - Verify that the database user has the necessary permissions

2. **Redis Connection Issues**:
   - Check the Redis connection string
   - Ensure Redis is running and accessible from the application server

3. **JWT Authentication Issues**:
   - Verify that the JWT keys are correctly configured
   - Check that the token issuers and audiences are correctly set

## Backup and Recovery

1. **Database Backup**:
   - Set up regular backups of the PostgreSQL and MS SQL databases
   - Test the restore process periodically

2. **Application Backup**:
   - Keep a backup of the application configuration files
   - Document any custom settings or modifications

## Security Considerations

1. **Sensitive Data**:
   - Ensure that sensitive data is not stored in configuration files
   - Use environment variables or a secure secret management system

2. **Network Security**:
   - Use a firewall to restrict access to the application
   - Configure the application to only accept connections from trusted sources

3. **Authentication and Authorization**:
   - Review the JWT configuration to ensure it meets security requirements
   - Regularly rotate JWT keys

## Scaling

The application can be scaled horizontally by deploying multiple instances behind a load balancer. When scaling:

1. Ensure that Redis is configured for distributed caching
2. Configure the load balancer for sticky sessions if needed
3. Monitor the performance of the database and Redis servers
