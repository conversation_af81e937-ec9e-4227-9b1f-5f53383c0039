﻿using DEK.Eshop.ApiCore.Extension;
using DEK.Eshop.ApiCore.Validation;
using Microsoft.AspNetCore.Mvc.Testing;

namespace DEK.Eshop.ApiCore.Tests;

// dotnet watch test --logger:"console;verbosity=detailed"
public class ExceptionHandler_Tests : Abstract_Tests<Program>
{
    public ExceptionHandler_Tests(WebApplicationFactory<Program> factory, ITestOutputHelper output) : base(factory, output)
    {
    }

    [Fact]
    public async void Test_Return403IfUserNotLoogedIn()
    {
        // Override user. Unlogged user.
        var userOverride = new Dictionary<string, string?>() {
            ["userEmail"] = null,
        };
        var client = this.CreateClient(userOverride);

        var response = await client.GetAsync("/get-logged-user");
        var errors = await response.Dek_ToObjectAsync<List<Error>>();

        Assert.NotNull(errors);
        Assert.Single(errors);
        Assert.Equal(403, (int)response.StatusCode);
    }
}
