﻿using Microsoft.EntityFrameworkCore;
using DEK.Eshop.ApiCore.Tests.Entity;

namespace DEK.Eshop.ApiCore.Tests.Context;

public class MssqlContext : DbContext
{
    public DbSet<ProductUnit> ProductUnit { get; set; } = null!;

    public MssqlContext(DbContextOptions<MssqlContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
    }
}
