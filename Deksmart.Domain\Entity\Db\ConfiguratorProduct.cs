﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Represents a single product option within a composition.
    /// Defines the product's code, name, unit, volume, and associated logic for quantity and visibility.
    /// Each instance corresponds to a specific product that can be selected to fulfill a configuration requirement.
    /// </summary>
    [Table("configurator_product", Schema = "dbo")]
    [Index("QuantityId", Name = "idx_configurator_product_quantity_id")]
    [Index("CompositionId", Name = "idx_configurator_product_composition_id")]
    [Index("VisibilityId", Name = "idx_configurator_product_visibility_id")]
    public partial class ConfiguratorProduct : IDeletableEntity
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// SKL_ID
        /// </summary>
        [Column("composition_id")]
        public int CompositionId { get; set; }

        /// <summary>
        /// ID_POL
        /// </summary>
        [Column("product_code")]
        [StringLength(20)]
        public string ProductCode { get; set; } = null!;

        /// <summary>
        /// POL_NAZEV
        /// </summary>
        [Column("title")]
        [StringLength(200)]
        public string Title { get; set; } = null!;

        /// <summary>
        /// PORADI
        /// </summary>
        [Column("order")]
        public int Order { get; set; }

        /// <summary>
        /// PROD_MJ
        /// </summary>
        [Column("product_unit")]
        [StringLength(30)]
        public string ProductUnit { get; set; } = null!;

        /// <summary>
        /// VEL_SPOTREBA_PROD_MJ
        /// </summary>
        [Column("product_volume")]
        public decimal ProductVolume { get; set; }

        [Column("visibility_id")]
        public int VisibilityId { get; set; }

        [Column("quantity_id")]
        public int QuantityId { get; set; }

        [Column("is_deleted")]
        public bool IsDeleted { get; set; }

        [ForeignKey("VisibilityId")]
        public virtual ConfiguratorExpression Visibility { get; set; } = null!;

        [ForeignKey("QuantityId")]
        public virtual ConfiguratorExpression Quantity { get; set; } = null!;

        //Business logic
        [NotMapped]
        public decimal CalculatedAmount { get; set; }

        [NotMapped]
        public decimal? UserAmount { get; set; }

        [NotMapped]
        public decimal PriceVatPackage { get; set; }

        [NotMapped]
        public string? PackageUnit { get; set; }

        [NotMapped]
        public decimal PriceVat { get; set; }

        [NotMapped]
        public decimal PriceNoVat { get; set; }

        [NotMapped]
        public decimal Vat { get; set; }

        [NotMapped]
        public decimal PackageQuantity { get; set; }

        [NotMapped]
        public decimal? UserPackageQuantity { get; set; }

        [NotMapped]
        public bool IsSelected { get; set; } = false;

        /// <summary>
        /// Contains validation error message for this product (e.g., pricing calculation failures).
        /// Similar to ConfiguratorField.ValidationError, enables per-product soft validation.
        /// </summary>
        [NotMapped]
        public string? ValidationError { get; set; }

        /// <summary>
        /// Indicates whether this product has a validation error.
        /// </summary>
        [NotMapped]
        public bool HasValidationError => !string.IsNullOrWhiteSpace(ValidationError);
    }
}