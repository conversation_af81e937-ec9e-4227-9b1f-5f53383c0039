﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Deksmart.Api.Resource {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class DeksmartApiResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal DeksmartApiResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Deksmart.Api.Resource.DeksmartApiResource", typeof(DeksmartApiResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is empty or not provided..
        /// </summary>
        internal static string FileIsEmpty {
            get {
                return ResourceManager.GetString("FileIsEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file format. Only .xlsx files are allowed..
        /// </summary>
        internal static string NotXlsx {
            get {
                return ResourceManager.GetString("NotXlsx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preset not found..
        /// </summary>
        internal static string PresetDoesNotExist {
            get {
                return ResourceManager.GetString("PresetDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid configurator ID format..
        /// </summary>
        internal static string Validation_InvalidConfiguratorId {
            get {
                return ResourceManager.GetString("Validation_InvalidConfiguratorId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid preset ID format..
        /// </summary>
        internal static string Validation_InvalidPresetId {
            get {
                return ResourceManager.GetString("Validation_InvalidPresetId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact information is required..
        /// </summary>
        internal static string Validation_ContactInfoRequired {
            get {
                return ResourceManager.GetString("Validation_ContactInfoRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email address is required..
        /// </summary>
        internal static string Validation_EmailRequired {
            get {
                return ResourceManager.GetString("Validation_EmailRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid email address format..
        /// </summary>
        internal static string Validation_InvalidEmailFormat {
            get {
                return ResourceManager.GetString("Validation_InvalidEmailFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name is required..
        /// </summary>
        internal static string Validation_NameRequired {
            get {
                return ResourceManager.GetString("Validation_NameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name cannot be longer than 100 characters..
        /// </summary>
        internal static string Validation_NameTooLong {
            get {
                return ResourceManager.GetString("Validation_NameTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number cannot be longer than 20 characters..
        /// </summary>
        internal static string Validation_PhoneTooLong {
            get {
                return ResourceManager.GetString("Validation_PhoneTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid phone number format. Only numbers, spaces, hyphens, parentheses and plus sign are allowed..
        /// </summary>
        internal static string Validation_InvalidPhoneFormat {
            get {
                return ResourceManager.GetString("Validation_InvalidPhoneFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .xlsx.
        /// </summary>
        internal static string XlsxSuffix {
            get {
                return ResourceManager.GetString("XlsxSuffix", resourceCulture);
            }
        }
    }
}
