namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a single product option within a configurator composition, including its code, name, unit, quantities, selection state, and pricing.
    /// Used to transfer, render, and validate product options and their calculated data between backend, API, and UI.
    /// Supports product selection, pricing, and configuration workflows by encapsulating all relevant product data for each composition.
    /// </summary>
    [Serializable]
    public class ConfiguratorProductDto
    {
        public int Id { get; set; }

        public string ProductCode { get; set; } = null!;

        public string Title { get; set; } = null!;

        public int Order { get; set; }

        public string? ProductUnit { get; set; }

        public decimal PackageQuantity { get; set; }

        public decimal? UserPackageQuantity { get; set; }

        public decimal CalculatedAmount { get; set; }

        public bool IsSelected { get; set; }

        public decimal PriceVatPackage { get; set; }

        public string? PackageUnit { get; set; }

        public decimal PriceVat { get; set; }

        /// <summary>
        /// Contains validation error message for this product (e.g., pricing calculation failures).
        /// Similar to ConfiguratorFieldDto.ValidationError, enables per-product soft validation in UI.
        /// </summary>
        public string? ValidationError { get; set; }

        /// <summary>
        /// Indicates whether this product has a validation error.
        /// </summary>
        public bool HasValidationError => !string.IsNullOrWhiteSpace(ValidationError);
    }
}
