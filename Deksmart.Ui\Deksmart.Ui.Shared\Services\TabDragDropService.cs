using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Logging;

namespace Deksmart.Ui.Shared.Services
{
    public class TabReorderEventArgs : EventArgs
    {
        public IConfiguratorGridService DraggedService { get; }
        public int TargetIndex { get; }

        public TabReorderEventArgs(IConfiguratorGridService draggedService, int targetIndex)
        {
            DraggedService = draggedService;
            TargetIndex = targetIndex;
        }
    }

    public class DragIndicatorEventArgs : EventArgs
    {
        public int? DropZoneIndex { get; }
        public IConfiguratorGridService? DragOverService { get; }

        public DragIndicatorEventArgs(int? dropZoneIndex, IConfiguratorGridService? dragOverService = null)
        {
            DropZoneIndex = dropZoneIndex;
            DragOverService = dragOverService;
        }
    }

    public class TabDragDropService
    {
        private readonly ILogger<TabDragDropService> _logger;
        
        private IConfiguratorGridService? _draggedService;
        private IConfiguratorGridService? _dragOverService;
        private int? _dropZoneIndex;
        private bool _isEditModeActive;

        public event EventHandler<TabReorderEventArgs>? TabReordered;
        public event EventHandler<DragIndicatorEventArgs>? IndicatorChanged;
        public event EventHandler? DragEnded;

        public IConfiguratorGridService? DraggedService => _draggedService;
        public int? DropZoneIndex => _dropZoneIndex;
        public IConfiguratorGridService? DragOverService => _dragOverService;

        public TabDragDropService(ILogger<TabDragDropService> logger)
        {
            _logger = logger;
        }

        public void SetEditMode(bool isEditModeActive)
        {
            _isEditModeActive = isEditModeActive;
        }

        public void StartDrag(IConfiguratorGridService service)
        {
            if (_isEditModeActive)
            {
                return;
            }

            _logger.LogInformation("Drag started for configurator ID: {ConfiguratorId}, Title: {Title}",
                service.ConfiguratorWrapper?.Id, service.ConfiguratorWrapper?.Title);
                
            _draggedService = service;
        }

        public void EndDrag()
        {
            _logger.LogInformation("Drag ended. Clearing dragged and drag-over states");
            _draggedService = null;
            _dragOverService = null;
            _dropZoneIndex = null;
            
            DragEnded?.Invoke(this, EventArgs.Empty);
        }

        public void HandleDropZoneOver(int index, List<IConfiguratorGridService> services)
        {
            if (_isEditModeActive || _draggedService == null)
            {
                return;
            }

            if (WouldResultInNoMovement(index, services))
            {
                ClearIndicators();
                return;
            }

            _logger.LogInformation("Dragging over drop zone at index: {Index}", index);
            _dropZoneIndex = index;
            _dragOverService = null; // Clear tab drag over when using drop zones
            
            IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex, _dragOverService));
        }

        public void HandleDropZoneLeave(int index)
        {
            if (_isEditModeActive)
            {
                return;
            }

            if (_dropZoneIndex == index)
            {
                _logger.LogInformation("Leaving drop zone at index: {Index}", index);
                _dropZoneIndex = null;
                IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex));
            }
        }

        public void HandleDropZoneDrop(int targetIndex)
        {
            if (_isEditModeActive || _draggedService == null)
            {
                return;
            }

            _logger.LogInformation("Drop zone operation - Dragged: {DraggedId} to target index: {TargetIndex}",
                _draggedService.ConfiguratorWrapper?.Id, targetIndex);

            TabReordered?.Invoke(this, new TabReorderEventArgs(_draggedService, targetIndex));

            _draggedService = null;
            _dropZoneIndex = null;
            _dragOverService = null;
        }

        public void HandleTabDragOver(IConfiguratorGridService service, int serviceIndex, DragEventArgs e, List<IConfiguratorGridService> services)
        {
            if (_isEditModeActive || _draggedService == service || _draggedService == null)
            {
                return;
            }

            // Determine which gap to show the indicator in based on mouse position
            var isLeftSide = e.ClientX == 0 || e.OffsetX < 60; // Assume left side if no position or left half
            
            var targetDropIndex = isLeftSide ? serviceIndex : serviceIndex + 1;

            if (WouldResultInNoMovement(targetDropIndex, services))
            {
                ClearIndicators();
                return;
            }

            _dropZoneIndex = targetDropIndex;

            _logger.LogInformation("Dragging over tab {ServiceId}, will show indicator at drop zone {DropZone}", 
                service.ConfiguratorWrapper?.Id, _dropZoneIndex);
            
            _dragOverService = service;
            IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex, _dragOverService));
        }

        public void HandleTabDragLeave(IConfiguratorGridService service)
        {
            if (_isEditModeActive)
            {
                return;
            }

            if (_dragOverService == service)
            {
                _dragOverService = null;
                _dropZoneIndex = null;
                IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex, _dragOverService));
            }
        }

        public void HandleTabDrop()
        {
            // Use the same logic as precision drop zone - the drop zone index is already calculated correctly
            if (_dropZoneIndex.HasValue)
            {
                HandleDropZoneDrop(_dropZoneIndex.Value);
            }
        }

        public void HandleAddButtonDragOver(List<IConfiguratorGridService> services)
        {
            if (_draggedService == null)
            {
                return;
            }

            var targetDropIndex = services.Count;
            
            if (WouldResultInNoMovement(targetDropIndex, services))
            {
                _dropZoneIndex = null;
                IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex));
                return;
            }

            // Show indicator in the last gap (end position)
            _dropZoneIndex = targetDropIndex;
            IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex));
        }

        public void HandleAddButtonDragLeave(List<IConfiguratorGridService> services)
        {
            if (_dropZoneIndex == services.Count)
            {
                _dropZoneIndex = null;
                IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex));
            }
        }

        public void HandleAddButtonDrop(List<IConfiguratorGridService> services)
        {
            if (_draggedService != null)
            {
                // Drop at end position
                HandleDropZoneDrop(services.Count);
            }
        }

        private bool WouldResultInNoMovement(int targetDropIndex, List<IConfiguratorGridService> services)
        {
            if (_draggedService == null) return false;
            
            var currentIndex = services.IndexOf(_draggedService);
            if (currentIndex == -1) return false;
            
            // Don't show indicator if dropping would result in same position
            return targetDropIndex == currentIndex || targetDropIndex == currentIndex + 1;
        }

        private void ClearIndicators()
        {
            _dropZoneIndex = null;
            _dragOverService = null;
            IndicatorChanged?.Invoke(this, new DragIndicatorEventArgs(_dropZoneIndex, _dragOverService));
        }
    }
}