using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Microsoft.Extensions.Logging;
using Moq;

namespace Deksmart.Domain.Tests
{
    public class ProductPriceServiceTest
    {
        private readonly IProductPriceService _sut;
        private readonly Mock<ILogger<ProductPriceService>> _loggerMock;
        private readonly Mock<IValidationService> _validationServiceMock;
        private readonly Mock<IUnitConversionService> _unitConversionServiceMock;

        public ProductPriceServiceTest()
        {
            _loggerMock = new Mock<ILogger<ProductPriceService>>();
            _validationServiceMock = new Mock<IValidationService>();
            _unitConversionServiceMock = new Mock<IUnitConversionService>();
            _validationServiceMock.Setup(x => x.CreateValidation()).Returns(new ValidationResult());

            _sut = new ProductPriceService(_loggerMock.Object, _validationServiceMock.Object, _unitConversionServiceMock.Object);
        }

        [Fact]
        public void CalculatePrices_WithValidProductsAndEnrichmentData_CalculatesPricesCorrectly()
        {
            // Arrange
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 2,
                    ProductUnit = "ks"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "ks",
                        UnitPackage = "bal",
                        UnitsInPackage = 1
                    }
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert
            Assert.False(result.HasErrors);
            var product = products.First();
            Assert.Equal(200, product.PriceNoVat); // 2 * 100
            Assert.Equal(42, product.Vat); // 200 * 0.21
            Assert.Equal(242, product.PriceVat); // 200 + 42
        }

        [Fact]
        public void CalculatePrices_WithMissingEnrichmentData_UsesSoftValidation()
        {
            // Arrange
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 2,
                    ProductUnit = "ks"
                }
            };

            var enrichmentData = new List<ProductPriceData>();

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert - Should use soft validation now
            Assert.False(result.HasErrors); // Global validation should NOT have errors
            
            var product = products.First();
            Assert.True(product.HasValidationError); // Product should have validation error
            Assert.Equal(string.Format(DeksmartDomainResource.NoEnrichmentDataFound, "PROD1"), product.ValidationError);
        }

        [Fact]
        public void CalculatePrices_WithInvalidUnit_UsesSoftValidation()
        {
            // Arrange
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 2,
                    ProductUnit = "invalid_unit"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "ks",
                        UnitPackage = "bal",
                        UnitsInPackage = 1
                    }
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert - Should use soft validation now
            Assert.False(result.HasErrors); // Global validation should NOT have errors
            
            var product = products.First();
            Assert.True(product.HasValidationError); // Product should have validation error
            Assert.Equal(string.Format(DeksmartDomainResource.InvalidProductUnit, "PROD1", "invalid_unit", "ks", "bal"), product.ValidationError);
        }

        [Fact]
        public void CalculatePrices_WithPackageUnits_CalculatesCorrectly()
        {
            // Arrange
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 5,
                    ProductUnit = "bal"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "ks",
                        UnitPackage = "bal",
                        UnitsInPackage = 10
                    }
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert
            Assert.False(result.HasErrors);
            var product = products.First();
            Assert.Equal(5000, product.PriceNoVat); // 5 * 100 * 10
            Assert.Equal(1050, product.Vat); // 500 * 0.21 * 10
            Assert.Equal(6050, product.PriceVat); // 5000 + 1050
        }

        [Fact]
        public void SetConfiguratorTotalPrices_WithMultipleProducts_CalculatesTotalsCorrectly()
        {
            // Arrange
            var configurator = new Configurator();
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct { PriceNoVat = 100, PriceVat = 121, Vat = 21 },
                new ConfiguratorProduct { PriceNoVat = 200, PriceVat = 242, Vat = 42 }
            };

            // Act
            _sut.SetConfiguratorTotalPrices(configurator, products);

            // Assert
            Assert.Equal(300, configurator.TotalPriceNoVat);
            Assert.Equal(363, configurator.TotalPriceVat);
            Assert.Equal(63, configurator.TotalVat);
        }

        [Fact]
        public void CalculateSelectedProductPrices_WithValidProducts_ReturnsCorrectPrices()
        {
            // Arrange
            var selectedProducts = new List<SelectedProduct>
            {
                new SelectedProduct
                {
                    ProductCode = "PROD1",
                    Amount = 2,
                    Unit = "ks"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "ks",
                        UnitPackage = "bal",
                        UnitsInPackage = 1
                    }
                }
            };

            // Act
            var (result, validation) = _sut.CalculateSelectedProductPrices(selectedProducts, "PROD1", enrichmentData);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.NotNull(result);
            Assert.Equal(242, result.PriceVat);
            Assert.Equal(200, result.TotalPriceNoVat);
            Assert.Equal(242, result.TotalPriceVat);
            Assert.Equal(42, result.TotalVat);
        }

        [Fact]
        public void CalculateSelectedProductPrices_WithNoProducts_ReturnsZeroPrices()
        {
            // Arrange
            var selectedProducts = new List<SelectedProduct>();
            var enrichmentData = new List<ProductPriceData>();

            // Act
            var (result, validation) = _sut.CalculateSelectedProductPrices(selectedProducts, "PROD1", enrichmentData);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);
            Assert.Equal(0, result.PriceVat);
            Assert.Equal(0, result.TotalPriceNoVat);
            Assert.Equal(0, result.TotalPriceVat);
            Assert.Equal(0, result.TotalVat);
        }

        [Fact]
        public void CalculateSelectedProductPrices_WithMissingCurrentProduct_ReturnsError()
        {
            // Arrange
            var selectedProducts = new List<SelectedProduct>
            {
                new SelectedProduct
                {
                    ProductCode = "PROD1",
                    Amount = 2,
                    Unit = "ks"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "ks",
                        UnitPackage = "bal",
                        UnitsInPackage = 1
                    }
                }
            };

            // Act
            var (result, validation) = _sut.CalculateSelectedProductPrices(selectedProducts, "NONEXISTENT", enrichmentData);

            // Assert
            Assert.Null(result);
            Assert.True(validation.HasErrors);
            Assert.Contains(string.Format(DeksmartDomainResource.ProductNotFoundInSelection, "NONEXISTENT"), validation.GetErrors());
        }

        [Fact]
        public void CalculatePrices_WithConvertedAmount_UsesApiConvertedValue()
        {
            // Arrange - Use values that give DIFFERENT results for ConvertedAmount vs Fallback
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 25m, // Original calculated amount - would result in 3 packages
                    ProductUnit = "m2"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21,
                        PriceVatPackage = 121
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "m2",
                        UnitPackage = "bal",
                        UnitsInPackage = 10
                    },
                    ConvertedAmount = 15m // API converted amount - would result in 2 packages
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert
            Assert.False(result.HasErrors);
            var product = products.First();
            
            // Verify that ConvertedAmount (15) was used instead of CalculatedAmount (25):
            // ConvertedAmount: Math.Ceiling(15 / 10) = 2 packages → 2000 price
            // vs Fallback: Math.Ceiling(25 / 10) = 3 packages → 3000 price
            Assert.Equal(25m, product.CalculatedAmount); // Original preserved
            Assert.Equal(2, product.PackageQuantity); // Should be Math.Ceiling(15 / 10) = 2
            Assert.Equal(2000, product.PriceNoVat); // 2 * 100 * 10 = 2000
            Assert.Equal(420, product.Vat); // 2000 * 0.21 = 420
            Assert.Equal(2420, product.PriceVat); // 2000 + 420 = 2420
        }

        [Fact]
        public void CalculatePrices_WithoutConvertedAmount_UsesFallbackConversion()
        {
            // Arrange
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 15, // Original calculated amount in sales units
                    ProductUnit = "m2" // Same as sales unit
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21,
                        PriceVatPackage = 121
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "m2",
                        UnitPackage = "bal",
                        UnitsInPackage = 10
                    },
                    ConvertedAmount = null // No API conversion available
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert
            Assert.False(result.HasErrors);
            var product = products.First();
            
            // Verify that manual fallback conversion was used:
            // CalculatedAmount (15) / UnitsInPackage (10) = 1.5, ceiling = 2 packages
            Assert.Equal(2, product.PackageQuantity); // Should be Math.Ceiling(15 / 10) = 2
            Assert.Equal(15, product.CalculatedAmount); // Original CalculatedAmount should be preserved
            Assert.Equal(2000, product.PriceNoVat); // 2 * 100 * 10
            Assert.Equal(420, product.Vat); // 2000 * 0.21
            Assert.Equal(2420, product.PriceVat); // 2000 + 420
        }

        [Fact]
        public void CalculateSelectedProductPrices_WithConvertedAmount_UsesApiConvertedValue()
        {
            // Arrange - Use decimal values to test proper decimal handling
            var selectedProducts = new List<SelectedProduct>
            {
                new SelectedProduct
                {
                    ProductCode = "PROD1",
                    Amount = 27.5m, // Original amount - would result in 4 packages (27.5/7.5 = 3.67, ceiling = 4)
                    Unit = "m"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 50,
                        Vat = 21,
                        PriceVatPackage = 60.5m
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "m2",
                        UnitPackage = "bal",
                        UnitsInPackage = 7.5m
                    },
                    ConvertedAmount = 18.75m // API converted amount - results in 3 packages (18.75/7.5 = 2.5, ceiling = 3)
                }
            };

            // Act
            var (result, validation) = _sut.CalculateSelectedProductPrices(selectedProducts, "PROD1", enrichmentData);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.NotNull(result);
            
            // Verify API conversion was used:
            // ConvertedAmount (18.75) / UnitsInPackage (7.5) = 2.5, ceiling = 3 packages
            // vs Fallback: (27.5) / UnitsInPackage (7.5) = 3.67, ceiling = 4 packages
            // Price: 3 packages * 50 (PriceNoVatSales) * 7.5 (UnitsInPackage) = 1125
            Assert.Equal(1125, result.TotalPriceNoVat); // 3 * 50 * 7.5 = 1125
            Assert.Equal(236.25m, result.TotalVat); // 1125 * 0.21 = 236.25
            Assert.Equal(1361.25m, result.TotalPriceVat); // 1125 + 236.25 = 1361.25
            Assert.Equal(1361.25m, result.PriceVat); // Same as total since only one product
        }

        [Fact]
        public void CalculatePrices_WithInvalidConvertedAmount_HandlesGracefully()
        {
            // Arrange
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 5,
                    ProductUnit = "m2"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 100,
                        Vat = 21,
                        PriceVatPackage = 121
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "m2",
                        UnitPackage = "bal",
                        UnitsInPackage = 10
                    },
                    ConvertedAmount = 0 // Edge case: zero converted amount
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert
            Assert.False(result.HasErrors);
            var product = products.First();
            
            // Should use Math.Ceiling(0 / 10) = 0, but price calculation should handle this
            Assert.Equal(0, product.PackageQuantity);
            Assert.Equal(5, product.CalculatedAmount); // Original CalculatedAmount should be preserved
            Assert.Equal(0, product.PriceNoVat);
            Assert.Equal(0, product.Vat);
            Assert.Equal(0, product.PriceVat);
        }

        [Fact]
        public void CalculatePrices_WithUserAmountAndConvertedAmount_UsesHigherValue()
        {
            // Arrange - Use decimal values to test decimal handling with user amounts
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    ProductCode = "PROD1",
                    CalculatedAmount = 12.8m, // Original calculated amount
                    UserAmount = 4.5m, // User specified amount (higher than converted amount but lower than calculated)
                    ProductUnit = "m2"
                }
            };

            var enrichmentData = new List<ProductPriceData>
            {
                new ProductPriceData
                {
                    ProductCode = "PROD1",
                    Pricing = new EshopProductPricing
                    {
                        PriceNoVatSales = 80.5m,
                        Vat = 21,
                        PriceVatPackage = 97.41m
                    },
                    Unit = new EshopProductUnit
                    {
                        UnitSales = "m2",
                        UnitPackage = "bal",
                        UnitsInPackage = 2.5m
                    },
                    ConvertedAmount = 8.75m // API converted amount results in 4 packages (8.75/2.5 = 3.5, ceiling = 4)
                }
            };

            // Act
            var result = _sut.CalculatePrices(products, enrichmentData);

            // Assert
            Assert.False(result.HasErrors);
            var product = products.First();
            
            // Should use UserAmount (4.5) since it's higher than converted amount ceiling (4)
            Assert.Equal(4, product.PackageQuantity); // Math.Ceiling(8.75 / 2.5) = 4 from converted amount
            Assert.Equal(12.8m, product.CalculatedAmount); // Original CalculatedAmount should be preserved
            Assert.Equal(4.5m, product.UserAmount); // User amount preserved
            Assert.Equal(4.5m, product.UserPackageQuantity); // User package quantity should be set
            Assert.Equal(905.62m, product.PriceNoVat); // Uses UserAmount: Math.Round(4.5 * 80.5 * 2.5, 2) = 905.62
            Assert.Equal(190.18m, product.Vat); // Math.Round(905.62 * 0.21, 2) = 190.18
            Assert.Equal(1095.8m, product.PriceVat); // Math.Round(905.62 + 190.18, 2) = 1095.8
        }
    }
}