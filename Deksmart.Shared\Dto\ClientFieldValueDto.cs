namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing the value of a single user-editable field in a configurator, identified by FieldId.
    /// Used to transfer user input (either a direct numeric value or a reference to a selected option) between client, API, and persistence layers.
    /// Supports both direct entry and multiple-choice fields in configuration workflows, enabling validation, persistence, and restoration of user selections.
    /// </summary>
    public class ClientFieldValueDto
    {
        public int FieldId { get; set; }

        /// <summary>
        /// Hodnota filtru (dří<PERSON> veli<PERSON>, input number) nebo id fieldValue
        /// </summary>
        public decimal? Value { get; set; }
    }
}