using Deksmart.Application.Service;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Service;
using Deksmart.Infrastructure.Cache;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Shared.Dto;
using Microsoft.Extensions.Logging;
using Moq;
using Deksmart.Domain.Enum;
using Deksmart.Shared.Enum;

namespace Deksmart.Application.Tests.Service
{
    public class ConfiguratorProcessingServiceTest
    {
        private readonly Mock<IConfiguratorExpressionCalculator> _calculatorMock;
        private readonly Mock<IFieldValueMatcher> _fieldValueMatcherMock;
        private readonly Mock<IConfiguratorRepository> _configuratorDaoMock;
        private readonly Mock<IConfiguratorCacheManager> _cacheManagerMock;
        private readonly Mock<IConfiguratorFieldRepository> _configuratorFieldDaoMock;
        private readonly Mock<ILogger<ConfiguratorProcessingService>> _loggerMock;
        private readonly Mock<IProductProcessingService> _productProcessingServiceMock;
        private readonly ConfiguratorProcessingService _service;

        public ConfiguratorProcessingServiceTest()
        {
            _calculatorMock = new Mock<IConfiguratorExpressionCalculator>();
            _fieldValueMatcherMock = new Mock<IFieldValueMatcher>();
            _configuratorDaoMock = new Mock<IConfiguratorRepository>();
            _cacheManagerMock = new Mock<IConfiguratorCacheManager>();
            _configuratorFieldDaoMock = new Mock<IConfiguratorFieldRepository>();
            _loggerMock = new Mock<ILogger<ConfiguratorProcessingService>>();
            _productProcessingServiceMock = new Mock<IProductProcessingService>();

            _service = new ConfiguratorProcessingService(
                _calculatorMock.Object,
                _fieldValueMatcherMock.Object,
                _configuratorDaoMock.Object,
                _cacheManagerMock.Object,
                _configuratorFieldDaoMock.Object,
                _productProcessingServiceMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task ProcessConfiguratorAsync_ShouldReturnError_WhenFieldMatchingFails()
        {
            // Arrange
            var configuratorId = 1;
            var clientFieldValues = new List<ClientFieldValueDto> { new ClientFieldValueDto { FieldId = 1, Value = 1.0m } };
            var configurator = CreateTestConfigurator(configuratorId);

            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);

            var failedValidation = new ValidationResult();
            failedValidation.AddError("Field matching failed");

            _cacheManagerMock.Setup(x => x.GetOrAddListAsync(configuratorId, It.IsAny<Func<int, Task<List<MultipleChoiceValue>>>>(), It.IsAny<int>()))
                .ReturnsAsync(new List<MultipleChoiceValue>());
            
            _cacheManagerMock.Setup(x => x.GetOrAddListAsync(configuratorId, It.IsAny<Func<int, Task<List<DirectValue>>>>(), It.IsAny<int>()))
                .ReturnsAsync(new List<DirectValue>());

            _fieldValueMatcherMock.Setup(x => x.MatchFieldMultipleChoiceValues(
                It.IsAny<Dictionary<int, decimal?>>(),
                It.IsAny<IEnumerable<MultipleChoiceValue>>(),
                It.IsAny<ValidationResult>()))
                .Callback<Dictionary<int, decimal?>, IEnumerable<MultipleChoiceValue>, ValidationResult>((d, f, v) => v.AddError("Field matching failed"))
                .Returns(new List<ValueIdent>());

            _fieldValueMatcherMock.Setup(x => x.MatchFieldDirectValues(
                It.IsAny<Dictionary<int, decimal?>>(),
                It.IsAny<IEnumerable<DirectValue>>(),
                It.IsAny<ValidationResult>()))
                .Returns(new List<ValueIdent>());

            _fieldValueMatcherMock.Setup(x => x.MatchExpressionFields(
                It.IsAny<IEnumerable<DirectValue>>(),
                It.IsAny<IEnumerable<ValueIdent>>()))
                .Returns(new List<ValueIdent>());

            // Act
            var (result, validation) = await _service.ProcessConfiguratorAsync(configuratorId, clientFieldValues, null);

            // Assert
            Assert.Null(result);
            Assert.True(validation.HasErrors);
            Assert.Contains("Field matching failed", validation.GetErrors());
        }

        [Fact]
        public async Task ProcessConfiguratorAsync_ShouldProcessSuccessfully_WhenValidInput()
        {
            // Arrange
            var configuratorId = 1;
            var clientFieldValues = new List<ClientFieldValueDto> { new ClientFieldValueDto { FieldId = 1, Value = 1.0m } };
            var configurator = CreateTestConfigurator(configuratorId);
            var matchedFields = new List<ValueIdent> { new ValueIdent { Ident = "field_1", Value = 1.0m } };

            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);

            _cacheManagerMock.Setup(x => x.GetOrAddListAsync(configuratorId, It.IsAny<Func<int, Task<List<MultipleChoiceValue>>>>(), It.IsAny<int>()))
                .ReturnsAsync(new List<MultipleChoiceValue>());
            
            _cacheManagerMock.Setup(x => x.GetOrAddListAsync(configuratorId, It.IsAny<Func<int, Task<List<DirectValue>>>>(), It.IsAny<int>()))
                .ReturnsAsync(new List<DirectValue>());

            _fieldValueMatcherMock.Setup(x => x.MatchFieldMultipleChoiceValues(
                It.IsAny<Dictionary<int, decimal?>>(),
                It.IsAny<IEnumerable<MultipleChoiceValue>>(),
                It.IsAny<ValidationResult>()))
                .Returns(matchedFields);

            _fieldValueMatcherMock.Setup(x => x.MatchFieldDirectValues(
                It.IsAny<Dictionary<int, decimal?>>(),
                It.IsAny<IEnumerable<DirectValue>>(),
                It.IsAny<ValidationResult>()))
                .Returns(new List<ValueIdent>());

            _fieldValueMatcherMock.Setup(x => x.MatchExpressionFields(
                It.IsAny<IEnumerable<DirectValue>>(),
                It.IsAny<IEnumerable<ValueIdent>>()))
                .Returns(new List<ValueIdent>());

            _productProcessingServiceMock.Setup(x => x.CalculatePricesForProductsAsync(
                It.IsAny<Configurator>(),
                It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            _productProcessingServiceMock.Setup(x => x.SetProductFieldsAsync(
                It.IsAny<Configurator>(),
                It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            _productProcessingServiceMock.Setup(x => x.SelectProducts(
                It.IsAny<Configurator>(),
                It.IsAny<List<ClientProductValueDto>>()));

            _productProcessingServiceMock.Setup(x => x.ValidateProducts(
                It.IsAny<Configurator>()))
                .Returns(true);

            _calculatorMock.Setup(x => x.CalculateAndFilterFields(configurator, matchedFields));
            _calculatorMock.Setup(x => x.FilterProducts(configurator, matchedFields));

            // Act
            var (result, validation) = await _service.ProcessConfiguratorAsync(configuratorId, clientFieldValues, null);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);
            Assert.Equal(configuratorId, result.Id);

            // Verify all methods were called
            _calculatorMock.Verify(x => x.CalculateAndFilterFields(configurator, matchedFields), Times.Once);
            _calculatorMock.Verify(x => x.FilterProducts(configurator, matchedFields), Times.Once);
        }

        [Fact]
        public async Task ProcessConfiguratorAsync_ShouldSelectProducts_WhenSelectedProductsProvided()
        {
            // Arrange
            var configuratorId = 1;
            var clientFieldValues = new List<ClientFieldValueDto>();
            var selectedProducts = new List<ClientProductValueDto>
            {
                new ClientProductValueDto { ProductId = 1, Amount = 2 }
            };

            // Create a configurator with no products - this should avoid the pricing logic
            var configurator = new Configurator
            {
                Id = configuratorId,
                ConfiguratorCompositions = new List<ConfiguratorComposition>(),
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>()
            };

            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);

            SetupDefaultMocks();

            // Act
            var (result, validation) = await _service.ProcessConfiguratorAsync(configuratorId, clientFieldValues, selectedProducts);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public async Task ProcessConfiguratorAsync_ShouldApplyCategoryStates_WhenCategoryStatesProvided()
        {
            // Arrange
            var configuratorId = 1;
            var clientFieldValues = new List<ClientFieldValueDto>();
            var categoryStates = new List<CategoryStateDto>
            {
                new CategoryStateDto { CategoryId = 1, CollapseState = CategoryCollapseStateDto.Collapsed }
            };

            var configurator = CreateTestConfiguratorWithCategories(configuratorId);

            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);

            SetupDefaultMocks();

            // Act
            var (result, validation) = await _service.ProcessConfiguratorAsync(configuratorId, clientFieldValues, null, categoryStates);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);

            var category = result.ConfiguratorFieldCategories.FirstOrDefault(c => c.Id == 1);
            Assert.NotNull(category);
            Assert.Equal(CategoryCollapseState.Collapsed, category.CollapseState);
        }


        [Fact]
        public void ApplyCategoryStates_ShouldUpdateCategoryCollapseStates()
        {
            // Arrange
            var configurator = CreateTestConfiguratorWithCategories(1);
            var categoryStates = new List<CategoryStateDto>
            {
                new CategoryStateDto { CategoryId = 1, CollapseState = CategoryCollapseStateDto.Collapsed },
                new CategoryStateDto { CategoryId = 2, CollapseState = CategoryCollapseStateDto.Expanded }
            };

            // Act
            _service.ApplyCategoryStates(configurator, categoryStates);

            // Assert
            var category1 = configurator.ConfiguratorFieldCategories.FirstOrDefault(c => c.Id == 1);
            var category2 = configurator.ConfiguratorFieldCategories.FirstOrDefault(c => c.Id == 2);

            Assert.NotNull(category1);
            Assert.NotNull(category2);
            Assert.Equal(CategoryCollapseState.Collapsed, category1.CollapseState);
            Assert.Equal(CategoryCollapseState.Expanded, category2.CollapseState);
        }


        [Fact]
        public async Task ProcessConfiguratorAsync_ShouldSetCanAddToCartFalse_WhenProductHasValidationError()
        {
            // Arrange
            var configuratorId = 1;
            var clientFieldValues = new List<ClientFieldValueDto>();
            var configurator = CreateTestConfiguratorWithProducts(configuratorId);
            
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);

            SetupDefaultMocks();

            // Setup product processing service to simulate product validation failure
            _productProcessingServiceMock.Setup(x => x.CalculatePricesForProductsAsync(
                It.IsAny<Configurator>(),
                It.IsAny<ValidationResult>()))
                .Callback<Configurator, ValidationResult>((config, validation) =>
                {
                    // Simulate a product validation error by setting ValidationError on the first product
                    var firstProduct = config.ConfiguratorCompositions.First().ConfiguratorProducts.First();
                    firstProduct.ValidationError = "Test pricing error";
                })
                .Returns(Task.CompletedTask);

            // Setup product processing service to return false for ValidateProducts (indicating validation errors)
            _productProcessingServiceMock.Setup(x => x.ValidateProducts(It.IsAny<Configurator>()))
                .Returns((Configurator config) =>
                {
                    // Check if any products have validation errors and set CanAddToCart accordingly
                    var hasValidationErrors = config.ConfiguratorCompositions
                        .SelectMany(c => c.ConfiguratorProducts)
                        .Any(p => p.HasValidationError);
                    config.CanAddToCart = !hasValidationErrors;
                    return !hasValidationErrors;
                });

            // Act
            var (result, validation) = await _service.ProcessConfiguratorAsync(configuratorId, clientFieldValues, null);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors); // Global validation should not have errors (soft validation)
            Assert.False(result.CanAddToCart); // Shopping cart should be disabled due to product error
        }

        [Fact]
        public async Task ProcessConfiguratorAsync_ShouldSetCanAddToCartTrue_WhenAllProductsValid()
        {
            // Arrange
            var configuratorId = 1;
            var clientFieldValues = new List<ClientFieldValueDto>();
            var configurator = CreateTestConfiguratorWithProducts(configuratorId);
            
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);

            SetupDefaultMocks();

            // Setup product processing service to succeed (no validation errors)
            // This is already handled by SetupDefaultMocks()

            // Act
            var (result, validation) = await _service.ProcessConfiguratorAsync(configuratorId, clientFieldValues, null);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);
            Assert.True(result.CanAddToCart); // Shopping cart should be enabled when all products are valid
        }

        private void SetupDefaultMocks()
        {
            _cacheManagerMock.Setup(x => x.GetOrAddListAsync(It.IsAny<int>(), It.IsAny<Func<int, Task<List<MultipleChoiceValue>>>>(), It.IsAny<int>()))
                .ReturnsAsync(new List<MultipleChoiceValue>());
            
            _cacheManagerMock.Setup(x => x.GetOrAddListAsync(It.IsAny<int>(), It.IsAny<Func<int, Task<List<DirectValue>>>>(), It.IsAny<int>()))
                .ReturnsAsync(new List<DirectValue>());

            _fieldValueMatcherMock.Setup(x => x.MatchFieldMultipleChoiceValues(
                It.IsAny<Dictionary<int, decimal?>>(),
                It.IsAny<IEnumerable<MultipleChoiceValue>>(),
                It.IsAny<ValidationResult>()))
                .Returns(new List<ValueIdent>());

            _fieldValueMatcherMock.Setup(x => x.MatchFieldDirectValues(
                It.IsAny<Dictionary<int, decimal?>>(),
                It.IsAny<IEnumerable<DirectValue>>(),
                It.IsAny<ValidationResult>()))
                .Returns(new List<ValueIdent>());

            _fieldValueMatcherMock.Setup(x => x.MatchExpressionFields(
                It.IsAny<IEnumerable<DirectValue>>(),
                It.IsAny<IEnumerable<ValueIdent>>()))
                .Returns(new List<ValueIdent>());

            _productProcessingServiceMock.Setup(x => x.CalculatePricesForProductsAsync(
                It.IsAny<Configurator>(),
                It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            _productProcessingServiceMock.Setup(x => x.SetProductFieldsAsync(
                It.IsAny<Configurator>(),
                It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            _productProcessingServiceMock.Setup(x => x.SelectProducts(
                It.IsAny<Configurator>(),
                It.IsAny<List<ClientProductValueDto>>()));

            _productProcessingServiceMock.Setup(x => x.ValidateProducts(
                It.IsAny<Configurator>()))
                .Returns(true);
        }

        private static Configurator CreateTestConfigurator(int id)
        {
            return new Configurator
            {
                Id = id,
                ConfiguratorCompositions = new List<ConfiguratorComposition>(),
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>()
            };
        }

        private static Configurator CreateTestConfiguratorWithProducts(int id)
        {
            return new Configurator
            {
                Id = id,
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        Id = 1,
                        Order = 1,
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct { Id = 1, ProductCode = "TEST1", Order = 1 },
                            new ConfiguratorProduct { Id = 2, ProductCode = "TEST2", Order = 2 }
                        }
                    }
                },
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>()
            };
        }

        private static Configurator CreateTestConfiguratorWithCategories(int id)
        {
            return new Configurator
            {
                Id = id,
                ConfiguratorCompositions = new List<ConfiguratorComposition>(),
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new ConfiguratorFieldCategory { Id = 1, CollapseState = CategoryCollapseState.Expanded },
                    new ConfiguratorFieldCategory { Id = 2, CollapseState = CategoryCollapseState.Expanded }
                }
            };
        }

        private static Configurator CreateTestConfiguratorWithProductFields()
        {
            return new Configurator
            {
                Id = 1,
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        Id = 1,
                        Order = 1,
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct { Id = 1, ProductCode = "TEST", Order = 1, IsSelected = true }
                        }
                    }
                },
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new ConfiguratorFieldCategory
                    {
                        Id = 1,
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new ConfiguratorField
                            {
                                Id = 1,
                                ComponentType = ComponentType.Product,
                                CompositionOrder = 1
                            }
                        }
                    }
                }
            };
        }
    }
}