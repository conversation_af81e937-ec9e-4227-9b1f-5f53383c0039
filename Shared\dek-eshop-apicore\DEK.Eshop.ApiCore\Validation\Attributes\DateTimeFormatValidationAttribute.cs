using System.ComponentModel.DataAnnotations;

namespace DEK.Eshop.ApiCore.Validation.Attribute;

[Obsolete]
public class DateTimeFormatValidationAttribute : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        var dateString = value as string;

        //null or empty string is valid
        if (string.IsNullOrWhiteSpace(dateString)) {
            return true;
        }

        DateTime _;
        return DateTime.TryParse(dateString, out _);
    }
}
