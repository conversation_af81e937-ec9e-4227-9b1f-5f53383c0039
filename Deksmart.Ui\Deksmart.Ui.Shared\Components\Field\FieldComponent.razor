﻿@using Deksmart.Ui.Model
@using Deksmart.Ui.Shared.Components.Product

@if (Field is TileFieldWrapper tileField)
{
    <div class="tile-container @(tileField.HasValidationError && tileField.IsDirty ? "field-with-error" : "")" id="@Field.Ident">
        @foreach (var value in tileField.FieldValues)
        {
            if (value.Image != null)
            {
                <div class="tile @GetTileClass(tileField, value)" @onclick="() => OnTileClick(tileField, value)">
                    @if (value.Image != null)
                    {
                        <img src="@value.Image" alt="@((MarkupString)value.Title)" class="tile-image" />
                    }
                    <p>@((MarkupString)value.Title)</p>
                </div>
            }
            else
            {
                <p>@((MarkupString)value.Title)</p>
            }
        }
    </div>
    @if (tileField.HasValidationError && tileField.IsDirty)
    {
        <div class="validation-error">
            @tileField.ValidationError
        </div>
    }
}
else if (Field is SingleChoiceFieldWrapper singleChoiceField)
{
    <div class="tile-container @(singleChoiceField.HasValidationError && singleChoiceField.IsDirty ? "field-with-error" : "")" id="@Field.Ident">
        @foreach (var value in singleChoiceField.FieldValues)
        {
            <div class="single-choice-filter-item">
                <input type="radio" id="@value.Id" name="@singleChoiceField.Ident" value="@value.Id" checked="@(singleChoiceField.Value == value.Id)" @onchange="() => OnTileClick(singleChoiceField, value)" />
                <label for="@value.Id">@((MarkupString)value.Title)</label>
            </div>
        }
    </div>
    @if (singleChoiceField.HasValidationError && singleChoiceField.IsDirty)
    {
        <div class="validation-error">
            @singleChoiceField.ValidationError
        </div>
    }
}
else if (Field is ProductFieldWrapper productField)
{
    <div class="@(productField.HasValidationError && productField.IsDirty ? "field-with-error" : "")">
        @if(productField.Detail != null)
        {
            <ProductDetail Detail="@productField.Detail" />
        }
        @if (productField.HasValidationError && productField.IsDirty)
        {
            <div class="validation-error">
                @productField.ValidationError
            </div>
        }
    </div>
}
else if (Field is CheckboxFieldWrapper)
{
    <div class="filter-row checkbox-field @(Field.HasValidationError && Field.IsDirty ? "field-with-error" : "")">
        <FieldSubComponent Field="@Field" OnDataLoaded="OnDataLoaded" OnIdWrapperValueChange="OnIdWrapperValueChange" />
        <div class="filter-info">
            @if (!string.IsNullOrEmpty(Field.Description))
            {
                <button class="info-button"
                        id="@($"info-button-{Field.Field.Id}")"
                        @onmouseover="@(() => OnTooltipShow(Field.Field.Id))"
                        @onmouseout="@(() => OnTooltipHide(Field.Field.Id))"
                        @ontouchstart="@(() => OnTooltipShow(Field.Field.Id))">
                    i
                </button>
                <div class="tooltip" id="@($"tooltip-{Field.Field.Id}")">
                    @((MarkupString)Field.Description)
                </div>
            }
        </div>
    </div>
    @if (Field.HasValidationError && Field.IsDirty)
    {
        <div class="validation-error">
            @Field.ValidationError
        </div>
    }
}
else
{
    <div class="filter-row @(Field.HasValidationError && Field.IsDirty ? "field-with-error" : "")">
        <div class="filter-name">
            <label>@((MarkupString)Field.Title)</label>
        </div>
        <div class="field-container">
            <FieldSubComponent Field="@Field" OnDataLoaded="OnDataLoaded" OnIdWrapperValueChange="OnIdWrapperValueChange" />
            @if (Field.ChildField is not null)
            {
                <FieldSubComponent Field="@Field.ChildField" OnDataLoaded="OnDataLoaded" OnIdWrapperValueChange="OnIdWrapperValueChange" />
            }
        </div>
        <div class="filter-info">
            @if (!string.IsNullOrEmpty(Field.Description))
            {
                <button class="info-button"
                        id="@($"info-button-{Field.Field.Id}")"
                        @onmouseover="@(() => OnTooltipShow(Field.Field.Id))"
                        @onmouseout="@(() => OnTooltipHide(Field.Field.Id))"
                        @ontouchstart="@(() => OnTooltipShow(Field.Field.Id))">
                    i
                </button>
                <div class="tooltip" id="@($"tooltip-{Field.Field.Id}")">
                    @((MarkupString)Field.Description)
                </div>
            }
        </div>
    </div>
    @if (Field.HasValidationError && Field.IsDirty)
    {
        <div class="validation-error">
            @Field.ValidationError
        </div>
    }
}

<script>
    window.updateTooltipPosition = (buttonId, tooltipId) => {
        console.log('updateTooltipPosition called with:', buttonId, tooltipId);
        const button = document.getElementById(buttonId);
        const tooltip = document.getElementById(tooltipId);
        console.log('Found elements:', { button, tooltip });

        if (!button || !tooltip) {
            console.log('Missing elements, returning');
            return;
        }

        const rect = button.getBoundingClientRect();
        console.log('Button rect:', rect);

        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        console.log('Screen dimensions:', { screenWidth, screenHeight });

        // First, make tooltip visible to get its actual height
        tooltip.style.display = 'block';
        tooltip.style.opacity = '1';
        tooltip.style.visibility = 'visible';
        tooltip.style.top = '0';
        tooltip.style.left = '0';

        // Get tooltip dimensions after it's visible
        const tooltipHeight = tooltip.offsetHeight;
        const tooltipWidth = tooltip.offsetWidth;
        console.log('Tooltip dimensions:', { tooltipHeight, tooltipWidth });

        // Calculate initial position (centered above the button)
        let tooltipTop = rect.top - tooltipHeight - 10;
        let tooltipLeft = rect.left + (rect.width / 2);
        console.log('Initial position:', { tooltipTop, tooltipLeft });

        // Check if tooltip would go off the left side
        if (tooltipLeft - (tooltipWidth / 2) < 0) {
            tooltipLeft = tooltipWidth / 2; // Keep minimum half width from left edge
        }
        // Check if tooltip would go off the right side
        else if (tooltipLeft + (tooltipWidth / 2) > screenWidth) {
            tooltipLeft = screenWidth - (tooltipWidth / 2); // Keep minimum half width from right edge
        }

        // Check if tooltip would go off the top
        if (tooltipTop < 0) {
            tooltipTop = rect.bottom + 10; // Show below the button instead
        }

        // Check if tooltip would go off the bottom
        if (tooltipTop + tooltipHeight > screenHeight) {
            tooltipTop = rect.top - tooltipHeight - 10; // Show above the button instead
        }

        console.log('Final position:', { tooltipTop, tooltipLeft });
        tooltip.style.top = `${tooltipTop}px`;
        tooltip.style.left = `${tooltipLeft}px`;
        console.log('Tooltip displayed');

        // Hide any other visible tooltips
        document.querySelectorAll('.tooltip').forEach(otherTooltip => {
            if (otherTooltip !== tooltip) {
                otherTooltip.style.display = 'none';
                otherTooltip.style.opacity = '0';
                otherTooltip.style.visibility = 'hidden';
            }
        });

        // Add touch event listener to hide tooltip when touching elsewhere
        const hideTooltipOnTouch = (e) => {
            if (!tooltip.contains(e.target) && !button.contains(e.target)) {
                hideTooltip(tooltipId);
                document.removeEventListener('touchstart', hideTooltipOnTouch);
            }
        };
        document.addEventListener('touchstart', hideTooltipOnTouch);
    };

    window.hideTooltip = (tooltipId) => {
        console.log('hideTooltip called with:', tooltipId);
        const tooltip = document.getElementById(tooltipId);
        console.log('Found tooltip:', tooltip);
        if (tooltip) {
            tooltip.style.display = 'none';
            tooltip.style.opacity = '0';
            tooltip.style.visibility = 'hidden';
            console.log('Tooltip hidden');
        }
    };
</script>
