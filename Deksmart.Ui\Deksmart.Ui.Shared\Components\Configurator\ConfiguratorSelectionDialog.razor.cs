using Microsoft.AspNetCore.Components;
using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Components.Configurator
{
    public partial class ConfiguratorSelectionDialog : ComponentBase
    {
        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public List<ChildConfiguratorDto> Configurators { get; set; } = new List<ChildConfiguratorDto>();

        [Parameter]
        public EventCallback<ChildConfiguratorDto> OnSelected { get; set; }

        [Parameter]
        public EventCallback OnCanceled { get; set; }

        private async Task OnConfiguratorSelected(ChildConfiguratorDto configurator)
        {
            await OnSelected.InvokeAsync(configurator);
        }

        private async Task OnCancel()
        {
            await OnCanceled.InvokeAsync();
        }
    }
} 