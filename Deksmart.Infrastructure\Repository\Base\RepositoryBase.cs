﻿using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Base
{
    public abstract class RepositoryBase : IRepositoryBase
    {
        protected readonly ConfiguratorContext _context;

        public RepositoryBase(ConfiguratorContext context)
        {
            _context = context;
        }

        /// <inheritdoc/>
        public async Task BeginTransactionAsync()
        {
            await _context.Database.BeginTransactionAsync();
        }

        /// <inheritdoc/>
        public async Task CommitTransactionAsync()
        {
            await _context.Database.CommitTransactionAsync();
        }

        /// <inheritdoc/>
        public async Task RollbackTransactionAsync()
        {
            await _context.Database.RollbackTransactionAsync();
        }
    }
}
