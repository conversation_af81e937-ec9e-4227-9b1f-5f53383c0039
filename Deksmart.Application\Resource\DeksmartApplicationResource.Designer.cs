﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Deksmart.Application.Resource {
    using System;


    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class DeksmartApplicationResource {

        private static global::System.Resources.ResourceManager resourceMan;

        private static global::System.Globalization.CultureInfo resourceCulture;

        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal DeksmartApplicationResource() {
        }

        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Deksmart.Application.Resource.DeksmartApplicationResource", typeof(DeksmartApplicationResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }

        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        internal static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string CancelButtonText {
            get {
                return ResourceManager.GetString("CancelButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Configuration.
        /// </summary>
        internal static string Configuration {
            get {
                return ResourceManager.GetString("Configuration", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Contact Information.
        /// </summary>
        internal static string ContactInformation {
            get {
                return ResourceManager.GetString("ContactInformation", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string EmailLabel {
            get {
                return ResourceManager.GetString("EmailLabel", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get product details: {0}.
        /// </summary>
        internal static string FailedToGetProductDetails {
            get {
                return ResourceManager.GetString("FailedToGetProductDetails", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get product parameters: {0}.
        /// </summary>
        internal static string FailedToGetProductParameters {
            get {
                return ResourceManager.GetString("FailedToGetProductParameters", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get product pricing: {0}.
        /// </summary>
        internal static string FailedToGetProductPricing {
            get {
                return ResourceManager.GetString("FailedToGetProductPricing", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get products: {0}.
        /// </summary>
        internal static string FailedToGetProducts {
            get {
                return ResourceManager.GetString("FailedToGetProducts", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get product specifications: {0}.
        /// </summary>
        internal static string FailedToGetProductSpecifications {
            get {
                return ResourceManager.GetString("FailedToGetProductSpecifications", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get product units: {0}.
        /// </summary>
        internal static string FailedToGetProductUnits {
            get {
                return ResourceManager.GetString("FailedToGetProductUnits", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to {0} is not filled..
        /// </summary>
        internal static string FieldNotFilledValidation {
            get {
                return ResourceManager.GetString("FieldNotFilledValidation", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to generate PDF: {0}.
        /// </summary>
        internal static string FailedToGeneratePdf {
            get {
                return ResourceManager.GetString("FailedToGeneratePdf", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to generate HTML: {0}.
        /// </summary>
        internal static string FailedToGenerateHtml {
            get {
                return ResourceManager.GetString("FailedToGenerateHtml", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to An error occurred during the import process: {0}.
        /// </summary>
        internal static string ImportError {
            get {
                return ResourceManager.GetString("ImportError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Import completed successfully..
        /// </summary>
        internal static string ImportSuccess {
            get {
                return ResourceManager.GetString("ImportSuccess", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string NameLabel {
            get {
                return ResourceManager.GetString("NameLabel", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        internal static string PhoneLabel {
            get {
                return ResourceManager.GetString("PhoneLabel", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Price per package.
        /// </summary>
        internal static string PricePerPackage {
            get {
                return ResourceManager.GetString("PricePerPackage", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Pricing: {0}.
        /// </summary>
        internal static string PricingError {
            get {
                return ResourceManager.GetString("PricingError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Product Code.
        /// </summary>
        internal static string ProductCode {
            get {
                return ResourceManager.GetString("ProductCode", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Product code is required.
        /// </summary>
        internal static string ProductCodeRequired {
            get {
                return ResourceManager.GetString("ProductCodeRequired", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Product not found.
        /// </summary>
        internal static string ProductNotFound {
            get {
                return ResourceManager.GetString("ProductNotFound", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Selected Products.
        /// </summary>
        internal static string SelectedProducts {
            get {
                return ResourceManager.GetString("SelectedProducts", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        internal static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Total Price.
        /// </summary>
        internal static string TotalPrice {
            get {
                return ResourceManager.GetString("TotalPrice", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Total Price (excl. VAT).
        /// </summary>
        internal static string TotalPriceExclVat {
            get {
                return ResourceManager.GetString("TotalPriceExclVat", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Total Price (incl. VAT).
        /// </summary>
        internal static string TotalPriceInclVat {
            get {
                return ResourceManager.GetString("TotalPriceInclVat", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Units: {0}.
        /// </summary>
        internal static string UnitsError {
            get {
                return ResourceManager.GetString("UnitsError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to VAT.
        /// </summary>
        internal static string Vat {
            get {
                return ResourceManager.GetString("Vat", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to No product codes provided.
        /// </summary>
        internal static string NoProductCodesProvided {
            get {
                return ResourceManager.GetString("NoProductCodesProvided", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to API returned {0}: {1}.
        /// </summary>
        internal static string ApiReturnedStatusCodeError {
            get {
                return ResourceManager.GetString("ApiReturnedStatusCodeError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to API returned {0} items, expected {1} items.
        /// </summary>
        internal static string ApiReturnedItemCountMismatch {
            get {
                return ResourceManager.GetString("ApiReturnedItemCountMismatch", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Network error while fetching data from {0}: {1}.
        /// </summary>
        internal static string NetworkErrorWhileFetchingData {
            get {
                return ResourceManager.GetString("NetworkErrorWhileFetchingData", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Unexpected error while fetching data from {0}: {1}.
        /// </summary>
        internal static string UnexpectedErrorWhileFetchingData {
            get {
                return ResourceManager.GetString("UnexpectedErrorWhileFetchingData", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Composite ID is required..
        /// </summary>
        internal static string CompositeIdRequired {
            get {
                return ResourceManager.GetString("CompositeIdRequired", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Composite ID must be a valid integer..
        /// </summary>
        internal static string CompositeIdInvalid {
            get {
                return ResourceManager.GetString("CompositeIdInvalid", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Configurator ID is required..
        /// </summary>
        internal static string ConfiguratorIdRequired {
            get {
                return ResourceManager.GetString("ConfiguratorIdRequired", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Configurator ID must be a valid integer..
        /// </summary>
        internal static string ConfiguratorIdInvalid {
            get {
                return ResourceManager.GetString("ConfiguratorIdInvalid", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Kč.
        /// </summary>
        internal static string CurrencySymbol {
            get {
                return ResourceManager.GetString("CurrencySymbol", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to get unit conversions: {0}.
        /// </summary>
        internal static string FailedToGetUnitConversions {
            get {
                return ResourceManager.GetString("FailedToGetUnitConversions", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Parameter arrays must be the same length.
        /// </summary>
        internal static string UnitConversionParameterArrayLengthMismatch {
            get {
                return ResourceManager.GetString("UnitConversionParameterArrayLengthMismatch", resourceCulture);
            }
        }
    }
}
