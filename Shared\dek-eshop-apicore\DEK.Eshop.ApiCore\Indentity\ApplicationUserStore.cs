using DEK.Eshop.ApiCore.Indentity;
using Microsoft.AspNetCore.Identity;
using System.Data.SqlTypes;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Config.Dto;

namespace DEK.Eshop.ApiCore.Indentity;

public class ApplicationUserStore : IUserStore<ApplicationUser>
{
    private readonly HttpContext httpContext;
    private readonly DbUserRepository userRepository;
    private readonly IConfiguration configuration;
    private readonly IHostEnvironment environment;
    private readonly HttpUserFactory httpUserFactory;


    public ApplicationUserStore(
        IHttpContextAccessor httpContextAccessor,
        DbUserRepository userRepository,
        IConfiguration configuration,
        IHostEnvironment env,
        HttpUserFactory httpUserFactory
     ) {
        if (httpContextAccessor.HttpContext is null) {
            throw new ArgumentNullException("Missing HttpContext");
        }

        this.httpContext = httpContextAccessor.HttpContext;
        this.userRepository = userRepository;
        this.configuration = configuration;
        this.environment = env;
        this.httpUserFactory = httpUserFactory;

    }

    public async Task<ApplicationUser?> FindByIdAsync(string userId, CancellationToken cancellationToken = default(CancellationToken))
    {
        if (userId != "http_user") {
            throw new NotImplementedException();
        }

        cancellationToken.ThrowIfCancellationRequested();

        var user = this.httpUserFactory.Create();

        if (user.UserEmail != null) {
            var bdUser = await this.userRepository.GetUser(user.UserEmail);
            if (bdUser != null) {
                user = user with {
                    BranchHomeCode = bdUser.BranchHomeCode,
                    PriceLevelEshop = bdUser.PriceLevelEshop,
                    PriceLevelRental = bdUser.PriceLevelRental,
                    HasRentalBan = bdUser.HasRentalBan,
                    CompanyId = bdUser.CompanyId,
                    IsB2B = bdUser.IsB2B,
                    IsCompanyAdmin = bdUser.IsCompanyAdmin,
                    Name = bdUser.SubjectName,
                };
            }
            //claims["seasonId"] = bdUser.SeasonId;
        }

        return user;
    }

    // Not implemented

    public async Task<IdentityResult> CreateAsync(ApplicationUser user, CancellationToken cancellationToken = default(CancellationToken))
    {
        throw new NotImplementedException();
    }

    public async Task<IdentityResult> DeleteAsync(ApplicationUser user, CancellationToken cancellationToken = default(CancellationToken))
    {
        throw new NotImplementedException();
    }

    public async Task<ApplicationUser> FindByNameAsync(string userName, CancellationToken cancellationToken = default(CancellationToken))
    {
        throw new NotImplementedException();
    }

    public Task<string> GetNormalizedUserNameAsync(ApplicationUser user, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public Task<string> GetUserIdAsync(ApplicationUser user, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public Task<string> GetUserNameAsync(ApplicationUser user, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public Task SetNormalizedUserNameAsync(ApplicationUser user, string normalizedName, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public Task SetUserNameAsync(ApplicationUser user, string userName, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }

    public Task<IdentityResult> UpdateAsync(ApplicationUser user, CancellationToken cancellationToken)
    {
        throw new NotImplementedException();
    }
    public void Dispose()
    {
    }
}
