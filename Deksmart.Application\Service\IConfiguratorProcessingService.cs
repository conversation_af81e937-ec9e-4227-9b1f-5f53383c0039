using Deksmart.Shared.Dto;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;

namespace Deksmart.Application.Service
{
    /// <summary>
    /// Service responsible for orchestrating configurator processing workflow.
    /// Coordinates domain services to apply field values, calculate expressions, select products, and validate configurations.
    /// </summary>
    public interface IConfiguratorProcessingService
    {
        /// <summary>
        /// Processes a configurator by applying field values, calculating expressions, selecting products, and validating the configuration.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator to process.</param>
        /// <param name="clientFieldValues">The field values provided by the client.</param>
        /// <param name="selectedProducts">The products selected by the client.</param>
        /// <param name="categoryStates">The category collapse states to apply.</param>
        /// <returns>
        /// A tuple containing the processed <see cref="Configurator"/> entity (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(Configurator? configurator, ValidationResult validation)> ProcessConfiguratorAsync(
            int configuratorId, 
            IEnumerable<ClientFieldValueDto> clientFieldValues, 
            List<ClientProductValueDto>? selectedProducts, 
            List<CategoryStateDto>? categoryStates = null);


        /// <summary>
        /// Applies category collapse states to the configurator.
        /// </summary>
        /// <param name="configurator">The configurator to update.</param>
        /// <param name="categoryStates">The category states to apply.</param>
        void ApplyCategoryStates(Configurator configurator, List<CategoryStateDto> categoryStates);
    }
}