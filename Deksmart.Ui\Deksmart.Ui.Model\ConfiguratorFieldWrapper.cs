using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Model
{
    public abstract class ConfiguratorFieldWrapper
    {
        public ConfiguratorFieldDto Field { get; }

        protected ConfiguratorFieldWrapper(ConfiguratorFieldDto field)
        {
            Field = field;
        }

        public virtual string Title => Field.Title;
        public virtual string? Description => Field.Description;
        public string? Suffix => Field.Suffix;
        public int Order => Field.Order;
        public string Ident => Field.Ident;
        public string? JavaScriptAfterRender => Field.JavaScriptAfterRender;
        public string? JavaScriptOnFocus => Field.JavaScriptOnFocus;
        public string? JavaScriptOnBlur => Field.JavaScriptOnBlur;
        public string? ValidationError => Field.ValidationError;
        public bool HasValidationError => !string.IsNullOrEmpty(ValidationError);
        public bool IsDirty { get; set; } = false;
        public bool IsPendingValidation { get; set; } = false;
        public List<ConfiguratorFieldValueWrapper> FieldValues { get; set; } = [];
        public ConfiguratorFieldWrapper? ChildField { get; set; }
    }

    public class NumericFieldWrapper : ConfiguratorFieldWrapper
    {
        public NumericFieldWrapper(ConfiguratorFieldDto field) : base(field) { }

        public decimal Value { get; set; }

        public decimal Min => Field.Min ?? 0;

        public virtual decimal Max => Field.Max ?? int.MaxValue;
    }

    //TODO step?
    public class SliderFieldWrapper : NumericFieldWrapper
    {
        public SliderFieldWrapper(ConfiguratorFieldDto field) : base(field) { }

        public override decimal Max => Field.Max ?? 100;
    }

    public class CheckboxFieldWrapper : ConfiguratorFieldWrapper
    {
        public CheckboxFieldWrapper(ConfiguratorFieldDto field) : base(field)
        {
            if (Field.Value.HasValue)
            {
                Value = Field.Value.Value == 1.0m;
            }
        }

        public bool Value { get; set; } = true;
    }

    public abstract class IdFieldWrapper : ConfiguratorFieldWrapper
    {
        protected IdFieldWrapper(ConfiguratorFieldDto field) : base(field)
        {
            if (Field.ConfiguratorFieldValues.Any())
            {
                var selectedFieldValue = Field.ConfiguratorFieldValues.FirstOrDefault(d => d.NumericValue == Field.Value);

                Value = selectedFieldValue?.Id;
            }
        }

        public int? Value { get; set; }

        public ConfiguratorFieldValueWrapper? SelectedWrapper => FieldValues.SingleOrDefault(d => d.Id == Value);
    }

    public class SingleChoiceFieldWrapper : IdFieldWrapper
    {
        public SingleChoiceFieldWrapper(ConfiguratorFieldDto field) : base(field)
        {
        }
    }

    public class SelectBoxFieldWrapper : IdFieldWrapper
    {
        public SelectBoxFieldWrapper(ConfiguratorFieldDto field) : base(field)
        {
        }
    }

    public class TileFieldWrapper : IdFieldWrapper
    {
        public TileFieldWrapper(ConfiguratorFieldDto field) : base(field)
        {
        }
    }

    public class DefaultFieldWrapper : ConfiguratorFieldWrapper
    {
        public DefaultFieldWrapper(ConfiguratorFieldDto field) : base(field) { }

        public object Value { get; set; }
    }

    public class ExpressionFieldWrapper : ConfiguratorFieldWrapper
    {
        public ExpressionFieldWrapper(ConfiguratorFieldDto field) : base(field) { }

        public string Text => Field.Value?.ToString("0.################") ?? string.Empty;
    }

    public class TextFieldWrapper : ConfiguratorFieldWrapper
    {
        public TextFieldWrapper(ConfiguratorFieldDto field) : base(field) { }

        public string Text => Field.Title ?? string.Empty;
    }

    public class ProductFieldWrapper : ConfiguratorFieldWrapper
    {
        public ProductFieldWrapper(ConfiguratorFieldDto field) : base(field)
        {
            if(Field.ProductDetailsDto != null)
                Detail = new ConfiguratorProductDetailWrapper(Field.ProductDetailsDto);
        }

        public ConfiguratorProductDetailWrapper? Detail { get; set; }
    }
}
