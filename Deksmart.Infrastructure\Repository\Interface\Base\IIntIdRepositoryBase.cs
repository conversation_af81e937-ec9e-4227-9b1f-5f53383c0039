﻿using Deksmart.Domain.Entity.Db.Interface;

namespace Deksmart.Infrastructure.Repository.Interface.Base
{
    /// <summary>
    /// Repository interface specialization for entities with an integer identifier, providing basic CRUD operations.
    /// </summary>
    /// <typeparam name="TEntity">The entity type with an integer identifier.</typeparam>
    public interface IIntIdRepositoryBase<TEntity> : IIdRepositoryBase<TEntity, int>
        where TEntity : class, IIntIdEntity
    {
    }
}
