
using DEK.Eshop.ApiCore.Config.Dto;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Indentity;
using DEK.Eshop.ApiCore.Extension;
using ConfigServiceBase = DEK.Eshop.ApiCore.Config.ConfigService;

namespace DEK.Eshop.ApiCore.Sandbox.Config;

public class ConfigService : ConfigServiceBase
{
    public ConfigService(IConfiguration config, IHostEnvironment env, HttpUserFactory httpUserFactory) : base(config, env, httpUserFactory)
    {
    }

    override public string GetEshopId()
    {
        return base.GetEshopId().Dek_IsNullOrEmpty()
            ? ConfigFactory.Create<Application>(_config).EshopId
            : base.GetEshopId();
    }
}
