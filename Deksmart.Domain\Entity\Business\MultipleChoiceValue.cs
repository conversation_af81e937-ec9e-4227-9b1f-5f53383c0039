﻿namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Represents a multiple-choice field in the configurator, encapsulating the set of allowed numeric options and the default value.
    /// Used to map, validate, and process user selections for fields with predefined choices, sourced from ConfiguratorField and its associated values.
    /// Supports validation to ensure user input matches one of the allowed options or the default.
    /// </summary>
    public class MultipleChoiceValue
    {
        public int Id { get; set; }

        public string Ident { get; set; } = null!;

        public decimal DefaultValue { get; set; } = 0;

        public List<decimal> Values { get; set; } = [];
    }
}
