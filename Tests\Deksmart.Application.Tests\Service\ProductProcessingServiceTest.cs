using Deksmart.Application.Service;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Shared.Dto;
using Microsoft.Extensions.Logging;
using Moq;
using Deksmart.Application.Service.Http;
using Deksmart.Domain.Service;
using Deksmart.Application.Mapping;

namespace Deksmart.Application.Tests.Service
{
    public class ProductProcessingServiceTest
    {
        private readonly Mock<IEshopApiService> _eshopApiServiceMock;
        private readonly Mock<IProductPriceService> _productPriceServiceMock;
        private readonly Mock<ILogger<ProductProcessingService>> _loggerMock;
        private readonly Mock<IConfiguratorMappingService> _mappingServiceMock;
        private readonly Mock<IProductAggregationMappingService> _aggregationMappingServiceMock;
        private readonly Mock<IProductEnrichmentService> _productEnrichmentServiceMock;
        private readonly ProductProcessingService _service;

        public ProductProcessingServiceTest()
        {
            _eshopApiServiceMock = new Mock<IEshopApiService>();
            _productPriceServiceMock = new Mock<IProductPriceService>();
            _loggerMock = new Mock<ILogger<ProductProcessingService>>();
            _mappingServiceMock = new Mock<IConfiguratorMappingService>();
            _aggregationMappingServiceMock = new Mock<IProductAggregationMappingService>();
            _productEnrichmentServiceMock = new Mock<IProductEnrichmentService>();

            _service = new ProductProcessingService(
                _loggerMock.Object,
                _eshopApiServiceMock.Object,
                _productPriceServiceMock.Object,
                _mappingServiceMock.Object,
                _aggregationMappingServiceMock.Object,
                _productEnrichmentServiceMock.Object
            );
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithSameCompositionName_GroupsProductsInSameComposition()
        {
            // Arrange
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001",
                            Title = "Product A",
                            CalculatedAmount = 10.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Floor Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD002",
                            Title = "Product B",
                            CalculatedAmount = 5.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Floor Assembly" // Same composition name
                        }
                    }
                }
            };

            SetupMocks();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(compositeConfigurator.ConfiguratorCompositions); // Should have only one composition
            var firstComposition = compositeConfigurator.ConfiguratorCompositions.First();
            Assert.Equal("Floor Assembly", firstComposition.Title);
            Assert.Equal(2, firstComposition.ConfiguratorProducts.Count); // Should contain both products
            
            var product1 = firstComposition.ConfiguratorProducts.First(p => p.ProductCode == "PROD001");
            var product2 = firstComposition.ConfiguratorProducts.First(p => p.ProductCode == "PROD002");
            
            Assert.Equal(7.0m, product1.CalculatedAmount);  // 10.0 * 0.7 = 7.0
            Assert.Equal(3.5m, product2.CalculatedAmount);  // 5.0 * 0.7 = 3.5
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithSameCompositionNameCaseInsensitive_GroupsProductsInSameComposition()
        {
            // Arrange
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001",
                            Title = "Product A",
                            CalculatedAmount = 10.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Floor Assembly" // Original case
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD002",
                            Title = "Product B",
                            CalculatedAmount = 5.0m,
                            ProductUnit = "m",
                            CompositionTitle = "floor assembly" // Different case
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD003",
                            Title = "Product C",
                            CalculatedAmount = 3.0m,
                            ProductUnit = "m",
                            CompositionTitle = "  Floor Assembly  " // With whitespace
                        }
                    }
                }
            };

            SetupMocks();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(compositeConfigurator.ConfiguratorCompositions); // Should have only one composition despite case/whitespace differences
            var firstComposition = compositeConfigurator.ConfiguratorCompositions.First();
            Assert.Equal("Floor Assembly", firstComposition.Title); // Should use the first encountered title
            Assert.Equal(3, firstComposition.ConfiguratorProducts.Count); // Should contain all three products
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithSameProductCodeInSameComposition_SumsAmounts()
        {
            // Arrange
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001",
                            Title = "Product A",
                            CalculatedAmount = 10.0m,
                            UserAmount = 2.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Floor Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Same product code
                            Title = "Product A",
                            CalculatedAmount = 5.0m,
                            UserAmount = 3.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Floor Assembly" // Same composition
                        }
                    }
                }
            };

            SetupMocks();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(compositeConfigurator.ConfiguratorCompositions); // Should have only one composition
            var firstComposition = compositeConfigurator.ConfiguratorCompositions.First();
            Assert.Single(firstComposition.ConfiguratorProducts); // Should have only one product (aggregated)
            
            var aggregatedProduct = firstComposition.ConfiguratorProducts.First();
            Assert.Equal("PROD001", aggregatedProduct.ProductCode);
            Assert.Equal(10.5m, aggregatedProduct.CalculatedAmount); // (10 + 5) * 0.7 = 15 * 0.7 = 10.5
            Assert.Equal(5.0m, aggregatedProduct.UserAmount); // 2 + 3
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithSameProductCodeInDifferentCompositions_CreatesCombinedComposition()
        {
            // Arrange
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001",
                            Title = "Product A",
                            CalculatedAmount = 10.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Floor Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Same product code
                            Title = "Product A",
                            CalculatedAmount = 5.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Wall Assembly" // Different composition
                        }
                    }
                }
            };

            SetupMocks();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(compositeConfigurator.ConfiguratorCompositions); // Should have only one composition with combined name
            var firstComposition = compositeConfigurator.ConfiguratorCompositions.First();
            Assert.Equal("Floor Assembly / Wall Assembly", firstComposition.Title); // Combined title
            Assert.Single(firstComposition.ConfiguratorProducts); // Should have only one aggregated product
            
            var aggregatedProduct = firstComposition.ConfiguratorProducts.First();
            Assert.Equal("PROD001", aggregatedProduct.ProductCode);
            Assert.Equal(10.5m, aggregatedProduct.CalculatedAmount); // (10 + 5) * 0.7 = 15 * 0.7 = 10.5 (amounts summed)
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithNonCompositeConfigurator_ReturnsEarly()
        {
            // Arrange
            var nonCompositeConfigurator = new Configurator
            {
                Id = 1,
                IsComposite = false // Not composite
            };
            var childProductStates = new List<ChildProductStateDto>();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(nonCompositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            // Should return early without making any API calls
            _eshopApiServiceMock.VerifyNoOtherCalls();
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithEmptyChildProducts_ClearsCompositions()
        {
            // Arrange
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>(); // Empty
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Empty(compositeConfigurator.ConfiguratorCompositions);
            _productPriceServiceMock.Verify(x => x.SetConfiguratorTotalPrices(compositeConfigurator, It.IsAny<List<ConfiguratorProduct>>()), Times.Once);
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithUserAmountPriorityLogic_CalculatesCorrectPackages()
        {
            // Arrange: Test case from USERAMOUNT_PRIORITY_LOGIC_ISSUE.md
            // 3 products with same code, UnitsInPackage=5, API conversion 1:1
            // Product 1: CalculatedAmount=1, UserAmount=null
            // Product 2: CalculatedAmount=2, UserAmount=null  
            // Product 3: CalculatedAmount=1, UserAmount=10
            // Expected: 11 packages total (Math.Ceiling((1+2)/5) + 10 = 1 + 10 = 11)
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001",
                            Title = "Test Product",
                            CalculatedAmount = 1.0m,
                            UserAmount = null, // No user amount
                            ProductUnit = "m",
                            CompositionTitle = "Test Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Same product code
                            Title = "Test Product",
                            CalculatedAmount = 2.0m,
                            UserAmount = null, // No user amount
                            ProductUnit = "m",
                            CompositionTitle = "Test Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Same product code
                            Title = "Test Product",
                            CalculatedAmount = 1.0m,
                            UserAmount = 10.0m, // User amount in packages
                            ProductUnit = "m",
                            CompositionTitle = "Test Assembly"
                        }
                    }
                }
            };

            // Setup mocks for 1:1 conversion and UnitsInPackage=5
            SetupUserAmountMocks();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(compositeConfigurator.ConfiguratorCompositions);
            var composition = compositeConfigurator.ConfiguratorCompositions.First();
            Assert.Single(composition.ConfiguratorProducts); // Should have one aggregated product
            
            var aggregatedProduct = composition.ConfiguratorProducts.First();
            Assert.Equal("PROD001", aggregatedProduct.ProductCode);
            
            // Verify current behavior (before UserAmount priority logic implementation)
            Assert.Equal(4.0m, aggregatedProduct.CalculatedAmount); // Sum of all calculated amounts (1+2+1)
            
            // This should FAIL with current implementation 
            // Current implementation just sums user amounts (10.0m), but correct behavior is:
            // UserAmount should be total packages: Math.Ceiling((1+2)/5) + 10 = 1 + 10 = 11 packages
            // When user sets ANY part of a product, UserAmount should equal final PackageQuantity
            Assert.Equal(11.0m, aggregatedProduct.UserAmount); // Should be total packages, not just sum of user inputs
            
            // TODO: Add PackageQuantity test once ProductPriceService integration is complete
            // Assert.Equal(11.0m, aggregatedProduct.PackageQuantity); // Should match UserAmount
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithUserAmountPriorityLogic_ShouldFailWithCurrentImplementation()
        {
            // Arrange: This test should FAIL with the current implementation
            // Test demonstrates the broken UserAmount priority logic for PackageQuantity
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001",
                            Title = "Test Product",
                            CalculatedAmount = 1.0m,
                            UserAmount = null, // No user amount
                            ProductUnit = "m",
                            CompositionTitle = "Test Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Same product code
                            Title = "Test Product",
                            CalculatedAmount = 2.0m,
                            UserAmount = null, // No user amount
                            ProductUnit = "m",
                            CompositionTitle = "Test Assembly"
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Same product code
                            Title = "Test Product",
                            CalculatedAmount = 1.0m,
                            UserAmount = 10.0m, // User amount in packages
                            ProductUnit = "m",
                            CompositionTitle = "Test Assembly"
                        }
                    }
                }
            };

            // Setup enhanced mocks that include PackageQuantity calculation
            SetupUserAmountMocksWithPackageCalculation();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Single(compositeConfigurator.ConfiguratorCompositions);
            var composition = compositeConfigurator.ConfiguratorCompositions.First();
            Assert.Single(composition.ConfiguratorProducts);
            
            var aggregatedProduct = composition.ConfiguratorProducts.First();
            Assert.Equal("PROD001", aggregatedProduct.ProductCode);
            Assert.Equal(4.0m, aggregatedProduct.CalculatedAmount); // Sum of all calculated amounts (1+2+1)
            
            // Test the corrected UserAmount priority logic:
            // UserAmount should be total packages: Math.Ceiling((1+2)/5) + 10 = 1 + 10 = 11 packages
            // PackageQuantity should be pure system recommendation: Math.Ceiling((1+2+1)/5) = Math.Ceiling(4/5) = 1 package
            Assert.Equal(11.0m, aggregatedProduct.UserAmount); // UserAmount priority logic result
            Assert.Equal(1.0m, aggregatedProduct.PackageQuantity); // Pure system recommendation (ignoring user input)
        }

        [Fact]
        public async Task AggregateFromChildProductStatesAsync_WithTabCompositionOrdering_UsesMinimumTabCompositionOrder()
        {
            // Arrange: Test your example with tab/composition ordering priority
            var compositeConfigurator = CreateCompositeConfigurator();
            var childProductStates = new List<ChildProductStateDto>
            {
                // Configurator1 (TabOrder 1)
                new ChildProductStateDto
                {
                    ConfiguratorId = 1,
                    TabOrder = 1,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD002", // Product2
                            Title = "Product2",
                            CalculatedAmount = 5.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Composition2",
                            CompositionOrder = 5, // 1/5
                            ProductOrder = 3
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Product1
                            Title = "Product1", 
                            CalculatedAmount = 7.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Composition1",
                            CompositionOrder = 4, // 1/4 - this should be the minimum for Composition1/Composition3
                            ProductOrder = 1
                        }
                    }
                },
                // Configurator2 (TabOrder 2)
                new ChildProductStateDto
                {
                    ConfiguratorId = 2,
                    TabOrder = 2,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD001", // Product1 (same - will be combined)
                            Title = "Product1",
                            CalculatedAmount = 8.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Composition3",
                            CompositionOrder = 1, // 2/1 - higher than 1/4
                            ProductOrder = 1
                        },
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD004", // Product4
                            Title = "Product4",
                            CalculatedAmount = 3.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Composition2",
                            CompositionOrder = 2, // 2/2 - higher than 1/5
                            ProductOrder = 1
                        }
                    }
                },
                // Configurator3 (TabOrder 3)
                new ChildProductStateDto
                {
                    ConfiguratorId = 3,
                    TabOrder = 3,
                    SelectedProducts = new List<ProductForAggregationDto>
                    {
                        new ProductForAggregationDto
                        {
                            ProductCode = "PROD005", // Product5
                            Title = "Product5",
                            CalculatedAmount = 8.0m,
                            ProductUnit = "m",
                            CompositionTitle = "Composition4",
                            CompositionOrder = 2, // 3/2
                            ProductOrder = 1
                        }
                    }
                }
            };

            SetupMocks();
            var validation = new ValidationResult();

            // Act
            await _service.AggregateFromChildProductStatesAsync(compositeConfigurator, childProductStates, validation);

            // Assert
            Assert.False(validation.HasErrors);
            Assert.Equal(3, compositeConfigurator.ConfiguratorCompositions.Count);
            
            var compositions = compositeConfigurator.ConfiguratorCompositions.ToList();
            
            // Expected order based on minimum tab/composition order:
            // 1. Composition1/Composition3: min(1/4, 2/1) = 1/4 = 10004
            // 2. Composition2: min(1/5, 2/2) = 1/5 = 10005  
            // 3. Composition4: 3/2 = 30002
            
            // The ordering should now be correct - let's verify step by step
            // Expected: Composition1/Composition3 (10004), Composition2 (10005), Composition4 (30002)
            
            // First verify we have the right number and ordering
            Assert.Equal("Composition1 / Composition3", compositions[0].Title);
            Assert.Equal(10004, compositions[0].Order); // 1*10000 + 4
            
            Assert.Equal("Composition2", compositions[1].Title);
            Assert.Equal(10005, compositions[1].Order); // 1*10000 + 5
            
            Assert.Equal("Composition4", compositions[2].Title);
            Assert.Equal(30002, compositions[2].Order); // 3*10000 + 2
            
            // Verify product aggregation  
            Assert.Single(compositions[0].ConfiguratorProducts);
            Assert.Equal("PROD001", compositions[0].ConfiguratorProducts.First().ProductCode);
            
            // Verify correct aggregation: PROD001 should sum to 10.5 (7.0*0.7 + 8.0*0.7 = 4.9 + 5.6 = 10.5)
            var prod1Amount = compositions[0].ConfiguratorProducts.First().CalculatedAmount;
            Assert.Equal(10.5m, prod1Amount); // (7.0 + 8.0) * 0.7 = 15.0 * 0.7 = 10.5
            
            Assert.Equal(2, compositions[1].ConfiguratorProducts.Count);
            Assert.Single(compositions[2].ConfiguratorProducts);
        }

        private Configurator CreateCompositeConfigurator()
        {
            return new Configurator
            {
                Id = 1,
                IsComposite = true,
                ConfiguratorCompositions = new List<ConfiguratorComposition>()
            };
        }

        private void SetupMocks()
        {
            // Setup unit API response - support up to 5 products
            var unitsData = new List<EshopProductUnit>
            {
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 1.0m },
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 1.0m },
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 1.0m },
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 1.0m },
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 1.0m }
            };
            var unitsResult = ApiResult<List<EshopProductUnit>>.Success(unitsData);

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(unitsResult);

            // Setup unit conversion API response - 1:0.7 conversion ratio to detect conversion vs aggregation issues
            // This way we can identify if the problem is in conversion mapping or aggregation logic
            _eshopApiServiceMock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync((List<string> codes, List<decimal> amounts, List<string> inputUnits, List<string> outputUnits) =>
                {
                    var conversionData = new List<EshopProductUnitConversion>();
                    for (int i = 0; i < codes.Count; i++)
                    {
                        conversionData.Add(new EshopProductUnitConversion 
                        { 
                            ProductCode = codes[i], 
                            QuantityOutput = amounts[i] * 0.7m // 1:0.7 conversion ratio
                        });
                    }
                    return ApiResult<List<EshopProductUnitConversion>>.Success(conversionData);
                });

            // Setup pricing API response - support up to 5 products
            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceVatSales = 100.0m },
                new EshopProductPricing { PriceVatSales = 200.0m },
                new EshopProductPricing { PriceVatSales = 300.0m },
                new EshopProductPricing { PriceVatSales = 300.0m },
                new EshopProductPricing { PriceVatSales = 800.0m }
            };
            var pricingResult = ApiResult<List<EshopProductPricing>>.Success(pricingData);

            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(pricingResult);

            // Setup product price service
            _productPriceServiceMock.Setup(x => x.CalculatePrices(It.IsAny<IEnumerable<ConfiguratorProduct>>(), It.IsAny<IEnumerable<ProductPriceData>>()))
                .Returns(new ValidationResult());
            _productPriceServiceMock.Setup(x => x.SetConfiguratorTotalPrices(It.IsAny<Configurator>(), It.IsAny<IEnumerable<ConfiguratorProduct>>()));
            
            SetupAggregationMappingService();
        }

        private void SetupUserAmountMocks()
        {
            // Setup unit API response for UserAmount test (UnitsInPackage=5)
            var unitsData = new List<EshopProductUnit>
            {
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 5 }
            };
            var unitsResult = ApiResult<List<EshopProductUnit>>.Success(unitsData);

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(unitsResult);

            // Setup 1:1 conversion API response for UserAmount test
            var conversionData = new List<EshopProductUnitConversion>
            {
                new EshopProductUnitConversion { ProductCode = "PROD001", QuantityOutput = 1.0m },
                new EshopProductUnitConversion { ProductCode = "PROD001", QuantityOutput = 2.0m },
                new EshopProductUnitConversion { ProductCode = "PROD001", QuantityOutput = 1.0m }
            };
            var conversionResult = ApiResult<List<EshopProductUnitConversion>>.Success(conversionData);

            _eshopApiServiceMock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync(conversionResult);

            // Setup pricing API response for UserAmount test
            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceVatSales = 100.0m }
            };
            var pricingResult = ApiResult<List<EshopProductPricing>>.Success(pricingData);

            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(pricingResult);

            // Setup product price service for UserAmount test
            _productPriceServiceMock.Setup(x => x.CalculatePrices(It.IsAny<IEnumerable<ConfiguratorProduct>>(), It.IsAny<IEnumerable<ProductPriceData>>()))
                .Returns(new ValidationResult());
            _productPriceServiceMock.Setup(x => x.SetConfiguratorTotalPrices(It.IsAny<Configurator>(), It.IsAny<IEnumerable<ConfiguratorProduct>>()));
            
            SetupAggregationMappingService();
        }

        private void SetupUserAmountMocksWithPackageCalculation()
        {
            // Setup unit API response for UserAmount test (UnitsInPackage=5)
            var unitsData = new List<EshopProductUnit>
            {
                new EshopProductUnit { UnitSales = "m", UnitsInPackage = 5 }
            };
            var unitsResult = ApiResult<List<EshopProductUnit>>.Success(unitsData);

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(unitsResult);

            // Setup 1:1 conversion API response for UserAmount test
            var conversionData = new List<EshopProductUnitConversion>
            {
                new EshopProductUnitConversion { ProductCode = "PROD001", QuantityOutput = 1.0m },
                new EshopProductUnitConversion { ProductCode = "PROD001", QuantityOutput = 2.0m },
                new EshopProductUnitConversion { ProductCode = "PROD001", QuantityOutput = 1.0m }
            };
            var conversionResult = ApiResult<List<EshopProductUnitConversion>>.Success(conversionData);

            _eshopApiServiceMock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync(conversionResult);

            // Setup pricing API response for UserAmount test
            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceVatSales = 100.0m }
            };
            var pricingResult = ApiResult<List<EshopProductPricing>>.Success(pricingData);

            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(pricingResult);

            // Setup product price service to simulate correct PackageQuantity and pricing calculation
            _productPriceServiceMock.Setup(x => x.CalculatePrices(It.IsAny<IEnumerable<ConfiguratorProduct>>(), It.IsAny<IEnumerable<ProductPriceData>>()))
                .Returns((IEnumerable<ConfiguratorProduct> products, IEnumerable<ProductPriceData> enrichment) =>
                {
                    // Implement correct behavior: PackageQuantity is pure system recommendation, pricing uses UserAmount
                    foreach (var product in products)
                    {
                        // PackageQuantity should always be pure system recommendation (already set by CreateAggregatedProduct)
                        // Use UserAmount for pricing if available, otherwise use PackageQuantity
                        var amountForPricing = product.UserAmount ?? product.PackageQuantity;
                        product.PriceVat = amountForPricing * 100.0m * 5.0m; // price per package * UnitsInPackage
                    }
                    return new ValidationResult(); // Return empty validation result
                });

            _productPriceServiceMock.Setup(x => x.SetConfiguratorTotalPrices(It.IsAny<Configurator>(), It.IsAny<IEnumerable<ConfiguratorProduct>>()));
            
            SetupAggregationMappingService();
        }

        [Fact]
        public async Task CalculateSelectedProductPricesAsync_ShouldReturnPrices_WhenValidInput()
        {
            // Arrange
            var selectedProducts = new List<SelectedProductDto>
            {
                new SelectedProductDto { ProductCode = "TEST1", Amount = 2.5m, Unit = "m2" },
                new SelectedProductDto { ProductCode = "TEST2", Amount = 1.0m, Unit = "pcs" }
            };
            var currentProductCode = "TEST1";

            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceNoVatSales = 100.0m, Vat = 21.0m },
                new EshopProductPricing { PriceNoVatSales = 50.0m, Vat = 21.0m }
            };

            var unitsData = new List<EshopProductUnit>
            {
                new EshopProductUnit { UnitSales = "m2", UnitPackage = "m2", UnitsInPackage = 1.0m },
                new EshopProductUnit { UnitSales = "pcs", UnitPackage = "pcs", UnitsInPackage = 1.0m }
            };

            var conversions = new List<EshopProductUnitConversion>
            {
                new EshopProductUnitConversion { ProductCode = "TEST1", QuantityOutput = 2.5m },
                new EshopProductUnitConversion { ProductCode = "TEST2", QuantityOutput = 1.0m }
            };

            var priceResponse = new ProductPriceCalculationResponse
            {
                PriceVat = 121.0m,
                TotalPriceNoVat = 150.0m,
                TotalPriceVat = 181.5m,
                TotalVat = 31.5m
            };

            // Setup API mocks
            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductPricing>>.Success(pricingData));

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnit>>.Success(unitsData));

            _eshopApiServiceMock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnitConversion>>.Success(conversions));

            // Setup mapping service mock
            _mappingServiceMock.Setup(x => x.MapToEntity(It.IsAny<SelectedProductDto>()))
                .Returns((SelectedProductDto dto) => new SelectedProduct
                {
                    ProductCode = dto.ProductCode,
                    Amount = dto.Amount,
                    Unit = dto.Unit
                });

            // Setup product price service mock
            _productPriceServiceMock.Setup(x => x.CalculateSelectedProductPrices(
                It.IsAny<List<SelectedProduct>>(),
                currentProductCode,
                It.IsAny<IEnumerable<ProductPriceData>>()))
                .Returns((priceResponse, new ValidationResult()));

            // Act
            var (result, validation) = await _service.CalculateSelectedProductPricesAsync(selectedProducts, currentProductCode);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);
            Assert.Equal(121.0m, result.PriceVat);
            Assert.Equal(150.0m, result.TotalPriceNoVat);

            // Verify unit conversion API was called with correct parameters
            _eshopApiServiceMock.Verify(x => x.GetProductUnitConversionsAsync(
                It.Is<List<string>>(codes => codes.Contains("TEST1") && codes.Contains("TEST2")),
                It.Is<List<decimal>>(quantities => quantities.Contains(2.5m) && quantities.Contains(1.0m)),
                It.Is<List<string>>(inputs => inputs.Contains("m2") && inputs.Contains("pcs")),
                It.Is<List<string>>(outputs => outputs.Contains("m2") && outputs.Contains("pcs"))),
                Times.Once);
        }

        [Fact]
        public async Task CalculateSelectedProductPricesAsync_ShouldFallbackToLegacyConversion_WhenUnitConversionFails()
        {
            // Arrange
            var selectedProducts = new List<SelectedProductDto>
            {
                new SelectedProductDto { ProductCode = "TEST1", Amount = 2.5m, Unit = "m2" }
            };
            var currentProductCode = "TEST1";

            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceNoVatSales = 100.0m, Vat = 21.0m }
            };

            var unitsData = new List<EshopProductUnit>
            {
                new EshopProductUnit { UnitSales = "m2", UnitPackage = "m2", UnitsInPackage = 1.0m }
            };

            var priceResponse = new ProductPriceCalculationResponse
            {
                PriceVat = 242.0m,
                TotalPriceNoVat = 200.0m,
                TotalPriceVat = 242.0m,
                TotalVat = 42.0m
            };

            // Setup API mocks with unit conversion failure
            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductPricing>>.Success(pricingData));

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnit>>.Success(unitsData));

            _eshopApiServiceMock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnitConversion>>.Error("Unit conversion failed"));

            // Setup mapping service mock
            _mappingServiceMock.Setup(x => x.MapToEntity(It.IsAny<SelectedProductDto>()))
                .Returns((SelectedProductDto dto) => new SelectedProduct
                {
                    ProductCode = dto.ProductCode,
                    Amount = dto.Amount,
                    Unit = dto.Unit
                });

            // Setup product price service mock - should be called with ConvertedAmount = null (triggering legacy conversion)
            _productPriceServiceMock.Setup(x => x.CalculateSelectedProductPrices(
                It.IsAny<List<SelectedProduct>>(),
                currentProductCode,
                It.Is<IEnumerable<ProductPriceData>>(data => data.All(d => d.ConvertedAmount == null))))
                .Returns((priceResponse, new ValidationResult()));

            // Act
            var (result, validation) = await _service.CalculateSelectedProductPricesAsync(selectedProducts, currentProductCode);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);
            Assert.Equal(242.0m, result.PriceVat);
            Assert.Equal(200.0m, result.TotalPriceNoVat);

            // Verify that ProductPriceService was called with ConvertedAmount = null (legacy conversion)
            _productPriceServiceMock.Verify(x => x.CalculateSelectedProductPrices(
                It.IsAny<List<SelectedProduct>>(),
                currentProductCode,
                It.Is<IEnumerable<ProductPriceData>>(data => data.All(d => d.ConvertedAmount == null))),
                Times.Once);
        }

        [Fact]
        public async Task CalculateSelectedProductPricesAsync_ShouldReturnError_WhenPricingApiFails()
        {
            // Arrange
            var selectedProducts = new List<SelectedProductDto>
            {
                new SelectedProductDto { ProductCode = "TEST1", Amount = 2.5m, Unit = "m2" }
            };
            var currentProductCode = "TEST1";

            // Setup API mocks with pricing failure
            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductPricing>>.Error("Pricing API failed"));

            // Act
            var (result, validation) = await _service.CalculateSelectedProductPricesAsync(selectedProducts, currentProductCode);

            // Assert
            Assert.Null(result);
            Assert.True(validation.HasErrors);
            Assert.Contains("Pricing API failed", validation.GetErrors());
        }

        [Fact]
        public async Task CalculateSelectedProductPricesAsync_ShouldReturnError_WhenUnitsApiFails()
        {
            // Arrange
            var selectedProducts = new List<SelectedProductDto>
            {
                new SelectedProductDto { ProductCode = "TEST1", Amount = 2.5m, Unit = "m2" }
            };
            var currentProductCode = "TEST1";

            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceNoVatSales = 100.0m, Vat = 21.0m }
            };

            // Setup API mocks with units failure
            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductPricing>>.Success(pricingData));

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnit>>.Error("Units API failed"));

            // Act
            var (result, validation) = await _service.CalculateSelectedProductPricesAsync(selectedProducts, currentProductCode);

            // Assert
            Assert.Null(result);
            Assert.True(validation.HasErrors);
            Assert.Contains("Units API failed", validation.GetErrors());
        }

        [Fact]
        public async Task CalculateSelectedProductPricesAsync_ShouldHandleConfiguratorProductType()
        {
            // Arrange - Test the generic method with ConfiguratorProduct type
            var selectedProducts = new List<SelectedProductDto>
            {
                new SelectedProductDto { ProductCode = "TEST1", Amount = 2.5m, Unit = "m2" }
            };
            var currentProductCode = "TEST1";

            var pricingData = new List<EshopProductPricing>
            {
                new EshopProductPricing { PriceNoVatSales = 100.0m, Vat = 21.0m }
            };

            var unitsData = new List<EshopProductUnit>
            {
                new EshopProductUnit { UnitSales = "m2", UnitPackage = "m2", UnitsInPackage = 1.0m }
            };

            var conversions = new List<EshopProductUnitConversion>
            {
                new EshopProductUnitConversion { ProductCode = "TEST1", QuantityOutput = 2.5m }
            };

            // Setup API mocks
            _eshopApiServiceMock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductPricing>>.Success(pricingData));

            _eshopApiServiceMock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnit>>.Success(unitsData));

            _eshopApiServiceMock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync(ApiResult<List<EshopProductUnitConversion>>.Success(conversions));

            // Setup mapping service mock
            _mappingServiceMock.Setup(x => x.MapToEntity(It.IsAny<SelectedProductDto>()))
                .Returns((SelectedProductDto dto) => new SelectedProduct
                {
                    ProductCode = dto.ProductCode,
                    Amount = dto.Amount,
                    Unit = dto.Unit
                });

            var priceResponse = new ProductPriceCalculationResponse
            {
                PriceVat = 121.0m,
                TotalPriceNoVat = 100.0m,
                TotalPriceVat = 121.0m,
                TotalVat = 21.0m
            };

            _productPriceServiceMock.Setup(x => x.CalculateSelectedProductPrices(
                It.IsAny<List<SelectedProduct>>(),
                currentProductCode,
                It.IsAny<IEnumerable<ProductPriceData>>()))
                .Returns((priceResponse, new ValidationResult()));

            // Act
            var (result, validation) = await _service.CalculateSelectedProductPricesAsync(selectedProducts, currentProductCode);

            // Assert
            Assert.NotNull(result);
            Assert.False(validation.HasErrors);

            // Verify that unit conversion was called with correct parameters for the product type
            _eshopApiServiceMock.Verify(x => x.GetProductUnitConversionsAsync(
                It.Is<List<string>>(codes => codes.Contains("TEST1")),
                It.Is<List<decimal>>(quantities => quantities.Contains(2.5m)),
                It.Is<List<string>>(inputs => inputs.Contains("m2")),
                It.Is<List<string>>(outputs => outputs.Contains("m2"))),
                Times.Once);
        }
        
        private void SetupAggregationMappingService()
        {
            // Setup aggregation mapping service - use real implementation for testing
            _aggregationMappingServiceMock.Setup(x => x.CreateAggregatedProduct(It.IsAny<List<ConfiguratorProduct>>(), It.IsAny<int>(), It.IsAny<EshopProductUnit>()))
                .Returns<List<ConfiguratorProduct>, int, EshopProductUnit>((products, order, unitData) => 
                {
                    var service = new ProductAggregationMappingService();
                    return service.CreateAggregatedProduct(products, order, unitData);
                });
                
            _aggregationMappingServiceMock.Setup(x => x.CreateProductAggregationData(It.IsAny<ProductForAggregationDto>(), It.IsAny<ChildProductStateDto>(), It.IsAny<int>()))
                .Returns<ProductForAggregationDto, ChildProductStateDto, int>((product, childState, id) => 
                {
                    var service = new ProductAggregationMappingService();
                    return service.CreateProductAggregationData(product, childState, id);
                });
                
            _aggregationMappingServiceMock.Setup(x => x.ConvertToSalesUnits(It.IsAny<ConfiguratorProduct>(), It.IsAny<EshopProductUnitConversion>(), It.IsAny<EshopProductUnit>()))
                .Returns<ConfiguratorProduct, EshopProductUnitConversion, EshopProductUnit>((product, conversion, unitInfo) => 
                {
                    var service = new ProductAggregationMappingService();
                    return service.ConvertToSalesUnits(product, conversion, unitInfo);
                });
        }
    }
}