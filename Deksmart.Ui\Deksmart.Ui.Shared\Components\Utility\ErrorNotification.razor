@using Deksmart.Ui.Shared.Services
@using Microsoft.Extensions.Logging
@inject NotificationService NotificationService
@inject ILogger<ErrorNotification> <PERSON><PERSON>
@implements IDisposable

<div class="notification-container">
    @if (NotificationService.Notifications.Count > 0)
    {
        @foreach (var notification in NotificationService.Notifications)
        {
            <div class="notification @notification.Type.ToString().ToLower()">
                <div class="notification-content">
                    <span class="notification-message">@notification.Message</span>
                    <button class="notification-close" @onclick="() => NotificationService.RemoveNotification(notification)">×</button>
                </div>
            </div>
        }
    }
</div>

@code {
    protected override void OnInitialized()
    {
        Logger.LogInformation("ErrorNotification component initialized");
        NotificationService.OnChange += OnNotificationChanged;
    }

    private void OnNotificationChanged()
    {
        Logger.LogDebug("Notification changed event received, notification count: {Count}",
            NotificationService.Notifications.Count);
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        Logger.LogInformation("ErrorNotification component disposing");
        NotificationService.OnChange -= OnNotificationChanged;
    }
}