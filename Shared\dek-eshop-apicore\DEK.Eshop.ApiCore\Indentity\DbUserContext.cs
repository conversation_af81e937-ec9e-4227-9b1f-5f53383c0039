using Microsoft.EntityFrameworkCore;
using DEK.Eshop.ApiCore.Database.Entity;

namespace DEK.Eshop.ApiCore.Indentity;

public class DbUserContext : DbContext
{
    public DbSet<DbUser> DbUser { get; set; }

    public DbUserContext(DbContextOptions<DbUserContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<DbUser>(builder => {
            // convert byte to int
            builder.Property(e => e.PriceLevelRental)
                .HasConversion<byte>();

            // convert byte to bool
            builder.Property(e => e.IsB2B)
                .HasConversion<byte>();
        });
    }
}
