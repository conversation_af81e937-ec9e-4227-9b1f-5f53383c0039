﻿@page "/deksmart/{id:int}"
@using Deksmart.Ui.Shared.Components
@using Deksmart.Ui.Shared.Components.Configurator
@using Deksmart.Ui.Shared.Resources

@if (ConfiguratorGridService.ConfiguratorWrapper == null)
{
    <LoadingSpinner IsOverlay="true" />
}
else if (ConfiguratorGridService.ConfiguratorWrapper.IsComposite)
{
    <TabbedConfigurator MainConfiguratorGridService="ConfiguratorGridService"/>
}
else
{
    <ConfiguratorGridBase Id="Id" ConfiguratorGridService="ConfiguratorGridService"/>
}

<ConfiguratorActions ConfiguratorGridService="ConfiguratorGridService" />
