using Deksmart.Domain.Entity.Business;

namespace Deksmart.Domain.Service.Base
{
    /// <summary>
    /// Base implementation for services that perform validation
    /// </summary>
    public class ValidationService : IValidationService
    {
        /// <summary>
        /// Creates a new ValidationResult instance
        /// </summary>
        /// <returns>A new ValidationResult instance</returns>
        public ValidationResult CreateValidation()
        {
            return new ValidationResult();
        }

        /// <summary>
        /// Creates a ValidationResult with a single error
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>A ValidationResult with the specified error</returns>
        public ValidationResult CreateValidationWithError(string errorMessage)
        {
            var validation = new ValidationResult();
            validation.AddError(errorMessage);
            return validation;
        }

        /// <summary>
        /// Creates a ValidationResult with multiple errors
        /// </summary>
        /// <param name="errorMessages">The error messages</param>
        /// <returns>A ValidationResult with the specified errors</returns>
        public ValidationResult CreateValidationWithErrors(IEnumerable<string> errorMessages)
        {
            var validation = new ValidationResult();
            validation.AddErrors(errorMessages);
            return validation;
        }
    }
}
