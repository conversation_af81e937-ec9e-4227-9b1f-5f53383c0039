namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing an option or selectable value for configurator fields that support multiple choices (e.g., selectbox, tile, radiobutton).
    /// Used to transfer, render, and validate selectable options, including display name, order, numeric value, and optional image.
    /// Supports dynamic UI generation and user selection workflows by encapsulating all relevant option data for each field.
    /// </summary>
    [Serializable]
    public class ConfiguratorFieldValueDto
    {
        public int Id { get; set; }

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public string? Image { get; set; }

        public int Order { get; set; }

        public decimal NumericValue { get; set; }
    }
}
