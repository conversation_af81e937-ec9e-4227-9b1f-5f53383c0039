using Deksmart.Domain.Entity.Business;

namespace Deksmart.Application.Service.Base
{
    /// <summary>
    /// Base interface for application services that provides standardized result types
    /// </summary>
    public interface IApplicationService
    {
        /// <summary>
        /// Creates a successful result with data
        /// </summary>
        /// <typeparam name="T">The type of data</typeparam>
        /// <param name="data">The data to return</param>
        /// <returns>A tuple with the data and an empty validation result</returns>
        (T data, ValidationResult validation) Success<T>(T data);

        /// <summary>
        /// Creates a failed result with validation errors
        /// </summary>
        /// <typeparam name="T">The type of data that would have been returned</typeparam>
        /// <param name="validation">The validation result with errors</param>
        /// <returns>A tuple with default data and the validation result</returns>
        (T? data, ValidationResult validation) Failure<T>(ValidationResult validation);

        /// <summary>
        /// Creates a failed result with a single error message
        /// </summary>
        /// <typeparam name="T">The type of data that would have been returned</typeparam>
        /// <param name="errorMessage">The error message</param>
        /// <returns>A tuple with default data and a validation result containing the error</returns>
        (T? data, ValidationResult validation) Failure<T>(string errorMessage);
    }
}
