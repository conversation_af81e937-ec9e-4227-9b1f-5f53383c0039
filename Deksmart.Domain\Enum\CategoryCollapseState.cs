namespace Deksmart.Domain.Enum
{
    /// <summary>
    /// Represents the collapse/expand state of a configurator field category.
    /// </summary>
    public enum CategoryCollapseState : short
    {
        /// <summary>
        /// The category cannot be collapsed or expanded.
        /// </summary>
        NotCollapsible = 0,
        /// <summary>
        /// The category is expanded (visible).
        /// </summary>
        Expanded = 1,
        /// <summary>
        /// The category is collapsed (hidden).
        /// </summary>
        Collapsed = 2
    }
} 