// Custom JavaScript for the Deksmart application

// Handle component errors
window.handleComponentError = function (error) {
    console.error('Component error:', error);

    // Log the error to the console with stack trace
    if (error && error.stack) {
        console.error('Error stack:', error.stack);
    }

    // Show the error UI
    const errorUI = document.getElementById('blazor-error-ui');
    if (errorUI) {
        errorUI.style.display = 'block';
    }

    return false; // Return false to show the default error UI
};

// Helper function to scroll to a specific element
window.scrollToElement = function (elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        return true;
    }
    return false;
};

// Helper function to scroll to a specific element with offset for the header
window.scrollToElementWithHeaderOffset = function (elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        // Get the current scroll position
        const currentScrollPosition = window.pageYOffset;

        // Get the element's position relative to the document
        const elementRect = element.getBoundingClientRect();
        const absoluteElementTop = elementRect.top + currentScrollPosition;

        // Use a large fixed offset (90px) to ensure the category is fully visible
        // This is more reliable than trying to calculate the exact header height
        const fixedOffset = 90;

        // Calculate the new scroll position
        const scrollPosition = absoluteElementTop - fixedOffset;

        // Scroll to the position
        window.scrollTo({
            top: scrollPosition,
            behavior: 'smooth'
        });

        // For debugging
        console.log('Scrolling to element:', elementId);
        console.log('Element top position:', absoluteElementTop);
        console.log('Scroll position:', scrollPosition);

        return true;
    }
    return false;
};

// Helper function to get the current scroll position
window.getScrollPosition = function () {
    return window.scrollY;
};

// Helper function to set the scroll position
window.setScrollPosition = function (position) {
    window.scrollTo(0, position);
    return true;
};

// Helper function to debug component associations
window.debugComponentAssociations = function () {
    console.log('Debugging component associations...');

    // Log all elements with Blazor-related attributes
    // We need to use a valid CSS selector - look for elements with attributes that start with _bl_
    const components = Array.from(document.querySelectorAll('*')).filter(el => {
        for (let attr of el.attributes) {
            if (attr.name.startsWith('_bl_')) {
                return true;
            }
        }
        return false;
    });
    console.log('Found ' + components.length + ' Blazor components');

    // Log details about each component
    if (components.length > 0) {
        console.log('Blazor component details:');
        components.forEach((el, index) => {
            const attrs = Array.from(el.attributes)
                .filter(attr => attr.name.startsWith('_bl_'))
                .map(attr => `${attr.name}="${attr.value}"`);
            console.log(`Component ${index + 1}: ${el.tagName} - ${attrs.join(', ')}`);
        });
    }

    // Log all elements with id attribute
    const elementsWithId = document.querySelectorAll('[id]');
    console.log('Found ' + elementsWithId.length + ' elements with ID');

    return true;
};

// Helper function to check if Blazor is already started
window.isBlazorStarted = function () {
    return window.Blazor && (window.Blazor._started === true);
};

// Function to download PDF files
window.downloadPdf = function (bytes, filename) {
    const blob = new Blob([bytes], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
};

// Add this function to adjust the sidebar top offset based on header height
window.adjustSidebarTop = function () {
    var header = document.getElementById('dek-header');
    var sidebar = document.getElementById('sidebar');
    var menuToggle = document.querySelector('.menu-toggle');
    if (header && sidebar && menuToggle) {
        var headerHeight = header.offsetHeight;
        var menuToggleHeight = menuToggle.offsetHeight;
        var totalHeight = headerHeight + menuToggleHeight;
        sidebar.style.setProperty('--sidebar-top', totalHeight + 'px');
        document.documentElement.style.setProperty('--header-total-height', totalHeight + 'px');
    } else if (header && sidebar) {
        sidebar.style.setProperty('--sidebar-top', header.offsetHeight + 'px');
        document.documentElement.style.setProperty('--header-total-height', header.offsetHeight + 'px');
    }
};

// Add event listener to update sidebar position on window resize
window.addEventListener('resize', window.adjustSidebarTop);

// Function to fetch DEK header and megamenu
window.fetchDekHeader = function () {
    try {
        // Základní URL pro proxy endpoint
        const path = '/api/proxy/eshop-header';
        const host = window.location.host === 'localhost:7209' ? 'https://localhost:7276' : ''

        fetch(host + path, {
            method: 'GET',
            headers: {
                'Accept': 'text/html'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch eshop header');
            }
            return response.text();
        })
        .then(html => {
            // Get the target element in our application
            const targetHeader = document.getElementById('dek-header');
            
            // Only proceed if we found the target element
            if (targetHeader && html) {
                // Log the HTML content for debugging
                console.log('Received HTML length:', html.length);
                
                // Set the extracted HTML directly
                targetHeader.innerHTML = html;
                console.log('Eshop header and megamenu injected successfully');
                
                // Fix any broken script tags or other elements
                const scripts = targetHeader.querySelectorAll('script');
                scripts.forEach(oldScript => {
                    if (oldScript.src) {
                        // Pro externí skripty vytvoříme a nahradíme novou instancí
                        const newScript = document.createElement('script');
                        Array.from(oldScript.attributes).forEach(attr => {
                            newScript.setAttribute(attr.name, attr.value);
                        });
                        oldScript.parentNode.replaceChild(newScript, oldScript);
                    }
                });
                // Adjust sidebar after header is injected
                window.adjustSidebarTop();
                // Update sidebar position with new header height
                window.updateSidebarPosition();
            } else {
                console.error('Could not find target element in app or header content was empty');
            }
        })
        .catch(error => {
            console.error('Error fetching DEK header:', error);
        });
    } catch (error) {
        console.error('Exception in fetchDekHeader:', error);
    }
};

// Helper function to scroll to a specific element with a fixed offset
window.scrollToElementWithFixedOffset = function (elementId, offsetPixels) {
    try {
        console.log('scrollToElementWithFixedOffset called for:', elementId);

        const element = document.getElementById(elementId);
        if (!element) {
            console.error('Element not found:', elementId);
            return false;
        }

        // Get the element's position
        const rect = element.getBoundingClientRect();
        const elementTop = rect.top + window.pageYOffset;

        // Use the provided offset or default to 80px
        const offset = offsetPixels || 80;

        // Calculate the scroll position
        const scrollPosition = elementTop - offset;

        console.log('Element found, scrolling to position:', scrollPosition);

        // Scroll to the position
        window.scrollTo({
            top: scrollPosition,
            behavior: 'smooth'
        });

        return true;
    } catch (error) {
        console.error('Error in scrollToElementWithFixedOffset:', error);
        return false;
    }
};

// Function to calculate and update dynamic header height for fixed sidebar positioning
window.updateSidebarPosition = function () {
    try {
        let visibleHeaderHeight = 0;
        
        // Get the main DEK header (includes webstore header content)
        const dekHeader = document.getElementById('dek-header');
        if (dekHeader) {
            const rect = dekHeader.getBoundingClientRect();
            // Only count header height if it's visible (not scrolled out of view)
            if (rect.bottom > 0) {
                visibleHeaderHeight += Math.max(0, rect.bottom);
            }
        }
        
        // Get the menu toggle bar
        const menuToggle = document.querySelector('.menu-toggle');
        if (menuToggle) {
            const rect = menuToggle.getBoundingClientRect();
            // Only count menu toggle height if it's visible
            if (rect.bottom > 0) {
                visibleHeaderHeight += Math.max(0, rect.bottom - Math.max(0, rect.top));
            }
        }
        
        // Round to avoid micro-adjustments that cause jitter
        visibleHeaderHeight = Math.round(visibleHeaderHeight);
        
        // Only update if there's a meaningful change (avoid micro-updates)
        const currentHeight = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--dynamic-header-height')) || 0;
        if (Math.abs(currentHeight - visibleHeaderHeight) > 2) {
            // Set CSS custom property for sidebar positioning
            document.documentElement.style.setProperty('--dynamic-header-height', visibleHeaderHeight + 'px');
            console.log('Updated sidebar position - visible header height:', visibleHeaderHeight + 'px');
        }
        
        return visibleHeaderHeight;
    } catch (error) {
        console.error('Error in updateSidebarPosition:', error);
        return 0;
    }
};

// Throttle function to limit scroll event frequency
window.throttle = function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Update sidebar position on window resize and scroll (throttled for performance)
window.addEventListener('resize', window.updateSidebarPosition);
window.addEventListener('scroll', window.throttle(window.updateSidebarPosition, 50)); // ~20fps for smoother experience

// Function to update sidebar position and then show it
window.showSidebarWithPosition = function () {
    window.updateSidebarPosition();
    return true;
};
