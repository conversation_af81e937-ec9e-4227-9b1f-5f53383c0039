using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorProductRepository : IntIdDaoBase<ConfiguratorProduct>, IConfiguratorProductRepository
    {
        public ConfiguratorProductRepository(ConfiguratorContext context) : base(context)
        {
        }
    }
}
