﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ComponentTypeDoesNotExist" xml:space="preserve">
    <value>Riadok {0}: Neplatný typ komponentu: {1}</value>
  </data>
  <data name="CollapseStateDoesNotExist" xml:space="preserve">
    <value>Riadok {0}: Neplatný stav zbalenia: {1}</value>
  </data>
  <data name="FieldPositionDoesNotExist" xml:space="preserve">
    <value>Riadok {0}: Neplatná pozícia poľa: {1}</value>
  </data>
  <data name="ExpressionDoesNotMatch" xml:space="preserve">
    <value>Výraz {0} nie je možné analyzovať, môže chýbať ident alebo nesprávna syntax.</value>
  </data>
  <data name="ExpressionValueCannotBeEmpty" xml:space="preserve">
    <value>Riadok {0}: Výraz nemôže byť vo výrazovom poli {1} prázdny.</value>
  </data>
  <data name="ImportConfiguratorError" xml:space="preserve">
    <value>Prvý list definujúci kalkulačku má nesprávny formát</value>
  </data>
  <data name="ImportProductsError" xml:space="preserve">
    <value>Tretí hárok definujúci produkty je nesprávne naformátovaný alebo chýba.</value>
  </data>
  <data name="IsNotBoolean" xml:space="preserve">
    <value>Riadok {0}: Hodnota {1} by mala byť boolovská.</value>
  </data>
  <data name="IsNotImageUrl" xml:space="preserve">
    <value>Riadok {0}: Hodnota {1} by mala byť adresa URL obrázka.</value>
  </data>
  <data name="IsNotInteger" xml:space="preserve">
    <value>Riadok {0}: Hodnota {1} by mala byť celé číslo.</value>
  </data>
  <data name="IsNotNumeric" xml:space="preserve">
    <value>Riadok {0}: Hodnota {1} by mala byť číselná.</value>
  </data>
  <data name="MarkdownConversionError" xml:space="preserve">
    <value>Riadok {0}: Markdown {1} nemožno previesť, uistite sa, že má správnu syntax.</value>
  </data>
  <data name="MissingAmountForProduct" xml:space="preserve">
    <value>Riadok {0}: Chýba suma za produkt a nedá sa nastaviť z predchádzajúceho produktu.</value>
  </data>
  <data name="MissingVisibilityForProduct" xml:space="preserve">
    <value>Riadok {0}: Viditeľnosť produktu chýba a nedá sa nastaviť z predchádzajúceho produktu.</value>
  </data>
  <data name="FieldValueDoesNotExist" xml:space="preserve">
    <value>FieldId: '{0}' nemá žiadnu hodnotu: {1}</value>
  </data>
  <data name="FieldExpressionDoesNotMatch" xml:space="preserve">
    <value>Expression {0} nemožno analyzovať, môže chýbať ident, nesprávna syntax alebo odkazuje na iný výrazové pole, nie je povolené žiadne vnorenie.</value>
  </data>
  <data name="ImportFieldsError" xml:space="preserve">
    <value>Druhý list definujúci pole má nesprávny formát alebo chýba</value>
  </data>
  <data name="MissingFieldForCategory" xml:space="preserve">
    <value>Riadok {0}: Kategória musí mať aspoň jedno pole.</value>
  </data>
  <data name="FieldValueOutOfRange" xml:space="preserve">
    <value>Pole id {0} hodnota {1} je mimo rozsah {2}-{3}</value>
  </data>
  <data name="ConfiguratorDoesNotExist" xml:space="preserve">
    <value>Kalkulátor s id {0} nenájdený.</value>
  </data>
  <data name="InvalidProductUnit" xml:space="preserve">
    <value>Produkt {0} má neplatnú jednotku '{1}'. Očakáva sa buď '{2}' alebo '{3}'</value>
  </data>
  <data name="NoEnrichmentDataFound" xml:space="preserve">
    <value>Pre produkt {0} neboli nájdené žiadne obohacujúce dáta</value>
  </data>
  <data name="ProductNotFoundInSelection" xml:space="preserve">
    <value>Produkt {0} nebol nájdený vo vybraných produktoch</value>
  </data>
  <data name="UnitsError" xml:space="preserve">
    <value>Chyba jednotiek: {0}</value>
  </data>
  <data name="InvalidProductAmount" xml:space="preserve">
    <value>Produkt {0} má neplatné množstvo: {1}</value>
  </data>
  <data name="CheckboxYesValue" xml:space="preserve">
    <value>Áno</value>
  </data>
  <data name="CheckboxNoValue" xml:space="preserve">
    <value>Nie</value>
  </data>
  <data name="IdentTooLong" xml:space="preserve">
    <value>Identifikátor '{0}' je príliš dlhý. Maximálna dĺžka je 10 znakov.</value>
  </data>
  <data name="IdentRequiredWhenTitlePresent" xml:space="preserve">
    <value>Pole s názvom '{0}' nemá identifikátor.</value>
  </data>
  <data name="ConfiguratorTitleRequired" xml:space="preserve">
    <value>Názov konfigurátora je povinný.</value>
  </data>
  <data name="ImportFieldsFirstCellError" xml:space="preserve">
    <value>Prvá bunka v prvom dátovom riadku (za hlavičkou) listu polí musí byť vyplnená.</value>
  </data>
  <data name="ImportProductsFirstCellError" xml:space="preserve">
    <value>Prvá bunka v prvom dátovom riadku (za hlavičkou) listu produktov musí byť vyplnená.</value>
  </data>
  <data name="ImportFieldsTitleRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je vyplnený identifikátor, musí byť vyplnený aj názov poľa.</value>
  </data>
  <data name="ImportFieldsComponentTypeRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je vyplnený identifikátor, musí byť vyplnený aj typ komponentu.</value>
  </data>
  <data name="ImportFieldsValueRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je typ poľa SingleChoice, Tile alebo Selectbox, musí byť definovaná aspoň jedna hodnota.</value>
  </data>
  <data name="ImportFieldsExpressionRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je typ poľa Expression, musí byť definovaný výraz.</value>
  </data>
  <data name="ImportFieldsProductRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je typ poľa Product, musí byť definovaný produkt.</value>
  </data>
  <data name="ImportFieldsNumericValueRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je vyplnený názov hodnoty, musí byť vyplnená aj číselná hodnota.</value>
  </data>
  <data name="ImportFieldsValueOrderRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je vyplnený názov hodnoty, musí byť vyplnené aj poradie hodnoty.</value>
  </data>
  <data name="MissingProductForComposition" xml:space="preserve">
    <value>Riadok {0}: Skladba musí mať aspoň jeden produkt.</value>
  </data>
  <data name="ImportCompositionsOrderRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je definovaná skladba, musí byť vyplnené poradie.</value>
  </data>
  <data name="ImportProductNameRequired" xml:space="preserve">
    <value>Riadok {0}: Názov produktu musí byť vyplnený.</value>
  </data>
  <data name="ImportProductCodeRequired" xml:space="preserve">
    <value>Riadok {0}: Kód produktu musí byť vyplnený.</value>
  </data>
  <data name="ImportProductOrderRequired" xml:space="preserve">
    <value>Riadok {0}: Poradie produktu musí byť vyplnené.</value>
  </data>
  <data name="ImportProductVolumeRequired" xml:space="preserve">
    <value>Riadok {0}: Objem produktu musí byť vyplnený.</value>
  </data>
  <data name="ImportProductUnitRequired" xml:space="preserve">
    <value>Riadok {0}: Jednotka produktu musí byť vyplnená.</value>
  </data>
  <data name="ImportCategoryOrderRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je vyplnená kategória, musí byť vyplnené aj poradie.</value>
  </data>
  <data name="ImportFieldOrderRequired" xml:space="preserve">
    <value>Riadok {0}: Ak je vyplnené pole, musí byť vyplnené aj poradie.</value>
  </data>
  <data name="CalculatorExpressionEmpty" xml:space="preserve">
    <value>Výraz nemôže byť prázdny alebo null</value>
  </data>
  <data name="CalculatorExpressionEvaluatedNull" xml:space="preserve">
    <value>Výraz '{0}' sa vyhodnotil ako null</value>
  </data>
  <data name="CalculatorResultParseError" xml:space="preserve">
    <value>Nedá sa previesť výsledok výrazu '{0}' na číslo</value>
  </data>
  <data name="CalculatorEvaluationError" xml:space="preserve">
    <value>Chyba pri vyhodnocovaní výrazu: {0}</value>
  </data>
  <data name="CalculatorInvalidArgument" xml:space="preserve">
    <value>Neplatný výraz alebo parametre: {0}</value>
  </data>
  <data name="CalculatorUnexpectedError" xml:space="preserve">
    <value>Neočakávaná chyba pri výpočte: {0}</value>
  </data>
</root>