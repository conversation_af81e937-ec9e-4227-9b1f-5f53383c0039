using Deksmart.Ui.Shared.Services;

namespace Deksmart.Ui.Shared.Factory
{
    /// <summary>
    /// Factory for creating instances of ConfiguratorGridService.
    /// </summary>
    public interface IConfiguratorGridServiceFactory
    {
        /// <summary>
        /// Creates a new instance of ConfiguratorGridService.
        /// </summary>
        /// <returns>A new ConfiguratorGridService.</returns>
        IConfiguratorGridService CreateService();
    }
} 