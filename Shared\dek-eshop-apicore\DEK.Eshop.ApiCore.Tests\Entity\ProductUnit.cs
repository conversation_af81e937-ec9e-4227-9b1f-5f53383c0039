using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace DEK.Eshop.ApiCore.Tests.Entity;

[Keyless]
public class ProductUnit
{
    [Column("code")]
    public string Code { get; set; } = null!;

    [Column("unitSales")]
    public string UnitSales { get; set; } = null!;

    [Column("unitPackage")]
    public string UnitPackage { get; set; } = null!;

    [Column("isPackagePrimary")]
    public bool IsPackagePrimary { get; set; }
}
