@namespace Deksmart.Ui.Shared.Components

<div class="loading-container @(Size?.ToLower() ?? "") @(IsOverlay ? "overlay" : "")">
    <svg class="loading-spinner">
        <circle r="40%" cx="50%" cy="50%" />
        <circle r="40%" cx="50%" cy="50%" />
    </svg>
    @if (ShowText)
    {
        <div class="loading-spinner-text">Loading</div>
    }
</div>

@code {
    [Parameter]
    public string? Size { get; set; }

    [Parameter]
    public bool IsOverlay { get; set; }

    [Parameter]
    public bool ShowText { get; set; } = true;
} 