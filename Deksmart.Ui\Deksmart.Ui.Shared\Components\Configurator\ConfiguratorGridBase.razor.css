﻿.accordion-item {
    background: white;
    margin-bottom: 0; /* No space between items */
    overflow: hidden;
    border: none;
    position: relative;
    border-bottom: 1px solid #e9e9e9;
}

/* Add a subtle top border to all but the first accordion item for distinction */
.accordion-item + .accordion-item {
    border-top: 1px solid #f0f0f0;
}

.accordion-header {
    padding: 0.75rem 1rem 0.75rem 2.5rem; /* Left padding for checkbox */
    background-color: white; /* White background */
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
    border-left: 4px solid #ccc; /* Neutral gray left border */
    box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* Subtle shadow for distinction */
    user-select: none; /* Prevent text selection when clicking */
}

/* Checkbox indicator for completed categories */
.accordion-header.completed::before {
    content: "✓";
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    background-color: #999; /* Neutral gray instead of green */
    color: white;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
}

.accordion-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0;
    background: none;
    border: none;
    text-align: left;
    font-size: 14px;
    margin-right: 10px;
}

.accordion-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--secondary-color);
    flex-grow: 1;
}

.selected-value-title {
    font-weight: normal;
    font-style: italic;
    margin-left: 5px;
    color: var(--secondary-color);
    opacity: 0.7;
}

.accordion-content {
    padding: 1.5rem 1.25rem;
    background-color: white;
    border-top: none;
    box-shadow: inset 0 5px 5px -5px rgba(0,0,0,0.05);
}

/* Arrow indicator */
.arrow {
    position: absolute;
    right: 1rem;
    display: inline-block;
    transition: transform 0.3s ease;
    color: var(--secondary-color);
    font-size: 0.8rem;
}

.arrow.collapsed {
    transform: rotate(-90deg);
}

/* Error indicator */
.accordion-header.has-error::before {
    content: "!";
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 1.25rem;
    height: 1.25rem;
    background-color: var(--danger-color);
    color: white;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
}

/* Different states for accordion items */
/* Active state - currently open */
.accordion-item.active .accordion-header {
    background-color: white;
    border-left-color: #666; /* Darker gray instead of red */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow instead of red border */
    z-index: 1; /* Ensure active item appears above others */
}

/* Make sure the active category's content has a white background */
.accordion-item.active .accordion-content {
    background-color: white;
}

/* Completed state - user has filled in all required fields */
.accordion-item.completed .accordion-header {
    border-left-color: #999; /* Neutral gray instead of green */
}

/* Error state - validation errors */
.accordion-item.has-error .accordion-header {
    border-left-color: var(--danger-color);
}

/* Disabled state - not yet available */
.accordion-item.disabled .accordion-header {
    background-color: #f8f8f8;
    border-left-color: #ccc;
    opacity: 0.7;
    cursor: not-allowed;
}

/* Hover state for better interaction */
.accordion-header:hover {
    background-color: #f9f9f9;
}

/* Filter Grid */
.filter-grid-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.filter-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.filter-grid-full-width {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Fields and RightFields Layout */
@media (min-width: 768px) {
    .filter-grid-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-lg);
    }

    .filter-grid-full-width {
        width: 100%;
    }

    .right-field {
        width: calc(50% - var(--spacing-lg) / 2);
    }

    .filter-grid:not(.right-field) {
        width: calc(50% - var(--spacing-lg) / 2);
    }
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    .filter-grid-container {
        flex-direction: column;
    }

    .right-field,
    .filter-grid:not(.right-field) {
        width: 100%;
    }
}

/* Category Descriptions */
.category-descriptions {
    display: block;
}

.category-description {
    margin-bottom: 10px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--header-bg);
    color: white;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
}

.page-header h1,
.page-header h2,
.page-header h3,
.page-header h4 {
    color: white;
    margin: 0;
}

.page-header .button,
.page-header .btn-primary {
    background-color: white;
    color: var(--primary-color);
    border-color: white;
}

.page-header .button:hover,
.page-header .btn-primary:hover {
    background-color: var(--light-color);
    color: var(--primary-dark);
    border-color: var(--light-color);
}
