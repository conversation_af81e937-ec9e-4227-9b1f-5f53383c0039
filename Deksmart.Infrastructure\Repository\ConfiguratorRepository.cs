using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorRepository : IntIdDaoBase<Configurator>, IConfiguratorRepository
    {
        public ConfiguratorRepository(ConfiguratorContext context) : base(context)
        {
        }

        /// <inheritdoc/>
        public async Task<bool> DoesConfiguratorExistAsync(int configuratorId)
        {
            return await _context.Configurators.AnyAsync(d => d.Id == configuratorId);
        }

        /// <inheritdoc/>
        public async Task<List<Configurator>> GetAllConfiguratorsAsync()
        {
            return await _context.Configurators.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<List<Configurator>> GetMenuConfiguratorsAsync()
        {
            return await _context.Configurators.Where(c => c.ShowInMenu).ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<Configurator?> GetCompleteConfiguratorAsync(int configuratorId)
        {
            var select = GetCompleteConfiguratorQuery();

            return await GetCompleteConfiguratorQuery().SingleOrDefaultAsync(d => d.Id == configuratorId);
        }

        /// <inheritdoc/>
        public async Task<Configurator?> GetCompleteConfiguratorNoTrackingAsync(int configuratorId)
        {
            var select = GetCompleteConfiguratorQuery();

            return await GetCompleteConfiguratorQuery().AsNoTracking().SingleOrDefaultAsync(d => d.Id == configuratorId);
        }

        /// <inheritdoc/>
        public async Task SaveConfiguratorAsync(Configurator configurator, bool isUpdate)
        {
            SetConfiguratorState(configurator, isUpdate);
            SetCompositionsState(configurator.ConfiguratorCompositions);
            SetFieldCategoriesState(configurator.ConfiguratorFieldCategories);
            SetChildConfiguratorsState(configurator.ChildConfigurators);

            await _context.SaveChangesAsync();
        }

        private IIncludableQueryable<Configurator, Configurator> GetCompleteConfiguratorQuery()
        {
            return _context.Configurators
                .AsSplitQuery()
                .Include(d => d.ConfiguratorFieldCategories)
                    .ThenInclude(d => d.Visibility)
                .Include(d => d.ConfiguratorFieldCategories)
                    .ThenInclude(d => d.ConfiguratorFields)
                        .ThenInclude(d => d.Expression)
                .Include(d => d.ConfiguratorFieldCategories)
                    .ThenInclude(d => d.ConfiguratorFields)
                        .ThenInclude(d => d.Visibility)
                .Include(d => d.ConfiguratorFieldCategories)
                    .ThenInclude(d => d.ConfiguratorFields)
                        .ThenInclude(d => d.Validation)
                .Include(d => d.ConfiguratorFieldCategories)
                    .ThenInclude(d => d.ConfiguratorFields)
                        .ThenInclude(d => d.ConfiguratorFieldValues)
                            .ThenInclude(d => d.Visibility)
                .Include(d => d.ConfiguratorCompositions)
                    .ThenInclude(d => d.Visibility)
                .Include(d => d.ConfiguratorCompositions)
                    .ThenInclude(d => d.ConfiguratorProducts)
                        .ThenInclude(d => d.Visibility)
                .Include(d => d.ConfiguratorCompositions)
                    .ThenInclude(d => d.ConfiguratorProducts)
                        .ThenInclude(d => d.Quantity)
                .Include(d => d.ChildConfigurators)
                    .ThenInclude(d => d.Configurator);
        }

        private void SetChildConfiguratorsState(IEnumerable<ChildConfigurator> childConfigurators)
        {
            foreach (var configurator in childConfigurators)
            {
                SetEntityState(configurator, true);
            }
        }

        private void SetConfiguratorState(Configurator configurator, bool isUpdate)
        {
            _context.Attach(configurator);
            SetEntityState(configurator, !isUpdate);
        }

        private void SetCompositionsState(IEnumerable<ConfiguratorComposition> compositions)
        {
            foreach (var composition in compositions)
            {
                SetEntityState(composition, composition.Id == 0);

                if (composition.Visibility != null)
                    SetEntityState(composition.Visibility, composition.Visibility.Id == 0);

                foreach (var product in composition.ConfiguratorProducts)
                {
                    SetEntityState(product, product.Id == 0);

                    if (product.Quantity != null)
                        SetEntityState(product.Quantity, product.Quantity.Id == 0);

                    if (product.Visibility != null)
                        SetEntityState(product.Visibility, product.Visibility.Id == 0);
                }
            }
        }

        private void SetFieldCategoriesState(IEnumerable<ConfiguratorFieldCategory> fieldCategories)
        {
            foreach (var fieldCategory in fieldCategories)
            {
                SetEntityState(fieldCategory, fieldCategory.Id == 0);

                if (fieldCategory.Visibility != null)
                    SetEntityState(fieldCategory.Visibility, fieldCategory.Visibility.Id == 0);

                foreach (var field in fieldCategory.ConfiguratorFields)
                {
                    SetEntityState(field, field.Id == 0);

                    if (field.Expression != null)
                        SetEntityState(field.Expression, field.Expression.Id == 0);

                    if (field.Visibility != null)
                        SetEntityState(field.Visibility, field.Visibility.Id == 0);

                    if (field.Validation != null)
                    {
                        // Make sure the validation expression is properly tracked
                        SetEntityState(field.Validation, field.Validation.Id == 0);
                    }

                    foreach (var fieldValue in field.ConfiguratorFieldValues)
                    {
                        SetEntityState(fieldValue, fieldValue.Id == 0);

                        if (fieldValue.Visibility != null)
                            SetEntityState(fieldValue.Visibility, fieldValue.Visibility.Id == 0);
                    }
                }
            }
        }

        private void SetEntityState<TEntity>(TEntity entity, bool isNew) where TEntity : class
        {
            _context.Entry(entity).State = isNew ? EntityState.Added : EntityState.Modified;
        }
    }
}
