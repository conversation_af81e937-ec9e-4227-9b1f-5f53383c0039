using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.AspNetCore.Mvc.ModelBinding.Metadata;
using System.Text.Json.Serialization;
using DEK.Eshop.ApiCore.Validation;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Indentity;
using DEK.Eshop.ApiCore.Bootstrap.Module;
using DEK.Eshop.ApiCore.Loging;
using DEK.Eshop.ApiCore.Mail;
using System;
using DEK.Eshop.ApiCore.Cache;

namespace DEK.Eshop.ApiCore.Bootstrap;

public static class CoreBootstrap
{
    /// <summary>
    /// Bootstrap services
    /// </summary>
    public static void AddCoreBootstrap(this IServiceCollection services, WebApplicationBuilder builder, Dictionary<string, ValidationMessage>? validatonMessageIndex = null)
    {
        // Register MailManager
        builder.Services.AddSingleton(new MailManager(builder.Configuration));

        // Setup Logger
        builder.Services.AddSingleton(serviceProvider => {
            var mailAdapter = new MailAdapter(
                builder.Environment,
                builder.Configuration,
                serviceProvider.GetRequiredService<IHttpContextAccessor>(),
                serviceProvider.GetRequiredService<MailManager>()
            );
            var consoleAdapter = new ConsoleAdapter(serviceProvider.GetRequiredService<IHttpContextAccessor>());
            var logger = new Logger();
            logger.SubscribeToUncatchExceptionHandler(mailAdapter);
            logger.SubscribeToUncatchExceptionHandler(consoleAdapter);
            logger.SubscribeToExceptionHandler(mailAdapter);
            logger.SubscribeToExceptionHandler(consoleAdapter);
            logger.SubscribeToHttpAccessHandler(consoleAdapter);
            return logger;
        });

        // Register LogingManager
        builder.Services.AddSingleton<LogingManager>();

        // @todo: remove it. It is replaced by FluentValidation
        // Overload default BadRequest (http status code 400) with custom error messages
        if (validatonMessageIndex is not null) {
            services.AddControllers().ConfigureApiBehaviorOptions(options => {
                options.InvalidModelStateResponseFactory = context => {
                    return new BadRequestObjectResult(new ValidationPayload(context, validatonMessageIndex));
                };
            });
        } else {
            services.AddControllers();
        }

        // camelCase JSONPath v ValidationMessage: https://learn.microsoft.com/en-us/aspnet/core/mvc/models/validation#use-json-property-names-in-validation-errors
        // do not send null values in JSON: https://docs.microsoft.com/en-us/dotnet/standard/serialization/system-text-json-how-to?pivots=dotnet-5-0#ignore-null-values
        services
          .AddControllers(options => {
            options.ModelMetadataDetailsProviders.Add(new SystemTextJsonValidationMetadataProvider()); // camelCase JSONPath v ValidationMessage
          }).AddJsonOptions(options => {
            options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull; // Nevrací null values v JSONu
            options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter()); // Enum value names istead of numbers
        });

        services.AddJwtBootstrap(builder.Configuration);
        services.AddSwaggerBootstrap(builder.Environment, builder.Configuration);
        services.AddSqlProfilerBootstrap(builder.Configuration);
        services.AddCacheBootstrap(builder.Configuration);

        services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddScoped<IConfigService, ConfigService>();
        services.AddScoped<HttpUserFactory>();
        services.AddIndentityBootstrap();
    }

    /// <summary>
    /// Setup Middlewares
    /// </summary>
    public static void UseCoreBootstrap(this WebApplication app)
    {
        //app.UsePathBase("/core");

        // Enable buffering for request body
        app.Use(next => context => {
            context.Request.EnableBuffering();
            return next(context);
        });

        // Enable CORS
        app.UseCorsBootstrap();

        // Enable JWT
        app.UseJwtBootstrap();

        // Enable Swagger
        app.UseSwaggerBootstrap(app.Environment);

        // Overload default Exception handler
        app.UseExceptionHandlerBootstrap();

        // Enable Profiler
        app.UseSqlProfilerBootstrap();

        app.UseHttpLoggerBootstrap();

        app.UseAuthorization();

        app.MapControllers();
    }
}
