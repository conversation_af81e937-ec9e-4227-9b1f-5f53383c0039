using Deksmart.Infrastructure.Cache;
using Deksmart.Domain.Service;
using Microsoft.Extensions.Logging;
using Deksmart.Application.Resource;
using Deksmart.Shared.Dto;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Enum;
using Deksmart.Infrastructure.Repository.Interface;
using System.Text.RegularExpressions;

namespace Deksmart.Application.Service
{
    public class ConfiguratorProcessingService : IConfiguratorProcessingService
    {
        private readonly IConfiguratorExpressionCalculator _calculator;
        private readonly IFieldValueMatcher _fieldValueMatcher;
        private readonly IConfiguratorRepository _configuratorDao;
        private readonly IConfiguratorCacheManager _cacheManager;
        private readonly IConfiguratorFieldRepository _configuratorFieldDao;
        private readonly IProductProcessingService _productProcessingService;
        private readonly ILogger<ConfiguratorProcessingService> _logger;

        public ConfiguratorProcessingService(
            IConfiguratorExpressionCalculator calculator,
            IFieldValueMatcher fieldValueMatcher,
            IConfiguratorRepository configuratorDao,
            IConfiguratorCacheManager cacheManager,
            IConfiguratorFieldRepository configuratorFieldDao,
            IProductProcessingService productProcessingService,
            ILogger<ConfiguratorProcessingService> logger)
        {
            _calculator = calculator;
            _fieldValueMatcher = fieldValueMatcher;
            _configuratorDao = configuratorDao;
            _cacheManager = cacheManager;
            _configuratorFieldDao = configuratorFieldDao;
            _productProcessingService = productProcessingService;
            _logger = logger;
        }

        public async Task<(Configurator? configurator, ValidationResult validation)> ProcessConfiguratorAsync(
            int configuratorId, 
            IEnumerable<ClientFieldValueDto> clientFieldValues, 
            List<ClientProductValueDto>? selectedProducts, 
            List<CategoryStateDto>? categoryStates = null)
        {
            var validation = new ValidationResult();

            // get entity
            var configurator = await _cacheManager.GetOrAddAsync(configuratorId, _configuratorDao.GetCompleteConfiguratorNoTrackingAsync);

            var matchedFieldsWithValue = await MatchParametersAsync(clientFieldValues, configurator!.Id, validation);

            if (validation.HasErrors)
                return (null, validation);

            _calculator.CalculateAndFilterFields(configurator, matchedFieldsWithValue);

            _calculator.FilterProducts(configurator, matchedFieldsWithValue);

            _productProcessingService.SelectProducts(configurator, selectedProducts);

            // Apply category states from the configurator state
            if (categoryStates != null && categoryStates.Count > 0)
            {
                ApplyCategoryStates(configurator, categoryStates);
            }

            await _productProcessingService.SetProductFieldsAsync(configurator, validation);
            await _productProcessingService.CalculatePricesForProductsAsync(configurator, validation);
            ValidateFields(configurator, matchedFieldsWithValue);
            _productProcessingService.ValidateProducts(configurator);

            return (configurator, validation);
        }

        public void ApplyCategoryStates(Configurator configurator, List<CategoryStateDto> categoryStates)
        {
            // Create a dictionary for efficient lookup
            var categoryStateDict = categoryStates.ToDictionary(cs => cs.CategoryId, cs => cs.CollapseState);

            // Update the CollapseState property for each category
            foreach (var category in configurator.ConfiguratorFieldCategories)
            {
                if (categoryStateDict.TryGetValue(category.Id, out var collapseStateDto))
                {
                    category.CollapseState = (CategoryCollapseState)collapseStateDto;
                }
            }
        }

        private async Task<IEnumerable<ValueIdent>> MatchParametersAsync(IEnumerable<ClientFieldValueDto> clientFieldValues, int configuratorId, ValidationResult validation)
        {
            var clientFieldValueDir = clientFieldValues.ToDictionary(cf => cf.FieldId, cf => cf.Value);

            var matchedParameters = new List<ValueIdent>();

            // select, match field multiple choice values
            var dbFieldMultipleChoiceValues = await _cacheManager.GetOrAddListAsync(configuratorId, _configuratorFieldDao.GetFieldMultipleChoiceValuesIdForConfiguratorAsync);
            var matchedFieldValues = _fieldValueMatcher.MatchFieldMultipleChoiceValues(clientFieldValueDir, dbFieldMultipleChoiceValues, validation);
            matchedParameters.AddRange(matchedFieldValues);

            // select, match field direct values
            var dbFieldDirectValues = await _cacheManager.GetOrAddListAsync(configuratorId, _configuratorFieldDao.GetFieldDirectValuesForConfiguratorAsync);
            var matchedFields = _fieldValueMatcher.MatchFieldDirectValues(clientFieldValueDir, dbFieldDirectValues, validation);
            matchedParameters.AddRange(matchedFields);

            //match and calculate fields with expression
            var matchedExpressionFields = _fieldValueMatcher.MatchExpressionFields(dbFieldDirectValues, matchedParameters);
            matchedParameters.AddRange(matchedExpressionFields);

            return matchedParameters;
        }

        private bool ValidateFields(Configurator configurator, IEnumerable<ValueIdent> matchedFields)
        {
            // Get all fields with validation expressions
            var fields = configurator.ConfiguratorFieldCategories
                .SelectMany(c => c.ConfiguratorFields)
                .Where(f => f.Validation != null)
                .ToList();

            var isValid = true;

            foreach (var field in fields)
            {
                var result = _calculator.GetValue(field.Validation!.Id, field.Validation.Expression, matchedFields);
                if (result <= 0) // 0 or less means false in our expression system
                {
                    field.ValidationError = field.ValidationMessage
                        ?? string.Format(DeksmartApplicationResource.FieldNotFilledValidation, RemoveHtmlTags(field.Title));
                    isValid = false;
                }
            }

            // Set the IsValid property for configurator
            configurator.IsValid = isValid;

            return isValid;
        }

        private static string RemoveHtmlTags(string? input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return string.Empty;
            }
            return Regex.Replace(input, "<.*?>", string.Empty);
        }
    }
}