using Microsoft.AspNetCore.Components;

namespace Deksmart.Ui.Shared.Components.Utility
{
    public partial class InquiryForm
    {
        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public EventCallback<bool> IsVisibleChanged { get; set; }

        [Parameter]
        public Func<(string? name, string? email, string? phone), Task<bool>> OnSubmit { get; set; } = default!;

        private string? Name { get; set; }
        private string? Email { get; set; }
        private string? Phone { get; set; }
        private bool IsSubmitting { get; set; }

        private bool CanSubmit => !string.IsNullOrWhiteSpace(Name) &&
                                !string.IsNullOrWhiteSpace(Email) &&
                                !string.IsNullOrWhiteSpace(Phone);

        private void OnInputChanged()
        {
            StateHasChanged();
        }

        private async Task Close()
        {
            IsVisible = false;
            await IsVisibleChanged.InvokeAsync(false);
        }

        private async Task Submit()
        {
            try
            {
                IsSubmitting = true;
                StateHasChanged();
                
                var result = await OnSubmit((Name, Email, Phone));
                if (result)
                {
                    await Close();
                }
            }
            finally
            {
                IsSubmitting = false;
                StateHasChanged();
            }
        }
    }
}
