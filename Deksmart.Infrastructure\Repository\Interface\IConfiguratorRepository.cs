﻿using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for accessing and managing <see cref="Configurator"/> entities, including retrieval with related data and persistence operations.
    /// </summary>
    public interface IConfiguratorRepository : IIntIdRepositoryBase<Configurator>
    {
        /// <summary>
        /// Retrieves all configurators from the database.
        /// </summary>
        /// <returns>A list of all <see cref="Configurator"/> entities.</returns>
        Task<List<Configurator>> GetAllConfiguratorsAsync();

        /// <summary>
        /// Retrieves configurators that are marked to be shown in the menu (where ShowInMenu is true).
        /// </summary>
        /// <returns>A list of menu <see cref="Configurator"/> entities.</returns>
        Task<List<Configurator>> GetMenuConfiguratorsAsync();

        /// <summary>
        /// Retrieves a configurator by ID, including all related entities (fields, categories, compositions, etc.).
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <returns>The complete <see cref="Configurator"/> entity, or null if not found.</returns>
        Task<Configurator?> GetCompleteConfiguratorAsync(int configuratorId);

        /// <summary>
        /// Retrieves a configurator by ID, including all related entities, with no tracking for read-only scenarios.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <returns>The complete <see cref="Configurator"/> entity, or null if not found.</returns>
        Task<Configurator?> GetCompleteConfiguratorNoTrackingAsync(int configuratorId);

        /// <summary>
        /// Saves a configurator and all its related entities. Handles both insert and update operations.
        /// </summary>
        /// <param name="configurator">The configurator entity to save.</param>
        /// <param name="isUpdate">True if updating an existing entity; false if inserting a new one.</param>
        Task SaveConfiguratorAsync(Configurator configurator, bool isUpdate);

        /// <summary>
        /// Checks if a configurator with the specified ID exists in the database.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <returns>True if the configurator exists; otherwise, false.</returns>
        Task<bool> DoesConfiguratorExistAsync(int configuratorId);
    }
}
