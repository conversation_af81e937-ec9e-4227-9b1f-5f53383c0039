using System.Data;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Enum;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Service.Base;

namespace Deksmart.Domain.Service
{
    /// <summary>
    /// Provides utility methods for validating imported configurator data, including cell value checks, type conversions, and expression validation.
    /// Methods return validation results or strongly-typed conversion results with validation feedback.
    /// </summary>
    public interface IImportValidationService
    {
        /// <summary>
        /// Validates the structure of the imported configurator data.
        /// </summary>
        /// <param name="result">The imported data as a DataSet.</param>
        /// <returns>A validation result containing any errors found in the data.</returns>
        ValidationResult ValidateImport(DataSet result);
        /// <summary>
        /// Validates that a cell is not empty. The error string must have {0} for the row number and {1} for the value if needed.
        /// </summary>
        /// <param name="v">The value to check.</param>
        /// <param name="error">The error message template. Must include {0} for row number, {1} for value if needed.</param>
        /// <param name="rowNumber">The row number (Excel-style, 1-based).</param>
        /// <returns>A ValidationResult with errors if validation fails.</returns>
        /// <remarks>
        /// The error string must always be formatted with string.Format(error, rowNumber, value, ...).
        /// </remarks>
        ValidationResult ValidateCellEmpty(object? v, string error, int rowNumber);
        ValidationResult ValidateCellEmpty(object? v, string error);
        /// <summary>
        /// Validates that a cell is numeric. The error string must have {0} for the row number and {1} for the value.
        /// </summary>
        /// <param name="value">The value to check.</param>
        /// <param name="error">The error message template. Must include {0} for row number, {1} for value.</param>
        /// <param name="rowNumber">The row number (Excel-style, 1-based).</param>
        /// <returns>A tuple with isValid, numeric value, and ValidationResult.</returns>
        /// <remarks>
        /// The error string must always be formatted with string.Format(error, rowNumber, value).
        /// </remarks>
        (bool isValid, decimal numeric, ValidationResult validation) ValidateCellNumeric(string? value, int rowNumber);
        /// <summary>
        /// Validates that a cell is an integer. The error string must have {0} for the row number and {1} for the value.
        /// </summary>
        /// <param name="value">The value to check.</param>
        /// <param name="error">The error message template. Must include {0} for row number, {1} for value.</param>
        /// <param name="rowNumber">The row number (Excel-style, 1-based).</param>
        /// <returns>A tuple with isValid, integer value, and ValidationResult.</returns>
        /// <remarks>
        /// The error string must always be formatted with string.Format(error, rowNumber, value).
        /// </remarks>
        (bool isValid, int integer, ValidationResult validation) ValidateCellInteger(string? value, int rowNumber);
        (bool isValid, int integer, ValidationResult validation) ValidateCellInteger(string? value, string error);
        /// <summary>
        /// Validates that a cell is a boolean. The error string must have {0} for the row number and {1} for the value.
        /// </summary>
        /// <param name="value">The value to check.</param>
        /// <param name="rowNumber">The row number (Excel-style, 1-based).</param>
        /// <returns>A tuple with isCellNull, boolean value, and ValidationResult.</returns>
        /// <remarks>
        /// The error string must always be formatted with string.Format(error, rowNumber, value).
        /// </remarks>
        (bool isCellNull, bool boolean, ValidationResult validation) ValidateCellBool(string? value, int rowNumber);
        /// <summary>
        /// Validates that a cell is an image URL. The error string must have {0} for the row number and {1} for the value.
        /// </summary>
        /// <param name="value">The value to check.</param>
        /// <param name="rowNumber">The row number (Excel-style, 1-based).</param>
        /// <returns>A tuple with isCellNull, image value, and ValidationResult.</returns>
        /// <remarks>
        /// The error string must always be formatted with string.Format(error, rowNumber, value).
        /// </remarks>
        (bool isCellNull, string? image, ValidationResult validation) ValidateCellImage(string? value, int rowNumber);
        /// <summary>
        /// Validates that a cell is a valid enum value. The error string must have {0} for the row number and {1} for the value.
        /// </summary>
        /// <param name="value">The value to check.</param>
        /// <param name="error">The error message template. Must include {0} for row number, {1} for value.</param>
        /// <param name="rowNumber">The row number (Excel-style, 1-based).</param>
        /// <returns>A tuple with isValid, enum value, and ValidationResult.</returns>
        /// <remarks>
        /// The error string must always be formatted with string.Format(error, rowNumber, value).
        /// </remarks>
        (bool isValid, TEnum enumValue, ValidationResult validation) ValidateCellEnum<TEnum>(string? value, string? error, int rowNumber) where TEnum : struct;
        ValidationResult ValidateExpressionSyntax(Configurator configurator, Dictionary<string, ConfiguratorExpression> expressionCache);
        ValidationResult ValidateExpressionFields(Configurator configurator);
        ValidationResult ValidateExpressionField(string? value, ConfiguratorField? currentField, int rowNumber);
        ValidationResult ValidateIdentMaxLength(string? ident);
        ValidationResult ValidateIdentRequiredWhenTitlePresent(string? ident, string? title);
        /// <summary>
        /// Validates that a value is required if another value is present (not null or empty).
        /// </summary>
        /// <param name="value">The value to check for requiredness.</param>
        /// <param name="otherValue">The value whose presence triggers the requirement.</param>
        /// <param name="error">The error message to use if validation fails.</param>
        /// <returns>A ValidationResult with errors if validation fails.</returns>
        ValidationResult ValidateCellRequiredIfOtherPresent(object? value, object? otherValue, string error);
    }

    public class ImportValidationService : IImportValidationService
    {
        private readonly IValidationService _validationService;

        public ImportValidationService(IValidationService validationService)
        {
            _validationService = validationService;
        }

        public ValidationResult ValidateImport(DataSet result)
        {
            var validation = _validationService.CreateValidation();

            if (!ValidateConfiguratorStructure(result, validation))
                return validation;

            if (!ValidateFieldsStructure(result, validation))
                return validation;

            if (!ValidateProductsStructure(result, validation))
                return validation;

            return validation;
        }

        private bool ValidateConfiguratorStructure(DataSet result, ValidationResult validation)
        {
            var currentTable = result.Tables[0];

            if (currentTable.Rows.Count < 2 || currentTable.Rows[1].ItemArray.Count() < 3)
            {
                validation.AddError(DeksmartDomainResource.ImportConfiguratorError);
                return false;
            }

            return true;
        }

        private bool ValidateFieldsStructure(DataSet result, ValidationResult validation)
        {
            if (result.Tables.Count < 2 || result.Tables[1].Rows.Count < 2 || result.Tables[1].Rows[0].ItemArray.Count() < 22)
            {
                validation.AddError(DeksmartDomainResource.ImportFieldsError);
                return false;
            }

            // Only check the first data row (row 1, after header) first cell is filled
            var fieldsTable = result.Tables[1];
            if (fieldsTable.Rows.Count > 1)
            {
                var firstCell = fieldsTable.Rows[1][0]?.ToString();
                if (string.IsNullOrWhiteSpace(firstCell))
                {
                    validation.AddError(DeksmartDomainResource.ImportFieldsFirstCellError);
                    return false;
                }
            }

            return true;
        }

        private bool ValidateProductsStructure(DataSet result, ValidationResult validation)
        {
            if (result.Tables.Count < 3)
            {
                return true;
            }

            // Check if the table exists and has the correct structure (at least header row)
            if (result.Tables[2].Rows.Count < 1 || result.Tables[2].Rows[0].ItemArray.Count() < 11)
            {
                validation.AddError(DeksmartDomainResource.ImportProductsError);
                return false;
            }

            // Only check the first data row (row 1, after header) first cell is filled
            var productsTable = result.Tables[2];
            if (productsTable.Rows.Count > 1)
            {
                var firstCell = productsTable.Rows[1][0]?.ToString();
                if (string.IsNullOrWhiteSpace(firstCell))
                {
                    validation.AddError(DeksmartDomainResource.ImportProductsFirstCellError);
                    return false;
                }
            }

            // If there's only a header row, that's valid - it means a configurator without products
            return true;
        }

        public ValidationResult ValidateCellEmpty(object? v, string error)
        {
            var validation = _validationService.CreateValidation();
            if (string.IsNullOrWhiteSpace(v?.ToString()))
            {
                validation.AddError(error);
                return validation;
            }

            return validation;
        }

        public ValidationResult ValidateCellEmpty(object? v, string error, int rowNumber)
        {
            return ValidateCellEmpty(v, string.Format(error, rowNumber, v?.ToString()));
        }

        public (bool isValid, decimal numeric, ValidationResult validation) ValidateCellNumeric(string? value, int rowNumber)
        {
            var validation = _validationService.CreateValidation();
            if (string.IsNullOrWhiteSpace(value))
            {
                return (false, 0, validation);
            }
            if (decimal.TryParse(value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.CurrentCulture, out decimal numeric))
            {
                return (true, numeric, validation);
            }
            if (decimal.TryParse(value, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out numeric))
            {
                return (true, numeric, validation);
            }
            var normalizedValue = value.Replace(',', '.').Replace('.', ',');
            if (decimal.TryParse(normalizedValue, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.CurrentCulture, out numeric))
            {
                return (true, numeric, validation);
            }
            validation.AddError(string.Format(DeksmartDomainResource.IsNotNumeric, rowNumber, value));
            return (false, 0, validation);
        }

        public (bool isValid, int integer, ValidationResult validation) ValidateCellInteger(string? value, string error)
        {
            var validation = _validationService.CreateValidation();
            if (string.IsNullOrWhiteSpace(value))
            {
                return (false, 0, validation);
            }
            if (int.TryParse(value, out int integer))
            {
                return (true, integer, validation);
            }

            validation.AddError(error);

            return (false, 0, validation);
        }

        public (bool isValid, int integer, ValidationResult validation) ValidateCellInteger(string? value, int rowNumber)
        {
            return ValidateCellInteger(value, string.Format(DeksmartDomainResource.IsNotInteger, rowNumber, value));
        }

        public (bool isCellNull, bool boolean, ValidationResult validation) ValidateCellBool(string? value, int rowNumber)
        {
            var validation = _validationService.CreateValidation();
            if (string.IsNullOrWhiteSpace(value))
            {
                return (false, false, validation);
            }
            if (value == "1")
                return (true, true, validation);
            if (value == "0")
                return (true, false, validation);
            if (bool.TryParse(value, out bool isBool))
            {
                return (true, isBool, validation);
            }
            validation.AddError(string.Format(DeksmartDomainResource.IsNotBoolean, rowNumber, value));
            return (false, false, validation);
        }

        public (bool isCellNull, string? image, ValidationResult validation) ValidateCellImage(string? value, int rowNumber)
        {
            var validation = _validationService.CreateValidation();
            if (string.IsNullOrWhiteSpace(value))
            {
                return (false, default, validation);
            }
            if (!Uri.TryCreate(value, UriKind.Absolute, out var uriResult) ||
                !(uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps))
            {
                validation.AddError(string.Format(DeksmartDomainResource.IsNotImageUrl, rowNumber, value));
                return (false, default, validation);
            }
            var validImageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp" };
            if (!validImageExtensions.Contains(Path.GetExtension(uriResult.AbsolutePath).ToLower()))
            {
                validation.AddError(string.Format(DeksmartDomainResource.IsNotImageUrl, rowNumber, value));
                return (false, default, validation);
            }
            return (true, value, validation);
        }

        public (bool isValid, TEnum enumValue, ValidationResult validation) ValidateCellEnum<TEnum>(string? value, string? error, int rowNumber)
            where TEnum : struct
        {
            var validation = _validationService.CreateValidation();
            if (string.IsNullOrWhiteSpace(value))
            {
                return (false, default, validation);
            }
            if (System.Enum.TryParse(value, true, out TEnum enumValue))
            {
                return (true, enumValue, validation);
            }
            validation.AddError(string.Format(error ?? string.Empty, rowNumber, value));
            return (false, default, validation);
        }

        public ValidationResult ValidateExpressionSyntax(Configurator configurator, Dictionary<string, ConfiguratorExpression> expressionCache)
        {
            var validation = _validationService.CreateValidation();

            var configuratorExpressions = new List<ConfiguratorExpression?>();

            // Get compositions and their visibilities
            var compositions = configurator.ConfiguratorCompositions.Where(d => !d.IsDeleted);
            configuratorExpressions.AddRange(compositions.Select(d => d.Visibility));

            // Get products and their expressions if there are any
            if (compositions.Any() && compositions.Any(c => c.ConfiguratorProducts.Any(p => !p.IsDeleted)))
            {
                var items = compositions.SelectMany(d => d.ConfiguratorProducts.Where(d => !d.IsDeleted));
                configuratorExpressions.AddRange(items.Select(d => d.Quantity));
                configuratorExpressions.AddRange(items.Select(d => d.Visibility));
            }

            // Get categories and their visibilities
            var categories = configurator.ConfiguratorFieldCategories.Where(d => !d.IsDeleted);
            configuratorExpressions.AddRange(categories.Select(d => d.Visibility));

            // Get fields and their visibilities, expressions, and validations
            var fields = categories.SelectMany(d => d.ConfiguratorFields.Where(d => !d.IsDeleted));
            configuratorExpressions.AddRange(fields.Select(d => d.Visibility));
            configuratorExpressions.AddRange(fields.Select(d => d.Expression));

            // Make sure to include validation expressions
            var validations = fields.Select(d => d.Validation).Where(v => v != null);
            configuratorExpressions.AddRange(validations);

            // Get field values and their visibilities
            var fieldValues = fields.SelectMany(d => d.ConfiguratorFieldValues.Where(d => !d.IsDeleted));
            configuratorExpressions.AddRange(fieldValues.Select(d => d.Visibility));

            var expressions = expressionCache.Select(d => d.Value.Expression).ToList();
            var idents = GetAllIdents(configurator, _ => true);

            ValidateExpressions(expressions, idents, DeksmartDomainResource.ExpressionDoesNotMatch, validation);

            return validation;
        }

        public ValidationResult ValidateExpressionFields(Configurator configurator)
        {
            var validation = _validationService.CreateValidation();

            var allFields = configurator.ConfiguratorFieldCategories
                .Where(d => !d.IsDeleted)
                .SelectMany(d => d.ConfiguratorFields)
                .Where(d => !d.IsDeleted && d.ComponentType == ComponentType.Expression)
                .Select(d => d.Expression!.Expression).ToList();

            //calculated should not reference other calculated, no expression nesting allowed
            var idents = GetAllIdents(configurator, (d) => d.ComponentType != ComponentType.Expression);

            ValidateExpressions(allFields, idents, DeksmartDomainResource.FieldExpressionDoesNotMatch, validation);

            return validation;
        }

        private void ValidateExpressions(List<string> expressions, Dictionary<string, int> idents, string error, ValidationResult validation)
        {
            foreach (var expression in expressions)
            {
                if (string.IsNullOrWhiteSpace(expression))
                    continue;

                try
                {
                    var ncalcExpression = new NCalc.Expression(expression);

                    foreach (var ident in idents)
                    {
                        ncalcExpression.Parameters.Add(ident.Key, ident.Value);
                    }

                    ncalcExpression.Evaluate();
                }
                catch
                {
                    validation.AddError(string.Format(error, expression));
                }
            }
        }

        public ValidationResult ValidateExpressionField(string? value, ConfiguratorField? currentField, int rowNumber)
        {
            var validation = _validationService.CreateValidation();

            if (currentField?.ComponentType == ComponentType.Expression)
            {
                if (string.IsNullOrWhiteSpace(value))
                {
                    validation.AddError(string.Format(DeksmartDomainResource.ExpressionValueCannotBeEmpty, rowNumber, currentField.Title));
                }
            }

            return validation;
        }

        private static Dictionary<string, int> GetAllIdents(Configurator configurator, Func<ConfiguratorField, bool> whereClause)
        {
            return configurator.ConfiguratorFieldCategories.Where(d => !d.IsDeleted)
                .SelectMany(d => d.ConfiguratorFields).Where(d => !d.IsDeleted).Where(whereClause)
                .Select(d => d.Ident).Where(d => d is not null).Distinct().ToDictionary(d => d!, d => 1);
        }

        public ValidationResult ValidateIdentMaxLength(string? ident)
        {
            var validation = _validationService.CreateValidation();
            if (!string.IsNullOrWhiteSpace(ident) && ident.Length > 10)
            {
                validation.AddError(string.Format(DeksmartDomainResource.IdentTooLong, ident));
            }
            return validation;
        }

        public ValidationResult ValidateIdentRequiredWhenTitlePresent(string? ident, string? title)
        {
            var validation = _validationService.CreateValidation();
            if (!string.IsNullOrWhiteSpace(title) && string.IsNullOrWhiteSpace(ident))
            {
                validation.AddError(string.Format(DeksmartDomainResource.IdentRequiredWhenTitlePresent, title));
            }
            return validation;
        }

        /// <inheritdoc/>
        public ValidationResult ValidateCellRequiredIfOtherPresent(object? value, object? otherValue, string error)
        {
            var validation = _validationService.CreateValidation();
            var other = otherValue as string;
            var val = value as string;
            if (!string.IsNullOrWhiteSpace(other) && string.IsNullOrWhiteSpace(val))
            {
                validation.AddError(error);
            }
            return validation;
        }
    }
}

