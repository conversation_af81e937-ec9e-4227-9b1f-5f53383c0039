namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a single specification or attribute of an e-shop product as a key-value pair (e.g., "Weight: 2kg").
    /// Used to transfer, display, and process product details and technical specifications in the e-commerce domain.
    /// Supports detailed product information in UI and API responses by encapsulating individual product attributes.
    /// </summary>
    public class EshopProductSpecItemDto
    {
        public string Title { get; set; }
        public string Value { get; set; }
    }
} 