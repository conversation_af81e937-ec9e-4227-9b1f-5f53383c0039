using Deksmart.Application.Resource;
using Deksmart.Domain.Entity.Business;
using Deksmart.Shared.Dto;
using Microsoft.Extensions.Logging;

namespace Deksmart.Application.Service
{
    public interface IConfiguratorViewOrchestrator
    {
        /// <summary>
        /// Generates an HTML representation of a configurator based on the provided configurator state.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="state">The state of the configurator to use for rendering.</param>
        /// <returns>
        /// A tuple containing the generated HTML (or null if generation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(string? html, ValidationResult validation)> GenerateHtmlFromStateAsync(int configuratorId, ConfiguratorStateDto state);

        /// <summary>
        /// Generates an HTML representation of a configurator using the state from a specified preset.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="presetId">The GUID of the preset to use for rendering.</param>
        /// <returns>
        /// A tuple containing the generated HTML (or null if generation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(string? html, ValidationResult validation)> GenerateHtmlFromPresetAsync(int configuratorId, Guid presetId);

        /// <summary>
        /// Generates a PDF document from a configurator based on the provided configurator state.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="state">The state of the configurator to use for rendering.</param>
        /// <returns>
        /// A tuple containing the PDF as a byte array (or null if generation fails), the configurator title, and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(byte[]? pdf, string? title, ValidationResult validation)> GeneratePdfFromStateAsync(int configuratorId, ConfiguratorStateDto state);

        /// <summary>
        /// Generates a PDF document from a configurator using the state from a specified preset.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="presetId">The GUID of the preset to use for rendering.</param>
        /// <returns>
        /// A tuple containing the PDF as a byte array (or null if generation fails), the configurator title, and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(byte[]? pdf, string? title, ValidationResult validation)> GeneratePdfFromPresetAsync(int configuratorId, Guid presetId);
    }

    public class ConfiguratorViewOrchestrator : IConfiguratorViewOrchestrator
    {
        private readonly IConfiguratorService _configuratorService;
        private readonly IConfiguratorPresetService _presetService;
        private readonly IConfiguratorViewGenerator _viewGenerator;
        private readonly ILogger<ConfiguratorViewOrchestrator> _logger;

        public ConfiguratorViewOrchestrator(
            IConfiguratorService configuratorService,
            IConfiguratorPresetService presetService,
            IConfiguratorViewGenerator viewGenerator,
            ILogger<ConfiguratorViewOrchestrator> logger)
        {
            _configuratorService = configuratorService;
            _presetService = presetService;
            _viewGenerator = viewGenerator;
            _logger = logger;
        }

        /// <summary>
        /// Generates an HTML representation of a configurator based on the provided configurator state.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="state">The state of the configurator to use for rendering.</param>
        /// <returns>
        /// A tuple containing the generated HTML (or null if generation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        public async Task<(string? html, ValidationResult validation)> GenerateHtmlFromStateAsync(int configuratorId, ConfiguratorStateDto state)
        {
            var validation = new ValidationResult();

            try
            {
                var (configurator, configValidation) = await _configuratorService.GetFilteredConfiguratorEntityAsync(configuratorId, state);

                if (configValidation.HasErrors)
                {
                    validation.AddError(configValidation.GetErrors());
                    return (null, validation);
                }

                var contactInfo = state is EmailConfiguratorStateDto emailState ? emailState.ContactInfo : null;
                var html = _viewGenerator.GenerateHtml(configurator!, contactInfo);
                return (html, validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate HTML from state for configurator {ConfiguratorId}", configuratorId);
                validation.AddError(string.Format(DeksmartApplicationResource.FailedToGenerateHtml, ex.Message));
                return (null, validation);
            }
        }

        /// <summary>
        /// Generates an HTML representation of a configurator using the state from a specified preset.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="presetId">The GUID of the preset to use for rendering.</param>
        /// <returns>
        /// A tuple containing the generated HTML (or null if generation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        public async Task<(string? html, ValidationResult validation)> GenerateHtmlFromPresetAsync(int configuratorId, Guid presetId)
        {
            var validation = new ValidationResult();

            try
            {
                var state = await _presetService.GetConfiguratorStateAsync(presetId);
                if (state == null)
                {
                    validation.AddError($"Preset with ID {presetId} does not exist");
                    return (null, validation);
                }

                var (configurator, configValidation) = await _configuratorService.GetFilteredConfiguratorEntityAsync(configuratorId, state);

                if (configValidation.HasErrors)
                {
                    validation.AddError(configValidation.GetErrors());
                    return (null, validation);
                }

                var contactInfo = state is EmailConfiguratorStateDto emailState ? emailState.ContactInfo : null;
                var html = _viewGenerator.GenerateHtml(configurator!, contactInfo);
                return (html, validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate HTML from preset {PresetId} for configurator {ConfiguratorId}", presetId, configuratorId);
                validation.AddError(string.Format(DeksmartApplicationResource.FailedToGenerateHtml, ex.Message));
                return (null, validation);
            }
        }

        /// <summary>
        /// Generates a PDF document from a configurator based on the provided configurator state.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="state">The state of the configurator to use for rendering.</param>
        /// <returns>
        /// A tuple containing the PDF as a byte array (or null if generation fails), the configurator title, and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        public async Task<(byte[]? pdf, string? title, ValidationResult validation)> GeneratePdfFromStateAsync(int configuratorId, ConfiguratorStateDto state)
        {
            var validation = new ValidationResult();

            try
            {
                var (configurator, configValidation) = await _configuratorService.GetFilteredConfiguratorEntityAsync(configuratorId, state);
                if (configValidation.HasErrors)
                {
                    validation.AddError(configValidation.GetErrors());
                    return (null, null, validation);
                }

                var (html, htmlValidation) = await GenerateHtmlFromStateAsync(configuratorId, state);
                if (htmlValidation.HasErrors)
                {
                    validation.AddError(htmlValidation.GetErrors());
                    return (null, null, validation);
                }

                var (pdfBytes, pdfValidation) = await _viewGenerator.GeneratePdfFromHtmlAsync(html!);
                if (pdfValidation.HasErrors)
                {
                    validation.AddError(pdfValidation.GetErrors());
                    return (null, null, validation);
                }

                return (pdfBytes, configurator!.Title, validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate PDF from state for configurator {ConfiguratorId}", configuratorId);
                validation.AddError(string.Format(DeksmartApplicationResource.FailedToGeneratePdf, ex.Message));
                return (null, null, validation);
            }
        }

        /// <summary>
        /// Generates a PDF document from a configurator using the state from a specified preset.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator.</param>
        /// <param name="presetId">The GUID of the preset to use for rendering.</param>
        /// <returns>
        /// A tuple containing the PDF as a byte array (or null if generation fails), the configurator title, and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        public async Task<(byte[]? pdf, string? title, ValidationResult validation)> GeneratePdfFromPresetAsync(int configuratorId, Guid presetId)
        {
            var validation = new ValidationResult();

            try
            {
                var state = await _presetService.GetConfiguratorStateAsync(presetId);
                if (state == null)
                {
                    validation.AddError($"Preset with ID {presetId} does not exist");
                    return (null, null, validation);
                }

                var (configurator, configValidation) = await _configuratorService.GetFilteredConfiguratorEntityAsync(configuratorId, state);
                if (configValidation.HasErrors)
                {
                    validation.AddError(configValidation.GetErrors());
                    return (null, null, validation);
                }

                var (html, htmlValidation) = await GenerateHtmlFromPresetAsync(configuratorId, presetId);
                if (htmlValidation.HasErrors)
                {
                    validation.AddError(htmlValidation.GetErrors());
                    return (null, null, validation);
                }

                var (pdfBytes, pdfValidation) = await _viewGenerator.GeneratePdfFromHtmlAsync(html!);
                if (pdfValidation.HasErrors)
                {
                    validation.AddError(pdfValidation.GetErrors());
                    return (null, null, validation);
                }

                return (pdfBytes, configurator!.Title, validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate PDF from preset {PresetId} for configurator {ConfiguratorId}", presetId, configuratorId);
                validation.AddError(string.Format(DeksmartApplicationResource.FailedToGeneratePdf, ex.Message));
                return (null, null, validation);
            }
        }
    }
}