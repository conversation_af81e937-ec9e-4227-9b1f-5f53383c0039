using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Shared.Dto;
using Deksmart.Shared.Enum;
using Deksmart.Domain.Enum;

namespace Deksmart.Application.Mapping
{
    public interface IConfiguratorMappingService
    {
        ConfiguratorDto MapToDto(Configurator configurator);
        ConfiguratorCompositionDto MapToDto(ConfiguratorComposition composition);
        ConfiguratorFieldDto MapToDto(ConfiguratorField field);
        ConfiguratorFieldCategoryDto MapToDto(ConfiguratorFieldCategory category);
        ConfiguratorFieldValueDto MapToDto(ConfiguratorFieldValue value);
        ConfiguratorProductDto MapToDto(ConfiguratorProduct product);
        ClientFieldValueDto MapToDto(ConfiguratorFieldCombination combination);
        ProductPriceCalculationResponseDto MapToDto(ProductPriceCalculationResponse response);
        SelectedProductDto MapToDto(SelectedProduct product);
        SelectedProduct MapToEntity(SelectedProductDto dto);
        List<ProductForAggregationDto> MapConfiguratorProductsToAggregationDtos(ConfiguratorDto configurator);
    }

    public class ConfiguratorMappingService : IConfiguratorMappingService
    {
        private readonly IEshopMappingService _eshopMappingService;

        public ConfiguratorMappingService(IEshopMappingService eshopMappingService)
        {
            _eshopMappingService = eshopMappingService;
        }

        private static ComponentTypeDto MapComponentType(Domain.Enum.ComponentType componentType)
        {
            return componentType switch
            {
                ComponentType.Numeric => ComponentTypeDto.Numeric,
                ComponentType.Slider => ComponentTypeDto.Slider,
                ComponentType.Checkbox => ComponentTypeDto.Checkbox,
                ComponentType.Selectbox => ComponentTypeDto.Selectbox,
                ComponentType.Tile => ComponentTypeDto.Tile,
                ComponentType.Expression => ComponentTypeDto.Expression,
                ComponentType.Text => ComponentTypeDto.Text,
                ComponentType.SingleChoice => ComponentTypeDto.SingleChoice,
                ComponentType.Product => ComponentTypeDto.Product,
                _ => throw new ArgumentOutOfRangeException(nameof(componentType), componentType, null)
            };
        }

        private static FieldPositionDto MapFieldPosition(Domain.Enum.FieldPosition fieldPosition)
        {
            return fieldPosition switch
            {
                FieldPosition.Left => FieldPositionDto.Left,
                FieldPosition.LeftSuffix => FieldPositionDto.LeftSuffix,
                FieldPosition.Right => FieldPositionDto.Right,
                FieldPosition.RightSuffix => FieldPositionDto.RightSuffix,
                FieldPosition.FullWidth => FieldPositionDto.FullWidth,
                FieldPosition.FullWidthSuffix => FieldPositionDto.FullWidthSuffix,
                _ => throw new ArgumentOutOfRangeException(nameof(fieldPosition), fieldPosition, null)
            };
        }

        public ConfiguratorDto MapToDto(Configurator configurator)
        {
            if (configurator == null) throw new ArgumentNullException(nameof(configurator));

            return new ConfiguratorDto
            {
                Id = configurator.Id,
                Title = configurator.Title,
                Description = configurator.Description,
                EndDescription = configurator.EndDescription,
                TotalPriceNoVat = configurator.TotalPriceNoVat,
                TotalPriceVat = configurator.TotalPriceVat,
                TotalVat = configurator.TotalVat,
                IsComposite = configurator.IsComposite,
                HasProducts = configurator.HasProducts,
                TabTitle = configurator.TabTitle,
                TabOrder = configurator.TabOrder,
                ConfiguratorCompositions = configurator.ConfiguratorCompositions.Select(MapToDto).ToList(),
                ConfiguratorFieldCategories = configurator.ConfiguratorFieldCategories.Select(MapToDto).ToList(),
                ChildUserConfigurators = configurator.ChildUserConfigurators.Select(MapToDto).ToList(),
                ChildConfigurators = configurator.ChildConfigurators.Select(MapToDto).ToList(),
                IsValid = configurator.IsValid,
                CanAddToCart = configurator.CanAddToCart,
                MetaDescription = configurator.MetaDescription
            };
        }

        public ConfiguratorCompositionDto MapToDto(ConfiguratorComposition composition)
        {
            if (composition == null) throw new ArgumentNullException(nameof(composition));

            return new ConfiguratorCompositionDto
            {
                Id = composition.Id,
                Title = composition.Title,
                Order = composition.Order,
                IsMultipleProducts = composition.IsMultipleProducts,
                ConfiguratorProducts = composition.ConfiguratorProducts.Select(MapToDto).ToList()
            };
        }

        public ConfiguratorFieldDto MapToDto(ConfiguratorField field)
        {
            if (field == null) throw new ArgumentNullException(nameof(field));

            return new ConfiguratorFieldDto
            {
                Id = field.Id,
                Title = field.Title,
                Suffix = field.Suffix,
                Description = field.Description,
                Order = field.Order,
                Ident = field.Ident,
                Max = field.MaxValue,
                Min = field.MinValue,
                ComponentType = MapComponentType(field.ComponentType),
                FieldPosition = MapFieldPosition(field.FieldPosition),
                Value = field.Value,
                ValidationError = field.ValidationError,
                JavaScriptAfterRender = field.JavaScriptAfterRender,
                JavaScriptOnFocus = field.JavaScriptOnFocus,
                JavaScriptOnBlur = field.JavaScriptOnBlur,
                ProductDetailsDto = field.ProductDetails != null ? _eshopMappingService.MapProductDetailsToDto(field.ProductDetails) : null,
                ConfiguratorFieldValues = field.ConfiguratorFieldValues?.Select(MapToDto).ToList() ?? new List<ConfiguratorFieldValueDto>()
            };
        }

        public ConfiguratorFieldCategoryDto MapToDto(ConfiguratorFieldCategory category)
        {
            if (category == null) throw new ArgumentNullException(nameof(category));

            return new ConfiguratorFieldCategoryDto
            {
                Id = category.Id,
                Title = category.Title,
                Description = category.Description,
                Order = category.Order,
                CollapseState = (CategoryCollapseStateDto)category.CollapseState,
                ConfiguratorFields = category.ConfiguratorFields.Select(MapToDto).ToList()
            };
        }

        public ConfiguratorFieldValueDto MapToDto(ConfiguratorFieldValue value)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));

            return new ConfiguratorFieldValueDto
            {
                Id = value.Id,
                Title = value.Title,
                Description = value.Description,
                Image = value.Image,
                Order = value.Order,
                NumericValue = value.NumericValue
            };
        }

        public ConfiguratorProductDto MapToDto(ConfiguratorProduct product)
        {
            if (product == null) throw new ArgumentNullException(nameof(product));

            return new ConfiguratorProductDto
            {
                Id = product.Id,
                ProductCode = product.ProductCode,
                Title = product.Title,
                Order = product.Order,
                ProductUnit = product.ProductUnit,
                PackageQuantity = product.PackageQuantity,
                UserPackageQuantity = product.UserPackageQuantity,
                CalculatedAmount = product.CalculatedAmount,
                IsSelected = product.IsSelected,
                PriceVatPackage = product.PriceVatPackage,
                PackageUnit = product.PackageUnit,
                PriceVat = product.PriceVat,
                ValidationError = product.ValidationError
            };
        }

        public ClientFieldValueDto MapToDto(ConfiguratorFieldCombination combination)
        {
            if (combination == null) throw new ArgumentNullException(nameof(combination));

            return new ClientFieldValueDto
            {
                FieldId = combination.FieldId,
                Value = combination.FieldValue
            };
        }

        public ProductPriceCalculationResponseDto MapToDto(ProductPriceCalculationResponse response)
        {
            if (response == null) throw new ArgumentNullException(nameof(response));

            return new ProductPriceCalculationResponseDto
            {
                PriceVat = response.PriceVat,
                TotalPriceNoVat = response.TotalPriceNoVat,
                TotalPriceVat = response.TotalPriceVat,
                TotalVat = response.TotalVat
            };
        }

        public SelectedProductDto MapToDto(SelectedProduct product)
        {
            if (product == null) throw new ArgumentNullException(nameof(product));

            return new SelectedProductDto
            {
                ProductCode = product.ProductCode,
                Amount = product.Amount,
                Unit = product.Unit
            };
        }

        public SelectedProduct MapToEntity(SelectedProductDto dto)
        {
            if (dto == null) throw new ArgumentNullException(nameof(dto));

            return new SelectedProduct
            {
                ProductCode = dto.ProductCode,
                Amount = dto.Amount,
                Unit = dto.Unit
            };
        }

        public ChildConfiguratorDto MapToDto(ChildConfigurator configurator)
        {
            if (configurator == null) throw new ArgumentNullException(nameof(configurator));

            return new ChildConfiguratorDto
            {
                Id = configurator.ConfiguratorId,
                Title = configurator.Configurator.Title,
                Order = configurator.DisplayOrder
            };
        }

        public List<ProductForAggregationDto> MapConfiguratorProductsToAggregationDtos(ConfiguratorDto configurator)
        {
            if (configurator == null) throw new ArgumentNullException(nameof(configurator));

            if (configurator.ConfiguratorCompositions == null || !configurator.ConfiguratorCompositions.Any())
                return new List<ProductForAggregationDto>();

            return configurator.ConfiguratorCompositions
                .SelectMany(c => c.ConfiguratorProducts ?? new List<ConfiguratorProductDto>())
                .Where(p => p.IsSelected)
                .Select(p => new ProductForAggregationDto
                {
                    ProductCode = p.ProductCode,
                    CalculatedAmount = p.CalculatedAmount,  // Use configurator amount, not package quantity
                    UserAmount = p.UserPackageQuantity,    // Use UserPackageQuantity for UserAmount priority logic
                    ProductUnit = p.ProductUnit ?? "",     // Use configurator unit, not package unit
                    Title = p.Title,
                    CompositionTitle = configurator.ConfiguratorCompositions
                        .FirstOrDefault(c => c.ConfiguratorProducts?.Contains(p) == true)?.Title ?? "",
                    CompositionOrder = configurator.ConfiguratorCompositions
                        .FirstOrDefault(c => c.ConfiguratorProducts?.Contains(p) == true)?.Order ?? 0,
                    ProductOrder = p.Order
                })
                .ToList();
        }

    }
}