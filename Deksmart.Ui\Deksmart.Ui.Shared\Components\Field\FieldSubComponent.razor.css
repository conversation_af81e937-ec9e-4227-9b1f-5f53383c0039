﻿.filter-component {
    display: flex;
    align-items: center;
    min-width: 0;
    justify-content: flex-start;
    height: 34px; /* Match the height of the selectbox */
}

/* Slider styles */
input[type="range"] {
    width: 100%;
    height: 6px;
    background: var(--light-color);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

input[type="range"]::-webkit-slider-runnable-track {
    width: 100%;
    height: 6px;
    background: var(--light-color);
    border-radius: 3px;
    cursor: pointer;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: var(--shadow-sm);
    margin-top: -6px;
}

input[type="range"]::-moz-range-track {
    width: 100%;
    height: 6px;
    background: var(--light-color);
    border-radius: 3px;
    cursor: pointer;
}

input[type="range"]::-moz-range-progress {
    background: var(--secondary-color);
    height: 6px;
    border-radius: 3px;
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: var(--shadow-sm);
}

/* Checkbox styles */
.checkbox-container {
    display: flex;
    align-items: center;
}

.checkbox-container label {
    margin-left: 8px;
    font-size: 1rem;
    cursor: pointer;
}

::deep input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 0;
    cursor: pointer;
    accent-color: #6c757d;
}

::deep input[type="checkbox"]:checked {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Select box styles */
::deep select {
    background-color: #f9f9f9;
    color: var(--secondary-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 0 8px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.2s ease;
    height: 34px;
    min-width: 200px;
    line-height: 34px;
    text-align: left;
}

::deep select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 1px rgba(192, 12, 24, 0.1);
    background-color: white;
}

/* Validation error styles */
::deep .has-validation-error {
    border-color: var(--danger-color) !important;
    background-color: rgba(188, 0, 22, 0.05);
    box-shadow: none;
}

::deep input[type="range"].has-validation-error::-webkit-slider-thumb {
    background-color: var(--danger-color);
}

::deep input[type="range"].has-validation-error::-moz-range-thumb {
    background-color: var(--danger-color);
}

::deep input[type="checkbox"].has-validation-error {
    outline: 2px solid var(--danger-color);
}

.field-suffix {
    margin-left: 8px;
    color: var(--secondary-color);
    font-size: 1rem;
}