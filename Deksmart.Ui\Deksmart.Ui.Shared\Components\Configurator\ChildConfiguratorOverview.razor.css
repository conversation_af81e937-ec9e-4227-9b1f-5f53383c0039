.child-overview-item {
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    background: white;
    overflow: hidden;
    position: relative;
}

.child-overview-header {
    padding: 0.75rem 1.25rem;
    background-color: var(--secondary-color);
    border-left: 4px solid var(--primary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
}

.accordion-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    flex-grow: 1;
}

.child-overview-content {
    padding: var(--spacing-lg);
    background-color: #f9f9f9;
}

.excel-table-container {
    width: 100%;
    overflow-x: auto;
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
}

.excel-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
    border: none;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.excel-table th {
    background-color: var(--secondary-color);
    font-weight: 600;
    text-align: left;
    padding: 0.6rem 0.75rem;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
    color: white;
}

.excel-table td {
    padding: 0.5rem 0.75rem;
    border: none;
    border-bottom: 1px solid var(--light-border);
    vertical-align: top;
    color: var(--dark-color);
}

.excel-table tr:last-child td {
    border-bottom: none;
}

.child-overview-white {
    background-color: white;
}

.child-overview-lightgray {
    background-color: var(--light-color);
}

.product-cell {
    padding: 0.5rem;
}

.product-group {
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--light-border);
}

.product-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.product-group-title {
    font-weight: 600;
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
    color: var(--secondary-color);
}

.product-item {
    display: flex;
    margin-bottom: 0.3rem;
    font-size: 0.8rem;
    padding: 0.3rem 0;
}

.product-item:last-child {
    margin-bottom: 0;
}

.product-code {
    font-weight: 600;
    margin-right: 0.75rem;
    min-width: 60px;
    color: var(--secondary-color);
}

.product-title {
    flex-grow: 1;
    margin-right: 0.75rem;
}

.product-quantity {
    min-width: 60px;
    text-align: right;
    font-weight: 500;
}

/* Ensure numeric values with suffixes stay on one line */
.numeric-with-suffix {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
}

/* Hover effect for rows */
.excel-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Mobile styles */
@media (max-width: 768px) {
    .excel-table {
        display: block;
    }

    .excel-table thead {
        display: none;
    }

    .excel-table tbody {
        display: block;
    }

    .excel-table tr {
        display: block;
        margin-bottom: 0.5rem;
        border-bottom: 1px solid var(--light-border);
        padding: 0.5rem 0;
    }

    .excel-table tr:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    .excel-table td {
        display: flex;
        padding: 0.4rem 0.75rem;
        border: none;
        border-bottom: none;
        align-items: center;
    }

    .excel-table td:before {
        content: attr(data-label);
        font-weight: 600;
        width: 40%;
        margin-right: 0.75rem;
        color: var(--secondary-color);
        font-size: 0.8rem;
    }

    .excel-table td:empty {
        display: none;
    }

    .product-cell {
        flex-direction: column;
        align-items: flex-start;
    }

    .product-cell:before {
        margin-bottom: 0.5rem;
    }

    .product-item {
        width: 100%;
    }
}
