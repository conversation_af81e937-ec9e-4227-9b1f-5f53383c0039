namespace Deksmart.Domain.Entity.Business.Eshop
{
    /// <summary>
    /// Represents a product in the e-shop domain, including identification, manufacturer, categorization, descriptive, and feature/recommendation information.
    /// Used for business logic and data transfer related to e-commerce product listings, details, and filtering.
    /// </summary>
    public class EshopProduct
    {
        public string Code { get; set; }
        public string Title { get; set; }
        public string ManufacturerTitle { get; set; }
        public string ManufacturerSlug { get; set; }
        public bool IsFeature { get; set; }
        public bool IsRecent { get; set; }
        public string ShortDescription { get; set; }
        public string Description { get; set; }
        public int Image { get; set; }
        public int CategoryId { get; set; }
        public string Slug { get; set; }
        public bool IsRecommended { get; set; }
        public int ManufacturerId { get; set; }
    }
}
