using Deksmart.Shared.Dto;
using Deksmart.Shared.Enum;

namespace Deksmart.Ui.Model
{
    public class ConfiguratorFieldCategoryWrapper
    {
        public ConfiguratorFieldCategoryDto Category { get; }

        public ConfiguratorFieldCategoryWrapper(ConfiguratorFieldCategoryDto category)
        {
            Category = category;
        }

        public string Title => Category.Title;
        public string? Description => Category.Description;

        public CategoryCollapseStateDto CollapseState
        {
            get => Category.CollapseState;
            set => Category.CollapseState = value;
        }

        public bool IsCollapsible => CollapseState != CategoryCollapseStateDto.NotCollapsible;

        public bool IsCollapsed => CollapseState == CategoryCollapseStateDto.Collapsed;

        public List<ConfiguratorFieldWrapper> Fields { get; set; } = [];
        public List<ConfiguratorFieldWrapper> RightFields { get; set; } = [];
        public List<ConfiguratorFieldWrapper> FullWidthFields { get; set; } = [];
        public List<ConfiguratorFieldWrapper> AllFields => GetAllFields();

        private List<ConfiguratorFieldWrapper> GetAllFields()
        {
            var result = Fields.Concat(RightFields).Concat(FullWidthFields);
            return result.Concat(result.Where(d => d.ChildField is not null).Select(d => d.ChildField!)).ToList();
        }

        public List<string?> ValueDescriptions => Fields.OfType<IdFieldWrapper>().Select(d => d.SelectedWrapper?.Value?.Description).Where(d => d is not null).ToList();
    }
}
