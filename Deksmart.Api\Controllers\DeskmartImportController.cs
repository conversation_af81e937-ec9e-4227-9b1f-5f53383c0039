using Deksmart.Api.Resource;
using Deksmart.Application.Service;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Shared.Dto;
using Microsoft.AspNetCore.Mvc;

namespace Deksmart.Api.Controllers
{
    /// <summary>
    /// API controller for importing configurator data, collections, and associating catalog IDs with presets.
    /// Handles file uploads for configurator and collection imports, and links catalog IDs to configurator presets.
    /// </summary>
    [ApiController]
    [Route("/api/import")]
    public class DeskmartImportController : DeksmartBaseController
    {
        private readonly IConfiguratorImportService _importService;
        private readonly ICompositeImportService _collectionImportService;
        private readonly IConfiguratorPresetRepository _configuratorPresetDao;

        public DeskmartImportController(
            IConfiguratorImportService importService,
            ICompositeImportService collectionImportService,
            IConfiguratorPresetRepository configuratorPresetDao)
        {
            _importService = importService;
            _collectionImportService = collectionImportService;
            _configuratorPresetDao = configuratorPresetDao;
        }

        /// <summary>
        /// Imports configurator data from an uploaded XLSX file.
        /// </summary>
        /// <param name="file">The XLSX file containing configurator data.</param>
        /// <returns>Success message if import is successful; otherwise, a validation error response.</returns>
        [HttpPost("import-configurator")]
        public async Task<IActionResult> ImportConfigurator(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return CreateErrorResponse(DeksmartApiResource.FileIsEmpty);

            if (!file.FileName.EndsWith(DeksmartApiResource.XlsxSuffix))
                return CreateErrorResponse(DeksmartApiResource.NotXlsx);

            var (success, validation) = await _importService.ParseConfiguratorImportsAsync(file);

            if (success)
                return CreateSuccessResponse("Import completed successfully.");
            else
                return CreateValidationErrorResponse(validation);
        }

        /// <summary>
        /// Imports composite configurator data from an uploaded XLSX file.
        /// </summary>
        /// <param name="file">The XLSX file containing composite configurator data.</param>
        /// <returns>Success message if import is successful; otherwise, a validation error response.</returns>
        [HttpPost("import-composite")]
        public async Task<IActionResult> ImportComposite(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return CreateErrorResponse(DeksmartApiResource.FileIsEmpty);

            if (!file.FileName.EndsWith(DeksmartApiResource.XlsxSuffix))
                return CreateErrorResponse(DeksmartApiResource.NotXlsx);

            var (success, validation) = await _collectionImportService.ImportCompositeAsync(file);

            if (success)
                return CreateSuccessResponse("Import completed successfully.");
            else
                return CreateValidationErrorResponse(validation);
        }

        /// <summary>
        /// Associates a catalog ID with a configurator preset.
        /// </summary>
        /// <param name="presetId">The GUID of the configurator preset.</param>
        /// <param name="catalogId">The catalog ID to associate.</param>
        /// <returns>Success if association is made; otherwise, not found or validation error.</returns>
        [HttpPost("import-catalog/{presetId}/{catalogId}")]
        public async Task<IActionResult> SetCatalogIdToPreset(string presetId, string catalogId)
        {
            if (Guid.TryParse(presetId, out var parsedPresetId))
            {
                var result = await _configuratorPresetDao.SetCatalogIdToPresetAsync(parsedPresetId, catalogId);

                if (result)
                    return CreateSuccessResponse(true);
            }

            return NotFound(ApiResponse<object>.CreateError(string.Format(DeksmartApiResource.PresetDoesNotExist, presetId)));
        }
    }
}
