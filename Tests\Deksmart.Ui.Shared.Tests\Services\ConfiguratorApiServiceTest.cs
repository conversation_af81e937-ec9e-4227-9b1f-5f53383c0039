using Moq;
using Microsoft.Extensions.Logging;
using Deksmart.Ui.Shared.Services;
using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class ConfiguratorApiServiceTest
    {
        private readonly Mock<ITestHttpService> _mockHttpService;
        private readonly Mock<ILogger<ConfiguratorApiService>> _mockLogger;
        private readonly Mock<ILogger<NotificationService>> _mockNotificationLogger;
        private readonly NotificationService _notificationService;
        private readonly TestConfiguratorApiService _service;

        public ConfiguratorApiServiceTest()
        {
            _mockHttpService = new Mock<ITestHttpService>();
            _mockLogger = new Mock<ILogger<ConfiguratorApiService>>();
            _mockNotificationLogger = new Mock<ILogger<NotificationService>>();
            _notificationService = new NotificationService(_mockNotificationLogger.Object);

            _service = new TestConfiguratorApiService(
                _mockHttpService.Object,
                _mockLogger.Object,
                _notificationService);
        }

        private ConfiguratorDto CreateTestConfiguratorDto(int id = 1)
        {
            return new ConfiguratorDto
            {
                Id = id,
                Title = "Test Configurator",
                Description = "Test Description",
                IsValid = true,
                Url = "test-url"
            };
        }

        private ConfiguratorStateDto CreateTestConfiguratorStateDto()
        {
            return new ConfiguratorStateDto
            {
                ConfiguratorId = 1,
                FieldValues = [],
                SelectedProducts = []
            };
        }

        [Fact]
        public async Task GetConfiguratorAsync_CallsHttpServiceGetWithCorrectParameters()
        {
            var expectedResult = CreateTestConfiguratorDto(42);
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.GetAsync<ConfiguratorDto>("42", cancellationToken))
                .ReturnsAsync(expectedResult);

            var result = await _service.GetConfiguratorAsync(42, cancellationToken);

            Assert.Equal(expectedResult, result);
            _mockHttpService.Verify(h => h.GetAsync<ConfiguratorDto>("42", cancellationToken), Times.Once);
        }

        [Fact]
        public async Task GetConfiguratorAsync_WithDefaultCancellationToken_UsesDefault()
        {
            var expectedResult = CreateTestConfiguratorDto(123);

            _mockHttpService.Setup(h => h.GetAsync<ConfiguratorDto>("123", It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            var result = await _service.GetConfiguratorAsync(123, CancellationToken.None);

            Assert.Equal(expectedResult, result);
            _mockHttpService.Verify(h => h.GetAsync<ConfiguratorDto>("123", It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetConfiguratorAsync_ReturnsNullWhenHttpServiceReturnsNull()
        {
            _mockHttpService.Setup(h => h.GetAsync<ConfiguratorDto>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((ConfiguratorDto?)null);

            var result = await _service.GetConfiguratorAsync(999, CancellationToken.None);

            Assert.Null(result);
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_CallsHttpServicePostWithCorrectParameters()
        {
            var expectedResult = CreateTestConfiguratorDto(42);
            var state = CreateTestConfiguratorStateDto();
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.PostAsync<ConfiguratorDto>(It.IsAny<string>(), state, cancellationToken))
                .ReturnsAsync(expectedResult);

            var result = await _service.GetFilteredConfiguratorAsync(42, state, cancellationToken);

            Assert.Equal(expectedResult, result);
            _mockHttpService.Verify(h => h.PostAsync<ConfiguratorDto>(It.IsAny<string>(), state, cancellationToken), Times.Once);
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_UsesFormattedUrlWithId()
        {
            var state = CreateTestConfiguratorStateDto();

            _mockHttpService.Setup(h => h.PostAsync<ConfiguratorDto>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateTestConfiguratorDto());

            await _service.GetFilteredConfiguratorAsync(42, state, CancellationToken.None);

            // Verify the URL contains the configurator ID (exact URL format depends on resource string)
            _mockHttpService.Verify(h => h.PostAsync<ConfiguratorDto>(It.Is<string>(url => url.Contains("42")), state, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetConfiguratorForPresetAsync_CallsHttpServiceGetWithCorrectParameters()
        {
            var expectedResult = CreateTestConfiguratorDto(42);
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.GetAsync<ConfiguratorDto>(It.IsAny<string>(), cancellationToken))
                .ReturnsAsync(expectedResult);

            var result = await _service.GetConfiguratorForPresetAsync(42, "test-preset", cancellationToken);

            Assert.Equal(expectedResult, result);
            _mockHttpService.Verify(h => h.GetAsync<ConfiguratorDto>(It.IsAny<string>(), cancellationToken), Times.Once);
        }

        [Fact]
        public async Task GetConfiguratorForPresetAsync_UsesFormattedUrlWithIdAndPreset()
        {
            _mockHttpService.Setup(h => h.GetAsync<ConfiguratorDto>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateTestConfiguratorDto());

            await _service.GetConfiguratorForPresetAsync(42, "my-preset", CancellationToken.None);

            // Verify the URL contains both the configurator ID and preset name
            _mockHttpService.Verify(h => h.GetAsync<ConfiguratorDto>(
                It.Is<string>(url => url.Contains("42") && url.Contains("my-preset")), 
                It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task SavePresetAsync_CallsHttpServicePostWithCorrectParameters()
        {
            var expectedGuid = Guid.NewGuid();
            var state = CreateTestConfiguratorStateDto();
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.PostAsync<Guid>(It.IsAny<string>(), state, cancellationToken))
                .ReturnsAsync(expectedGuid);

            var result = await _service.SavePresetAsync(42, state, cancellationToken);

            Assert.Equal(expectedGuid, result);
            _mockHttpService.Verify(h => h.PostAsync<Guid>(It.IsAny<string>(), state, cancellationToken), Times.Once);
        }

        [Fact]
        public async Task SavePresetAsync_UsesFormattedUrlWithConfiguratorId()
        {
            var state = CreateTestConfiguratorStateDto();

            _mockHttpService.Setup(h => h.PostAsync<Guid>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(Guid.NewGuid());

            await _service.SavePresetAsync(42, state, CancellationToken.None);

            // Verify the URL contains the configurator ID
            _mockHttpService.Verify(h => h.PostAsync<Guid>(It.Is<string>(url => url.Contains("42")), state, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task LoadProductDetailAsync_CallsHttpServiceGetWithCorrectParameters()
        {
            var expectedResult = new ProductDetailsDto
            {
                Product = new EshopProductDto { Code = "PROD123", Title = "Test Product", Description = "Test" },
                Specifications = []
            };
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.GetAsync<ProductDetailsDto>("product-detail/PROD123", cancellationToken))
                .ReturnsAsync(expectedResult);

            var result = await _service.LoadProductDetailAsync("PROD123", cancellationToken);

            Assert.Equal(expectedResult, result);
            _mockHttpService.Verify(h => h.GetAsync<ProductDetailsDto>("product-detail/PROD123", cancellationToken), Times.Once);
        }

        [Fact]
        public async Task LoadProductDetailAsync_WithDifferentProductCodes_UsesCorrectUrls()
        {
            _mockHttpService.Setup(h => h.GetAsync<ProductDetailsDto>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ProductDetailsDto { Product = new EshopProductDto { Code = "", Title = "", Description = "" }, Specifications = [] });

            await _service.LoadProductDetailAsync("ABC123", CancellationToken.None);
            await _service.LoadProductDetailAsync("XYZ789", CancellationToken.None);

            _mockHttpService.Verify(h => h.GetAsync<ProductDetailsDto>("product-detail/ABC123", It.IsAny<CancellationToken>()), Times.Once);
            _mockHttpService.Verify(h => h.GetAsync<ProductDetailsDto>("product-detail/XYZ789", It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task CalculateProductPricesAsync_CallsHttpServicePostWithCorrectParameters()
        {
            var request = new ProductPriceCalculationRequestDto
            {
                ConfiguratorId = 1,
                CurrentProductCode = "PROD123",
                SelectedProducts = []
            };
            var expectedResult = new ProductPriceCalculationResponseDto
            {
                PriceVat = 100.0m,
                TotalPriceNoVat = 80.0m,
                TotalPriceVat = 100.0m,
                TotalVat = 20.0m
            };
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.PostAsync<ProductPriceCalculationResponseDto>(It.IsAny<string>(), request, cancellationToken))
                .ReturnsAsync(expectedResult);

            var result = await _service.CalculateProductPricesAsync(request, cancellationToken);

            Assert.Equal(expectedResult, result);
            _mockHttpService.Verify(h => h.PostAsync<ProductPriceCalculationResponseDto>(It.IsAny<string>(), request, cancellationToken), Times.Once);
        }

        [Fact]
        public async Task CalculateProductPricesAsync_WithSuccessfulResult_LogsInformation()
        {
            var request = new ProductPriceCalculationRequestDto
            {
                ConfiguratorId = 1,
                CurrentProductCode = "PROD123",
                SelectedProducts = []
            };
            var expectedResult = new ProductPriceCalculationResponseDto
            {
                PriceVat = 100.0m,
                TotalPriceNoVat = 80.0m,
                TotalPriceVat = 100.0m,
                TotalVat = 20.0m
            };

            _mockHttpService.Setup(h => h.PostAsync<ProductPriceCalculationResponseDto>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            await _service.CalculateProductPricesAsync(request, CancellationToken.None);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("Calculated product prices for PROD123")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CalculateProductPricesAsync_WithNullResult_DoesNotLog()
        {
            var request = new ProductPriceCalculationRequestDto
            {
                ConfiguratorId = 1,
                CurrentProductCode = "PROD123",
                SelectedProducts = []
            };

            _mockHttpService.Setup(h => h.PostAsync<ProductPriceCalculationResponseDto>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((ProductPriceCalculationResponseDto?)null);

            await _service.CalculateProductPricesAsync(request, CancellationToken.None);

            _mockLogger.Verify(
                l => l.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains("Calculated product prices")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Never);
        }

        [Fact]
        public async Task GenerateConfigurationPdfAsync_WithSuccessfulResult_ReturnsPdfBytesAndFileName()
        {
            var state = CreateTestConfiguratorStateDto();
            var expectedBytes = new byte[] { 1, 2, 3, 4, 5 };
            var expectedFileName = "test-config.pdf";
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.PostBinaryAsync(It.IsAny<string>(), state, cancellationToken))
                .ReturnsAsync((expectedBytes, expectedFileName));

            var result = await _service.GenerateConfigurationPdfAsync(42, state, cancellationToken);

            Assert.Equal(expectedBytes, result.pdfBytes);
            Assert.Equal(expectedFileName, result.fileName);
            _mockHttpService.Verify(h => h.PostBinaryAsync(It.IsAny<string>(), state, cancellationToken), Times.Once);
        }

        [Fact]
        public async Task GenerateConfigurationPdfAsync_WithNullBytes_ShowsErrorAndReturnsDefaultFileName()
        {
            var state = CreateTestConfiguratorStateDto();

            _mockHttpService.Setup(h => h.PostBinaryAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(((byte[]?)null, (string?)null));

            var result = await _service.GenerateConfigurationPdfAsync(42, state, CancellationToken.None);

            Assert.Null(result.pdfBytes);
            Assert.Equal("configuration.pdf", result.fileName);
            Assert.Single(_notificationService.Notifications);
            Assert.Equal(NotificationType.Error, _notificationService.Notifications[0].Type);
        }

        [Fact]
        public async Task GenerateConfigurationPdfAsync_WithNullFileName_ReturnsDefaultFileName()
        {
            var state = CreateTestConfiguratorStateDto();
            var expectedBytes = new byte[] { 1, 2, 3 };

            _mockHttpService.Setup(h => h.PostBinaryAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((expectedBytes, (string?)null));

            var result = await _service.GenerateConfigurationPdfAsync(42, state, CancellationToken.None);

            Assert.Equal(expectedBytes, result.pdfBytes);
            Assert.Equal("configuration.pdf", result.fileName);
        }

        [Fact]
        public async Task GenerateConfigurationPdfAsync_UsesFormattedUrlWithConfiguratorId()
        {
            var state = CreateTestConfiguratorStateDto();

            _mockHttpService.Setup(h => h.PostBinaryAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((new byte[] { 1 }, "test.pdf"));

            await _service.GenerateConfigurationPdfAsync(42, state, CancellationToken.None);

            // Verify the URL contains the configurator ID
            _mockHttpService.Verify(h => h.PostBinaryAsync(It.Is<string>(url => url.Contains("42")), state, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task SendInquiryAsync_WithSuccessfulResult_ShowsSuccessNotificationAndReturnsTrue()
        {
            var emailState = new EmailConfiguratorStateDto
            {
                ConfiguratorId = 42,
                ContactInfo = new ContactInfoDto { Email = "<EMAIL>", Name = "Test User" }
            };
            var cancellationToken = new CancellationToken();

            _mockHttpService.Setup(h => h.PostAsync(It.IsAny<string>(), emailState, cancellationToken))
                .ReturnsAsync(true);

            var result = await _service.SendInquiryAsync(42, emailState, cancellationToken);

            Assert.True(result);
            Assert.Single(_notificationService.Notifications);
            Assert.Equal(NotificationType.Success, _notificationService.Notifications[0].Type);
            _mockHttpService.Verify(h => h.PostAsync(It.IsAny<string>(), emailState, cancellationToken), Times.Once);
        }

        [Fact]
        public async Task SendInquiryAsync_WithFailedResult_DoesNotShowSuccessNotificationAndReturnsFalse()
        {
            var emailState = new EmailConfiguratorStateDto
            {
                ConfiguratorId = 42,
                ContactInfo = new ContactInfoDto { Email = "<EMAIL>", Name = "Test User" }
            };

            _mockHttpService.Setup(h => h.PostAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(false);

            var result = await _service.SendInquiryAsync(42, emailState, CancellationToken.None);

            Assert.False(result);
            Assert.Empty(_notificationService.Notifications);
        }

        [Fact]
        public async Task SendInquiryAsync_UsesFormattedUrlWithConfiguratorId()
        {
            var emailState = new EmailConfiguratorStateDto { ConfiguratorId = 42 };

            _mockHttpService.Setup(h => h.PostAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);

            await _service.SendInquiryAsync(42, emailState, CancellationToken.None);

            // Verify the URL contains the configurator ID
            _mockHttpService.Verify(h => h.PostAsync(It.Is<string>(url => url.Contains("42")), emailState, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task All_Methods_HandleCancellationToken_Correctly()
        {
            var cancellationToken = new CancellationToken(true); // Cancelled token
            var state = CreateTestConfiguratorStateDto();
            var emailState = new EmailConfiguratorStateDto();
            var request = new ProductPriceCalculationRequestDto
            {
                ConfiguratorId = 1,
                CurrentProductCode = "PROD123",
                SelectedProducts = []
            };

            // All methods should pass the cancellation token to HttpService
            await _service.GetConfiguratorAsync(1, cancellationToken);
            await _service.GetFilteredConfiguratorAsync(1, state, cancellationToken);
            await _service.GetConfiguratorForPresetAsync(1, "preset", cancellationToken);
            await _service.SavePresetAsync(1, state, cancellationToken);
            await _service.LoadProductDetailAsync("PROD123", cancellationToken);
            await _service.CalculateProductPricesAsync(request, cancellationToken);
            await _service.GenerateConfigurationPdfAsync(1, state, cancellationToken);
            await _service.SendInquiryAsync(1, emailState, cancellationToken);

            // Verify all calls used the provided cancellation token
            _mockHttpService.Verify(h => h.GetAsync<ConfiguratorDto>(It.IsAny<string>(), cancellationToken), Times.Exactly(2));
            _mockHttpService.Verify(h => h.PostAsync<ConfiguratorDto>(It.IsAny<string>(), It.IsAny<object>(), cancellationToken), Times.Once);
            _mockHttpService.Verify(h => h.PostAsync<Guid>(It.IsAny<string>(), It.IsAny<object>(), cancellationToken), Times.Once);
            _mockHttpService.Verify(h => h.GetAsync<ProductDetailsDto>(It.IsAny<string>(), cancellationToken), Times.Once);
            _mockHttpService.Verify(h => h.PostAsync<ProductPriceCalculationResponseDto>(It.IsAny<string>(), It.IsAny<object>(), cancellationToken), Times.Once);
            _mockHttpService.Verify(h => h.PostBinaryAsync(It.IsAny<string>(), It.IsAny<object>(), cancellationToken), Times.Once);
            _mockHttpService.Verify(h => h.PostAsync(It.IsAny<string>(), It.IsAny<object>(), cancellationToken), Times.Once);
        }
    }
}