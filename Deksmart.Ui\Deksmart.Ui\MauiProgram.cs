﻿using System.Text.Json.Serialization;
using System.Text.Json;
using Deksmart.Ui.Services;
using Deksmart.Ui.Shared.Services;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using Deksmart.Ui.Shared.Factory;
using System.Globalization;

namespace Deksmart.Ui
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                });

            // Add device-specific services used by the Deksmart.Ui.Shared project
            builder.Services.AddSingleton<IFormFactor, FormFactor>();

            builder.Services.AddMauiBlazorWebView();
            builder.Services.AddTransient<IConfiguratorGridService, Deksmart.Ui.Shared.Services.ConfiguratorGridService>();
            builder.Services.AddScoped<IConfiguratorGridServiceFactory, ConfiguratorGridServiceFactory>();
            builder.Services.AddScoped<IConfiguratorApiService, ConfiguratorApiService>();
            builder.Services.AddScoped<IConfiguratorStateService, ConfiguratorStateService>();
            builder.Services.AddScoped<IConfiguratorValidationService, ConfiguratorValidationService>();
            builder.Services.AddScoped<IChildServiceManager, ChildServiceManager>();
            builder.Services.AddScoped<NotificationService>();
            builder.Services.AddScoped<ConfiguratorNavigationService>();
            builder.Services.AddScoped<HeaderStateService>();
            builder.Services.AddScoped<TabDragDropService>();

            var jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new JsonStringEnumConverter() }
            };

            builder.Services.AddSingleton(jsonOptions);

            // Set up localization with Czech as default
            var cultureInfo = new CultureInfo("cs-CZ");
            CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
            CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

            SetUpHttpClients(builder);
#if DEBUG
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }

        private static void SetUpHttpClients(MauiAppBuilder builder)
        {
            SetUpHttClient(builder, "DeksmartApi", "https://localhost:7276/api/", "https://********:7276/api/");
            builder.Services.AddScoped<HttpService>();
        }

        private static void SetUpHttClient(MauiAppBuilder builder, string ident, string url, string androidUrl)
        {
#if ANDROID && DEBUG
            // Add HttpClient named "DeksmartApi" with Android-debug specific configuration
            builder.Services.AddHttpClient(ident, client =>
            {
                client.BaseAddress = new Uri(androidUrl);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                UseProxy = false,
                ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => true
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());
#else
            // Add HttpClient named "DeksmartApi" with default configuration
            builder.Services.AddHttpClient(ident, client =>
            {
                client.BaseAddress = new Uri(url);
            })
            .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
            {
                UseProxy = false
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());
#endif
        }

        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(3, retryAttempt =>
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
        }

        private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
        }
    }
}
