namespace Deksmart.Domain.Enum
{
    /// <summary>
    /// Defines the type of UI component used to render a configurator field.
    /// Specifies how the field is presented and interacted with in the user interface (e.g., numeric input, slider, checkbox, select box, etc.).
    /// </summary>
    public enum ComponentType
    {
        Numeric = 1,
        Slider = 2,
        Checkbox = 3,
        Selectbox = 4,
        Tile = 5,
        Expression = 6,
        Text = 7,
        SingleChoice = 8,
        Product = 9
    }
} 