﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AnUnexpectedErrorOccurred" xml:space="preserve">
    <value>An unexpected error occurred: {0}</value>
  </data>
  <data name="CategoryCollapsed" xml:space="preserve">
    <value>category-{0}-collapsed</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Configurators" xml:space="preserve">
    <value>Configurators</value>
  </data>
  <data name="ConfiguratorsUrl" xml:space="preserve">
    <value>configurators</value>
  </data>
  <data name="Deksmart" xml:space="preserve">
    <value>deksmart</value>
  </data>
  <data name="DeksmartBrand" xml:space="preserve">
    <value>DEKSMART</value>
  </data>
  <data name="EnterCompositionNumber" xml:space="preserve">
    <value>Enter the composition number from the catalog:</value>
  </data>
  <data name="FinalPrice" xml:space="preserve">
    <value>Final price including VAT</value>
  </data>
  <data name="FinalPriceNoVat" xml:space="preserve">
    <value>The resulting price for the structure excluding VAT</value>
  </data>
  <data name="Find" xml:space="preserve">
    <value>Find</value>
  </data>
  <data name="LoadConfiguratorUrl" xml:space="preserve">
    <value>{0}/configurator</value>
  </data>
  <data name="LoadPresetUrl" xml:space="preserve">
    <value>{0}/configurator/{1}</value>
  </data>
  <data name="NoConfiguratorSelected" xml:space="preserve">
    <value>No configurator selected</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="PriceVAT" xml:space="preserve">
    <value>Price with VAT</value>
  </data>
  <data name="ProductListing" xml:space="preserve">
    <value>Product listing</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>Product name</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="ProductNumber" xml:space="preserve">
    <value>Product number</value>
  </data>
  <data name="PricingError" xml:space="preserve">
    <value>Pricing Error</value>
  </data>
  <data name="QuantityPerStructure" xml:space="preserve">
    <value>Required quantity per structure</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="SavePresetUrl" xml:space="preserve">
    <value>{0}/save</value>
  </data>
  <data name="CalculateProductPricesUrl" xml:space="preserve">
    <value>calculate-product-prices</value>
  </data>
  <data name="VAT" xml:space="preserve">
    <value>VAT</value>
  </data>
  <data name="DownloadConfigurationPdf" xml:space="preserve">
    <value>Download PDF</value>
  </data>
  <data name="JsonFormatError" xml:space="preserve">
    <value>Invalid JSON format received from the server.</value>
  </data>
  <data name="ArgumentException" xml:space="preserve">
    <value>Invalid argument provided.</value>
  </data>
  <data name="InvalidOperationError" xml:space="preserve">
    <value>Invalid operation attempted.</value>
  </data>
  <data name="UnauthorizedAccessError" xml:space="preserve">
    <value>You don't have permission to perform this action.</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="NewTab" xml:space="preserve">
    <value>New Tab</value>
  </data>
  <data name="GenerateConfigurationPdfUrl" xml:space="preserve">
    <value>view/{0}/pdf</value>
  </data>
  <data name="ValidationErrorsInTab" xml:space="preserve">
    <value>This tab contains validation errors</value>
  </data>
  <data name="FailedToGeneratePdf" xml:space="preserve">
    <value>Failed to generate PDF</value>
  </data>
  <data name="SendInquiryUrl" xml:space="preserve">
    <value>view/{0}/email</value>
  </data>
  <data name="SendInquiryButtonText" xml:space="preserve">
    <value>Send inquiry</value>
  </data>
  <data name="SendInquirySuccess" xml:space="preserve">
    <value>Inquiry has been sent successfully</value>
  </data>
  <data name="SendInquiryError" xml:space="preserve">
    <value>Failed to send inquiry</value>
  </data>
  <data name="NameLabel" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="EmailLabel" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="PhoneLabel" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="CancelButtonText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>Validation error occurred while sending data to {0}: {1}</value>
  </data>
  <data name="UnknownError" xml:space="preserve">
    <value>An unknown error occurred. Please try again later.</value>
  </data>
  <data name="ResourceNotFound" xml:space="preserve">
    <value>The requested {0} was not found.</value>
  </data>
  <data name="ForbiddenError" xml:space="preserve">
    <value>You don't have permission to access this resource.</value>
  </data>
  <data name="BadRequestError" xml:space="preserve">
    <value>The request was invalid. Please check your input and try again.</value>
  </data>
  <data name="ServerError" xml:space="preserve">
    <value>A server error occurred. Please try again later or contact support if the problem persists.</value>
  </data>
  <data name="LoadingApplication" xml:space="preserve">
    <value>Loading application...</value>
  </data>
  <data name="PageNotFound" xml:space="preserve">
    <value>Not found</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>Sorry, there's nothing at this address.</value>
  </data>
  <data name="ChildConfiguratorOverview" xml:space="preserve">
    <value>Overall Overview</value>
  </data>
  <data name="ConfiguratorName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Field" xml:space="preserve">
    <value>Field</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="SelectOption" xml:space="preserve">
    <value>Select...</value>
  </data>
  <data name="SelectConfigurator" xml:space="preserve">
    <value>Select Configurator</value>
  </data>
  <data name="MetaDescription_MainPage" xml:space="preserve">
    <value>DEKSMART – product configurator. Design, adjust and calculate material and cost quickly and accurately for your project.</value>
  </data>
  <data name="MetaDescription_ConfiguratorTemplate" xml:space="preserve">
    <value>DEKSMART – {0}. Calculate material and price for your project.</value>
  </data>
  <data name="CurrencySymbol" xml:space="preserve">
    <value>Kč</value>
  </data>
  <data name="AddToAgendaButtonText" xml:space="preserve">
    <value>Add to agenda</value>
  </data>
  <data name="AddToCartButtonText" xml:space="preserve">
    <value>Add to shopping cart</value>
  </data>
  <data name="ProductPricingErrorMessage" xml:space="preserve">
    <value>Some products could not be priced. Shopping cart is disabled until errors are resolved.</value>
  </data>
  <data name="TotalsExcludeErrorsMessage" xml:space="preserve">
    <value>Totals exclude products with pricing errors</value>
  </data>
  <data name="ProcessCompositeConfiguratorUrl" xml:space="preserve">
    <value>{0}/composite</value>
  </data>
</root>