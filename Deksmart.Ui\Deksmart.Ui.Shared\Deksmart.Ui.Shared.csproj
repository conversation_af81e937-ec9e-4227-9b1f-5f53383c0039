﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Deksmart.Ui.Shared.Tests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Deksmart.Ui.Model\Deksmart.Ui.Model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\UiSharedResource.Designer.cs">
      <DependentUpon>UiSharedResource.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\UiSharedResource.resx">
      <LastGenOutput>UiSharedResource.Designer.cs</LastGenOutput>
      <Generator>ResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>

	<ItemGroup>
		<Content Update="wwwroot\favicon.png">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\css\app.css">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\css\form-field-fixes.css">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Update="wwwroot\lib\bootstrap\dist\css\bootstrap.min.css">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>