using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Enum;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Represents the saved UI state of a configurator category within a preset.
    /// Links a preset to a specific category and stores the collapsed or expanded state (IsTreeView), enabling restoration of the category's UI state when reloading a configurator preset.
    /// </summary>
    [Table("preset_configurator_category_state", Schema = "dbo")]
    public class PresetConfiguratorCategoryState
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("preset_id")]
        public Guid PresetId { get; set; }

        [Column("category_id")]
        public int CategoryId { get; set; }

        /// <summary>
        /// Represents the collapse/expand state of the category for preset restoration.
        /// </summary>
        [Column("collapse_state")]
        public CategoryCollapseState CollapseState { get; set; }
    }
}
