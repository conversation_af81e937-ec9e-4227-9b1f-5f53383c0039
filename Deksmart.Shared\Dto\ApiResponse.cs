using System.Text.Json.Serialization;

namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Standardized API response format for all Deksmart API endpoints
    /// </summary>
    /// <typeparam name="T">The type of data being returned</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// The data payload of the response
        /// </summary>
        [JsonPropertyName("data")]
        public T? Data { get; set; }

        /// <summary>
        /// Indicates whether the request was successful
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// List of validation errors, if any
        /// </summary>
        [JsonPropertyName("validationErrors")]
        public List<string>? ValidationErrors { get; set; }

        /// <summary>
        /// General error message, if any
        /// </summary>
        [JsonPropertyName("errorMessage")]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Creates a successful response with data
        /// </summary>
        /// <param name="data">The data to return</param>
        /// <returns>A successful API response</returns>
        public static ApiResponse<T> CreateSuccess(T data)
        {
            return new ApiResponse<T>
            {
                Data = data,
                Success = true
            };
        }

        /// <summary>
        /// Creates an error response with a general error message
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>An error API response</returns>
        public static ApiResponse<T> CreateError(string errorMessage)
        {
            return new ApiResponse<T>
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// Creates a validation error response with a list of validation errors
        /// </summary>
        /// <param name="validationErrors">The list of validation errors</param>
        /// <returns>A validation error API response</returns>
        public static ApiResponse<T> CreateValidationError(IEnumerable<string> validationErrors)
        {
            return new ApiResponse<T>
            {
                Success = false,
                ValidationErrors = validationErrors.ToList()
            };
        }
    }
}
