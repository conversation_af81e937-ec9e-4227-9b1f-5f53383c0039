main {
    padding-top: 0;
}

.page {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--body-bg);
}

.content-wrapper {
    position: relative;
    min-height: calc(100vh - var(--header-total-height, 0px));
}

.sidebar {
    position: fixed;
    left: 0;
    top: var(--dynamic-header-height, 0px);
    height: calc(100vh - var(--dynamic-header-height, 0px));
    z-index: 999;
    background-color: var(--header-bg);
    color: var(--header-text);
    width: 250px;
    transition: transform 0.3s ease-in-out, top 0.1s ease-out, height 0.1s ease-out;
    transform: translateX(-250px); /* Start with sidebar hidden */
    background-color: white;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar.show {
    transform: translateX(0); /* Show sidebar when .show class is applied */
    overflow: hidden; /* Prevent scrollbar on the sidebar container */
}

.top-row {
    background-color: var(--header-bg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
    padding: 0 1rem;
}

.top-row ::deep a, .top-row ::deep .btn-link {
    white-space: nowrap;
    margin-left: 1.5rem;
    text-decoration: none;
    color: var(--header-text);
    opacity: 0.8;
    transition: opacity 0.2s ease-in-out;
}

.top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
    opacity: 1;
    text-decoration: none;
}

.top-row ::deep a:first-child {
    overflow: hidden;
    text-overflow: ellipsis;
}

.content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.top-row {
    position: sticky;
    top: 0;
    z-index: 1;
}

.top-row.auth ::deep a:first-child {
    flex: 1;
    text-align: right;
    width: 0;
}

.top-row, article {
    padding-left: 2rem !important;
    padding-right: 1.5rem !important;
}

/* Menu toggle button positioning */
.menu-toggle {
    padding: 0 0.75rem; /* Horizontal padding */
    display: flex;
    align-items: center;
    justify-content: space-between; /* Space between left, center, and right sections */
    width: 100%; /* Full width header area */
    height: 3.5rem; /* Fixed height for the header area */
    background-color: var(--body-bg); /* Match body background */
    box-shadow: 0 2px 4px rgba(0,0,0,0.15); /* Enhanced shadow for better separation */
    border-bottom: 1px solid rgba(0,0,0,0.1); /* Subtle border for visual separation */
}

/* Header sections for desktop */
.header-left {
    display: flex;
    align-items: center;
}

.header-center {
    flex: 2; /* Give more space to the center section */
    text-align: center;
    padding: 0 1rem;
}

.header-right {
    display: flex;
    align-items: center;
}

/* Configurator title for desktop */
.configurator-title {
    margin: 0;
    font-size: 1.25rem; /* Increased to match DEKSMART brand size on desktop */
    font-weight: 700; /* Increased to match DEKSMART brand weight */
    color: var(--secondary-color);
}

/* Save button for desktop */
.save-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.save-button:hover {
    background-color: var(--primary-hover-color, #a00a14);
}

.save-button:disabled {
    background-color: var(--disabled-bg, #e9ecef);
    color: var(--disabled-text, #6c757d);
    cursor: not-allowed;
    opacity: 0.6;
}

.save-button:disabled:hover {
    background-color: var(--disabled-bg, #e9ecef);
}

/* Navbar toggler button */
.navbar-toggler {
    background-color: #333; /* Dark gray background like in the image */
    border: none;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2); /* Subtle shadow for better visibility */
    margin-right: 0.75rem; /* Add space between button and brand */
}

/* Header brand styling */
.header-brand {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color); /* Use primary color (red) for DEKSMART */
    text-decoration: none;
    letter-spacing: 1px;
    transition: opacity 0.2s ease;
}

.header-brand:hover {
    opacity: 0.8;
    text-decoration: none;
}

.navbar-toggler:hover {
    opacity: 0.8;
}

.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cpath stroke='rgba(255, 255, 255, 0.9)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
    background-size: 100% 100%;
}

/* Menu backdrop for click-outside closing */
.menu-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 998;
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@media (max-width: 641px) {
    .top-row {
        justify-content: space-between;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }

    main {
        padding: 0.5rem 0;
        margin-top: 0; /* No margin on mobile */
    }

    /* Create a consistent header area for the menu button and brand */
    .menu-toggle {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1002; /* Increased z-index to ensure it's above the sidebar */
        margin: 0; /* Remove any margin */
        width: 100%; /* Full width header area */
        height: 3.5rem; /* Fixed height for the header area */
        background-color: var(--body-bg); /* Match body background */
        display: flex;
        align-items: center;
        justify-content: space-between; /* Space between left, center, and right sections */
        padding: 0 0.5rem; /* Padding on both sides */
        box-shadow: 0 2px 4px rgba(0,0,0,0.15); /* Enhanced shadow for better separation */
        border-bottom: 1px solid rgba(0,0,0,0.1); /* Subtle border for visual separation */
    }

    /* Header sections */
    .header-left {
        display: flex;
        align-items: center;
    }

    .header-center {
        flex: 2; /* Give more space to the center section */
        text-align: center;
        padding: 0 0.5rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .header-right {
        display: flex;
        align-items: center;
    }

    /* Configurator title */
    .configurator-title {
        margin: 0;
        font-size: 1.1rem; /* Increased to match DEKSMART brand size on mobile */
        font-weight: 700; /* Increased to match DEKSMART brand weight */
        color: var(--secondary-color);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Save button */
    .save-button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 0.4rem 0.75rem;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .save-button:hover {
        background-color: var(--primary-hover-color, #a00a14);
    }

    .save-button:disabled {
        background-color: var(--disabled-bg, #e9ecef);
        color: var(--disabled-text, #6c757d);
        cursor: not-allowed;
        opacity: 0.6;
    }

    .save-button:disabled:hover {
        background-color: var(--disabled-bg, #e9ecef);
    }

    /* Mobile-specific header brand styling */
    .menu-toggle .header-brand {
        font-size: 1.1rem; /* Slightly smaller on mobile */
        margin-left: 0.5rem;
    }

    /* Ensure content starts below the menu button area */
    .content {
        padding: 0 0.5rem;
        margin-top: 0; /* Remove margin as we're handling spacing in main */
    }

    /* Mobile sidebar styles */
    .sidebar {
        position: fixed;
        width: 85%;
        max-width: 300px;
        height: calc(100vh - var(--dynamic-header-height, 0px));
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out, top 0.1s ease-out, height 0.1s ease-out;
        background-color: white;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        top: var(--dynamic-header-height, 0px);
        left: 0;
    }

    .sidebar.show {
        transform: translateX(0);
        overflow: hidden; /* Prevent scrollbar on the sidebar container */
    }

    /* Adjust page layout for mobile */
    .page {
        padding-top: 0; /* Remove padding from page */
    }
}

