﻿using System.Net.Http.Json;
using System.Web;
using Deksmart.Infrastructure.Cache;
using Deksmart.Application.Resource;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;

namespace Deksmart.Application.Service.Http
{
    public interface IEshopApiService
    {
        Task<ApiResult<List<EshopProduct>>> GetEshopProductsAsync(List<string> codes);
        Task<ApiResult<List<EshopProductUnit>>> GetEshopProductUnitsAsync(List<string> codes);
        Task<ApiResult<List<EshopProductPricing>>> GetEshopProductPricingAsync(List<string> codes);
        Task<ApiResult<List<List<EshopProductSpecItem>>>> GetEshopProductParametersAsync(List<string> codes);
        Task<ApiResult<List<EshopProductUnitConversion>>> GetProductUnitConversionsAsync(List<string> codes, List<decimal> quantities, List<string> unitInputs, List<string> unitOutputs);
        Task<ApiResult<ProductDetails>> GetProductDetailsAsync(string code);
    }

    public class EshopApiService : BaseHttpService, IEshopApiService
    {
        private readonly IConfiguratorCacheManager _cacheManager;
        private const string UnitConversionEndpoint = "get-product-unit-conversion-deksmart-list";

        public EshopApiService(
            IHttpClientFactory httpClientFactory, 
            IConfiguratorCacheManager cacheManager) 
            : base(httpClientFactory, "EShop")
        {
            _cacheManager = cacheManager;
        }

        private async Task<ApiResult<List<T>>> GetCachedDataAsync<T>(
            List<string> codes,
            string endpoint,
            string errorString,
            int cacheMinutes = 60)
        {
            try
            {
                var data = await _cacheManager.GetOrAddListByItemAndSaveAsync(
                    codes,
                    async (missingCodes) => {
                        var result = await GetDataAsync<T>(endpoint, missingCodes);
                        return result.Validation.HasErrors ? new List<T>() : result.Data;
                    },
                    minutes: cacheMinutes);

                return ApiResult<List<T>>.Success(data);
            }
            catch (Exception ex)
            {
                return ApiResult<List<T>>.Error(string.Format(errorString, ex.Message));
            }
        }

        public Task<ApiResult<List<EshopProduct>>> GetEshopProductsAsync(List<string> codes)
            => GetCachedDataAsync<EshopProduct>(codes, "get-product-detail-deksmart-list", DeksmartApplicationResource.FailedToGetProducts);

        public Task<ApiResult<List<EshopProductUnit>>> GetEshopProductUnitsAsync(List<string> codes)
            => GetCachedDataAsync<EshopProductUnit>(codes, "get-product-unit-deksmart-list", DeksmartApplicationResource.FailedToGetProductUnits);

        public Task<ApiResult<List<EshopProductPricing>>> GetEshopProductPricingAsync(List<string> codes)
            => GetCachedDataAsync<EshopProductPricing>(codes, "get-product-price-deksmart-list", DeksmartApplicationResource.FailedToGetProductPricing);

        public Task<ApiResult<List<List<EshopProductSpecItem>>>> GetEshopProductParametersAsync(List<string> codes)
            => GetCachedDataAsync<List<EshopProductSpecItem>>(codes, "get-product-parameter-deksmart-list", DeksmartApplicationResource.FailedToGetProductParameters);

        public async Task<ApiResult<List<EshopProductUnitConversion>>> GetProductUnitConversionsAsync(List<string> codes, List<decimal> quantities, List<string> unitInputs, List<string> unitOutputs)
        {
            try
            {
                if (codes.Count != quantities.Count || codes.Count != unitInputs.Count || codes.Count != unitOutputs.Count)
                {
                    return ApiResult<List<EshopProductUnitConversion>>.Error(string.Format(DeksmartApplicationResource.FailedToGetUnitConversions, DeksmartApplicationResource.UnitConversionParameterArrayLengthMismatch));
                }

                var result = await GetUnitConversionsDataAsync(codes, quantities, unitInputs.Select(d => d.Trim()).ToList(), unitOutputs.Select(d => d.Trim()).ToList());
                return result;
            }
            catch (Exception ex)
            {
                return ApiResult<List<EshopProductUnitConversion>>.Error(string.Format(DeksmartApplicationResource.FailedToGetUnitConversions, ex.Message));
            }
        }

        private async Task<ApiResult<List<EshopProductUnitConversion>>> GetUnitConversionsDataAsync(List<string> codes, List<decimal> quantities, List<string> unitInputs, List<string> unitOutputs)
        {
            try
            {
                if (codes.Count == 0)
                {
                    return ApiResult<List<EshopProductUnitConversion>>.Error(DeksmartApplicationResource.NoProductCodesProvided);
                }

                var queryParams = new List<string>();
                for (int i = 0; i < codes.Count; i++)
                {
                    queryParams.Add($"codes={HttpUtility.UrlEncode(codes[i])}");
                    queryParams.Add($"quantities={quantities[i].ToString(System.Globalization.CultureInfo.InvariantCulture)}");
                    queryParams.Add($"unitInputs={HttpUtility.UrlEncode(unitInputs[i])}");
                    queryParams.Add($"unitOutputs={HttpUtility.UrlEncode(unitOutputs[i])}");
                }

                var queryString = string.Join("&", queryParams);
                var uriBuilder = new UriBuilder(new Uri(_httpClient.BaseAddress!, $"{BaseEndpoint}/{UnitConversionEndpoint}"))
                {
                    Query = queryString
                };

                var uri = uriBuilder.Path + uriBuilder.Query;
                var response = await _httpClient.GetAsync(uri);

                if (!response.IsSuccessStatusCode)
                {
                    var error = await response.Content.ReadAsStringAsync();
                    return ApiResult<List<EshopProductUnitConversion>>.Error(string.Format(DeksmartApplicationResource.ApiReturnedStatusCodeError, response.StatusCode, error));
                }

                var data = await response.Content.ReadFromJsonAsync<List<EshopProductUnitConversion>>();
                if (data == null || data.Count != codes.Count)
                {
                    return ApiResult<List<EshopProductUnitConversion>>.Error(string.Format(DeksmartApplicationResource.ApiReturnedItemCountMismatch, data?.Count ?? 0, codes.Count));
                }

                // Populate ProductCode property to match with the input order
                for (int i = 0; i < data.Count; i++)
                {
                    data[i].ProductCode = codes[i];
                }

                return ApiResult<List<EshopProductUnitConversion>>.Success(data);
            }
            catch (HttpRequestException ex)
            {
                return ApiResult<List<EshopProductUnitConversion>>.Error(string.Format(DeksmartApplicationResource.NetworkErrorWhileFetchingData, UnitConversionEndpoint, ex.Message));
            }
            catch (Exception ex)
            {
                return ApiResult<List<EshopProductUnitConversion>>.Error(string.Format(DeksmartApplicationResource.UnexpectedErrorWhileFetchingData, UnitConversionEndpoint, ex.Message));
            }
        }

        public async Task<ApiResult<ProductDetails>> GetProductDetailsAsync(string code)
        {
            try
            {
                if (string.IsNullOrEmpty(code))
                {
                    return ApiResult<ProductDetails>.Error(DeksmartApplicationResource.ProductCodeRequired);
                }

                var codes = new List<string> { code };
                var productTask = GetEshopProductsAsync(codes);
                var specsTask = GetEshopProductParametersAsync(codes);

                await Task.WhenAll(productTask, specsTask);

                var productResult = await productTask;
                var specsResult = await specsTask;

                if (productResult.Validation.HasErrors)
                {
                    return ApiResult<ProductDetails>.Error(
                        string.Format(DeksmartApplicationResource.FailedToGetProductDetails, productResult.Validation.GetErrors()));
                }

                if (specsResult.Validation.HasErrors)
                {
                    return ApiResult<ProductDetails>.Error(
                        string.Format(DeksmartApplicationResource.FailedToGetProductSpecifications, specsResult.Validation.GetErrors()));
                }

                if (!productResult.Data.Any() || !specsResult.Data.Any() || productResult.Data.All(d => d is null))
                {
                    return ApiResult<ProductDetails>.Error(DeksmartApplicationResource.ProductNotFound);
                }

                return ApiResult<ProductDetails>.Success(new ProductDetails(
                    productResult.Data[0],
                    specsResult.Data[0]
                ));
            }
            catch (Exception ex)
            {
                return ApiResult<ProductDetails>.Error(
                    string.Format(DeksmartApplicationResource.FailedToGetProductDetails, ex.Message));
            }
        }
    }
}
