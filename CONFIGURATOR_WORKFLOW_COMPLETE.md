# Complete Configurator Workflow Documentation

## Overview
This document describes the complete configurator processing workflow in the Deksmart ASP system, including all paths, data flows, and aggregation logic.

## Configurator Types

### 1. Simple Configurator
- Single configurator with products
- Direct field processing and product selection
- Basic price calculation

### 2. Composite Configurator  
- Parent configurator containing child configurators
- Always uses Flow C for live updates
- Flow B only used for presets/static scenarios

## Main Entry Points

### 1. `GetFilteredConfiguratorAsync`
- **Purpose**: Standard configurator processing (simple configurators)
- **Input**: ConfiguratorId + ConfiguratorStateDto
- **Flow**: Uses Flow A for simple, Flow B for composite (presets only)

### 2. `ProcessCompositeConfiguratorAsync`
- **Purpose**: Live composite configurator processing
- **Input**: ConfiguratorId + CompositeConfiguratorStateDto
- **Returns**: Both processed active child + composite summary
- **Always uses Flow C**

## Core Processing Flows

### Flow A: Simple Configurator Processing
**Used for**: Single configurators, individual child configurators
```
GetFilteredConfiguratorEntityAsync (if no child states)
↓
ProcessConfiguratorAsync
├── MatchParametersAsync (field values)
├── CalculateAndFilterFields
├── FilterProducts  
├── SelectProducts
├── SetProductFieldsAsync
├── CalculatePricesForProductsAsync
└── ValidateProducts
```

### Flow B: Saved Composite Preset Loading
**Used for**: Loading saved composite configurators via `GetConfiguratorByPreset` endpoint
**The only way composite can have ChildConfiguratorStates in GetFilteredConfiguratorAsync**
```
GetConfiguratorByPreset (loads saved composite state from DB)
↓
GetFilteredConfiguratorAsync (with ChildConfiguratorStates from DB)
↓
GetFilteredConfiguratorEntityAsync (has child states)
↓
For each child state:
├── ProcessConfiguratorAsync (Process saved child)
└── Add to ChildUserConfigurators
↓
AggregateAndCalculateProductsAsync
├── AggregateChildConfiguratorProducts
└── CalculatePricesForProductsAsync
```

### Flow C: Live Composite Updates (Main Flow)
**Used for**: All live composite configurator updates in UI
**This is where UserAmount issues occur**
```
UI Update Composite Configurator
↓
ProcessCompositeConfiguratorAsync
├── Step 1: Process Active Child (the one being updated)
│   └── GetFilteredConfiguratorAsync (active child) [Uses Flow A]
├── Step 2: Build Composite Summary
│   ├── Convert active child products to ProductForAggregationDto
│   ├── Add other child products (OtherChildProducts from UI state)
│   └── AggregateFromChildProductStatesAsync
│       └── AggregateProductsByCode ← UserAmount logic issue here
└── Return: ProcessedActiveChild + CompositeConfiguratorSummary
```

## Product Aggregation Methods (Refactored Architecture)

### 1. `AggregateChildConfiguratorProducts`
- **Used by**: Flow B (composite with child configurators)
- **Input**: List<Configurator> childConfigurators
- **Logic**: Groups products by ProductCode, creates combined compositions
- **Helper Methods**: Uses `FindOrCreateComposition` and `CreateAggregatedProduct`
- **Output**: List<ConfiguratorComposition> with aggregated products

### 2. `AggregateFromChildProductStatesAsync`
- **Used by**: Flow C (composite with active child + other products)
- **Input**: List<ChildProductStateDto> childProductStates
- **Logic**: 
  1. Convert all products to ConfiguratorProduct
  2. Call `ConvertProductsToSalesUnitsAsync` for unit conversions
  3. Call `AggregateProductsByCode` with converted products
  4. Use `CalculateProductPricesAsync` for pricing
- **Output**: Aggregated compositions in the composite configurator

### 3. `AggregateProductsByCode`
- **Used by**: `AggregateFromChildProductStatesAsync`
- **Core Logic**: Groups by ProductCode, applies UserAmount priority logic
- **Helper Methods**: Uses `FindOrCreateComposition` and `CreateAggregatedProduct`
- **UserAmount Rules**:
  - Products WITHOUT UserAmount: Sum converted amounts → convert to packages
  - Products WITH UserAmount: Sum UserAmounts directly (already packages)
  - Final: Add calculated packages + user packages
  - CalculatedAmount: ALWAYS sum original amounts

## Helper Methods (Refactored)

### 1. `FindOrCreateComposition`
- **Purpose**: Finds existing composition by normalized title or creates new one
- **Logic**: Case/whitespace insensitive composition grouping
- **Used by**: Both `AggregateChildConfiguratorProducts` and `AggregateProductsByCode`

### 2. `CreateAggregatedProduct`
- **Purpose**: Creates aggregated product by summing amounts from products with same ProductCode
- **Logic**: Implements UserAmount priority logic with proper package calculation
- **UserAmount Priority**: When user sets ANY part of a product, calculates total packages correctly
- **Used by**: Both aggregation methods for consistent product creation

### 3. `CalculateProductPricesAsync`
- **Purpose**: Calculates prices for products and sets configurator totals
- **Logic**: Handles empty product lists, gets enrichment data, calculates prices
- **Used by**: All pricing operations across different flows

### 4. `ConvertProductsToSalesUnitsAsync`
- **Purpose**: Converts products to sales units using convert-first-then-sum logic
- **Logic**: Batch unit conversions, create converted products with tuple structure
- **Architecture**: Uses `List<(ConfiguratorProduct Product, string CompositionTitle)>` to prevent data corruption
- **Used by**: `AggregateFromChildProductStatesAsync` for unit standardization

### 5. `GetProductEnrichmentDataAsync`
- **Purpose**: Gets pricing, units, and conversion data for products
- **Logic**: Batch API calls, fallback handling for conversion failures
- **Used by**: All pricing calculation operations

### 6. `GetUnitConversionsAsync`
- **Purpose**: Handles unit conversions with different product types
- **Logic**: Supports both SelectedProductDto and ConfiguratorProduct types
- **Used by**: Multiple conversion scenarios

## Data Transfer Objects

### ConfiguratorStateDto
```csharp
{
    FieldValues: List<ClientFieldValueDto>
    SelectedProducts: List<ClientProductValueDto>  // ProductId + Amount
    CategoryStates: List<CategoryStateDto>
    ChildConfiguratorStates: List<ChildConfiguratorStateDto>
}
```

### CompositeConfiguratorStateDto
```csharp
{
    ActiveChildState: ConfiguratorStateDto          // Child to process normally
    OtherChildProducts: List<ChildProductStateDto>  // Pre-processed products
}
```

### ChildProductStateDto
```csharp
{
    ConfiguratorId: int
    SelectedProducts: List<ProductForAggregationDto>
}
```

### ProductForAggregationDto
```csharp
{
    ProductCode: string
    CalculatedAmount: decimal     // Original amount (sales units)
    UserAmount: decimal?          // User override (package units)
    ProductUnit: string           // Sales unit
    Title: string
    CompositionTitle: string
}
```

## Key Business Rules

### UserAmount Priority Logic (IMPLEMENTED & WORKING)
1. **UserAmount Definition**: User-defined quantity in package units
2. **CalculatedAmount Definition**: System-calculated quantity in sales units  
3. **Priority**: When UserAmount exists, it takes precedence for package calculations
4. **Summation**: CalculatedAmount always sums ALL original amounts, UserAmount affects only PackageQuantity
5. **Implementation**: 
   - Products WITHOUT UserAmount: Sum amounts → Math.Ceiling(sum / UnitsInPackage)
   - Products WITH UserAmount: Sum UserAmounts directly (already packages)
   - Final UserAmount: calculatedPackages + totalUserPackages
6. **PackageQuantity vs UserPackageQuantity**:
   - **PackageQuantity**: Pure system recommendation (ignores user input)
   - **UserPackageQuantity**: User's final decision (with UserAmount priority logic)

### Unit Conversion Flow
1. **Input**: Products with various units (m, m², ks, etc.)
2. **Conversion**: API converts to sales units (UnitSales)
3. **Package Calculation**: Sales units → packages using UnitsInPackage
4. **UserAmount**: Bypass conversion, use directly as packages

### Composition Aggregation
1. **Group by ProductCode**: Products with same code are always aggregated
2. **Composition Titles**: Combined when products from different compositions
3. **Normalization**: Case/whitespace insensitive composition grouping

## Error Handling & Validation

### Validation Points
- Configurator existence
- Field value matching
- Product selection validation
- API call failures (pricing, units, conversions)
- Business rule validation

### Soft vs Hard Errors
- **Hard Errors**: Stop processing, return validation errors
- **Soft Errors**: Mark products as invalid, continue with others

## API Dependencies

### EshopApiService Calls
1. **GetProductDetailsAsync**: Product specifications for field components
2. **GetEshopProductPricingAsync**: Pricing data for all products
3. **GetEshopProductUnitsAsync**: Unit information (sales, package, conversion ratios)
4. **GetProductUnitConversionsAsync**: Convert amounts between units

### Caching Strategy
- **ConfiguratorCacheManager**: Cache complete configurator entities
- **Repository Pattern**: Cached field values, multiple choice options

## Performance Considerations

### Batch Operations
- API calls are batched for multiple products
- Unit conversions handle duplicate product codes efficiently
- Parallel processing where possible

### Optimization Patterns
- Lookup dictionaries for unit information
- Distinct product codes for API calls
- Reuse conversion results for same products

## Testing Strategy

### Unit Tests
- Mock all external dependencies (EshopApiService)
- Test each aggregation method independently
- Verify UserAmount priority logic with edge cases

### Integration Tests
- Test complete flows end-to-end
- Verify API integration
- Test error scenarios

## Common Issues & Solutions

### 1. UserAmount Priority Logic (RESOLVED)
- **Symptom**: PackageQuantity was showing only UserAmount, ignoring calculated amounts
- **Root Cause**: Aggregation logic wasn't properly adding calculated + user packages
- **Resolution**: Fixed in `CreateAggregatedProduct` helper method with proper UserAmount priority logic
- **Current Status**: ✅ **WORKING CORRECTLY** - UserAmount priority logic implemented and tested

### 2. Unit Conversion Failures
- **Symptom**: Wrong quantities or errors
- **Solution**: Fallback to legacy conversion, log API failures

### 3. Missing Products
- **Symptom**: Products not appearing in results
- **Causes**: Filtering, selection, or validation issues

### 4. Performance Issues
- **Symptom**: Slow response times
- **Solutions**: Check API batching, caching effectiveness

## Debugging Guidelines

### Logging Points
- Method entry/exit with key parameters
- API call results and timing
- Aggregation intermediate results
- Validation errors and warnings

### Key Metrics to Monitor
- Number of products being aggregated
- API call counts and timing
- Cache hit/miss ratios
- Validation error frequencies

## Recent Critical Fixes

### Data Corruption Bug Resolution (Commits: 686809b, cf0e340)
1. **Fixed Terrible Programming Pattern**:
   ```csharp
   // BEFORE (broken):
   product.PackageUnit = selectedProduct.CompositionTitle; // Data corruption!
   
   // AFTER (fixed):
   List<(ConfiguratorProduct Product, string CompositionTitle)> // Proper separation
   ```

2. **Fixed Missing Products in Aggregation**:
   ```csharp
   // BEFORE (broken):
   var product = products.Cast<ConfiguratorProduct>().First(p => p.ProductCode == productCode);
   
   // AFTER (fixed):
   var product = products.Cast<ConfiguratorProduct>().ElementAt(i);
   ```

3. **Fixed Active Configurator UserAmount Mapping**:
   ```csharp
   // BEFORE (broken - ConfiguratorService.cs:296):
   UserAmount = null,
   
   // AFTER (fixed):
   UserAmount = p.UserPackageQuantity,
   ```

4. **Fixed Divide by Zero in Tests**:
   ```csharp
   // BEFORE (broken):
   new EshopProductUnit { UnitSales = "m" }, // UnitsInPackage defaults to 0
   
   // AFTER (fixed):
   new EshopProductUnit { UnitSales = "m", UnitsInPackage = 1.0m },
   ```

### Integration Test Coverage Added
- End-to-end UserAmount priority logic verification
- Complete API → Service → Domain → Infrastructure testing
- 4-product UserAmount scenario with proper package calculations
- Verification that all layers work together correctly

## Future Enhancements

### Immediate Improvements
- Better error handling for partial failures
- More sophisticated caching strategies
- Performance monitoring and alerting
- Enhanced logging and debugging tools

### Event-Driven Architecture for Real-Time Updates

#### Current State (Request-Response Pattern)
The current configurator system uses traditional request-response patterns for **single-user, full-recalculation** scenarios:
```
User Input → API Call → Full Recalculation → Complete New Configurator → UI Reset
```

**Current Architecture Challenges**:
- **Unpredictable Changes**: Any field change can transform entire configurator structure
- **Full Recalculation Required**: Cannot predict what will change, so recalculate everything
- **Complete Object Replacement**: New configurator object created, entire UI tree rebuilt
- **Latency Issues**: Full round-trip processing for every change impacts user experience
- **No Change Smoothness**: User waits for complete recalculation before seeing any updates

**Note**: This is a **single-user application** - no collaboration needed. The challenge is **speed and smoothness** of changes, not multi-user coordination.

#### Proposed Event-Driven Architecture for Single-User Speed Optimization

**Core Insight**: Since we must do full recalculation anyway (unpredictable cascading changes), the goal is **immediate UI feedback** and **background processing** to minimize perceived latency.

##### 1. Immediate UI Feedback with Background Processing
```csharp
public class ConfiguratorHub : Hub
{
    public async Task StartFieldChange(int configuratorId, int fieldId, string newValue)
    {
        // Immediate feedback to UI
        await Clients.Caller.SendAsync("FieldChangeStarted", fieldId, newValue);
        
        // Start background processing
        _ = Task.Run(async () =>
        {
            var result = await _configuratorService.ProcessConfiguratorAsync(configuratorId, fieldChange);
            await Clients.Caller.SendAsync("ConfiguratorRecalculated", result);
        });
    }
}
```

##### 2. Progressive Loading Events
```csharp
public class ConfiguratorProgressEvent
{
    public int ConfiguratorId { get; set; }
    public string Stage { get; set; } // "FieldsProcessed", "ProductsCalculated", "PricesCalculated"
    public int Progress { get; set; } // 0-100
    public string Message { get; set; } // "Calculating prices for 15 products..."
}

public class ConfiguratorRecalculationService
{
    private readonly IHubContext<ConfiguratorHub> _hubContext;
    
    public async Task<Configurator> ProcessWithProgressAsync(int configuratorId, FieldChange change)
    {
        await NotifyProgress(configuratorId, "FieldsProcessed", 20, "Processing field dependencies...");
        
        var configurator = await ProcessFieldsAsync(configuratorId, change);
        
        await NotifyProgress(configuratorId, "ProductsCalculated", 60, "Calculating product quantities...");
        
        await ProcessProductsAsync(configurator);
        
        await NotifyProgress(configuratorId, "PricesCalculated", 90, "Finalizing prices...");
        
        await CalculatePricesAsync(configurator);
        
        await NotifyProgress(configuratorId, "Complete", 100, "Ready");
        
        return configurator;
    }
    
    private async Task NotifyProgress(int configuratorId, string stage, int progress, string message)
    {
        await _hubContext.Clients.All.SendAsync("ConfiguratorProgress", new ConfiguratorProgressEvent
        {
            ConfiguratorId = configuratorId,
            Stage = stage,
            Progress = progress,
            Message = message
        });
    }
}
```

##### 3. Optimistic UI Updates with Rollback
```csharp
public class OptimisticUpdateService
{
    public async Task<ConfiguratorUpdateResult> ProcessOptimisticChange(
        ConfiguratorWrapper currentWrapper, 
        FieldChange change)
    {
        // 1. Apply change immediately to UI (optimistic)
        var optimisticWrapper = ApplyOptimisticChange(currentWrapper, change);
        
        // 2. Start background recalculation
        var recalculationTask = _configuratorService.ProcessConfiguratorAsync(change);
        
        // 3. Return immediate result for UI
        return new ConfiguratorUpdateResult
        {
            OptimisticWrapper = optimisticWrapper,
            RecalculationTask = recalculationTask,
            ChangeId = Guid.NewGuid() // For rollback if needed
        };
    }
    
    private ConfiguratorWrapper ApplyOptimisticChange(ConfiguratorWrapper wrapper, FieldChange change)
    {
        // Apply only the direct field change, don't recalculate dependencies
        var optimisticWrapper = wrapper.Clone();
        optimisticWrapper.UpdateFieldValue(change.FieldId, change.NewValue);
        
        // Maybe show "calculating..." indicators on dependent fields
        optimisticWrapper.MarkDependentFieldsAsCalculating(change.FieldId);
        
        return optimisticWrapper;
    }
}
```

##### 4. Background Processing Queue
```csharp
public class ConfiguratorProcessingQueue
{
    private readonly Channel<ConfiguratorRecalculationRequest> _queue;
    private readonly IHubContext<ConfiguratorHub> _hubContext;
    
    public async Task QueueRecalculation(int configuratorId, FieldChange change, string connectionId)
    {
        // Debouncing: if user makes rapid changes, only process the latest
        var request = new ConfiguratorRecalculationRequest
        {
            ConfiguratorId = configuratorId,
            Change = change,
            ConnectionId = connectionId,
            RequestTime = DateTime.UtcNow
        };
        
        await _queue.Writer.WriteAsync(request);
    }
    
    private async Task ProcessQueue()
    {
        await foreach (var request in _queue.Reader.ReadAllAsync())
        {
            // Debounce: only process if no newer request for same configurator
            if (IsLatestRequest(request))
            {
                var result = await _configuratorService.ProcessConfiguratorAsync(request);
                await _hubContext.Clients.Client(request.ConnectionId)
                    .SendAsync("ConfiguratorRecalculated", result);
            }
        }
    }
}
```

#### Honest Assessment: Event-Driven vs Current Request-Response

**Current System Already Provides**:
- ✅ Immediate UI updates (optimistic)
- ✅ Background processing (async HTTP calls)
- ✅ Smart debouncing
- ✅ Non-blocking UI
- ✅ Calculation cancellation

**What Event-Driven Would Actually Add**:

##### 1. Real-Time Progress Updates (The Main Benefit)
```typescript
// Current: No progress visibility during calculation
await httpClient.PostAsync("process-configurator", data); // Black box

// Event-Driven: Progressive updates
connection.on("ConfiguratorProgress", (progress) => {
    updateProgressBar(progress.progress, progress.message);
    // "Processing fields... 25%"
    // "Calculating products... 60%" 
    // "Finalizing prices... 90%"
});
```

##### 2. Better Error Handling During Calculation
```typescript
// Current: Wait for entire calculation to fail
try {
    await processConfigurator(data);
} catch (error) {
    // Only know it failed at the end
}

// Event-Driven: Fail fast with context
connection.on("ConfiguratorError", (error) => {
    // "Failed during product calculation: Invalid wall height"
    // User knows exactly what went wrong and when
});
```

##### 3. Calculation Interruption (Marginal Benefit)
```typescript
// Current: HTTP request cancellation (already possible)
const controller = new AbortController();
await fetch("/process", { signal: controller.signal });
controller.abort(); // Already works

// Event-Driven: More granular control
connection.invoke("CancelCalculation", configuratorId);
// Slightly cleaner, but not revolutionary
```

##### 4. Persistent Connection Benefits
- **Faster subsequent requests** (no HTTP handshake overhead)
- **Server can push updates** (e.g., "Material X is out of stock")
- **Connection state management** (detect disconnections)

**The Brutal Truth**: For your single-user, full-recalculation scenario, event-driven architecture provides **incremental improvements**, not revolutionary changes.

**When Event-Driven Would Be Worth It**:
1. **Long calculations** (>5 seconds) where progress feedback is valuable
2. **External data integration** (live material prices, stock levels)
3. **Server-initiated updates** (system maintenance notifications)
4. **Multiple simultaneous calculations** (background prefetching)

**When to Stick with Current Approach**:
1. **Calculations are fast** (<2 seconds typically)
2. **HTTP overhead is minimal** compared to calculation time
3. **Progress feedback isn't critical** for user experience
4. **System already works well** and users are satisfied

#### **Decision: Stick with Current Request-Response Approach**

After analysis, **we decided to maintain the current request-response architecture** because:

1. **Current system already provides the key benefits**:
   - ✅ Optimistic UI updates (immediate field changes)
   - ✅ Background processing (non-blocking HTTP calls)
   - ✅ Smart debouncing (request cancellation)
   - ✅ Smooth user experience

2. **Event-driven would only add**:
   - Progress indicators during calculation
   - Slightly better error context
   - Minimal performance gains

3. **Current approach advantages**:
   - **Simpler architecture** - No WebSocket state management
   - **Better reliability** - HTTP is more resilient than persistent connections
   - **Easier debugging** - Standard HTTP request/response patterns
   - **Less complexity** - No SignalR infrastructure needed
   - **Works well** - Users are satisfied with current performance

**Conclusion**: The incremental benefits of event-driven architecture don't justify the added complexity for our single-user, deterministic recalculation system. The current approach effectively delivers the required user experience.

#### ~~Implementation Phases for Single-User Performance~~ (NOT IMPLEMENTED - DECIDED AGAINST)

**Note**: These implementation phases were considered but **not pursued** due to the decision to maintain the current request-response architecture.

~~##### Phase 1: Basic Non-Blocking Infrastructure (2-3 weeks)~~
~~##### Phase 2: Advanced Responsiveness (3-4 weeks)~~
~~##### Phase 3: Performance Optimization (2-3 weeks)~~
~~##### Phase 4: Enhanced User Experience (2-3 weeks)~~

**Rationale**: Current system already provides adequate performance and user experience without the added complexity of event-driven architecture.

#### Technical Considerations

##### 1. Event Store Design
```csharp
public class ConfiguratorEventStore
{
    public async Task AppendEventAsync<T>(T domainEvent) where T : IDomainEvent
    {
        // Store event for audit trail and potential replay
        var eventData = JsonSerializer.Serialize(domainEvent);
        await _context.ConfiguratorEvents.AddAsync(new ConfiguratorEvent
        {
            EventType = typeof(T).Name,
            EventData = eventData,
            ConfiguratorId = domainEvent.ConfiguratorId,
            Timestamp = DateTime.UtcNow
        });
    }
}
```

##### 2. Connection Management
```csharp
public class ConfiguratorConnectionManager
{
    private readonly ConcurrentDictionary<string, ConfiguratorSession> _sessions = new();
    
    public async Task HandleUserConnected(string connectionId, int configuratorId, string userId)
    {
        _sessions[connectionId] = new ConfiguratorSession
        {
            ConfiguratorId = configuratorId,
            UserId = userId,
            ConnectedAt = DateTime.UtcNow
        };
        
        // Notify other users someone joined
        await NotifyUserJoined(configuratorId, userId);
    }
}
```

##### 3. Conflict Resolution
```csharp
public class ConfiguratorConflictResolver
{
    public async Task<ConflictResolution> ResolveFieldConflict(
        int fieldId, 
        string value1, 
        string value2, 
        string user1, 
        string user2)
    {
        // Implement business rules for conflict resolution
        // E.g., last writer wins, user priority, manual resolution
        return new ConflictResolution
        {
            ResolvedValue = value2, // Last writer wins
            RequiresUserInput = false,
            NotifyUsers = new[] { user1 } // Notify the user whose change was overridden
        };
    }
}
```

#### Example User Scenarios for Single-User Performance

##### Scenario 1: Smooth Field Changes in Complex Configurator
```
1. User opens composite configurator with complex dependencies
2. User changes room height from 2.5m to 3.0m
3. UI immediately shows:
   - Field value updates to 3.0m instantly
   - "Recalculating..." indicator appears
   - Progress bar shows "Processing field dependencies... 20%"
4. Background calculation runs:
   - Updates wall area calculations
   - Recalculates material quantities  
   - Adjusts pricing for all affected products
   - Updates composite totals
5. User sees progressive updates:
   - "Calculating product quantities... 60%"
   - "Finalizing prices... 90%"
   - Complete new configurator replaces optimistic version
6. Total time: <2 seconds, but user never waits
```

##### Scenario 2: Rapid Consecutive Changes (Debouncing) - ALREADY WORKS WITH CURRENT APPROACH
```
1. User adjusts room width slider rapidly: 4.0m → 4.2m → 4.5m → 4.8m
2. UI shows each change immediately (optimistic updates) ✅ CURRENT
3. System intelligently debounces:
   - Cancels calculation for 4.0m, 4.2m, 4.5m ✅ CURRENT
   - Only processes final value 4.8m ✅ CURRENT
4. User sees smooth slider movement without calculation lag ✅ CURRENT
5. Single background calculation runs for final value ✅ CURRENT
6. Result arrives and replaces optimistic state ✅ CURRENT
```

**Reality Check**: This scenario describes your EXISTING functionality perfectly!

##### Scenario 3: Complex Composite Configurator Updates
```
1. User changes flooring type in child configurator 1
2. UI immediately updates the field value
3. Background processing shows progress:
   - "Updating flooring products... 25%"
   - "Recalculating child configurator 1... 50%"
   - "Updating composite summary... 75%"
   - "Finalizing totals... 100%"
4. Entire composite configurator updates with new:
   - Product selections in child 1
   - Updated pricing across all children
   - New composite totals
5. User experiences smooth, non-blocking workflow
```

#### Migration Strategy

##### 1. Backward Compatibility
- Keep existing REST endpoints functional
- Add WebSocket as enhancement, not replacement
- Gradual migration of UI components

##### 2. Feature Flags
```csharp
public class ConfiguratorFeatureFlags
{
    public bool EnableRealTimeUpdates { get; set; } = false;
    public bool EnableCollaborativeEditing { get; set; } = false;
    public bool EnableIncrementalCalculation { get; set; } = false;
}
```

##### 3. A/B Testing
- Test performance with small user groups
- Compare user engagement metrics
- Monitor server resource usage

This event-driven approach would transform the configurator from a traditional web application into a modern, collaborative, real-time tool similar to Google Docs or Figma, but for construction product configuration.