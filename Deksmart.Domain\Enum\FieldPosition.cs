namespace Deksmart.Domain.Enum
{
    /// <summary>
    /// Defines the position of a configurator field within a category.
    /// Specifies where the field is displayed in the configurator UI (e.g., left, right, full width, etc.).
    /// </summary>
    public enum FieldPosition : short
    {
        /// <summary>
        /// The field is displayed on the left side.
        /// </summary>
        Left = 0,
        /// <summary>
        /// The field is displayed on same line as the left field (must have same order), after the left field.
        /// </summary>
        LeftSuffix = 1,
        /// <summary>
        /// The field is displayed on the right side.
        /// </summary>
        Right = 2,
        /// <summary>
        /// The field is displayed on same line as the right field (must have same order), before the right field.
        /// </summary>
        RightSuffix = 3,
        /// <summary>
        /// The field is displayed on the full width of the category.
        /// </summary>
        FullWidth = 4,
        /// <summary>
        /// The field is displayed on same line as the full width field (must have same order), before the full width field.
        /// </summary>
        FullWidthSuffix = 5
    }
} 