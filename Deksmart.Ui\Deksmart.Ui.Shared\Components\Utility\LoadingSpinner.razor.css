.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: var(--loading-spinner-min-height);
    width: 100%;
    position: relative;
}

.loading-spinner {
    width: var(--loading-progress-size);
    height: var(--loading-progress-size);
    margin: 0 auto;
}

.loading-spinner circle {
    fill: none;
    stroke: var(--loading-progress-bg-color);
    stroke-width: var(--loading-progress-stroke-width);
    transform-origin: 50% 50%;
    transform: rotate(-90deg);
}

.loading-spinner circle:last-child {
    stroke: var(--loading-progress-color);
    stroke-dasharray: 125.6, 500%;
    animation: loading-spin 1s linear infinite;
}

.loading-spinner-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    margin-top: 1rem;
    color: var(--loading-progress-text-color);
}

@keyframes loading-spin {
    0% { stroke-dasharray: 0, 500%; }
    50% { stroke-dasharray: 250, 500%; }
    100% { stroke-dasharray: 500, 500%; }
}

/* Loading Spinner Variations */
.loading-container.small .loading-spinner {
    width: calc(var(--loading-progress-size) * 0.5);
    height: calc(var(--loading-progress-size) * 0.5);
}

.loading-container.small .loading-spinner circle {
    stroke-width: calc(var(--loading-progress-stroke-width) * 0.5);
}

.loading-container.large .loading-spinner {
    width: calc(var(--loading-progress-size) * 1.5);
    height: calc(var(--loading-progress-size) * 1.5);
}

.loading-container.large .loading-spinner circle {
    stroke-width: calc(var(--loading-progress-stroke-width) * 1.5);
}

.loading-container.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--loading-spinner-overlay-bg);
    z-index: 1000;
} 