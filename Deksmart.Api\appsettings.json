{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning",
            "Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware": "None",
            "Microsoft.EntityFrameworkCore": "Warning"
        }
    },
    "EshopSettings": {
        "HomepageUrl": "https://www.dek.cz"
    },
    "ApiCore": {
        "Route": {
            "BasePath": "api"
        },
        "Mail": {
            "Host": "testmail.dek.cz",
            "Port": 587,
            "MailFrom": "<EMAIL>",
            "Password": "r2vE3TntU4Rf7d8VjSws",
            "NameFrom": "Eshop DEK",
            "MailTo": "<EMAIL>", //default mail pro logovaní
            "DefaultEmailTo": "<EMAIL>" // Default recipient for configurator inquiries
        },
        "Logger": {
            "MailExceptionEnabled": false,
            //"MailExceptionSubject": "Uncatch exception...",
            "MailExceptionMinutesInterval": 30,
            "MailExceptionTo": [
                "<EMAIL>"
            ],
            "MailStdLogTo": [
                "<EMAIL>"
            ]
        },
        "SqlProfiler": {
            "Enabled": true
        },
        "Swagger": {
            "Enabled": true,
            "ProjectName": "Deksmart Api",
            "Version": "1.0.0"
        },
        "Cache": {
            "Redis": {
                "Enabled": true, // If false -> CacheManager is not registered as a service.
                "ConnectTimeout": 100,
                "Host": "localhost:6379",
                "ReplaceRedisWithDevNullAdapter": true // If true -> Disable Cache without code changes. If you already use CacheManager, you can disable it by setting.
            }
        }
    },
    "Application": {
        "EshopId": "dek_cz",
        "DefaultCulture": "cs-CZ"
    },
    "Mssql": {
        "User": "ESHOP",
        "Password": "ES2007",
        "Database": "TEST_ESHOP", // Database se přepisuje podle property "instance" v JWT, pokud je setnutá. Což v 99% je.
        "Host": "apps-e.dek.cz\\ESHOP2"
    },
    "Pgsql": {
        // Produkce podle Global:EshopId. Devel podle JWT
        "dek_cz": {
            "Host": "postgresqltest.dek.cz",
            "Port": 5432,
            "Database": "eshop_dek_cz",
            "Username": "eshop_dek_cz",
            "Password": "GWdJeFV4",
            "Pooling": true
        },
        "dek_sk": {
            "Host": "postgresqltest.dek.cz",
            "Port": 5432,
            "Database": "eshop_dek_sk",
            "Username": "eshop_dek_sk",
            "Password": "jwJ4Tffj",
            "Pooling": true
        },
        "argos_cz": {
            "Host": "postgresqltest.dek.cz",
            "Port": 5432,
            "Database": "eshop_argos_cz",
            "Username": "eshop_argos_cz",
            "Password": "ST7gRtVn",
            "Pooling": true
        }
    },
    "ConfiguratorDb": {
        "Host": "dev12.dek.cz",
        "Port": 5432,
        "Database": "configurator",
        "Username": "configurator",
        "Password": "dek123",
        "Pooling": true
    },
    "Jwt": {
        "Enabled": true,
        "Expiration": false,
        "AsymmetricKeys": [
            "MIIBigKCAYEAw8wZjc7vItdcx+R4IxzoAJRIhEcfXKNSXcwE8Ty6y7xCg0DAg8HdxrOztI5ntnBQa4DveHvdmwtMV9mcE9NzGt6eU6OdZU32SqYJPuUXr/eCiG9pV2PMzCXAMu9+sXlvKZor7mbkMog4e03pElKedEwxgIujWUMhD0TnUmdU1A5+HnBWv0MlASyevVWBzw3aPF8ybmVxPXbBuxBE5KOIYNxxXii8JCjdzZJ/9owbWAckzB1MSCkWHzQJRx3cUeGYNa2BSWx0CMfW3nHxMX0QKAlvSQSxTYhtvgMBqDPK2zLF9W4VZ4Z0FpZg7IFGod7aOFapyywoslXRgYLKdhALqv2z46/fnnWwQlwwEPfGfX9mkE4Skrxf7cY4EqHVI8yP+WM1oLGN6mqFIUniX+tsTS/7Bfx3SD7VCwx9ykv6WkQXuYMXSfiXQaMfLwSQzchUeveFTMmiWaGBgsP/UDvDZhCfBvdBcRttDMzbtMi3ERg31jXM22VadcCzxCwWcgj9AgMBAAE="
        ],
        //"SymmetricKeys": [
        //    "$ecretf0rt3st$ecretf0rt3st"
        //],
        "Issuers": [
            "web.eshop"
        ],
        "Audiences": [
            "api.cart"
        ]
    },
    "AllowedHosts": "*"
}
