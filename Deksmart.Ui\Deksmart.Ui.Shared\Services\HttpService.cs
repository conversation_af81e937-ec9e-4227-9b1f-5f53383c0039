﻿using System.Net.Http.Json;
using System.Text.Json;
using System.Net.Sockets;
using Deksmart.Ui.Shared.Resources;
using Microsoft.Extensions.Logging;
using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Services
{
    public class HttpService
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly ILogger<HttpService> _logger;
        private readonly NotificationService _notificationService;
        private string ClientName => "DeksmartApi";

        public HttpService(IHttpClientFactory httpClientFactory, JsonSerializerOptions jsonOptions, ILogger<HttpService> logger, NotificationService notificationService)
        {
            _httpClient = httpClientFactory.CreateClient(ClientName);
            _jsonOptions = jsonOptions;
            _logger = logger;
            _notificationService = notificationService;

            // Add Accept-Language header to ensure server returns localized error messages
            _httpClient.DefaultRequestHeaders.AcceptLanguage.Clear();
            _httpClient.DefaultRequestHeaders.AcceptLanguage.ParseAdd("cs-CZ");

            _logger.LogInformation($"HttpClient '{ClientName}' created with base address: {_httpClient.BaseAddress}");
        }

        public async Task<T?> GetAsync<T>(string url, CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _httpClient.GetAsync(url, cancellationToken);
                return await ProcessResponseAsync<T>(response, url, cancellationToken);
            }
            catch (Exception ex)
            {
                if (HandleException(ex))
                {
                    throw;
                }
                return default;
            }
        }

        public async Task<T?> PostAsync<T>(string url, object data, CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync(url, data, _jsonOptions, cancellationToken);
                return await ProcessResponseAsync<T>(response, url, cancellationToken);
            }
            catch (Exception ex)
            {
                if (HandleException(ex))
                {
                    throw;
                }
                return default;
            }
        }

        public async Task<(byte[]? data, string? fileName)> PostBinaryAsync(string url, object data, CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync(url, data, _jsonOptions, cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    // For binary responses, we don't expect an ApiResponse wrapper
                    var bytes = await response.Content.ReadAsByteArrayAsync(cancellationToken);

                    // Try to extract filename from Content-Disposition header
                    string? fileName = null;
                    if (response.Content.Headers.ContentDisposition != null)
                    {
                        fileName = response.Content.Headers.ContentDisposition.FileName;
                        // Remove quotes if present
                        if (fileName != null && fileName.StartsWith("\"") && fileName.EndsWith("\""))
                        {
                            fileName = fileName.Substring(1, fileName.Length - 2);
                        }
                        _logger.LogInformation($"Extracted filename from Content-Disposition: {fileName}");
                    }

                    return (bytes, fileName);
                }
                else
                {
                    await HandleResponseError(response, url, cancellationToken);
                    return (null, null);
                }
            }
            catch (Exception ex)
            {
                if (HandleException(ex))
                {
                    throw;
                }
                return (null, null);
            }
        }

        public async Task<bool> PostAsync(string url, object data, CancellationToken cancellationToken = default)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync(url, data, _jsonOptions, cancellationToken);
                if (response.IsSuccessStatusCode)
                {
                    var apiResponse = await ProcessApiResponseAsync<bool>(response, url, cancellationToken);
                    return apiResponse != null && apiResponse.Success;
                }
                else
                {
                    await HandleResponseError(response, url, cancellationToken);
                    return false;
                }
            }
            catch (Exception ex)
            {
                if (HandleException(ex))
                {
                    throw;
                }
                return false;
            }
        }

        private async Task<T?> ProcessResponseAsync<T>(HttpResponseMessage response, string url, CancellationToken cancellationToken)
        {
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await ProcessApiResponseAsync<T>(response, url, cancellationToken);
                return apiResponse?.Success == true ? apiResponse.Data : default;
            }
            else
            {
                await HandleResponseError(response, url, cancellationToken);
                return default;
            }
        }

        private async Task<ApiResponse<T>?> ProcessApiResponseAsync<T>(HttpResponseMessage response, string url, CancellationToken cancellationToken)
        {
            try
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("Raw API response for {Url}: {ResponseContent}", url, responseContent);

                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<T>>(_jsonOptions, cancellationToken);

                if (apiResponse == null)
                {
                    _logger.LogError("Failed to deserialize API response for {Url}. Response content: {ResponseContent}", url, responseContent);
                    _notificationService.ShowError(UiSharedResource.UnknownError);
                    return null;
                }

                if (!apiResponse.Success)
                {
                    // Handle API-level error
                    string errorMessage = GetErrorMessageFromApiResponse(apiResponse);
                    _logger.LogError("API error for {Url}: {ErrorMessage}. ValidationErrors: {ValidationErrors}, ErrorMessage: {ApiErrorMessage}",
                        url, errorMessage,
                        apiResponse.ValidationErrors != null ? string.Join(", ", apiResponse.ValidationErrors) : "null",
                        apiResponse.ErrorMessage);
                    _notificationService.ShowError(errorMessage);
                }
                return apiResponse;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ProcessApiResponseAsync for {Url}", url);
                _notificationService.ShowError(UiSharedResource.UnknownError);
                return null;
            }
        }

        private static string GetErrorMessageFromApiResponse<T>(ApiResponse<T> apiResponse)
        {
            return !string.IsNullOrEmpty(apiResponse.ErrorMessage)
                ? apiResponse.ErrorMessage
                : (apiResponse.ValidationErrors != null && apiResponse.ValidationErrors.Count > 0
                    ? string.Join(", ", apiResponse.ValidationErrors)
                    : UiSharedResource.UnknownError);
        }

        private static bool IsTransientError(Exception ex)
        {
            // These are the types of errors that Polly should handle
            return ex is HttpRequestException ||
                   ex is TaskCanceledException ||
                   ex is OperationCanceledException ||
                   ex is IOException ||
                   ex is SocketException;
        }

        private bool HandleException(Exception ex)
        {
            if (ex is OperationCanceledException)
            {
                // Let Polly handle cancellation
                return true;
            }

            // Only handle non-transient errors here
            if (!IsTransientError(ex))
            {
                var errorMessage = GetUserFriendlyErrorMessage(ex);
                _logger.LogError("Error handling request: {ErrorMessage}", errorMessage);
                _notificationService.ShowError(errorMessage);
                return false;
            }
            else
            {
                // Let Polly handle transient errors
                return true;
            }
        }

        private static string GetUserFriendlyErrorMessage(Exception ex)
        {
            return ex switch
            {
                JsonException => UiSharedResource.JsonFormatError,
                ArgumentException => UiSharedResource.ArgumentException,
                InvalidOperationException => UiSharedResource.InvalidOperationError,
                UnauthorizedAccessException => UiSharedResource.UnauthorizedAccessError,
                _ => string.Format(UiSharedResource.AnUnexpectedErrorOccurred, ex.Message)
            };
        }

        private async Task HandleResponseError(HttpResponseMessage response, string url, CancellationToken cancellationToken)
        {
            try
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("Raw error response for {Url} (Status: {StatusCode}): {ResponseContent}",
                    url, response.StatusCode, responseContent);

                // Handle empty responses
                if (string.IsNullOrWhiteSpace(responseContent))
                {
                    _logger.LogWarning("Empty response body received for {Url} (Status: {StatusCode})", url, response.StatusCode);

                    // For 404 errors, show a specific message
                    if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        string errorMessage = string.Format(UiSharedResource.ResourceNotFound, GetResourceNameFromUrl(url));
                        LogAndShowError(response.StatusCode, url, errorMessage);
                        return;
                    }

                    // For other status codes, show a generic message based on the status code
                    string statusCodeMessage = GetMessageForStatusCode(response.StatusCode, url);
                    LogAndShowError(response.StatusCode, url, statusCodeMessage);
                    return;
                }

                // Try to parse non-empty responses as ApiResponse
                try
                {
                    // Try to parse the error as an ApiResponse
                    var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<object>>(_jsonOptions, cancellationToken);
                    if (apiResponse != null)
                    {
                        string errorMessage = GetErrorMessageFromApiResponse(apiResponse);
                        _logger.LogError("API error response for {Url} (Status: {StatusCode}): {ErrorMessage}. ValidationErrors: {ValidationErrors}, ErrorMessage: {ApiErrorMessage}",
                            url, response.StatusCode, errorMessage,
                            apiResponse.ValidationErrors != null ? string.Join(", ", apiResponse.ValidationErrors) : "null",
                            apiResponse.ErrorMessage);
                        LogAndShowError(response.StatusCode, url, errorMessage);
                        return;
                    }
                    else
                    {
                        _logger.LogError("API returned null response object for {Url} (Status: {StatusCode})", url, response.StatusCode);
                    }
                }
                catch (JsonException ex)
                {
                    // If we can't parse as ApiResponse, log the exception and fall back to the old way
                    _logger.LogError(ex, "Failed to parse error response as ApiResponse for {Url} (Status: {StatusCode}): {ResponseContent}",
                        url, response.StatusCode, responseContent);
                }

                // Fall back to reading as string if we couldn't parse as ApiResponse
                // If the response content looks like HTML, extract just the error message or use a generic message
                if (responseContent.Contains("<html") || responseContent.Contains("<!DOCTYPE"))
                {
                    _logger.LogWarning("HTML response received instead of JSON for {Url} (Status: {StatusCode})", url, response.StatusCode);
                    LogAndShowError(response.StatusCode, url, GetMessageForStatusCode(response.StatusCode, url));
                }
                else
                {
                    LogAndShowError(response.StatusCode, url, responseContent);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in HandleResponseError for {Url} (Status: {StatusCode})", url, response.StatusCode);
                _notificationService.ShowError(UiSharedResource.UnknownError);
            }
        }

        private string GetResourceNameFromUrl(string url)
        {
            // Extract a user-friendly resource name from the URL
            // Example: "deksmart/configuratorview/501/email" -> "configurator"

            string resourceName = "resource";

            if (url.Contains("configurator"))
            {
                resourceName = "configurator";
            }
            else if (url.Contains("preset"))
            {
                resourceName = "preset";
            }
            else if (url.Contains("product"))
            {
                resourceName = "product";
            }

            return resourceName;
        }

        private string GetMessageForStatusCode(System.Net.HttpStatusCode statusCode, string url)
        {
            return statusCode switch
            {
                System.Net.HttpStatusCode.NotFound => string.Format(UiSharedResource.ResourceNotFound, GetResourceNameFromUrl(url)),
                System.Net.HttpStatusCode.Unauthorized => UiSharedResource.UnauthorizedAccessError,
                System.Net.HttpStatusCode.Forbidden => UiSharedResource.ForbiddenError,
                System.Net.HttpStatusCode.BadRequest => UiSharedResource.BadRequestError,
                System.Net.HttpStatusCode.InternalServerError => UiSharedResource.ServerError,
                _ => UiSharedResource.UnknownError
            };
        }

        private void LogAndShowError(System.Net.HttpStatusCode statusCode, string url, string errorMessage)
        {
            // Make sure we have a non-empty error message to display
            if (string.IsNullOrWhiteSpace(errorMessage))
            {
                errorMessage = UiSharedResource.UnknownError;
                _logger.LogWarning("Empty error message received for {Url}, using default error message", url);
            }

            if (statusCode == System.Net.HttpStatusCode.UnprocessableEntity)
            {
                _logger.LogError("Validation error for {Url}: {ErrorMessage}", url, errorMessage);
            }
            else
            {
                _logger.LogError("Failed to process request for {Url}: {ErrorMessage}", url, errorMessage);
            }

            // Always show the error notification
            _notificationService.ShowError(errorMessage);

            // Log that we've shown the notification to help with debugging
            _logger.LogDebug("Error notification shown for {Url}: {ErrorMessage}", url, errorMessage);
        }
    }
}
