﻿const interval = setInterval(() => {

    if (document.querySelector(".information-container") === null) {
        return;
    } else {
        clearInterval(interval);
    }

    const base = window.location.pathname.replace("/doc/index.html", "");
    const swagger = document.querySelector('.information-container a');

    const html = `
        <ul>
            <li>
                <a target="_blank" href="${swagger.href}">Swagger.json</a>
            </li>
            <li>
                <a target="_blank" href="${base}/redoc">Redoc</a>
            </li>
            <li>
                <a target="_blank" href="${base}/profiler/results-index">Profiler</a>
            </li>
        </ul>
    `;

    swagger.remove();
    document.querySelector('.information-container .title').insertAdjacentHTML("afterend", html);

}, 50);
