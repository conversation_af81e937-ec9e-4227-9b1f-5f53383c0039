﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Enum;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// CZ_ESHOP_KONFIGURATOR_FILTRY
    /// Represents a user-interactive field within a configurator.
    /// Defines properties, behavior, and metadata for a single input or filter element, including type, validation, display logic, and associated expressions.
    /// Central to configuring how users interact with and provide input to the configurator.
    /// </summary>
    [Table("configurator_field", Schema = "dbo")]
    [Index("ExpressionId", Name = "idx_configurator_field_expression_id")]
    [Index("FieldCategoryId", Name = "idx_configurator_field_field_category_id")]
    [Index("VisibilityId", Name = "idx_configurator_field_visibility_id")]
    [Index("ValidationId", Name = "idx_configurator_field_validation_id")]
    public partial class ConfiguratorField : IDeletableEntity
    {
        /// <summary>
        /// FILTR_ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("field_category_id")]
        public int FieldCategoryId { get; set; }

        /// <summary>
        /// FILTR_NAZEV
        /// </summary>
        [Column("title")]
        public string Title { get; set; } = null!;

        /// <summary>
        /// POZNAMKA
        /// </summary>
        [Column("description")]
        [StringLength(2000)]
        public string? Description { get; set; }

        [Column("suffix")]
        [StringLength(20)]
        public string? Suffix { get; set; }

        /// <summary>
        /// PORADI
        /// </summary>
        [Column("order")]
        public int Order { get; set; }

        [Column("ident")]
        [StringLength(10)]
        public string Ident { get; set; } = null!;

        [Column("is_deleted")]
        public bool IsDeleted { get; set; }

        [Column("component_type")]
        public ComponentType ComponentType { get; set; }

        [Column("field_position")]
        public FieldPosition FieldPosition { get; set; }

        [Column("max_value")]
        public int? MaxValue { get; set; }

        [Column("min_value")]
        public int? MinValue { get; set; }

        /// <summary>
        /// Only for product component type. Refers to the order/index of the composition whose selected product details should be shown for this field.
        /// </summary>
        [Column("composition_order")]
        public int? CompositionOrder { get; set; }

        [Column("javascript_after_render")]
        public string? JavaScriptAfterRender { get; set; }

        [Column("javascript_on_focus")]
        public string? JavaScriptOnFocus { get; set; }

        [Column("javascript_on_blur")]
        public string? JavaScriptOnBlur { get; set; }

        [Column("expression_id")]
        public int? ExpressionId { get; set; }

        [Column("visibility_id")]
        public int? VisibilityId { get; set; }

        [Column("default_value")]
        public decimal DefaultValue { get; set; }

        [Column("validation_id")]
        public int? ValidationId { get; set; }

        [Column("validation_message")]
        [StringLength(2000)]
        public string? ValidationMessage { get; set; }

        [ForeignKey("ExpressionId")]
        public virtual ConfiguratorExpression? Expression { get; set; }

        [ForeignKey("VisibilityId")]
        public virtual ConfiguratorExpression? Visibility { get; set; }

        [ForeignKey("ValidationId")]
        public virtual ConfiguratorExpression? Validation { get; set; }

        public virtual ICollection<ConfiguratorFieldValue> ConfiguratorFieldValues { get; set; } = new List<ConfiguratorFieldValue>();

        // Business logic
        [NotMapped]
        public decimal? Value { get; set; }

        [NotMapped]
        public ProductDetails? ProductDetails { get; set; }

        [NotMapped]
        public string? ValidationError { get; set; }
    }
}
