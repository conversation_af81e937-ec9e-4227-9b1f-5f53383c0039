﻿using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Model
{
    public class ConfiguratorProductDetailWrapper
    {
        private readonly ProductDetailsDto _configuratorProductDetail;

        public ConfiguratorProductDetailWrapper(ProductDetailsDto configuratorProductDetail)
        {
            _configuratorProductDetail = configuratorProductDetail;
        }

        public string Title => _configuratorProductDetail.Product.Title ?? _configuratorProductDetail.Product.Code;

        public string Description => _configuratorProductDetail.Product.Description;

        public string? ImageUrl => $"https://cdn1.idek.cz/dek/img/product/{_configuratorProductDetail.Product.Image}_ew230.webp";

        public List<(string Name, string Value)> TechSpecs => _configuratorProductDetail.Specifications.Select(x => (x.Title, x.Value)).ToList();
    }
}
