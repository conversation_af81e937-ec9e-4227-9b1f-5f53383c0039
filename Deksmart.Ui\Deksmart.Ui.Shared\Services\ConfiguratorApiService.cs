using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Resources;
using Microsoft.Extensions.Logging;

namespace Deksmart.Ui.Shared.Services
{
    public class ConfiguratorApiService : IConfiguratorApiService
    {
        private readonly HttpService _httpService;
        private readonly ILogger<ConfiguratorApiService> _logger;
        private readonly NotificationService _notificationService;

        public ConfiguratorApiService(
            HttpService httpService,
            ILogger<ConfiguratorApiService> logger,
            NotificationService notificationService)
        {
            _httpService = httpService;
            _logger = logger;
            _notificationService = notificationService;
        }

        public async Task<ConfiguratorDto?> GetConfiguratorAsync(int id, CancellationToken cancellationToken)
        {
            return await _httpService.GetAsync<ConfiguratorDto>(id.ToString(), cancellationToken);
        }

        public async Task<ConfiguratorDto?> GetFilteredConfiguratorAsync(int id, ConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            return await _httpService.PostAsync<ConfiguratorDto>(
                string.Format(UiSharedResource.LoadConfiguratorUrl, id), 
                state, 
                cancellationToken);
        }

        public async Task<CompositeConfiguratorResponseDto?> ProcessCompositeConfiguratorAsync(int id, CompositeConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            return await _httpService.PostAsync<CompositeConfiguratorResponseDto>(
                $"{id}/composite", 
                state, 
                cancellationToken);
        }

        public async Task<ConfiguratorDto?> GetConfiguratorForPresetAsync(int id, string preset, CancellationToken cancellationToken)
        {
            return await _httpService.GetAsync<ConfiguratorDto>(
                string.Format(UiSharedResource.LoadPresetUrl, id, preset), 
                cancellationToken);
        }

        public async Task<Guid?> SavePresetAsync(int configuratorId, ConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            return await _httpService.PostAsync<Guid>(
                string.Format(UiSharedResource.SavePresetUrl, configuratorId), 
                state, 
                cancellationToken);
        }

        public async Task<ProductDetailsDto?> LoadProductDetailAsync(string productCode, CancellationToken cancellationToken)
        {
            return await _httpService.GetAsync<ProductDetailsDto>($"product-detail/{productCode}", cancellationToken);
        }

        public async Task<ProductPriceCalculationResponseDto?> CalculateProductPricesAsync(ProductPriceCalculationRequestDto request, CancellationToken cancellationToken)
        {
            var result = await _httpService.PostAsync<ProductPriceCalculationResponseDto>(
                UiSharedResource.CalculateProductPricesUrl,
                request,
                cancellationToken);

            if (result != null)
            {
                _logger.LogInformation($"Calculated product prices for {request.CurrentProductCode}");
            }

            return result;
        }

        public async Task<(byte[]? pdfBytes, string fileName)> GenerateConfigurationPdfAsync(int configuratorId, ConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            var (pdfBytes, fileName) = await _httpService.PostBinaryAsync(
                string.Format(UiSharedResource.GenerateConfigurationPdfUrl, configuratorId),
                state,
                cancellationToken);

            if (pdfBytes == null)
            {
                _notificationService.ShowError(UiSharedResource.FailedToGeneratePdf);
                return (null, "configuration.pdf");
            }

            return (pdfBytes, fileName ?? "configuration.pdf");
        }

        public async Task<bool> SendInquiryAsync(int configuratorId, EmailConfiguratorStateDto emailState, CancellationToken cancellationToken)
        {
            var success = await _httpService.PostAsync(
                string.Format(UiSharedResource.SendInquiryUrl, configuratorId),
                emailState,
                cancellationToken);

            if (success)
            {
                _notificationService.ShowSuccess(UiSharedResource.SendInquirySuccess);
            }

            return success;
        }
    }
}