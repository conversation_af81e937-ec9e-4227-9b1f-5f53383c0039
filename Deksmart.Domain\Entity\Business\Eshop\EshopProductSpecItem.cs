namespace Deksmart.Domain.Entity.Business.Eshop
{
    /// <summary>
    /// Represents a single specification or attribute of an e-shop product as a key-value pair (e.g., "Weight: 2kg").
    /// Used for displaying or processing product details in the e-commerce domain.
    /// </summary>
    public class EshopProductSpecItem
    {
        public string Title { get; set; }
        public string Value { get; set; }
    }
} 