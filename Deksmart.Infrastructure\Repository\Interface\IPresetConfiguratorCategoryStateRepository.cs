using Deksmart.Domain.Entity.Db;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for managing <see cref="PresetConfiguratorCategoryState"/> entities, including retrieval, insertion, and deletion of category states for a preset.
    /// </summary>
    public interface IPresetConfiguratorCategoryStateRepository
    {
        /// <summary>
        /// Retrieves all category states associated with a given preset.
        /// </summary>
        /// <param name="presetId">The unique identifier of the preset.</param>
        /// <returns>A list of <see cref="PresetConfiguratorCategoryState"/> entities for the preset.</returns>
        Task<List<PresetConfiguratorCategoryState>> GetCategoryStatesForPresetAsync(Guid presetId);

        /// <summary>
        /// Inserts a new category state for a preset.
        /// </summary>
        /// <param name="categoryState">The <see cref="PresetConfiguratorCategoryState"/> entity to insert.</param>
        Task InsertAsync(PresetConfiguratorCategoryState categoryState);

        /// <summary>
        /// Deletes all category states associated with a given preset.
        /// </summary>
        /// <param name="presetId">The unique identifier of the preset.</param>
        Task DeleteAllForPresetAsync(Guid presetId);
    }
}
