using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorCompositionRepository : IntIdDaoBase<ConfiguratorComposition>, IConfiguratorCompositionRepository
    {
        public ConfiguratorCompositionRepository(ConfiguratorContext context) : base(context)
        {
        }

        /// <inheritdoc/>
        public async Task<List<ConfiguratorComposition>> GetCompositionsForConfiguratorAsync(int configuratorId)
        {
            return await _context.ConfiguratorCompositions
                .AsNoTracking()
                .AsSplitQuery()
                .Include(d => d.Visibility)
                .Include(d => d.ConfiguratorProducts)
                    .ThenInclude(i => i.Visibility)
                .Include(d => d.ConfiguratorProducts)
                    .ThenInclude(i => i.Quantity)
                .Where(d => d.ConfiguratorId == configuratorId)
                .ToListAsync();
        }
    }
}
