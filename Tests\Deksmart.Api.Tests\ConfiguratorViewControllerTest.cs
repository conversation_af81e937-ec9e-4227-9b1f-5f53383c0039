using Moq;
using Deksmart.Api.Controllers;
using Microsoft.AspNetCore.Mvc;
using Deksmart.Application.Service;
using Deksmart.Shared.Dto;
using Xunit;
using Deksmart.Api.Validator;
using DEK.Eshop.ApiCore.Mail;
using Microsoft.Extensions.Configuration;
using FluentValidation.Results;
using ValidationResult = FluentValidation.Results.ValidationResult;
using DomainValidationResult = Deksmart.Domain.Entity.Business.ValidationResult;

namespace Deksmart.Api.Tests
{

    public class ConfiguratorViewControllerTest
    {
        private readonly Mock<IConfiguratorViewOrchestrator> _viewOrchestratorMock;
        private readonly Mock<IConfiguratorRouteValidator> _configuratorRouteValidatorMock;
        private readonly Mock<IPresetRouteValidator> _presetRouteValidatorMock;
        private readonly Mock<IEmailConfiguratorStateValidator> _emailConfiguratorStateValidatorMock;
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly MailManager _mailManager;
        private readonly ConfiguratorViewController _controller;

        public ConfiguratorViewControllerTest()
        {
            _viewOrchestratorMock = new Mock<IConfiguratorViewOrchestrator>();
            _configuratorRouteValidatorMock = new Mock<IConfiguratorRouteValidator>();
            _presetRouteValidatorMock = new Mock<IPresetRouteValidator>();
            _emailConfiguratorStateValidatorMock = new Mock<IEmailConfiguratorStateValidator>();
            _configurationMock = new Mock<IConfiguration>();

            // Set up configuration for mail manager
            _configurationMock.Setup(x => x["ApiCore:Mail:Host"]).Returns("smtp.example.com");
            _configurationMock.Setup(x => x["ApiCore:Mail:Port"]).Returns("587");
            _configurationMock.Setup(x => x["ApiCore:Mail:MailFrom"]).Returns("<EMAIL>");
            _configurationMock.Setup(x => x["ApiCore:Mail:NameFrom"]).Returns("Sender");
            _configurationMock.Setup(x => x["ApiCore:Mail:DefaultEmailTo"]).Returns("<EMAIL>");

            _mailManager = new MailManager(_configurationMock.Object);

            _controller = new ConfiguratorViewController(
                _viewOrchestratorMock.Object,
                _configuratorRouteValidatorMock.Object,
                _presetRouteValidatorMock.Object,
                _emailConfiguratorStateValidatorMock.Object,
                _mailManager,
                _configurationMock.Object
            );
        }

        [Fact]
        public async Task GenerateHtmlFromState_WithValidInput_ReturnsHtmlContent()
        {
            // Arrange
            var id = "1";
            var configuratorState = new ConfiguratorStateDto(1, new List<ClientFieldValueDto>(), new List<ClientProductValueDto>());
            var expectedHtml = "<html>Test HTML</html>";
            var fluentValidationResult = new ValidationResult();
            var domainValidation = new DomainValidationResult();

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(fluentValidationResult);
            _viewOrchestratorMock.Setup(x => x.GenerateHtmlFromStateAsync(1, configuratorState))
                .ReturnsAsync((expectedHtml, domainValidation));

            // Act
            var result = await _controller.GenerateHtmlFromState(id, configuratorState);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Equal("text/html", contentResult.ContentType);
            Assert.Equal(expectedHtml, contentResult.Content);
        }

        [Fact]
        public async Task GenerateHtmlFromState_WithEmailState_ReturnsHtmlContent()
        {
            // Arrange
            var id = "1";
            var contactInfo = new ContactInfoDto
            {
                Name = "John Doe",
                Email = "<EMAIL>"
            };
            var configuratorState = new EmailConfiguratorStateDto
            {
                ConfiguratorId = 1,
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>(),
                ContactInfo = contactInfo
            };
            var expectedHtml = "<html>Test HTML</html>";
            var fluentValidationResult = new ValidationResult();
            var domainValidation = new DomainValidationResult();

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(fluentValidationResult);
            _viewOrchestratorMock.Setup(x => x.GenerateHtmlFromStateAsync(1, configuratorState))
                .ReturnsAsync((expectedHtml, domainValidation));

            // Act
            var result = await _controller.GenerateHtmlFromState(id, configuratorState);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Equal("text/html", contentResult.ContentType);
            Assert.Equal(expectedHtml, contentResult.Content);
        }

        [Fact]
        public async Task GenerateHtmlFromState_WithInvalidConfigurator_ReturnsBadRequest()
        {
            // Arrange
            var id = "1";
            var configuratorState = new ConfiguratorStateDto(1, new List<ClientFieldValueDto>(), new List<ClientProductValueDto>());
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("id", "Invalid configurator ID"));

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(validationResult);

            // Act
            var result = await _controller.GenerateHtmlFromState(id, configuratorState);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Invalid configurator ID", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task GenerateHtmlFromState_WithGenerationError_ReturnsBadRequest()
        {
            // Arrange
            var id = "1";
            var configuratorState = new ConfiguratorStateDto(1, new List<ClientFieldValueDto>(), new List<ClientProductValueDto>());
            var errorMessage = "Generation failed";
            var domainValidation = new DomainValidationResult();
            domainValidation.AddError(errorMessage);

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _viewOrchestratorMock.Setup(x => x.GenerateHtmlFromStateAsync(1, configuratorState))
                .ReturnsAsync((null, domainValidation));

            // Act
            var result = await _controller.GenerateHtmlFromState(id, configuratorState);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains(errorMessage, apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task GeneratePdfFromState_WithValidInput_ReturnsPdfFile()
        {
            // Arrange
            var id = "1";
            var configuratorState = new ConfiguratorStateDto(1, new List<ClientFieldValueDto>(), new List<ClientProductValueDto>());
            var expectedPdfBytes = new byte[] { 1, 2, 3, 4 };
            var domainValidation = new DomainValidationResult();

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _viewOrchestratorMock.Setup(x => x.GeneratePdfFromStateAsync(1, configuratorState))
                .ReturnsAsync((expectedPdfBytes, "Test Configurator", domainValidation));

            // Act
            var result = await _controller.GeneratePdfFromState(id, configuratorState);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/pdf", fileResult.ContentType);
            Assert.Equal("Test_Configurator.pdf", fileResult.FileDownloadName);
            Assert.Equal(expectedPdfBytes, fileResult.FileContents);
        }

        [Fact]
        public async Task GeneratePdfFromState_WithEmailState_ReturnsPdfFile()
        {
            // Arrange
            var id = "1";
            var contactInfo = new ContactInfoDto
            {
                Name = "John Doe",
                Email = "<EMAIL>"
            };
            var configuratorState = new EmailConfiguratorStateDto
            {
                ConfiguratorId = 1,
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>(),
                ContactInfo = contactInfo
            };
            var expectedPdfBytes = new byte[] { 1, 2, 3, 4 };
            var domainValidation = new DomainValidationResult();

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _viewOrchestratorMock.Setup(x => x.GeneratePdfFromStateAsync(1, configuratorState))
                .ReturnsAsync((expectedPdfBytes, "Test Configurator", domainValidation));

            // Act
            var result = await _controller.GeneratePdfFromState(id, configuratorState);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/pdf", fileResult.ContentType);
            Assert.Equal("Test_Configurator.pdf", fileResult.FileDownloadName);
            Assert.Equal(expectedPdfBytes, fileResult.FileContents);
        }

        [Fact]
        public async Task GeneratePdfFromPreset_WithValidInput_ReturnsPdfFile()
        {
            // Arrange
            var id = "1";
            var presetId = "123e4567-e89b-12d3-a456-************";
            var expectedPdfBytes = new byte[] { 1, 2, 3, 4 };
            var domainValidation = new DomainValidationResult();

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _presetRouteValidatorMock.Setup(x => x.ValidateAsync(presetId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _viewOrchestratorMock.Setup(x => x.GeneratePdfFromPresetAsync(1, Guid.Parse(presetId)))
                .ReturnsAsync((expectedPdfBytes, "Test Configurator", domainValidation));

            // Act
            var result = await _controller.GeneratePdfFromPreset(id, presetId);

            // Assert
            var fileResult = Assert.IsType<FileContentResult>(result);
            Assert.Equal("application/pdf", fileResult.ContentType);
            Assert.Equal("Test_Configurator.pdf", fileResult.FileDownloadName);
            Assert.Equal(expectedPdfBytes, fileResult.FileContents);
        }

        [Fact]
        public async Task GenerateHtmlFromPreset_WithValidInput_ReturnsHtmlContent()
        {
            // Arrange
            var id = "1";
            var presetId = "123e4567-e89b-12d3-a456-************";
            var expectedHtml = "<html>Test HTML</html>";
            var domainValidation = new DomainValidationResult();

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _presetRouteValidatorMock.Setup(x => x.ValidateAsync(presetId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _viewOrchestratorMock.Setup(x => x.GenerateHtmlFromPresetAsync(1, Guid.Parse(presetId)))
                .ReturnsAsync((expectedHtml, domainValidation));

            // Act
            var result = await _controller.GenerateHtmlFromPreset(id, presetId);

            // Assert
            var contentResult = Assert.IsType<ContentResult>(result);
            Assert.Equal("text/html", contentResult.ContentType);
            Assert.Equal(expectedHtml, contentResult.Content);
        }

        [Fact]
        public async Task SendEmailFromState_WithInvalidContactInfo_ReturnsBadRequest()
        {
            // Arrange
            var id = "1";
            var contactInfo = new ContactInfoDto
            {
                Name = "John Doe",
                Email = "invalid-email"
            };
            var configuratorState = new EmailConfiguratorStateDto
            {
                ConfiguratorId = 1,
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>(),
                ContactInfo = contactInfo
            };

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _emailConfiguratorStateValidatorMock.Setup(x => x.ValidateAsync(configuratorState, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult(new List<ValidationFailure> { new ValidationFailure("Email", "Invalid email format") }));

            // Act
            var result = await _controller.SendEmailFromState(id, configuratorState);

            // Assert
            // ValidationErrorResult is a protected class inside DeksmartBaseController
            // It inherits from ObjectResult, so we can check if it's an ObjectResult
            var objectResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, objectResult.StatusCode);
            var apiResponse = Assert.IsType<ApiResponse<object>>(objectResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Single(apiResponse.ValidationErrors);
            Assert.Equal("Invalid email format", apiResponse.ValidationErrors[0]);
        }

        // NOTE: Email functionality (SendEmailFromState and SendEmailFromPreset success paths) cannot be unit tested
        // because the controller depends on a concrete MailManager class rather than an interface.
        // The MailManager attempts to connect to a real SMTP server, making it impossible to mock.
        // Only validation scenarios can be tested for these endpoints.
        // To enable proper unit testing, MailManager would need to be abstracted behind an interface.

        [Fact]
        public async Task SendEmailFromPreset_WithInvalidConfigurator_ReturnsBadRequest()
        {
            // Arrange
            var id = "1";
            var presetId = "123e4567-e89b-12d3-a456-************";
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("id", "Invalid configurator ID"));

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(validationResult);

            // Act
            var result = await _controller.SendEmailFromPreset(id, presetId);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Invalid configurator ID", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task SendEmailFromPreset_WithInvalidPreset_ReturnsBadRequest()
        {
            // Arrange
            var id = "1";
            var presetId = "123e4567-e89b-12d3-a456-************";
            var validationResult = new ValidationResult();
            validationResult.Errors.Add(new ValidationFailure("presetId", "Invalid preset ID"));

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _presetRouteValidatorMock.Setup(x => x.ValidateAsync(presetId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(validationResult);

            // Act
            var result = await _controller.SendEmailFromPreset(id, presetId);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Invalid preset ID", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task SendEmailFromPreset_WithGenerationError_ReturnsBadRequest()
        {
            // Arrange
            var id = "1";
            var presetId = "123e4567-e89b-12d3-a456-************";
            var errorMessage = "HTML generation failed";
            var domainValidation = new DomainValidationResult();
            domainValidation.AddError(errorMessage);

            _configuratorRouteValidatorMock.Setup(x => x.ValidateAsync(id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _presetRouteValidatorMock.Setup(x => x.ValidateAsync(presetId, It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ValidationResult());
            _viewOrchestratorMock.Setup(x => x.GenerateHtmlFromPresetAsync(1, Guid.Parse(presetId)))
                .ReturnsAsync((null, domainValidation));

            // Act
            var result = await _controller.SendEmailFromPreset(id, presetId);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains(errorMessage, apiResponse.ValidationErrors!);
        }
    }
}