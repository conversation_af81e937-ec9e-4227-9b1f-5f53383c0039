﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Represents the saved state of a single configurator field within a preset.
    /// Links a preset to a specific field and stores the value assigned to that field, enabling restoration of user input for each field when reloading a configurator preset.
    /// </summary>
    [PrimaryKey("PresetId", "FieldId")]
    [Table("configurator_field_combination", Schema = "dbo")]
    [Index("PresetId", Name = "idx_field_combination_combination_id")]
    public partial class ConfiguratorFieldCombination
    {
        [Key]
        [Column("preset_id")]
        public Guid PresetId { get; set; }

        [Key]
        [Column("field_id")]
        public int FieldId { get; set; }

        [Column("field_value")]
        public decimal FieldValue { get; set; }
    }
}
