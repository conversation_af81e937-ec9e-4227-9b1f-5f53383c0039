﻿using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorPresetRepository : IdDaoBase<ConfiguratorPreset, Guid>, IConfiguratorPresetRepository
    {
        public ConfiguratorPresetRepository(ConfiguratorContext context) : base(context)
        {
        }

        /// <inheritdoc/>
        public async Task<ConfiguratorPreset?> GetPresetIdByCatalogId(string catalogId)
        {
            return await _context.ConfiguratorPresets
                .AsNoTracking()
                .Where(d => d.CatalogId == catalogId)
                .SingleOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<ConfiguratorPreset?> GetFieldPresetByIdAsync(Guid presetId)
        {
            return await _context.ConfiguratorPresets
                .AsNoTracking()
                .Include(d => d.ConfiguratorFieldCombinations)
                .Include(d => d.ChildPresets)
                    .ThenInclude(cp => cp.Preset)
                        .ThenInclude(p => p.ConfiguratorFieldCombinations)
                .SingleOrDefaultAsync(d => d.Id == presetId);
        }

        /// <inheritdoc/>
        public async Task<List<ConfiguratorProductCombination>> GetProductPresetByIdAsync(Guid presetId)
        {
            return await _context.ConfiguratorProductCombinations
                .AsNoTracking()
                .Where(d => d.PresetId == presetId)
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<bool> SetCatalogIdToPresetAsync(Guid presetId, string catalogId)
        {
            var preset = await _context.ConfiguratorPresets.FindAsync(presetId);

            if (preset == null)
                return false;

            preset.CatalogId = catalogId;

            _context.ConfiguratorPresets.Update(preset);
            await _context.SaveChangesAsync();

            return true;
        }

        /// <inheritdoc/>
        public async Task UpdateAsync(ConfiguratorPreset preset)
        {
            _context.ConfiguratorPresets.Update(preset);
            await _context.SaveChangesAsync();
        }
    }
}
