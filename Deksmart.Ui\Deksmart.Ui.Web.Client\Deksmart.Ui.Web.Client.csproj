﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.5" PrivateAssets="all" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.3" />
    <PackageReference Include="Polly" Version="8.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Deksmart.Shared\Deksmart.Shared.csproj" />
    <ProjectReference Include="..\Deksmart.Ui.Model\Deksmart.Ui.Model.csproj" />
    <ProjectReference Include="..\Deksmart.Ui.Shared\Deksmart.Ui.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\appsettings.DockerDevelopment.json">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
