namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a request to calculate product prices within a configurator.
    /// Contains the configurator identifier, the list of selected products (with codes, amounts, and units), and the code of the current product for which detailed pricing is required.
    /// Used to transfer calculation context and selection from UI to API, supporting dynamic price calculation workflows.
    /// </summary>
    public class ProductPriceCalculationRequestDto
    {
        public int ConfiguratorId { get; set; }
        public List<SelectedProductDto> SelectedProducts { get; set; } = new();
        public string CurrentProductCode { get; set; } = null!;
    }

    /// <summary>
    /// Data Transfer Object representing the results of a product price calculation, including VAT and total prices for the current product and the overall selection.
    /// Used to transfer calculated pricing outcomes from API to UI, supporting display, validation, and further business logic.
    /// </summary>
    public class ProductPriceCalculationResponseDto
    {
        public decimal PriceVat { get; set; }
        public decimal TotalPriceNoVat { get; set; }
        public decimal TotalPriceVat { get; set; }
        public decimal TotalVat { get; set; }
    }
}