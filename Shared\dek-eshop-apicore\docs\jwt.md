# JWT

- [Simple authorization](https://learn.microsoft.com/en-us/aspnet/core/security/authorization/simple)
- [Role-based authorization](https://learn.microsoft.com/en-us/aspnet/core/security/authorization/roles)

appsettings.json\

- AsymmetricKeys: public key pksc1 (-----BEGIN RSA PUBLIC KEY-----). Viz př<PERSON>, je možné vkládat bez komentáře a zalomení řádků
- SymmetricKeys: musí mít asi přes 15 znaků jinak to neprojde a blbě se to debuguje
- Issuers: bude v napros<PERSON> vetšině 'eshop' (ten kdo podepisuje jwt)\
- Audiences: pro koho je JWT určeno (tag pro službu: cart, branch, product, user, atd.)
```json
 {
    "Jwt": {
        "AsymmetricKeys": [
            "MIIBigKCAYEAw8wZjc7vItdcx+R4IxzoAJRIhEcfXKNSXcwE8Ty6y7xCg0DAg8HdxrOztI5ntnBQa4DveHvdmwtMV9mcE9NzGt6eU6OdZU32SqYJPuUXr/eCiG9pV2PMzCXAMu9+sXlvKZor7mbkMog4e03pElKedEwxgIujWUMhD0TnUmdU1A5+HnBWv0MlASyevVWBzw3aPF8ybmVxPXbBuxBE5KOIYNxxXii8JCjdzZJ/9owbWAckzB1MSCkWHzQJRx3cUeGYNa2BSWx0CMfW3nHxMX0QKAlvSQSxTYhtvgMBqDPK2zLF9W4VZ4Z0FpZg7IFGod7aOFapyywoslXRgYLKdhALqv2z46/fnnWwQlwwEPfGfX9mkE4Skrxf7cY4EqHVI8yP+WM1oLGN6mqFIUniX+tsTS/7Bfx3SD7VCwx9ykv6WkQXuYMXSfiXQaMfLwSQzchUeveFTMmiWaGBgsP/UDvDZhCfBvdBcRttDMzbtMi3ERg31jXM22VadcCzxCwWcgj9AgMBAAE="
        ],
        "SymmetricKeys": [
            "$ecretf0rt3st$ecretf0rt3st"
        ],
        "Issuers": [
            "eshop"
        ],
        "Audiences": [
            "cart"
        ]
    },
}
```
