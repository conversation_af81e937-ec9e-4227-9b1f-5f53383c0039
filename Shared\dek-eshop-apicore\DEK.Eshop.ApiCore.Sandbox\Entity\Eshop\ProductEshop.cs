namespace DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;

using System.ComponentModel.DataAnnotations;

public class ProductEshop
{
    /// <summary>
    ///  Stringové Id položky
    /// </summary>
    [Required]
    public string Code { get; set; } = null!;

    public string? Title { get; set; } = null;

    public string? Slug { get; set; } = null;

    public Unit? Unit { get; set; } = null;

    public Price? Price { get; set; } = null;

    public bool? IsFeatured { get; set; } = null;

    public int? Image { get; set; } = null;

    public ProductEshop(string code) => (Code) = (code);
}
