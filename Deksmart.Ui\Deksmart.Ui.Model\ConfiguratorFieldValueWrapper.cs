using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Model
{
    public class ConfiguratorFieldValueWrapper
    {
        public ConfiguratorFieldValueDto Value { get; }

        public ConfiguratorFieldValueWrapper(ConfiguratorFieldValueDto value)
        {
            Value = value;
        }

        public string Title => Value.Title;
        public string? Image => Value.Image;
        public int Id => Value.Id;
    }
}
