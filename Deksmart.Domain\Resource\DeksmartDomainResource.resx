﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ComponentTypeDoesNotExist" xml:space="preserve">
    <value>Row {0}: Invalid component type: {1}</value>
  </data>
  <data name="CollapseStateDoesNotExist" xml:space="preserve">
    <value>Row {0}: Invalid collapse state: {1}</value>
  </data>
  <data name="FieldPositionDoesNotExist" xml:space="preserve">
    <value>Row {0}: Invalid field position: {1}</value>
  </data>
  <data name="ExpressionDoesNotMatch" xml:space="preserve">
    <value>Expression {0} cannot be parsed, there could be missing ident or incorrect syntax.</value>
  </data>
  <data name="ExpressionValueCannotBeEmpty" xml:space="preserve">
    <value>Row {0}: Expression cannot be empty in expression field {1}.</value>
  </data>
  <data name="ImportConfiguratorError" xml:space="preserve">
    <value>The first sheet defining the calculator has the wrong format</value>
  </data>
  <data name="ImportProductsError" xml:space="preserve">
    <value>The third sheet defining the products is incorrectly formatted or missing.</value>
  </data>
  <data name="IsNotBoolean" xml:space="preserve">
    <value>Row {0}: Value {1} should be boolean.</value>
  </data>
  <data name="IsNotImageUrl" xml:space="preserve">
    <value>Row {0}: Value {1} should be image url.</value>
  </data>
  <data name="IsNotInteger" xml:space="preserve">
    <value>Row {0}: Value {1} should be integer.</value>
  </data>
  <data name="IsNotNumeric" xml:space="preserve">
    <value>Row {0}: Value {1} should be numeric.</value>
  </data>
  <data name="MarkdownConversionError" xml:space="preserve">
    <value>Row {0}: Markdown string {1} could not be converted, make sure it has correct syntax.</value>
  </data>
  <data name="MissingAmountForProduct" xml:space="preserve">
    <value>Row {0}: Amount for product is missing and cannot be set from previous product.</value>
  </data>
  <data name="MissingVisibilityForProduct" xml:space="preserve">
    <value>Row {0}: Visibility for product is missing and cannot be set from previous product.</value>
  </data>
  <data name="FieldValueDoesNotExist" xml:space="preserve">
    <value>FieldId: '{0}' has no value: {1}</value>
  </data>
  <data name="FieldExpressionDoesNotMatch" xml:space="preserve">
    <value>Expression {0} cannot be parsed, there could be missing ident, incorrect syntax or referencing other expression field, no nesting is allowed.</value>
  </data>
  <data name="ImportFieldsError" xml:space="preserve">
    <value>The second sheet defining fields is incorrectly formatted or missing</value>
  </data>
  <data name="MissingFieldForCategory" xml:space="preserve">
    <value>Row {0}: A category must have at least one field.</value>
  </data>
  <data name="FieldValueOutOfRange" xml:space="preserve">
    <value>Field id {0} value {1} out of range {2}-{3}</value>
  </data>
  <data name="ConfiguratorDoesNotExist" xml:space="preserve">
    <value>Calculator with id {0} not found.</value>
  </data>
  <data name="InvalidProductUnit" xml:space="preserve">
    <value>Product {0} has invalid unit '{1}'. Expected either '{2}' or '{3}'</value>
  </data>
  <data name="NoEnrichmentDataFound" xml:space="preserve">
    <value>No enrichment data found for product {0}</value>
  </data>
  <data name="ProductNotFoundInSelection" xml:space="preserve">
    <value>Product {0} not found in selected products</value>
  </data>
  <data name="UnitsError" xml:space="preserve">
    <value>Failed to get product units: {0}</value>
  </data>
  <data name="InvalidProductAmount" xml:space="preserve">
    <value>Product {0} has invalid amount: {1}</value>
  </data>
  <data name="CheckboxYesValue" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="CheckboxNoValue" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="IdentTooLong" xml:space="preserve">
    <value>Ident '{0}' is too long. Maximum length is 10 characters.</value>
  </data>
  <data name="IdentRequiredWhenTitlePresent" xml:space="preserve">
    <value>Field with title '{0}' is missing identificator.</value>
  </data>
  <data name="ConfiguratorTitleRequired" xml:space="preserve">
    <value>Configurator title is required.</value>
  </data>
  <data name="ImportFieldsFirstCellError" xml:space="preserve">
    <value>The first cell in the first data row (after the header) of the fields sheet must be filled.</value>
  </data>
  <data name="ImportProductsFirstCellError" xml:space="preserve">
    <value>The first cell in the first data row (after the header) of the products sheet must be filled.</value>
  </data>
  <data name="ImportFieldsTitleRequired" xml:space="preserve">
    <value>Row {0}: If Ident is filled, Title must also be filled.</value>
  </data>
  <data name="ImportFieldsComponentTypeRequired" xml:space="preserve">
    <value>Row {0}: If Ident is filled, ComponentType must also be filled.</value>
  </data>
  <data name="ImportFieldsValueRequired" xml:space="preserve">
    <value>Row {0}: If ComponentType is SingleChoice, Tile, or Selectbox, at least one value must be defined.</value>
  </data>
  <data name="ImportFieldsExpressionRequired" xml:space="preserve">
    <value>Row {0}: If ComponentType is Expression, an expression must be defined.</value>
  </data>
  <data name="ImportFieldsProductRequired" xml:space="preserve">
    <value>Row {0}: If ComponentType is Product, a product must be defined.</value>
  </data>
  <data name="ImportFieldsNumericValueRequired" xml:space="preserve">
    <value>Row {0}: If Value Title is filled, Numeric Value must also be filled.</value>
  </data>
  <data name="ImportFieldsValueOrderRequired" xml:space="preserve">
    <value>Row {0}: If Value Title is filled, Value Order must also be filled.</value>
  </data>
  <data name="MissingProductForComposition" xml:space="preserve">
    <value>Row {0}: A composition must have at least one product.</value>
  </data>
  <data name="ImportCompositionsOrderRequired" xml:space="preserve">
    <value>Row {0}: If a composition is defined, the order must also be filled.</value>
  </data>
  <data name="ImportProductNameRequired" xml:space="preserve">
    <value>Row {0}:  Product name must be filled.</value>
  </data>
  <data name="ImportProductCodeRequired" xml:space="preserve">
    <value>Row {0}: Product code must be filled.</value>
  </data>
  <data name="ImportProductOrderRequired" xml:space="preserve">
    <value>Row {0}: Product order must be filled.</value>
  </data>
  <data name="ImportProductVolumeRequired" xml:space="preserve">
    <value>Row {0}: Product volume must be filled.</value>
  </data>
  <data name="ImportProductUnitRequired" xml:space="preserve">
    <value>Row {0}: Product unit must be filled.</value>
  </data>
  <data name="ImportCategoryOrderRequired" xml:space="preserve">
    <value>Row {0}: If a category is defined, the order must also be filled.</value>
  </data>
  <data name="ImportFieldOrderRequired" xml:space="preserve">
    <value>Row {0}: If a field is defined, the order must also be filled.</value>
  </data>
  <data name="CalculatorExpressionEmpty" xml:space="preserve">
    <value>Expression string cannot be null or empty</value>
  </data>
  <data name="CalculatorExpressionEvaluatedNull" xml:space="preserve">
    <value>Expression '{0}' evaluated to null</value>
  </data>
  <data name="CalculatorResultParseError" xml:space="preserve">
    <value>Cannot parse expression result '{0}' to decimal</value>
  </data>
  <data name="CalculatorEvaluationError" xml:space="preserve">
    <value>Expression evaluation failed: {0}</value>
  </data>
  <data name="CalculatorInvalidArgument" xml:space="preserve">
    <value>Invalid expression or parameters: {0}</value>
  </data>
  <data name="CalculatorUnexpectedError" xml:space="preserve">
    <value>Unexpected error during calculation: {0}</value>
  </data>
</root>