{"version": "0.2.0", "configurations": [{"name": "API", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api", "program": "${workspaceFolder}/Deksmart.Api/bin/Debug/net9.0/Deksmart.Api.dll", "args": [], "cwd": "${workspaceFolder}/Deksmart.Api", "stopAtEntry": false, "postDebugTask": "kill-dotnet-processes", "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7276;http://localhost:5144"}}, {"name": "API with Swagger", "type": "coreclr", "request": "launch", "preLaunchTask": "build-api", "program": "${workspaceFolder}/Deksmart.Api/bin/Debug/net9.0/Deksmart.Api.dll", "args": [], "cwd": "${workspaceFolder}/Deksmart.Api", "stopAtEntry": false, "postDebugTask": "kill-dotnet-processes", "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s/deksmart/doc"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7276;http://localhost:5144"}}, {"name": "Web (Blazor WebAssembly)", "type": "b<PERSON><PERSON><PERSON><PERSON>", "request": "launch", "cwd": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web", "url": "https://localhost:7209", "browser": "chrome", "webRoot": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web", "preLaunchTask": "build-web", "postDebugTask": "kill-dotnet-processes"}, {"name": "Web (Server)", "type": "coreclr", "request": "launch", "preLaunchTask": "build-web", "program": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web/bin/Debug/net9.0/Deksmart.Ui.Web.dll", "args": [], "cwd": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web", "stopAtEntry": false, "postDebugTask": "kill-dotnet-processes", "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7209;http://localhost:5213"}}, {"name": "UI Server", "type": "coreclr", "request": "launch", "preLaunchTask": "build-ui-server", "program": "${workspaceFolder}/Deksmart.Ui.Server/bin/Debug/net9.0/Deksmart.Ui.Server.dll", "args": [], "cwd": "${workspaceFolder}/Deksmart.Ui.Server", "stopAtEntry": false, "postDebugTask": "kill-dotnet-processes", "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7209;http://localhost:5213"}}, {"name": "Web (Firefox)", "type": "firefox", "request": "launch", "url": "https://localhost:7209/", "webRoot": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web", "preLaunchTask": "run-blazor-wasm-background", "postDebugTask": "kill-dotnet-processes", "pathMappings": [{"url": "https://localhost:7209", "path": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui.Web/wwwroot"}], "profile": "default", "keepProfileChanges": true, "reAttach": true, "timeout": 30000}, {"name": "MAUI Windows", "type": "coreclr", "request": "launch", "preLaunchTask": "build-maui-windows", "program": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui/bin/Debug/net9.0-windows10.0.19041.0/win10-x64/Deksmart.Ui.exe", "cwd": "${workspaceFolder}/Deksmart.Ui/Deksmart.Ui", "postDebugTask": "kill-dotnet-processes"}], "compounds": [{"name": "API + Web", "configurations": ["API", "Web (Blazor WebAssembly)"]}, {"name": "API + Web (Firefox)", "configurations": ["API", "Web (Firefox)"], "stopAll": true}, {"name": "API with Swagger + Web (Firefox)", "configurations": ["API with Swagger", "Web (Firefox)"], "stopAll": true}, {"name": "API + MAUI Windows", "configurations": ["API", "MAUI Windows"]}, {"name": "API + UI Server", "configurations": ["API", "UI Server"], "stopAll": true}, {"name": "API with Swagger + UI Server", "configurations": ["API with Swagger", "UI Server"], "stopAll": true}]}