using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Deksmart.Ui.Shared.Services;
using Microsoft.Extensions.Logging;
using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Factory;

namespace Deksmart.Ui.Shared.Components.Configurator
{
    public partial class TabbedConfigurator : ComponentBase, IDisposable
    {
        [Inject]
        public IConfiguratorGridServiceFactory ConfiguratorGridServiceFactory { get; set; } = default!;

        [Parameter]
        public IConfiguratorGridService MainConfiguratorGridService { get; set; } = default!;

        [Inject]
        protected ILogger<TabbedConfigurator> Logger { get; set; } = default!;

        [Inject]
        protected TabDragDropService DragDropService { get; set; } = default!;

        public string? MainEndDescription => MainConfiguratorGridService.ConfiguratorWrapper?.EndDescription;
        private bool _showConfiguratorSelection;
        private IConfiguratorGridService? _activeConfiguratorService;
        private IConfiguratorGridService? _editingService;

        // Drag/drop state is now managed by the service
        private IConfiguratorGridService? _draggedService => DragDropService.DraggedService;
        private IConfiguratorGridService? _dragOverService => DragDropService.DragOverService;
        private int? _dropZoneIndex => DragDropService.DropZoneIndex;

        private IConfiguratorGridService? ActiveConfiguratorService
        {
            get
            {
                if (_activeConfiguratorService == null && MainConfiguratorGridService.ChildServices.Any())
                {
                    _activeConfiguratorService = MainConfiguratorGridService.ChildServices.First();
                }
                return _activeConfiguratorService;
            }
            set
            {
                if (_activeConfiguratorService != value)
                {
                    _activeConfiguratorService = value;
                    if (_activeConfiguratorService != null)
                    {
                        LogTabOperation("Active configurator changed", _activeConfiguratorService);
                    }
                    StateHasChanged();
                }
            }
        }

        protected override void OnInitialized()
        {
            if (MainConfiguratorGridService.ChildServices.Any())
            {
                if (_activeConfiguratorService == null)
                {
                    ActiveConfiguratorService = MainConfiguratorGridService.ChildServices.First();
                    // Set the active child configurator in the main service
                    SetActiveChildService(_activeConfiguratorService);
                }
            }

            // Subscribe to validation state changes
            SubscribeToValidationEvents(MainConfiguratorGridService);
            foreach (var service in MainConfiguratorGridService.ChildServices)
            {
                SubscribeToValidationEvents(service);
            }
            
            // Subscribe to loading state changes
            SubscribeToLoadingEvents(MainConfiguratorGridService);
            foreach (var service in MainConfiguratorGridService.ChildServices)
            {
                SubscribeToLoadingEvents(service);
            }

            // Subscribe to drag/drop service events
            DragDropService.TabReordered += OnTabReordered;
            DragDropService.IndicatorChanged += OnIndicatorChanged;
            DragDropService.DragEnded += OnDragEnded;

            // Update the header with the main configurator's title
            UpdateHeaderState();

            base.OnInitialized();
        }

        protected override void OnParametersSet()
        {
            base.OnParametersSet();

            // Reset active configurator service when parameters change to ensure proper refresh
            if (MainConfiguratorGridService.ChildServices.Any())
            {
                _activeConfiguratorService = null; // Force reset
                ActiveConfiguratorService = MainConfiguratorGridService.ChildServices.First();
                SetActiveChildService(ActiveConfiguratorService);
            }

            // Update the header whenever parameters change
            UpdateHeaderState();
        }

        private void UpdateHeaderState()
        {
            if (MainConfiguratorGridService?.ConfiguratorWrapper != null)
            {
                var title = MainConfiguratorGridService.ConfiguratorWrapper.Title;
                var saveCallback = EventCallback.Factory.Create(this, SaveMainConfigurator);
                HeaderState.UpdateHeader(title, true, MainConfiguratorGridService.IsLoading, saveCallback);
            }
        }

        private async Task SaveMainConfigurator()
        {
            if (MainConfiguratorGridService != null)
            {
                await MainConfiguratorGridService.SavePreset();
            }
        }

        private void OnValidationStateChanged(object? sender, EventArgs e)
        {
            InvokeAsync(() =>
            {
                UpdateParentValidationState();
                StateHasChanged();
            });
        }

        private void SetActiveConfigurator(IConfiguratorGridService service)
        {
            if (_activeConfiguratorService != service)
            {
                // Set the new active service
                ActiveConfiguratorService = service;

                // Update the MainConfiguratorGridService with the active child service
                SetActiveChildService(service);
            }
        }

        private void ShowConfiguratorSelection()
        {
            _showConfiguratorSelection = true;
            StateHasChanged();
        }

        private void HideConfiguratorSelection()
        {
            _showConfiguratorSelection = false;
            StateHasChanged();
        }

        private async Task OnConfiguratorSelected(ChildConfiguratorDto selectedConfigurator)
        {
            HideConfiguratorSelection();
            await OpenNewConfigurator(selectedConfigurator);
        }

        private async Task OpenNewConfigurator(ChildConfiguratorDto configuratorTemplate)
        {
            Logger.LogInformation("Opening new configurator based on template ID: {TemplateId}", configuratorTemplate.Id);

            var newService = ConfiguratorGridServiceFactory.CreateService();
            newService.MainConfiguratorGridService = MainConfiguratorGridService;

            await newService.LoadDataAsync(configuratorTemplate.Id);

            if (newService.ConfiguratorWrapper != null)
            {
                // Add to the service list first
                MainConfiguratorGridService.ChildServices.Add(newService);
                
                // Update TabOrder for all services, including the new one
                UpdateTabOrders();

                // Set the TabTitle for multiple instances
                var existingInstances = MainConfiguratorGridService.ChildServices
                    .Where(c => c.ConfiguratorWrapper?.Id == newService.ConfiguratorWrapper.Id)
                    .ToList();
                if (existingInstances.Count > 1)
                {
                    newService.ConfiguratorWrapper.TabTitle = $"{newService.ConfiguratorWrapper.Title} {existingInstances.Count}";
                }
                LogConfiguratorAction("Added new configurator", newService, "to main list");

                // Subscribe to validation state changes for the new service
                SubscribeToValidationEvents(newService);
                
                // Subscribe to loading state changes for the new service
                SubscribeToLoadingEvents(newService);

                ActiveConfiguratorService = newService;

                // Update the MainConfiguratorGridService with the active child service
                SetActiveChildService(newService);

                // Update the parent's IsValid property after adding a new tab
                if (MainConfiguratorGridService.ConfiguratorWrapper != null)
                {
                    UpdateParentValidationState();
                }
            }
            else
            {
                Logger.LogError("Failed to load configurator data for template ID: {TemplateId}", configuratorTemplate.Id);
                newService.Dispose();
            }
        }

        private void CloseTab(IConfiguratorGridService service)
        {
            if (MainConfiguratorGridService.ChildServices.Count > 1)
            {
                LogTabOperation("Attempting to close tab", service);

                if (MainConfiguratorGridService.ChildServices.Remove(service))
                {
                    LogTabOperation("Removed service", service);

                    // Unsubscribe from validation state changes
                    UnsubscribeFromValidationEvents(service);
                    service.Dispose();

                    if (ActiveConfiguratorService == service)
                    {
                        LogTabOperation("Closed the active tab, setting new active tab");
                        ActiveConfiguratorService = MainConfiguratorGridService.ChildServices.First();

                        // Update the MainConfiguratorGridService with the new active child service
                        SetActiveChildService(ActiveConfiguratorService);
                    }

                    // Update the parent's IsValid property after removing a tab
                    if (MainConfiguratorGridService.ConfiguratorWrapper != null)
                    {
                        UpdateParentValidationState();
                    }
                }
                else
                {
                    Logger.LogWarning("Failed to remove service from main list");
                }
            }
            else
            {
                Logger.LogWarning("CloseTab called but cannot remove the last tab");
            }
        }

        private void OnDragStart(IConfiguratorGridService service)
        {
            DragDropService.SetEditMode(_editingService != null);
            DragDropService.StartDrag(service);
            StateHasChanged();
        }

        private void OnDragEnd()
        {
            DragDropService.EndDrag();
        }

        // Event handlers for drag/drop service events
        private void OnTabReordered(object? sender, TabReorderEventArgs e)
        {
            var services = MainConfiguratorGridService.ChildServices.ToList();
            var draggedIndex = services.IndexOf(e.DraggedService);

            if (draggedIndex != -1 && e.TargetIndex >= 0 && e.TargetIndex <= services.Count)
            {
                // Remove the dragged service first
                services.RemoveAt(draggedIndex);

                // Adjust target index if needed (since we removed an item)
                var insertIndex = e.TargetIndex > draggedIndex ? e.TargetIndex - 1 : e.TargetIndex;

                // Ensure we don't insert beyond the list bounds
                insertIndex = Math.Min(insertIndex, services.Count);

                // Insert at the new position
                services.Insert(insertIndex, e.DraggedService);

                // Update the main service's child list
                MainConfiguratorGridService.ChildServices.Clear();
                MainConfiguratorGridService.ChildServices.AddRange(services);

                // Update TabOrder for all services to match their new order (1-based)
                UpdateTabOrders();

                // Update validation state after reordering
                if (MainConfiguratorGridService.ConfiguratorWrapper != null)
                {
                    UpdateParentValidationState();
                }

                Logger.LogInformation("Successfully reordered tabs - Dragged: {DraggedId} to index: {TargetIndex}",
                    e.DraggedService.ConfiguratorWrapper?.Id, e.TargetIndex);
                
                StateHasChanged();
            }
        }

        private void OnIndicatorChanged(object? sender, DragIndicatorEventArgs e)
        {
            StateHasChanged();
        }

        private void OnDragEnded(object? sender, EventArgs e)
        {
            StateHasChanged();
        }


        private void StartEditing(IConfiguratorGridService service)
        {
            _editingService = service;
            StateHasChanged();
        }

        private void StopEditing()
        {
            _editingService = null;
            StateHasChanged();
        }

        private void UpdateTabName(IConfiguratorGridService service, string? newName)
        {
            if (service.ConfiguratorWrapper != null)
            {
                if (string.IsNullOrWhiteSpace(newName))
                {
                    service.ConfiguratorWrapper.TabTitle = null;
                }
                else
                {
                    service.ConfiguratorWrapper.TabTitle = newName;
                }
                StateHasChanged();
            }
        }

        private void HandleKeyDown(KeyboardEventArgs e, IConfiguratorGridService service)
        {
            if (e.Key == "Enter")
            {
                StopEditing();
            }
            else if (e.Key == "Escape")
            {
                if (service.ConfiguratorWrapper != null)
                {
                    service.ConfiguratorWrapper.TabTitle = null;
                }
                StopEditing();
            }
        }

        // Delegate all drag/drop operations to the service
        private void OnDropZoneOver(int index)
        {
            DragDropService.SetEditMode(_editingService != null);
            DragDropService.HandleDropZoneOver(index, MainConfiguratorGridService.ChildServices);
        }

        private void OnDropZoneLeave(int index)
        {
            DragDropService.HandleDropZoneLeave(index);
        }

        private void OnDropZoneDrop(int targetIndex)
        {
            DragDropService.HandleDropZoneDrop(targetIndex);
        }

        private void OnTabDragOver(IConfiguratorGridService service, int serviceIndex, Microsoft.AspNetCore.Components.Web.DragEventArgs e)
        {
            DragDropService.SetEditMode(_editingService != null);
            DragDropService.HandleTabDragOver(service, serviceIndex, e, MainConfiguratorGridService.ChildServices);
        }

        private void OnTabDragLeave(IConfiguratorGridService service)
        {
            DragDropService.HandleTabDragLeave(service);
        }

        private void OnTabDrop(IConfiguratorGridService targetService, int targetIndex)
        {
            DragDropService.HandleTabDrop();
        }

        // Add button drag/drop delegation
        private void OnAddButtonDragOver()
        {
            DragDropService.HandleAddButtonDragOver(MainConfiguratorGridService.ChildServices);
        }

        private void OnAddButtonDragLeave()
        {
            DragDropService.HandleAddButtonDragLeave(MainConfiguratorGridService.ChildServices);
        }

        private void OnAddButtonDrop()
        {
            DragDropService.HandleAddButtonDrop(MainConfiguratorGridService.ChildServices);
        }

        private bool AreAllChildServicesValid()
        {
            return MainConfiguratorGridService.ChildServices
                .Where(s => s.ConfiguratorWrapper != null)
                .All(s => s.ConfiguratorWrapper!.IsValid);
        }

        private bool CanAllChildServicesAddToCart()
        {
            return MainConfiguratorGridService.ChildServices
                .Where(s => s.ConfiguratorWrapper != null)
                .All(s => s.ConfiguratorWrapper!.CanAddToCart);
        }

        private void SubscribeToValidationEvents(IConfiguratorGridService service)
        {
            service.ValidationStateChanged += OnValidationStateChanged;
        }

        private void UnsubscribeFromValidationEvents(IConfiguratorGridService service)
        {
            service.ValidationStateChanged -= OnValidationStateChanged;
        }

        private void SubscribeToLoadingEvents(IConfiguratorGridService service)
        {
            service.LoadingStateChanged += OnLoadingStateChanged;
        }

        private void UnsubscribeFromLoadingEvents(IConfiguratorGridService service)
        {
            service.LoadingStateChanged -= OnLoadingStateChanged;
        }

        private void OnLoadingStateChanged(object? sender, EventArgs e)
        {
            InvokeAsync(() =>
            {
                UpdateHeaderState();
                StateHasChanged();
            });
        }

        private void UpdateParentValidationState()
        {
            if (MainConfiguratorGridService.ConfiguratorWrapper != null)
            {
                bool allChildrenValid = AreAllChildServicesValid();
                bool allChildrenCanAddToCart = CanAllChildServicesAddToCart();
                
                // Only update if values actually changed to avoid infinite loops
                bool currentIsValid = MainConfiguratorGridService.ConfiguratorWrapper.IsValid;
                bool currentCanAddToCart = MainConfiguratorGridService.ConfiguratorWrapper.CanAddToCart;
                
                if (currentIsValid != allChildrenValid || currentCanAddToCart != allChildrenCanAddToCart)
                {
                    MainConfiguratorGridService.ConfiguratorWrapper.UpdateIsValid(allChildrenValid);
                    MainConfiguratorGridService.ConfiguratorWrapper.UpdateCanAddToCart(allChildrenCanAddToCart);
                    MainConfiguratorGridService.NotifyValidationStateChanged();
                }
            }
        }

        private void SetActiveChildService(IConfiguratorGridService service)
        {
            MainConfiguratorGridService.ActiveChildConfiguratorService = service;
            Logger.LogInformation("Set active child service to configurator ID: {ConfiguratorId}", service.ConfiguratorWrapper?.Id);
        }

        private void LogConfiguratorAction(string action, IConfiguratorGridService service, string? additionalInfo = null)
        {
            var message = string.IsNullOrEmpty(additionalInfo) 
                ? $"{action} for configurator ID: {{ConfiguratorId}}, Title: {{Title}}"
                : $"{action} for configurator ID: {{ConfiguratorId}}, Title: {{Title}} - {additionalInfo}";
                
            Logger.LogInformation(message, service.ConfiguratorWrapper?.Id, service.ConfiguratorWrapper?.Title);
        }

        private void LogTabOperation(string operation, IConfiguratorGridService? service = null)
        {
            if (service != null)
            {
                Logger.LogInformation("{Operation} for Configurator ID: {ConfiguratorId}", operation, service.ConfiguratorWrapper?.Id);
            }
            else
            {
                Logger.LogInformation("{Operation}", operation);
            }
        }

        /// <summary>
        /// Updates TabOrder for all child services to match their current position (1-based indexing)
        /// </summary>
        private void UpdateTabOrders()
        {
            for (int i = 0; i < MainConfiguratorGridService.ChildServices.Count; i++)
            {
                var service = MainConfiguratorGridService.ChildServices[i];
                if (service.ConfiguratorWrapper != null)
                {
                    service.ConfiguratorWrapper.TabOrder = i + 1;
                }
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from all validation state change events
                UnsubscribeFromValidationEvents(MainConfiguratorGridService);

                foreach (var service in MainConfiguratorGridService.ChildServices)
                {
                    UnsubscribeFromValidationEvents(service);
                }
                
                // Unsubscribe from all loading state change events
                UnsubscribeFromLoadingEvents(MainConfiguratorGridService);

                foreach (var service in MainConfiguratorGridService.ChildServices)
                {
                    UnsubscribeFromLoadingEvents(service);
                }

                // Unsubscribe from drag/drop service events
                DragDropService.TabReordered -= OnTabReordered;
                DragDropService.IndicatorChanged -= OnIndicatorChanged;
                DragDropService.DragEnded -= OnDragEnded;

                // Clear the header state when this component is disposed
                HeaderState.ClearHeader();
            }
        }
    }
}