{
    "Logging": {
        "LogLevel": {
            "Default": "Information",
            "Microsoft.AspNetCore": "Warning",
            "Microsoft.AspNetCore.Diagnostics.ExceptionHandlerMiddleware": "None"
        }
    },
    "ApiCore": {
        "Route": {
            "BasePath": "core"
        },
        "Mail": {
            "Host": "testmail.dek.cz",
            "Port": 587,
            "MailFrom": "<EMAIL>",
            "Password": "r2vE3TntU4Rf7d8VjSws",
            "NameFrom": "Eshop DEK"
        },
        "Logger": {
            "DefaultMailTo": "<EMAIL>",
            "MailUncatchExceptionEnabled": true,
            "MailUncatchExceptionMinutesInterval": 30,
            "MailSubjectAffix": "exception affix",
            "MailUncatchExceptionTo": [
                "<EMAIL>"
            ],
            "MailExceptionTo": [
                "<EMAIL>"
            ],
            "MailStdLogTo": [
                "<EMAIL>"
            ]
        },
        "SqlProfiler": {
            "Enabled": true
        },
        "Swagger": {
            "Enabled": true,
            "ProjectName": "Core Api",
            "Version": "1.0.0"
        },
        "Cache": {
            "Redis": {
                "Enabled": true, // If false -> CacheManager is not registered as a service.
                "Host": "it-pazourek2.dek.cz:6379",
                "ReplaceRedisWithDevNullAdapter": false // If ture -> Disable Cache without code changes. If you already use CacheManager, you can disable it by setting.
            }
        }
    },
    "Application": {
        "EshopId": "dek_cz"
    },
    "Global": {
        "EshopId": "dek_cz"
    },
    "Mssql": {
        "User": "USER_API",
        "Password": "aBMajfDBj%wd4jAd-!Q7cuqJU",
        "Database": "TEST_ESHOP", // Devel podle JWT -> instance // Weber nema v tokenu na devel instance
        "Host": "apps-e.dek.cz\\ESHOP2"
    },
    "Pgsql": {
        // Produkce podle Global:EshopId. Devel podle JWT
        "dek_cz": {
            "Host": "postgresqltest.dek.cz",
            "Port": 5160,
            "Database": "eshop_dek_cz",
            "Username": "eshop_dek_cz",
            "Password": "pxPGEfCQ7rsv8SRDTKhc6nHqN",
            "Pooling": true
        }
    },
    "Jwt": {
        "Enabled": true,
        "Expiration": false,
        "AsymmetricKeys": [
            "MIIBigKCAYEAw8wZjc7vItdcx+R4IxzoAJRIhEcfXKNSXcwE8Ty6y7xCg0DAg8HdxrOztI5ntnBQa4DveHvdmwtMV9mcE9NzGt6eU6OdZU32SqYJPuUXr/eCiG9pV2PMzCXAMu9+sXlvKZor7mbkMog4e03pElKedEwxgIujWUMhD0TnUmdU1A5+HnBWv0MlASyevVWBzw3aPF8ybmVxPXbBuxBE5KOIYNxxXii8JCjdzZJ/9owbWAckzB1MSCkWHzQJRx3cUeGYNa2BSWx0CMfW3nHxMX0QKAlvSQSxTYhtvgMBqDPK2zLF9W4VZ4Z0FpZg7IFGod7aOFapyywoslXRgYLKdhALqv2z46/fnnWwQlwwEPfGfX9mkE4Skrxf7cY4EqHVI8yP+WM1oLGN6mqFIUniX+tsTS/7Bfx3SD7VCwx9ykv6WkQXuYMXSfiXQaMfLwSQzchUeveFTMmiWaGBgsP/UDvDZhCfBvdBcRttDMzbtMi3ERg31jXM22VadcCzxCwWcgj9AgMBAAE="
        ],
        "SymmetricKeys": [
            "$ecretf0rt3st$ecretf0rt3st$ecretf0rt3st$ecretf0rt3st"
        ],
        //"Issuers": [],
        //"Audiences": []
    },
    "AllowedHosts": "*"
}
