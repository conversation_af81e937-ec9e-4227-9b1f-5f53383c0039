using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Deksmart.Infrastructure.Context;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Enum;

namespace Deksmart.Integration.Tests.Fixtures
{
    /// <summary>
    /// Seeds test data for integration tests with configurator entities and relationships.
    /// Provides thread-safe seeding for in-memory database used in integration testing.
    /// </summary>
    public class IntegrationTestDataSeeder
    {
        private static readonly SemaphoreSlim _seedingSemaphore = new(1, 1);
        private static bool _isSeeded = false;

        public static async Task SeedSimpleTestDataAsync(IServiceProvider serviceProvider)
        {
            await _seedingSemaphore.WaitAsync();
            
            try
            {
                if (_isSeeded)
                {
                    Console.WriteLine("Database already seeded by another test, skipping...");
                    return;
                }

                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<ConfiguratorContext>();

                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("Database created/ensured");
                
                // Double-check if data already exists to avoid duplicate seeding
                var existingExpressions = await context.ConfiguratorExpressions.CountAsync();
                Console.WriteLine($"Existing expressions count: {existingExpressions}");
                
                if (existingExpressions > 0)
                {
                    Console.WriteLine("Data already seeded, skipping...");
                    _isSeeded = true;
                    return; // Data already seeded
                }
                
                Console.WriteLine("Starting database seeding...");
                
                // Seed minimal but functional test data
                await SeedExpressionsAsync(context);
                await SeedConfiguratorsAsync(context);
                await SeedCategoriesAsync(context);
                await SeedFieldsAsync(context);
                await SeedCompositionsAsync(context);
                await SeedProductsAsync(context);
                
                var finalCount = await context.Configurators.CountAsync();
                Console.WriteLine($"Seeding completed. Final configurator count: {finalCount}");
                
                _isSeeded = true;
            }
            finally
            {
                _seedingSemaphore.Release();
            }
        }

        private static async Task SeedExpressionsAsync(ConfiguratorContext context)
        {
            var expressions = new List<ConfiguratorExpression>
            {
                new ConfiguratorExpression { Id = 1, Expression = "1" }, // Always visible
                new ConfiguratorExpression { Id = 2, Expression = "[HEIGHT] * [WIDTH]" }, // Area calculation
                new ConfiguratorExpression { Id = 3, Expression = "[HEIGHT] * 2 + [WIDTH] * 2" } // Perimeter
            };

            context.ConfiguratorExpressions.AddRange(expressions);
            await context.SaveChangesAsync();
        }

        private static async Task SeedConfiguratorsAsync(ConfiguratorContext context)
        {
            var configurators = new List<Configurator>
            {
                new Configurator
                {
                    Id = 1,
                    Title = "Simple Test Configurator",
                    Description = "Basic configurator for integration testing",
                    IsComposite = false,
                    ShowInMenu = true
                }
            };

            context.Configurators.AddRange(configurators);
            await context.SaveChangesAsync();
        }

        private static async Task SeedCategoriesAsync(ConfiguratorContext context)
        {
            var categories = new List<ConfiguratorFieldCategory>
            {
                new ConfiguratorFieldCategory
                {
                    Id = 1,
                    ConfiguratorId = 1,
                    Title = "Basic Inputs",
                    Order = 1,
                    CollapseState = CategoryCollapseState.Expanded,
                    VisibilityId = 1 // Always visible
                }
            };

            context.ConfiguratorFieldCategories.AddRange(categories);
            await context.SaveChangesAsync();
        }

        private static async Task SeedFieldsAsync(ConfiguratorContext context)
        {
            var fields = new List<ConfiguratorField>
            {
                new ConfiguratorField
                {
                    Id = 1,
                    FieldCategoryId = 1,
                    Title = "Height",
                    Suffix = "m",
                    Order = 1,
                    Ident = "HEIGHT",
                    ComponentType = ComponentType.Numeric,
                    FieldPosition = FieldPosition.Left,
                    MinValue = 2,
                    MaxValue = 4,
                    DefaultValue = 2.7m
                },
                new ConfiguratorField
                {
                    Id = 2,
                    FieldCategoryId = 1,
                    Title = "Width", 
                    Suffix = "m",
                    Order = 2,
                    Ident = "WIDTH",
                    ComponentType = ComponentType.Numeric,
                    FieldPosition = FieldPosition.Right,
                    MinValue = 1,
                    MaxValue = 10,
                    DefaultValue = 3.0m
                }
            };

            context.ConfiguratorFields.AddRange(fields);
            await context.SaveChangesAsync();
        }

        private static async Task SeedCompositionsAsync(ConfiguratorContext context)
        {
            var compositions = new List<ConfiguratorComposition>
            {
                new ConfiguratorComposition
                {
                    Id = 1,
                    ConfiguratorId = 1,
                    Title = "Products",
                    Order = 1,
                    VisibilityId = 1, // Always visible
                    IsMultipleProducts = false
                }
            };

            context.ConfiguratorCompositions.AddRange(compositions);
            await context.SaveChangesAsync();
        }

        private static async Task SeedProductsAsync(ConfiguratorContext context)
        {
            var products = new List<ConfiguratorProduct>
            {
                new ConfiguratorProduct
                {
                    Id = 1,
                    CompositionId = 1,
                    ProductCode = "TEST-PRODUCT-001",
                    Title = "Test Product",
                    Order = 1,
                    ProductUnit = "m2",
                    ProductVolume = 1.0m,
                    VisibilityId = 1, // Always visible
                    QuantityId = 2 // Area calculation
                }
            };

            context.ConfiguratorProducts.AddRange(products);
            await context.SaveChangesAsync();
        }

        public static async Task<List<Configurator>> GetTestConfiguratorsAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ConfiguratorContext>();
            
            return await context.Configurators
                .Include(c => c.ConfiguratorFieldCategories.Where(cat => !cat.IsDeleted))
                .ThenInclude(cat => cat.ConfiguratorFields.Where(f => !f.IsDeleted))
                .Include(c => c.ConfiguratorCompositions.Where(comp => !comp.IsDeleted))
                .ThenInclude(comp => comp.ConfiguratorProducts.Where(p => !p.IsDeleted))
                .Where(c => c.ShowInMenu)
                .ToListAsync();
        }
    }
}