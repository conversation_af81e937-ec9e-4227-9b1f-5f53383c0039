using DEK.Eshop.ApiCore.Database;
using DEK.Eshop.ApiCore.Indentity;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;

namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class IndentityBootstrap
{
    public static void AddIndentityBootstrap(this IServiceCollection services)
    {
        services.AddScoped<IUserStore<ApplicationUser>, ApplicationUserStore>();
        services.AddScoped<ApplicationUserManager>();

        services.AddScoped<MssqlContextFactory<DbUserContext>>();
        services.AddScoped<DbUserRepository>();
    }
}
