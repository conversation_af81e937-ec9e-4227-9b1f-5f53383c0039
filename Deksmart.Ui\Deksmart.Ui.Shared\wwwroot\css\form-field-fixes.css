/* Form field fixes for label alignment */

/* Target paragraphs inside labels and other markup elements to remove bottom margin */
.filter-name label p,
.checkbox-container label p,
.filter-component label p,
.single-choice-filter-item label p,
.tile p,
.tooltip p,
.input-suffix p,
option p,
.card-body p,
.excel-table td p {
    margin-top: 0;
    margin-bottom: 0;
}

/* Fix for all elements that might contain markup content */
p:has(> span[blazor-internal-id]),
div:has(> span[blazor-internal-id]) > p,
label:has(> span[blazor-internal-id]) > p,
td:has(> span[blazor-internal-id]) > p,
th:has(> span[blazor-internal-id]) > p,
option:has(> span[blazor-internal-id]) > p,
span:has(> span[blazor-internal-id]) > p {
    margin-top: 0;
    margin-bottom: 0;
}

/* Ensure checkbox container labels are vertically centered */
.checkbox-container {
    display: flex;
    align-items: center;
}

.checkbox-container label {
    margin-top: 0;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

/* Fix for single choice radio buttons */
.single-choice-filter-item {
    display: block;
    margin-bottom: 0.1rem; /* Minimal spacing between radio buttons */
    line-height: 1.2; /* Reduced line height for more compact appearance */
}

.single-choice-filter-item:last-child {
    margin-bottom: 0; /* Remove margin from last item */
}

.single-choice-filter-item label {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0.5rem;
    display: inline;
}

/* Fix for paragraphs inside radio button labels */
.single-choice-filter-item label p {
    display: inline;
}

/* Container for radio buttons */
.tile-container:has(.single-choice-filter-item) {
    margin-top: 0.1rem; /* Minimal top margin to match other components */
    margin-bottom: 0.1rem; /* Minimal bottom margin to match other components */
    gap: 0; /* Remove gap between radio buttons */
}

/* Fix for numeric input fields */
.trim-input-container {
    display: flex;
    align-items: center;
}

/* Fix for select boxes */
.filter-component select {
    margin-top: 0;
    margin-bottom: 0;
}

/* Ensure filter rows have consistent height and alignment */
.filter-row {
    align-items: center;
}

.filter-name {
    display: flex;
    align-items: center;
}

/* Fix for any nested elements inside labels */
.filter-name label > *,
.checkbox-container label > *,
.input-suffix > *,
option > * {
    margin-top: 0;
    margin-bottom: 0;
}

/* Ensure consistent vertical alignment for all form elements */
.filter-component,
.filter-name label,
.checkbox-container label,
.trim-input-container,
.input-suffix {
    display: flex;
    align-items: center;
}

/* Fix for tiles with markup content */
.tile p {
    margin-bottom: 0;
}

/* Fix for table cells in the child configurator overview */
.excel-table td[data-label="Field"],
.excel-table td[data-label="Value"] {
    display: flex;
    align-items: center;
}

/* Fix for tooltips with markup content */
.tooltip p:last-child {
    margin-bottom: 0;
}
