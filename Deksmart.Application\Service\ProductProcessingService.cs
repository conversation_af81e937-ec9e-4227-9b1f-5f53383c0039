using Deksmart.Domain.Service;
using Microsoft.Extensions.Logging;
using Deksmart.Application.Resource;
using Deksmart.Application.Service.Http;
using Deksmart.Shared.Dto;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Enum;
using Deksmart.Application.Mapping;

namespace Deksmart.Application.Service
{
    public interface IProductProcessingService
    {
        /// <summary>
        /// Sets product fields for configurator components
        /// </summary>
        /// <param name="configurator">The configurator to set product fields for</param>
        /// <param name="validation">The validation result to add errors to</param>
        Task SetProductFieldsAsync(Configurator configurator, ValidationResult validation);

        /// <summary>
        /// Selects products in configurator compositions based on client selections
        /// </summary>
        /// <param name="configurator">The configurator to select products in</param>
        /// <param name="selectedProducts">The selected products from client</param>
        void SelectProducts(Configurator configurator, List<ClientProductValueDto>? selectedProducts);

        /// <summary>
        /// Calculates prices for products in a single configurator
        /// </summary>
        /// <param name="configurator">The configurator to calculate prices for</param>
        /// <param name="validation">The validation result to add errors to</param>
        Task CalculatePricesForProductsAsync(Configurator configurator, ValidationResult validation);

        /// <summary>
        /// Validates products in the configurator
        /// </summary>
        /// <param name="configurator">The configurator to validate products for</param>
        /// <returns>True if all products are valid</returns>
        bool ValidateProducts(Configurator configurator);

        /// <summary>
        /// Aggregates products from child configurators and calculates prices for the composite configurator
        /// </summary>
        /// <param name="compositeConfigurator">The composite configurator to aggregate products for</param>
        /// <param name="validation">The validation result to add errors to</param>
        Task AggregateAndCalculateProductsAsync(Configurator compositeConfigurator, ValidationResult validation);

        /// <summary>
        /// Aggregates products from multiple child product states into a composite configurator
        /// </summary>
        /// <param name="compositeConfigurator">The composite configurator to aggregate products into</param>
        /// <param name="childProductStates">Product states from all child configurators</param>
        /// <param name="validation">The validation result to add errors to</param>
        Task AggregateFromChildProductStatesAsync(
            Configurator compositeConfigurator, 
            List<ChildProductStateDto> childProductStates, 
            ValidationResult validation);

        /// <summary>
        /// Calculates prices for selected products using external e-shop API data
        /// </summary>
        /// <param name="selectedProducts">The list of selected products for which to calculate prices</param>
        /// <param name="currentProductCode">The current product code for context</param>
        /// <returns>
        /// A tuple containing the <see cref="ProductPriceCalculationResponse"/> (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors
        /// </returns>
        Task<(ProductPriceCalculationResponse? result, ValidationResult validation)> CalculateSelectedProductPricesAsync(
            List<SelectedProductDto> selectedProducts,
            string currentProductCode);
    }

    public class ProductProcessingService : IProductProcessingService
    {
        private readonly ILogger<ProductProcessingService> _logger;
        private readonly IEshopApiService _eshopApiService;
        private readonly IProductPriceService _productPriceService;
        private readonly IConfiguratorMappingService _mappingService;
        private readonly IProductAggregationMappingService _aggregationMappingService;

        public ProductProcessingService(
            ILogger<ProductProcessingService> logger,
            IEshopApiService eshopApiService,
            IProductPriceService productPriceService,
            IConfiguratorMappingService mappingService,
            IProductAggregationMappingService aggregationMappingService)
        {
            _logger = logger;
            _eshopApiService = eshopApiService;
            _productPriceService = productPriceService;
            _mappingService = mappingService;
            _aggregationMappingService = aggregationMappingService;
        }

        public async Task SetProductFieldsAsync(Configurator configurator, ValidationResult validation)
        {
            var productFields = configurator.ConfiguratorFieldCategories
                .SelectMany(c => c.ConfiguratorFields)
                .Where(f => f.ComponentType == ComponentType.Product)
                .ToList();

            //api call in foreach, but practically 0-2 calls
            foreach (var productField in productFields)
            {
                var productCode = configurator.ConfiguratorCompositions.FirstOrDefault(c => c.Order == productField.CompositionOrder)?.ConfiguratorProducts.Where(p => p.IsSelected).FirstOrDefault()?.ProductCode;
                if (string.IsNullOrEmpty(productCode))
                    continue;

                var productDetails = await _eshopApiService.GetProductDetailsAsync(productCode);
                if (productDetails.Validation.HasErrors)
                {
                    validation.AddError(productDetails.Validation.GetErrors());
                    continue;
                }
                else
                {
                    productField.ProductDetails = productDetails.Data;
                }
            }
        }

        public void SelectProducts(Configurator configurator, List<ClientProductValueDto>? selectedProducts)
        {
            // If there are no compositions or products, just return
            if (configurator.ConfiguratorCompositions.Count == 0 ||
                !configurator.ConfiguratorCompositions.Any(c => c.ConfiguratorProducts.Count > 0))
            {
                return;
            }

            // Create a dictionary of all products for efficient lookup
            var allProducts = configurator.ConfiguratorCompositions
                .SelectMany(c => c.ConfiguratorProducts)
                .ToDictionary(p => p.Id);

            // If we have selected products, process them
            if (selectedProducts != null && selectedProducts.Count > 0)
            {
                foreach (var selectedProduct in selectedProducts)
                {
                    if (allProducts.TryGetValue(selectedProduct.ProductId, out var product))
                    {
                        product.UserAmount = selectedProduct.Amount;
                        product.IsSelected = true;
                    }
                }
            }

            // For each composition, if no products are selected, select the first one by order
            foreach (var composition in configurator.ConfiguratorCompositions)
            {
                if (composition.ConfiguratorProducts.Count > 0 && !composition.ConfiguratorProducts.Any(p => p.IsSelected))
                {
                    var firstProduct = composition.ConfiguratorProducts.OrderBy(p => p.Order).First();
                    firstProduct.IsSelected = true;
                }
            }
        }

        public async Task CalculatePricesForProductsAsync(Configurator configurator, ValidationResult validation)
        {
            var products = configurator.ConfiguratorCompositions
                .SelectMany(cc => cc.ConfiguratorProducts.Where(d => d.IsSelected).ToList())
                .ToList();

            await CalculateProductPricesAsync(configurator, products, validation);
        }

        public bool ValidateProducts(Configurator configurator)
        {
            // Get all products across all compositions
            var products = configurator.ConfiguratorCompositions
                .SelectMany(c => c.ConfiguratorProducts)
                .ToList();

            var hasValidationErrors = products.Any(p => p.HasValidationError);

            // Set CanAddToCart based on product validation errors (separate from IsValid for field validation)
            configurator.CanAddToCart = !hasValidationErrors;

            return !hasValidationErrors;
        }

        public async Task AggregateAndCalculateProductsAsync(Configurator compositeConfigurator, ValidationResult validation)
        {
            _logger.LogInformation("AggregateAndCalculateProductsAsync called for configurator {ConfiguratorId}. IsComposite: {IsComposite}, ChildCount: {ChildCount}", 
                compositeConfigurator.Id, compositeConfigurator.IsComposite, compositeConfigurator.ChildUserConfigurators.Count);
                
            if (!compositeConfigurator.IsComposite || !compositeConfigurator.ChildUserConfigurators.Any())
            {
                _logger.LogInformation("Skipping aggregation - not composite or no children");
                return;
            }

            // Aggregate products from all child configurators
            var aggregatedCompositions = await AggregateChildConfiguratorProductsAsync(compositeConfigurator.ChildUserConfigurators, validation);
            if (validation.HasErrors)
            {
                return;
            }
            _logger.LogInformation("Created {CompositionCount} aggregated compositions with {ProductCount} total products", 
                aggregatedCompositions.Count, aggregatedCompositions.SelectMany(c => c.ConfiguratorProducts).Count());
                
            compositeConfigurator.ConfiguratorCompositions = aggregatedCompositions;

            // Calculate prices for aggregated products
            var allAggregatedProducts = compositeConfigurator.ConfiguratorCompositions
                .SelectMany(c => c.ConfiguratorProducts)
                .ToList();
            await CalculateProductPricesAsync(compositeConfigurator, allAggregatedProducts, validation);
            
            _logger.LogInformation("Aggregation completed for configurator {ConfiguratorId}", compositeConfigurator.Id);
        }

        /// <summary>
        /// Aggregates products from all child configurators into combined compositions for the composite configurator.
        /// Products with the same ProductCode are grouped together with summed amounts.
        /// </summary>
        /// <param name="childConfigurators">Collection of child configurators to aggregate products from</param>
        /// <param name="validation">Validation result to capture any errors</param>
        /// <returns>List of aggregated configurator compositions</returns>
        private async Task<List<ConfiguratorComposition>> AggregateChildConfiguratorProductsAsync(IEnumerable<Configurator> childConfigurators, ValidationResult validation)
        {
            // Extract products with composition titles and ordering info from child configurators
            var productGroups = childConfigurators
                .SelectMany(child => child.ConfiguratorCompositions
                    .SelectMany(comp => comp.ConfiguratorProducts.Where(p => p.IsSelected)
                        .Select(product => new { Product = product, CompositionTitle = comp.Title, ConfiguratorId = child.Id, TabOrder = child.TabOrder })))
                .GroupBy(x => x.Product.ProductCode)
                .ToList();

            var aggregatedCompositions = new List<ConfiguratorComposition>();

            if (!productGroups.Any())
            {
                return aggregatedCompositions;
            }

            // Fetch unit data for all products
            var uniqueProductCodes = productGroups.Select(g => g.Key).ToList();
            var unitsResult = await _eshopApiService.GetEshopProductUnitsAsync(uniqueProductCodes);
            
            if (unitsResult.Validation.HasErrors)
            {
                validation.AddError($"Failed to get units for UserAmount calculation: {unitsResult.Validation.GetErrors()}");
                return aggregatedCompositions;
            }

            // Create unit lookup by product code
            var unitsLookup = uniqueProductCodes.Zip(unitsResult.Data, (code, unit) => new { Code = code, Unit = unit })
                .ToDictionary(x => x.Code, x => x.Unit);

            foreach (var productGroup in productGroups)
            {
                var products = productGroup.ToList();
                var firstProduct = products.First().Product;
                var productCode = productGroup.Key;
                
                // Get unit data for this product
                var unitData = unitsLookup.GetValueOrDefault(productCode);
                if (unitData == null)
                {
                    validation.AddError($"Unit data not found for product {productCode}");
                    continue;
                }
                
                // Create aggregated composition title from distinct composition titles
                var compositionTitles = products
                    .Select(p => p.CompositionTitle?.Trim())
                    .Where(title => !string.IsNullOrWhiteSpace(title))
                    .Distinct(StringComparer.OrdinalIgnoreCase)
                    .ToList();
                
                var aggregatedCompositionTitle = compositionTitles.Count > 1 
                    ? string.Join(" / ", compositionTitles)
                    : compositionTitles.FirstOrDefault() ?? "Aggregated Products";

                // Find or create composition using helper method
                var composition = FindOrCreateComposition(aggregatedCompositions, aggregatedCompositionTitle);
                
                // Store ordering information for later sorting
                if (composition.Order == 0) // Only set ordering info if not already set
                {
                    // Use the minimum TabOrder for this composition (first occurrence)
                    var minTabOrder = products
                        .Where(p => p.TabOrder.HasValue)
                        .Select(p => p.TabOrder!.Value)
                        .DefaultIfEmpty(int.MaxValue)
                        .Min();
                    
                    composition.Order = minTabOrder == int.MaxValue ? 0 : minTabOrder;
                }

                // Create aggregated product using mapping service
                var aggregatedProduct = _aggregationMappingService.CreateAggregatedProduct(
                    products.Select(p => p.Product).ToList(),
                    composition.ConfiguratorProducts.Count + 1,
                    unitData);

                composition.ConfiguratorProducts.Add(aggregatedProduct);
            }

            // Sort compositions by order (first occurrence TabOrder)
            return aggregatedCompositions.OrderBy(c => c.Order).ToList();
        }

        /// <summary>
        /// Gets basic pricing and units data for products
        /// </summary>
        private async Task<(List<EshopProductPricing> pricing, List<EshopProductUnit> units, ValidationResult validation)> GetBasicEnrichmentDataAsync(List<string> productCodes)
        {
            var validation = new ValidationResult();
            
            // Get enrichment data for all products
            var pricingTask = _eshopApiService.GetEshopProductPricingAsync(productCodes);
            var unitsTask = _eshopApiService.GetEshopProductUnitsAsync(productCodes);

            await Task.WhenAll(pricingTask, unitsTask);

            var pricingResult = await pricingTask;
            var unitsResult = await unitsTask;

            if (pricingResult.Validation.HasErrors)
            {
                validation.AddError(string.Format(DeksmartApplicationResource.PricingError, pricingResult.Validation.GetErrors()));
                _logger.LogWarning("Failed to get product pricing: {Errors}", pricingResult.Validation.GetErrors());
                return (new List<EshopProductPricing>(), new List<EshopProductUnit>(), validation);
            }

            if (unitsResult.Validation.HasErrors)
            {
                validation.AddError(string.Format(DeksmartApplicationResource.UnitsError, unitsResult.Validation.GetErrors()));
                _logger.LogWarning("Failed to get product units: {Errors}", unitsResult.Validation.GetErrors());
                return (new List<EshopProductPricing>(), new List<EshopProductUnit>(), validation);
            }

            return (pricingResult.Data, unitsResult.Data, validation);
        }

        /// <summary>
        /// Creates ProductPriceData collection from pricing, units, and conversion data
        /// </summary>
        private static IEnumerable<ProductPriceData> CreateProductPriceDataCollection(
            List<string> productCodes,
            List<EshopProductPricing> pricingData,
            List<EshopProductUnit> unitsData,
            ILookup<string?, decimal> conversionLookup)
        {
            return productCodes
                .Select((code, index) => new ProductPriceData
                {
                    ProductCode = code,
                    Pricing = pricingData[index],
                    Unit = unitsData[index],
                    // Use null if no conversion found for this product (triggers legacy conversion)
                    ConvertedAmount = conversionLookup.Contains(code) ? (decimal?)conversionLookup[code].FirstOrDefault() : null
                })
                .Where(x => x.Pricing != null && x.Unit != null);
        }

        /// <summary>
        /// Gets enrichment data for products including pricing, units, and conversions
        /// </summary>
        private async Task<IEnumerable<ProductPriceData>> GetProductEnrichmentDataAsync<T>(
            List<string> productCodes,
            List<T> products,
            ValidationResult validation) where T : class
        {
            var (pricingData, unitsData, enrichmentValidation) = await GetBasicEnrichmentDataAsync(productCodes);
            if (enrichmentValidation.HasErrors)
            {
                validation.AddError(enrichmentValidation.GetErrors());
                return Enumerable.Empty<ProductPriceData>();
            }

            // Get unit conversion data using common method
            var conversionResult = await GetUnitConversionsAsync(productCodes, products, unitsData, validation);
            if (validation.HasErrors)
            {
                return Enumerable.Empty<ProductPriceData>();
            }

            // Create lookup dictionaries for explicit matching
            var conversionLookup = conversionResult.ToLookup(x => x.ProductCode, x => x.QuantityOutput);
            
            // Create enrichment data - always using sales units
            return CreateProductPriceDataCollection(productCodes, pricingData, unitsData, conversionLookup);
        }

        /// <summary>
        /// Gets unit conversions for products
        /// </summary>
        private async Task<List<EshopProductUnitConversion>> GetUnitConversionsAsync<T>(
            List<string> productCodes,
            List<T> products,
            List<EshopProductUnit> unitsData,
            ValidationResult validation) where T : class
        {
            // Prepare unit conversion parameters
            var quantities = new List<decimal>();
            var unitInputs = new List<string>();
            var unitOutputs = new List<string>();
            
            for (int i = 0; i < productCodes.Count; i++)
            {
                var productCode = productCodes[i];
                var unitsInfo = unitsData[i];
                
                // Handle different product types - use index-based lookup to handle duplicate product codes
                decimal quantity;
                string productUnit;
                
                if (typeof(T) == typeof(SelectedProductDto))
                {
                    var selectedProduct = products.Cast<SelectedProductDto>().ElementAt(i);
                    quantity = selectedProduct.Amount;
                    productUnit = selectedProduct.Unit;
                }
                else if (typeof(T) == typeof(ConfiguratorProduct))
                {
                    var product = products.Cast<ConfiguratorProduct>().ElementAt(i);
                    quantity = product.CalculatedAmount;
                    productUnit = product.ProductUnit;
                }
                else
                {
                    throw new ArgumentException($"Unsupported product type: {typeof(T)}");
                }
                
                // Use original configurator unit as input, convert to sales unit as output
                quantities.Add(quantity);
                unitInputs.Add(productUnit);
                
                unitOutputs.Add(unitsInfo.UnitSales);
            }
            
            var conversionResult = await _eshopApiService.GetProductUnitConversionsAsync(productCodes, quantities, unitInputs, unitOutputs);
            
            if (conversionResult.Validation.HasErrors)
            {
                // Log the API failure but don't stop processing - fall back to legacy conversion
                _logger.LogWarning("Unit conversion API failed, falling back to legacy conversion logic: {Errors}", conversionResult.Validation.GetErrors());
                
                // Return empty list to trigger legacy conversion fallback
                return new List<EshopProductUnitConversion>();
            }

            return conversionResult.Data;
        }

        public async Task AggregateFromChildProductStatesAsync(
            Configurator compositeConfigurator, 
            List<ChildProductStateDto> childProductStates, 
            ValidationResult validation)
        {
            if (!compositeConfigurator.IsComposite)
            {
                _logger.LogWarning("AggregateFromChildProductStatesAsync called on non-composite configurator {ConfiguratorId}", compositeConfigurator.Id);
                return;
            }

            _logger.LogInformation("Aggregating products from {ChildCount} child product states for configurator {ConfiguratorId}", 
                childProductStates.Count, compositeConfigurator.Id);

            // Create configurator products with composition titles and ordering info from child product states
            var allChildProductsWithComposition = new List<ProductAggregationData>();

            foreach (var childProductState in childProductStates)
            {
                foreach (var selectedProduct in childProductState.SelectedProducts)
                {
                    // Create ProductAggregationData using mapping service
                    var aggregationData = _aggregationMappingService.CreateProductAggregationData(
                        selectedProduct, 
                        childProductState, 
                        allChildProductsWithComposition.Count + 1);

                    allChildProductsWithComposition.Add(aggregationData);
                }
            }

            if (!allChildProductsWithComposition.Any())
            {
                _logger.LogInformation("No products found in child product states, clearing compositions");
                compositeConfigurator.ConfiguratorCompositions = new List<ConfiguratorComposition>();
                _productPriceService.SetConfiguratorTotalPrices(compositeConfigurator, new List<ConfiguratorProduct>());
                return;
            }

            // Convert products to sales units using convert-first-then-sum logic
            var convertedProductsWithComposition = await ConvertProductsToSalesUnitsAsync(allChildProductsWithComposition, validation);
            if (validation.HasErrors)
            {
                return;
            }

            // Now aggregate the converted products (sum the converted amounts) with ordering logic
            var aggregatedCompositions = await AggregateProductsByCodeAsync(convertedProductsWithComposition, validation);
            if (validation.HasErrors)
            {
                return;
            }
            compositeConfigurator.ConfiguratorCompositions = aggregatedCompositions;

            // Calculate prices for aggregated products
            var allAggregatedProducts = aggregatedCompositions
                .SelectMany(c => c.ConfiguratorProducts)
                .ToList();

            await CalculateProductPricesAsync(compositeConfigurator, allAggregatedProducts, validation);

            _logger.LogInformation("Aggregation completed for configurator {ConfiguratorId}: {CompositionCount} compositions, {ProductCount} products", 
                compositeConfigurator.Id, aggregatedCompositions.Count, allAggregatedProducts.Count);
        }

        /// <summary>
        /// Groups products by ProductCode first (priority), then organizes by composition names with complete ordering logic.
        /// Products with same ProductCode are always summed together.
        /// Products are organized into compositions based on normalized composition names (case/whitespace insensitive).
        /// Compositions are ordered by first occurrence TabOrder.CompositionOrder.ProductOrder.
        /// </summary>
        private async Task<List<ConfiguratorComposition>> AggregateProductsByCodeAsync(List<ProductAggregationData> allProductsWithComposition, ValidationResult validation)
        {
            var aggregatedCompositions = new List<ConfiguratorComposition>();

            if (!allProductsWithComposition.Any())
            {
                return aggregatedCompositions;
            }

            // Fetch unit data for all products
            var uniqueProductCodes = allProductsWithComposition.Select(x => x.Product.ProductCode).Distinct().ToList();
            var unitsResult = await _eshopApiService.GetEshopProductUnitsAsync(uniqueProductCodes);
            
            if (unitsResult.Validation.HasErrors)
            {
                validation.AddError($"Failed to get units for UserAmount calculation: {unitsResult.Validation.GetErrors()}");
                return aggregatedCompositions;
            }

            // Create unit lookup by product code
            var unitsLookup = uniqueProductCodes.Zip(unitsResult.Data, (code, unit) => new { Code = code, Unit = unit })
                .ToDictionary(x => x.Code, x => x.Unit);

            // Group by ProductCode first (this is the priority - same products must be summed)
            var productGroups = allProductsWithComposition
                .GroupBy(x => x.Product.ProductCode)
                .ToList();

            foreach (var productGroup in productGroups)
            {
                var productsWithComposition = productGroup.ToList();
                var productCode = productsWithComposition.First().Product.ProductCode;
                
                // Get unit data for this product
                var unitData = unitsLookup.GetValueOrDefault(productCode);
                if (unitData == null)
                {
                    validation.AddError($"Unit data not found for product {productCode}");
                    continue;
                }
                
                // Create aggregated composition title from distinct composition titles using tuple data
                var compositionTitles = productsWithComposition
                    .Select(p => p.CompositionTitle)
                    .Where(title => !string.IsNullOrWhiteSpace(title))
                    .Distinct(StringComparer.OrdinalIgnoreCase)
                    .ToList();

                var aggregatedCompositionTitle = compositionTitles.Count > 1
                    ? string.Join(" / ", compositionTitles)
                    : compositionTitles.FirstOrDefault() ?? "Aggregated Products";

                // Find existing composition with same normalized title or create new one
                var existingComposition = FindOrCreateComposition(aggregatedCompositions, aggregatedCompositionTitle);
                
                // Store ordering information for later sorting using minimum TabOrder.CompositionOrder
                if (existingComposition.Order == 0) // Only set ordering info if not already set
                {
                    // Find the minimum TabOrder.CompositionOrder combination for this composition
                    // Priority: TabOrder > CompositionOrder (ignore ProductOrder for composition ordering)
                    var minTabComposition = productsWithComposition
                        .Select(p => new { TabOrder = p.TabOrder ?? int.MaxValue, CompositionOrder = p.CompositionOrder })
                        .OrderBy(x => x.TabOrder)
                        .ThenBy(x => x.CompositionOrder)
                        .First();
                    
                    // Create a composite order: TabOrder * 10000 + CompositionOrder
                    // This ensures proper ordering: 1/4 < 1/5 < 3/2
                    if (minTabComposition.TabOrder == int.MaxValue)
                    {
                        existingComposition.Order = int.MaxValue;
                    }
                    else
                    {
                        existingComposition.Order = minTabComposition.TabOrder * 10000 + minTabComposition.CompositionOrder;
                    }
                }

                // Extract products from tuples for aggregation
                var products = productsWithComposition.Select(p => p.Product).ToList();
                
                // Create aggregated product using mapping service
                var aggregatedProduct = _aggregationMappingService.CreateAggregatedProduct(products, existingComposition.ConfiguratorProducts.Count + 1, unitData);

                existingComposition.ConfiguratorProducts.Add(aggregatedProduct);
            }

            // Sort compositions by order (active configurator priority, then TabOrder)
            return aggregatedCompositions.OrderBy(c => c.Order).ToList();
        }

        /// <summary>
        /// Normalizes composition title for case/whitespace insensitive comparison
        /// </summary>
        private string NormalizeCompositionTitle(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return string.Empty;

            return title.Trim().ToLowerInvariant().Replace(" ", "");
        }

        /// <summary>
        /// Finds existing composition with same normalized title or creates a new one
        /// </summary>
        private ConfiguratorComposition FindOrCreateComposition(List<ConfiguratorComposition> compositions, string title)
        {
            var normalizedTitle = NormalizeCompositionTitle(title);
            var existingComposition = compositions
                .FirstOrDefault(c => NormalizeCompositionTitle(c.Title) == normalizedTitle);

            if (existingComposition == null)
            {
                existingComposition = new ConfiguratorComposition
                {
                    Id = compositions.Count + 1,
                    Title = title,
                    Order = 0, // Initialize to 0 so the ordering logic can set it properly
                    IsMultipleProducts = false,
                    ConfiguratorProducts = new List<ConfiguratorProduct>()
                };
                compositions.Add(existingComposition);
            }

            return existingComposition;
        }


        /// <summary>
        /// Calculates prices for a list of products and sets configurator total prices
        /// </summary>
        private async Task CalculateProductPricesAsync(Configurator configurator, List<ConfiguratorProduct> products, ValidationResult validation)
        {
            if (!products.Any())
            {
                _productPriceService.SetConfiguratorTotalPrices(configurator, products);
                return;
            }

            var productCodes = products.Select(p => p.ProductCode).Distinct().ToList();
            var enrichmentData = await GetProductEnrichmentDataAsync(productCodes, products, validation);
            
            if (!validation.HasErrors)
            {
                _productPriceService.CalculatePrices(products, enrichmentData);
            }

            _productPriceService.SetConfiguratorTotalPrices(configurator, products);
        }

        /// <summary>
        /// Converts products to sales units using the convert-first-then-sum logic
        /// </summary>
        private async Task<List<ProductAggregationData>> ConvertProductsToSalesUnitsAsync(List<ProductAggregationData> allChildProductsWithComposition, ValidationResult validation)
        {
            var allChildProducts = allChildProductsWithComposition.Select(x => x.Product).ToList();
            var uniqueProductCodes = allChildProducts.Select(p => p.ProductCode).Distinct().ToList();
            
            // Get units for all products first
            var unitsResult = await _eshopApiService.GetEshopProductUnitsAsync(uniqueProductCodes);
            if (unitsResult.Validation.HasErrors)
            {
                validation.AddError($"Failed to get units: {unitsResult.Validation.GetErrors()}");
                return new List<ProductAggregationData>();
            }

            // Create unit lookup for all products (including duplicates)
            var unitsLookup = uniqueProductCodes.Zip(unitsResult.Data, (code, unit) => new { Code = code, Unit = unit })
                .ToDictionary(x => x.Code, x => x.Unit);

            // Prepare full lists for GetUnitConversionsAsync (with duplicates to match products)
            var allProductCodes = allChildProducts.Select(p => p.ProductCode).ToList();
            var allUnitsData = allChildProducts.Select(p => unitsLookup[p.ProductCode]).ToList();

            // Batch convert all products using existing GetUnitConversionsAsync method
            var conversionResult = await GetUnitConversionsAsync(
                allProductCodes,
                allChildProducts,
                allUnitsData,
                validation);

            if (validation.HasErrors)
            {
                return new List<ProductAggregationData>();
            }

            // Create converted products with sales amounts and units, preserving all ordering info
            var convertedProductsWithComposition = new List<ProductAggregationData>();
            
            for (int i = 0; i < allChildProductsWithComposition.Count; i++)
            {
                var aggregationData = allChildProductsWithComposition[i];
                var unitInfo = unitsLookup.GetValueOrDefault(aggregationData.Product.ProductCode);
                
                if (i < conversionResult.Count && conversionResult[i] != null && unitInfo != null)
                {
                    // Convert product using mapping service
                    var convertedProduct = _aggregationMappingService.ConvertToSalesUnits(
                        aggregationData.Product, 
                        conversionResult[i], 
                        unitInfo);
                    
                    convertedProductsWithComposition.Add(new ProductAggregationData
                    {
                        Product = convertedProduct,
                        CompositionTitle = aggregationData.CompositionTitle,
                        ConfiguratorId = aggregationData.ConfiguratorId,
                        TabOrder = aggregationData.TabOrder,
                        CompositionOrder = aggregationData.CompositionOrder,
                        ProductOrder = aggregationData.ProductOrder
                    });
                }
                else
                {
                    // Use original data if conversion fails
                    convertedProductsWithComposition.Add(aggregationData);
                }
            }

            return convertedProductsWithComposition;
        }

        public async Task<(ProductPriceCalculationResponse? result, ValidationResult validation)> CalculateSelectedProductPricesAsync(
            List<SelectedProductDto> selectedProducts,
            string currentProductCode)
        {
            var validation = new ValidationResult();

            // Get all product codes for enrichment data
            var productCodes = selectedProducts.Select(p => p.ProductCode).ToList();

            // Get basic enrichment data using common method
            var (pricingData, unitsData, enrichmentValidation) = await GetBasicEnrichmentDataAsync(productCodes);
            if (enrichmentValidation.HasErrors)
            {
                validation.AddError(enrichmentValidation.GetErrors());
                return (null, validation);
            }

            // Get unit conversions
            var quantities = selectedProducts.Select(p => p.Amount).ToList();
            var unitInputs = selectedProducts.Select(p => p.Unit).ToList();
            var unitOutputs = unitsData.Select(u => u.UnitSales).ToList();

            var conversionResult = await _eshopApiService.GetProductUnitConversionsAsync(productCodes, quantities, unitInputs, unitOutputs);

            List<EshopProductUnitConversion> conversions;
            if (conversionResult.Validation.HasErrors)
            {
                // Log the API failure but don't stop processing - fall back to legacy conversion
                _logger.LogWarning("Unit conversion API failed, falling back to legacy conversion logic: {Errors}", conversionResult.Validation.GetErrors());
                conversions = new List<EshopProductUnitConversion>();
            }
            else
            {
                conversions = conversionResult.Data;
            }

            // Create lookup dictionaries for explicit matching
            var conversionLookup = conversions.ToLookup(x => x.ProductCode, x => x.QuantityOutput);
            
            // Create enrichment data - always using sales units
            var enrichmentData = CreateProductPriceDataCollection(productCodes, pricingData, unitsData, conversionLookup);

            var (priceResult, priceValidation) = _productPriceService.CalculateSelectedProductPrices(
                [.. selectedProducts.Select(_mappingService.MapToEntity)],
                currentProductCode,
                enrichmentData);

            if (priceValidation.HasErrors)
            {
                validation.AddError(priceValidation.GetErrors());
                return (null, validation);
            }

            return (priceResult, validation);
        }
    }
}