namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing the aggregation of all relevant details and technical specifications for a single product, combining core product data and its specification attributes.
    /// Used to transfer, present, and process detailed product information in the configurator.
    /// Supports UI rendering, API responses, and business logic that require both product metadata and technical details.
    /// </summary>
    public class ProductDetailsDto
    {
        public EshopProductDto Product { get; set; }
        public List<EshopProductSpecItemDto> Specifications { get; set; }
    }
} 