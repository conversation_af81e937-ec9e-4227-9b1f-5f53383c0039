﻿using Moq;
using Deksmart.Domain.Service;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;

namespace Deksmart.Domain.Tests
{
    public class ConfiguratorExpressionCalculatorTest
    {
        private readonly Mock<ICalculator> _calculatorMock;
        private readonly ConfiguratorExpressionCalculator SUT;

        public ConfiguratorExpressionCalculatorTest()
        {
            _calculatorMock = new Mock<ICalculator>();
            SUT = new ConfiguratorExpressionCalculator(_calculatorMock.Object);
        }

        [Fact]
        public void GetValue_ReturnsCachedValue_WhenValueIsCached()
        {
            // Arrange
            int id = 1;
            string expression = "expression";
            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression(expression, valueIdents)).Returns((42, new ValidationResult()));

            // Act
            var result1 = SUT.GetValue(id, expression, valueIdents);
            var result2 = SUT.GetValue(id, expression, valueIdents);

            // Assert
            Assert.Equal(42, result1);
            Assert.Equal(42, result2);
            _calculatorMock.Verify(x => x.CalculateExpression(expression, valueIdents), Times.Once);
        }

        [Fact]
        public void GetValue_CalculatesNewValue_WhenValueIsNotCached()
        {
            // Arrange
            int id = 1;
            string expression = "expression";
            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression(expression, valueIdents)).Returns((42, new ValidationResult()));

            // Act
            var result = SUT.GetValue(id, expression, valueIdents);

            // Assert
            Assert.Equal(42, result);
            _calculatorMock.Verify(x => x.CalculateExpression(expression, valueIdents), Times.Once);
        }

        [Fact]
        public void FilterProducts_RemovesComposition_WhenCompositionIsInvisible()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new() { Visibility = new ConfiguratorExpression { Id = 3, Expression = "1" } }
                        }
                    },
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 2, Expression = "0" },
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new() { Visibility = new ConfiguratorExpression { Id = 4, Expression = "1" } }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("0", valueIdents)).Returns((0, new ValidationResult()));

            // Act
            SUT.FilterProducts(configurator, valueIdents);

            // Assert
            Assert.Single(configurator.ConfiguratorCompositions);
            var composition = configurator.ConfiguratorCompositions.First();
            Assert.NotNull(composition.Visibility);
            Assert.Equal("1", composition.Visibility.Expression);
        }

        [Fact]
        public void FilterProducts_RemovesComposition_WhenAllProductsAreInvisible()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new() { Visibility = new ConfiguratorExpression { Id = 2, Expression = "0" } },
                            new() { Visibility = new ConfiguratorExpression { Id = 3, Expression = "0" } }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("0", valueIdents)).Returns((0, new ValidationResult()));

            // Act
            SUT.FilterProducts(configurator, valueIdents);

            // Assert
            Assert.Empty(configurator.ConfiguratorCompositions);
        }

        [Fact]
        public void FilterProducts_CalculatesProductAmounts()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new()
                            {
                                Visibility = new ConfiguratorExpression { Id = 2, Expression = "1" },
                                Quantity = new ConfiguratorExpression { Id = 3, Expression = "2" },
                                ProductVolume = 3
                            }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("2", valueIdents)).Returns((2, new ValidationResult()));

            // Act
            SUT.FilterProducts(configurator, valueIdents);

            // Assert
            var product = configurator.ConfiguratorCompositions.First().ConfiguratorProducts.First();
            Assert.Equal(6, product.CalculatedAmount); // 2 * 3
        }

        [Fact]
        public void CalculateAndFilterFields_RemovesCategory_WhenCategoryIsInvisible()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new() { Ident = "F1", Visibility = new ConfiguratorExpression { Id = 3, Expression = "1" } }
                        }
                    },
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 2, Expression = "0" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new() { Ident = "F2",  Visibility = new ConfiguratorExpression { Id = 4, Expression = "1" } }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>() { new() { Ident = "F1", Value = 1 }, new() { Ident = "F2", Value = 0 } };
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("0", valueIdents)).Returns((0, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            Assert.Single(configurator.ConfiguratorFieldCategories);
            var category = configurator.ConfiguratorFieldCategories.First();
            Assert.NotNull(category.Visibility);
            Assert.Equal("1", category.Visibility.Expression);
        }

        [Fact]
        public void CalculateAndFilterFields_RemovesCategory_WhenAllFieldsAreInvisible()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new() { Visibility = new ConfiguratorExpression { Id = 2, Expression = "0" } },
                            new() { Visibility = new ConfiguratorExpression { Id = 3, Expression = "0" } }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("0", valueIdents)).Returns((0, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            Assert.Empty(configurator.ConfiguratorFieldCategories);
        }

        [Fact]
        public void CalculateAndFilterFields_CalculatesFieldValues_WithExpression()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new()
                            {
                                Visibility = new ConfiguratorExpression { Id = 2, Expression = "1" },
                                Expression = new ConfiguratorExpression { Id = 3, Expression = "2" }
                            }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("2", valueIdents)).Returns((2, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            var field = configurator.ConfiguratorFieldCategories.First().ConfiguratorFields.First();
            Assert.Equal(2, field.Value);
        }

        [Fact]
        public void CalculateAndFilterFields_CalculatesFieldValues_WithDirectValue()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new()
                            {
                                Visibility = new ConfiguratorExpression { Id = 2, Expression = "1" },
                                Ident = "F1",
                                DefaultValue = 5
                            }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>
            {
                new() { Ident = "F1", Value = 10 }
            };

            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            var field = configurator.ConfiguratorFieldCategories.First().ConfiguratorFields.First();
            Assert.Equal(10, field.Value);
        }

        [Fact]
        public void CalculateAndFilterFields_UsesDefaultValue_WhenNoDirectValue()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new()
                            {
                                Visibility = new ConfiguratorExpression { Id = 2, Expression = "1" },
                                Ident = "F1",
                                DefaultValue = 5
                            }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>() { new() { Ident = "F1", Value = null } };
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            var field = configurator.ConfiguratorFieldCategories.First().ConfiguratorFields.First();
            Assert.Equal(5, field.Value);
        }

        [Fact]
        public void CalculateAndFilterFields_RemovesField_WhenMultipleChoiceHasNoVisibleValues()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new()
                            {
                                Ident = "F1",
                                Visibility = new ConfiguratorExpression { Id = 2, Expression = "1" },
                                ConfiguratorFieldValues = new List<ConfiguratorFieldValue>
                                {
                                    new() { Visibility = new ConfiguratorExpression { Id = 3, Expression = "0" } }
                                }
                            }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>() { new() { Ident = "F1", Value = 1} };
            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));
            _calculatorMock.Setup(x => x.CalculateExpression("0", valueIdents)).Returns((0, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            Assert.Empty(configurator.ConfiguratorFieldCategories);
        }

        [Fact]
        public void CalculateAndFilterFields_ResetsValue_WhenValueNotInVisibleOptions()
        {
            // Arrange
            var configurator = new Configurator
            {
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>
                {
                    new()
                    {
                        Visibility = new ConfiguratorExpression { Id = 1, Expression = "1" },
                        ConfiguratorFields = new List<ConfiguratorField>
                        {
                            new()
                            {
                                Ident = "F1",
                                Visibility = new ConfiguratorExpression { Id = 2, Expression = "1" },
                                Value = 10,
                                DefaultValue = 5,
                                ConfiguratorFieldValues = new List<ConfiguratorFieldValue>
                                {
                                    new() { Visibility = new ConfiguratorExpression { Id = 3, Expression = "1" }, NumericValue = 5 }
                                }
                            }
                        }
                    }
                }
            };

            var valueIdents = new List<ValueIdent>
            {
                new() { Ident = "F1", Value = 10 }
            };

            _calculatorMock.Setup(x => x.CalculateExpression("1", valueIdents)).Returns((1, new ValidationResult()));

            // Act
            SUT.CalculateAndFilterFields(configurator, valueIdents);

            // Assert
            var field = configurator.ConfiguratorFieldCategories.First().ConfiguratorFields.First();
            Assert.Equal(5, field.Value);
            Assert.Equal(5, valueIdents.First(v => v.Ident == "F1").Value);
        }

        [Fact]
        public void ClearCache_RemovesAllCachedValues()
        {
            // Arrange
            int id = 1;
            string expression = "expression";
            var valueIdents = new List<ValueIdent>();
            _calculatorMock.Setup(x => x.CalculateExpression(expression, valueIdents)).Returns((42, new ValidationResult()));

            // Act - First call to populate cache
            SUT.GetValue(id, expression, valueIdents);
            SUT.ClearCache();
            // Second call should recalculate
            var result = SUT.GetValue(id, expression, valueIdents);

            // Assert
            Assert.Equal(42, result);
            _calculatorMock.Verify(x => x.CalculateExpression(expression, valueIdents), Times.Exactly(2));
        }
    }
}