using Microsoft.Extensions.Logging;
using Moq;
using Deksmart.Ui.Shared.Services;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class NotificationServiceTest
    {
        private readonly Mock<ILogger<NotificationService>> _mockLogger;
        private readonly NotificationService _service;

        public NotificationServiceTest()
        {
            _mockLogger = new Mock<ILogger<NotificationService>>();
            _service = new NotificationService(_mockLogger.Object);
        }

        [Fact]
        public void Constructor_InitializesEmptyNotificationsList()
        {
            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowError_ValidMessage_AddsNotificationWithErrorType()
        {
            var message = "Test error message";

            _service.ShowError(message);

            Assert.Single(_service.Notifications);
            var notification = _service.Notifications.First();
            Assert.Equal(message, notification.Message);
            Assert.Equal(NotificationType.Error, notification.Type);
        }

        [Fact]
        public void ShowError_ValidMessage_SetsTimestamp()
        {
            var beforeTime = DateTime.Now;
            var message = "Test error message";

            _service.ShowError(message);

            var notification = _service.Notifications.First();
            var afterTime = DateTime.Now;
            Assert.True(notification.Timestamp >= beforeTime && notification.Timestamp <= afterTime);
        }

        [Fact]
        public void ShowError_ValidMessage_RaisesOnChangeEvent()
        {
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.ShowError("Test message");

            Assert.True(eventRaised);
        }

        [Fact]
        public void ShowError_EmptyMessage_DoesNotAddNotification()
        {
            _service.ShowError("");

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowError_NullMessage_DoesNotAddNotification()
        {
            _service.ShowError(null);

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowError_WhitespaceMessage_DoesNotAddNotification()
        {
            _service.ShowError("   ");

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowError_EmptyMessage_DoesNotRaiseOnChangeEvent()
        {
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.ShowError("");

            Assert.False(eventRaised);
        }

        [Fact]
        public void ShowSuccess_ValidMessage_AddsNotificationWithSuccessType()
        {
            var message = "Test success message";

            _service.ShowSuccess(message);

            Assert.Single(_service.Notifications);
            var notification = _service.Notifications.First();
            Assert.Equal(message, notification.Message);
            Assert.Equal(NotificationType.Success, notification.Type);
        }

        [Fact]
        public void ShowSuccess_ValidMessage_SetsTimestamp()
        {
            var beforeTime = DateTime.Now;
            var message = "Test success message";

            _service.ShowSuccess(message);

            var notification = _service.Notifications.First();
            var afterTime = DateTime.Now;
            Assert.True(notification.Timestamp >= beforeTime && notification.Timestamp <= afterTime);
        }

        [Fact]
        public void ShowSuccess_ValidMessage_RaisesOnChangeEvent()
        {
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.ShowSuccess("Test message");

            Assert.True(eventRaised);
        }

        [Fact]
        public void ShowSuccess_EmptyMessage_DoesNotAddNotification()
        {
            _service.ShowSuccess("");

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowSuccess_NullMessage_DoesNotAddNotification()
        {
            _service.ShowSuccess(null);

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowSuccess_WhitespaceMessage_DoesNotAddNotification()
        {
            _service.ShowSuccess("   ");

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void ShowSuccess_EmptyMessage_DoesNotRaiseOnChangeEvent()
        {
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.ShowSuccess("");

            Assert.False(eventRaised);
        }

        [Fact]
        public void RemoveNotification_ExistingNotification_RemovesFromList()
        {
            _service.ShowError("Test message");
            var notification = _service.Notifications.First();

            _service.RemoveNotification(notification);

            Assert.Empty(_service.Notifications);
        }

        [Fact]
        public void RemoveNotification_ExistingNotification_RaisesOnChangeEvent()
        {
            _service.ShowError("Test message");
            var notification = _service.Notifications.First();
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.RemoveNotification(notification);

            Assert.True(eventRaised);
        }

        [Fact]
        public void RemoveNotification_NonExistingNotification_DoesNotAffectList()
        {
            _service.ShowError("Test message 1");
            _service.ShowError("Test message 2");
            var originalCount = _service.Notifications.Count;
            var nonExistingNotification = new Notification("Non-existing message", NotificationType.Error);

            _service.RemoveNotification(nonExistingNotification);

            Assert.Equal(originalCount, _service.Notifications.Count);
        }

        [Fact]
        public void RemoveNotification_NonExistingNotification_StillRaisesOnChangeEvent()
        {
            _service.ShowError("Test message");
            var nonExistingNotification = new Notification("Non-existing message", NotificationType.Error);
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.RemoveNotification(nonExistingNotification);

            Assert.True(eventRaised);
        }

        [Fact]
        public void MultipleNotifications_AddsToListInOrder()
        {
            _service.ShowError("Error 1");
            _service.ShowSuccess("Success 1");
            _service.ShowError("Error 2");

            Assert.Equal(3, _service.Notifications.Count);
            Assert.Equal("Error 1", _service.Notifications[0].Message);
            Assert.Equal("Success 1", _service.Notifications[1].Message);
            Assert.Equal("Error 2", _service.Notifications[2].Message);
        }

        [Fact]
        public void MultipleNotifications_HasCorrectTypes()
        {
            _service.ShowError("Error message");
            _service.ShowSuccess("Success message");

            Assert.Equal(NotificationType.Error, _service.Notifications[0].Type);
            Assert.Equal(NotificationType.Success, _service.Notifications[1].Type);
        }

        [Fact]
        public void Notifications_ReturnsReadOnlyCollection()
        {
            _service.ShowError("Test message");

            var notifications = _service.Notifications;

            Assert.IsType<System.Collections.ObjectModel.ReadOnlyCollection<Notification>>(notifications);
        }

        [Fact]
        public void OnChangeEvent_MultipleSubscribers_AllGetNotified()
        {
            bool subscriber1Called = false;
            bool subscriber2Called = false;
            _service.OnChange += () => subscriber1Called = true;
            _service.OnChange += () => subscriber2Called = true;

            _service.ShowError("Test message");

            Assert.True(subscriber1Called);
            Assert.True(subscriber2Called);
        }

        [Fact]
        public void NotificationClass_Constructor_SetsAllProperties()
        {
            var beforeTime = DateTime.Now;
            var message = "Test message";
            var type = NotificationType.Error;

            var notification = new Notification(message, type);

            var afterTime = DateTime.Now;
            Assert.Equal(message, notification.Message);
            Assert.Equal(type, notification.Type);
            Assert.True(notification.Timestamp >= beforeTime && notification.Timestamp <= afterTime);
        }

        [Fact]
        public void NotificationType_HasExpectedValues()
        {
            Assert.Equal(0, (int)NotificationType.Error);
            Assert.Equal(1, (int)NotificationType.Success);
        }
    }
}