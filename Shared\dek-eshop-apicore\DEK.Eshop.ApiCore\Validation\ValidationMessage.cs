namespace DEK.Eshop.ApiCore.Validation;

using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

[Obsolete]
public record ValidationMessage
{
    [Required]
    public string Code { get; init; }

    [Required]
    public string Message { get; init; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Path { get; init; } = null;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public string? Value { get; init; } = null;

    /// <summary>
    /// https://learn.microsoft.com/en-us/aspnet/core/mvc/models/validation?view=aspnetcore-7.0#use-json-property-names-in-validation-errors
    /// </summary>
    public ValidationMessage(string code, string message, string? path = null)
    {
        Code = code;
        Message = message;
        Path = path;
    }
}
