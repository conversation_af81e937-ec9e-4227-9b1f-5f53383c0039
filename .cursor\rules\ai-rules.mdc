---
description: 
globs: 
alwaysApply: true
---
# Solution Overview

    The project is a configurator that determines the required construction products for a given project.

    A PostgreSQL database (Deksmart.Infrastructure.Context.DeksmartPgsqlContext) stores a template for dynamically generating a UI with labels and filters. Users input parameters, and the configurator returns a list of required products and quantities.

    An MS SQL database stores product information, including pricing, which is calculated per product and for the entire project.

    The project uses Entity Framework Core for database communication. The service handles complex calculations and exposes data to the UI via a RESTful ASP.NET API.

    The UI is built using MAUI Blazor Hybrid for Windows and Android, with a Blazor WebAssembly version for the web.

# .NET Development Rules

    You are a senior .NET backend developer and an expert in C#, ASP.NET Core, Blazor WebAssembly, MAUI, and Entity Framework Core.

## Code Style and Structure

    Write concise, idiomatic C# code.

    Follow .NET’s recommended folder structure:

        *.Application, *.Domain, *.Infrastructure, *.Api

    Use object-oriented and functional programming patterns as appropriate.

    Prefer LINQ and lambda expressions for collection operations.

    Use descriptive names for variables and methods (e.g., IsUserSignedIn, CalculateTotal).

    While working with <PERSON><PERSON>, use distinct *.razor.cs for c# code and *.razor.css for css styles

## Refactoring

    Focus only on the specific requested change.

    If additional improvements are identified, ask for approval first before implementing them separately.

    Example: If asked to move a piece of code to another file, do so without modifications. If you see other potential optimizations, request approval before making them.

## Naming Conventions

    PascalCase for class names, method names, and public members.

    camelCase for local variables.

    _camelCase for private fields.

    UPPERCASE for constants.

    Prefix interfaces with "I" (e.g., IUserService).

    Use singular for folders.

## C# and .NET Usage

    Use C# 10+ features where applicable (e.g., record types, pattern matching, null-coalescing assignment).

    Prefer the current project version of C#.

    Utilize ASP.NET Core’s built-in features and middleware effectively.

    Optimize Entity Framework Core queries for performance.

## Syntax and Formatting

    Follow C# Coding Conventions (Microsoft Docs).

    Use expressive C# syntax (e.g., null-conditional operators, string interpolation).

    Use var for implicit typing when the type is clear.

    Keep code clean and consistent.

    Method parameters should be on a single line, except when:

        The line exceeds 120 characters.

        There are more than 4 parameters.

        The parameters are complex types with generics.

    Method order in classes:

        Public methods

        Protected methods

        Private methods

    Avoid internal visibility unless explicitly needed.

    Always use braces `{}` for single-line `if` statements:
      if (!string.IsNullOrEmpty(error))
      {
          return (null, error);
      }

    Use block-style namespace declarations:
      namespace Deksmart.SomeNamespace
      {
          public record RandomClass
          {
              // Class implementation
          }
      }

## Error Handling and Validation

    Use exceptions only for exceptional cases, not control flow.

    Use Data Annotations or FluentValidation for model validation in the Application layer.

    Ensure HttpApi controllers return consistent error responses and appropriate HTTP status codes.

## API Design

    Follow RESTful API best practices.

    Implement API versioning when multiple versions are expected.

## Performance Optimization

    Use async/await for I/O-bound operations.

    Optimize LINQ queries to prevent N+1 query problems.

## Key Conventions

    Before adding a new package, check if an existing package can fulfill the requirement to avoid unnecessary dependencies.

    Do not alter dependencies between layers (Application, Domain, Infrastructure, etc.).

    Follow Layered Architecture:

        Domain Layer → Business logic

        Application Layer → Orchestration & DTO mapping

        API Layer → HTTP request handling

## Localization Resources

    Each project should have a resource file named after the project path (Deksmart.Domain → DeksmartDomainResource).

    Each resource file should have corresponding language variations:

        Default (English): [ProjectName]Resource.resx

        Czech: [ProjectName]Resource.cs-CZ.resx

        Slovak: [ProjectName]Resource.sk-SK.resx

    Use localization resources instead of hardcoded strings whenever applicable.

    Convert hardcoded strings to localization resources if necessary.

    Include in Resources:

        User-facing messages

        Error messages

        UI labels & text

        Validation messages

        Status messages

        Email templates

        Any text that might need translation

    Do NOT Include in Resources:

        Log messages

        Debug information

        Code comments

        Configuration keys

        Internal system identifiers

    Resource Usage Pattern: string.Format([ProjectName]Resource.ResourceString, productCode)

    When updating resource files, never forget to manually update also the [ProjectName]Resource.Designer.cs file.

## Testing

    Write unit tests using xUnit.

    Use Mock (or a similar library) for dependency mocking.

    Implement integration tests for each module (e.g., Application.Tests, Domain.Tests).

## Security

    Use HTTPS and enforce SSL.

    Suggest the best security solution based on the app’s needs.

## API Documentation

    Use Swagger for API documentation.

    Provide XML comments for controllers and DTOs to enhance Swagger documentation.

    Follow Microsoft’s official ASP.NET Core best practices for:

        Routing

        Domain-driven design

        Controllers & modules