namespace Deksmart.Domain.Entity.Business.Eshop
{
    /// <summary>
    /// Represents unit and packaging information for an e-shop product, including sales and package units, unit relationships,
    /// and minimum quantity constraints. Used for business logic and data transfer related to product ordering, packaging, and display.
    /// </summary>
    public class EshopProductUnit
    {
        public string UnitSales { get; set; }
        public string UnitPackage { get; set; }
        public decimal UnitsInPackage { get; set; }
        public bool IsPackagePrimary { get; set; }
        public string UnitPrimary { get; set; }
        public string UnitSecondary { get; set; }
        public decimal MinimumQuantitySales { get; set; }
        public int MinimumQuantityPrimary { get; set; }
    }
} 