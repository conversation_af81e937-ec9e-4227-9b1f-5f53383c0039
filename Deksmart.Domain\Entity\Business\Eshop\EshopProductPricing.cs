namespace Deksmart.Domain.Entity.Business.Eshop
{
    /// <summary>
    /// Represents detailed pricing information for an e-shop product, including current and original prices (with and without VAT),
    /// discount data, historical lowest prices, and tax/package flags. Used for business logic and data transfer related to product pricing,
    /// display, and calculations in the e-commerce domain.
    /// </summary>
    public class EshopProductPricing
    {
        public decimal Vat { get; set; }
        public decimal PriceNoVatSalesOrigin { get; set; }
        public decimal PriceVatSalesOrigin { get; set; }
        public decimal PriceNoVatSales { get; set; }
        public decimal PriceVatSales { get; set; }
        public decimal PriceNoVatPackageOrigin { get; set; }
        public decimal PriceVatPackageOrigin { get; set; }
        public decimal PriceNoVatPackage { get; set; }
        public decimal PriceVatPackage { get; set; }
        public decimal DiscountPercent { get; set; }
        public bool IsVisibleDiscountHistory { get; set; }
        public decimal PriceNoVatLowestHistory { get; set; }
        public decimal PriceVatLowestHistory { get; set; }
        public bool IsPackagePrimary { get; set; }
        public bool HasTax { get; set; }
    }
} 