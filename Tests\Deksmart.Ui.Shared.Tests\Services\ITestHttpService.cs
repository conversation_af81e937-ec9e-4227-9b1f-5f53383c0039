namespace Deksmart.Ui.Shared.Tests.Services
{
    /// <summary>
    /// Test interface for HttpService to enable mocking in unit tests.
    /// Provides the same HTTP operation signatures as the concrete HttpService class
    /// but as an interface that can be mocked with frameworks like Moq.
    /// </summary>
    public interface ITestHttpService
    {
        /// <summary>
        /// Performs an HTTP GET request and deserializes the response to the specified type
        /// </summary>
        /// <typeparam name="T">The type to deserialize the response to</typeparam>
        /// <param name="url">The relative URL for the GET request</param>
        /// <param name="cancellationToken">Optional cancellation token to cancel the operation</param>
        /// <returns>The deserialized response object, or null if the request fails or returns null</returns>
        Task<T?> GetAsync<T>(string url, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs an HTTP POST request with JSON payload and deserializes the response to the specified type
        /// </summary>
        /// <typeparam name="T">The type to deserialize the response to</typeparam>
        /// <param name="url">The relative URL for the POST request</param>
        /// <param name="data">The object to serialize as JSON and send in the request body</param>
        /// <param name="cancellationToken">Optional cancellation token to cancel the operation</param>
        /// <returns>The deserialized response object, or null if the request fails or returns null</returns>
        Task<T?> PostAsync<T>(string url, object data, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs an HTTP POST request with JSON payload and returns a boolean indicating success
        /// </summary>
        /// <param name="url">The relative URL for the POST request</param>
        /// <param name="data">The object to serialize as JSON and send in the request body</param>
        /// <param name="cancellationToken">Optional cancellation token to cancel the operation</param>
        /// <returns>True if the request was successful, false otherwise</returns>
        Task<bool> PostAsync(string url, object data, CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs an HTTP POST request with JSON payload and returns binary response data with filename
        /// </summary>
        /// <param name="url">The relative URL for the POST request</param>
        /// <param name="data">The object to serialize as JSON and send in the request body</param>
        /// <param name="cancellationToken">Optional cancellation token to cancel the operation</param>
        /// <returns>A tuple containing the binary data as byte array and the filename, both nullable if the request fails</returns>
        Task<(byte[]? data, string? fileName)> PostBinaryAsync(string url, object data, CancellationToken cancellationToken = default);
    }
}