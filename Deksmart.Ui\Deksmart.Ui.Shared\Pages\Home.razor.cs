﻿using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Resources;
using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Deksmart.Ui.Shared.Pages
{
    public partial class Home : ComponentBase
    {
        private List<ConfiguratorDto>? configurators;
        private string inputText = string.Empty;

        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;
        [Inject]
        private HttpService Http { get; set; } = default!;
        [Inject]
        public IJSRuntime JSRuntime { get; set; } = default!;
        [Inject]
        public MetaDescriptionService MetaDescriptionService { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            configurators = await Http.GetAsync<List<ConfiguratorDto>>(UiSharedResource.ConfiguratorsUrl);
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                var metaDescription = MetaDescriptionService.GetMetaDescription(null);
                await JSRuntime.InvokeVoidAsync("setMetaDescription", metaDescription);
            }
        }

        private void LoadConfigurator(int id)
        {
            NavigationManager.NavigateTo(UiSharedResource.Deksmart + "/" + id.ToString());
        }

        private async Task GetPresetForCatalog()
        {
            var preset = await Http.GetAsync<PresetDto?>($"catalog/{inputText}");
            if (preset != null)
            {
                NavigationManager.NavigateTo(UiSharedResource.Deksmart + "/" + string.Format(UiSharedResource.LoadPresetUrl, preset.ConfiguratorId, preset.PresetId));
            }
        }
    }
}
