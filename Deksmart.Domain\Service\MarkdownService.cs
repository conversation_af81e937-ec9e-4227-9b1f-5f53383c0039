﻿using Deksmart.Domain.Resource;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Service.Base;
using Markdig;

namespace Deksmart.Domain.Service
{
    public interface IMarkdownService
    {
        /// <summary>
        /// Converts markdown to HTML
        /// </summary>
        /// <param name="markdown">The markdown to convert</param>
        /// <returns>The HTML representation of the markdown and a validation result</returns>
        (string html, ValidationResult validation) ConvertToHtml(string markdown, int rowNumber);
    }

    public class MarkdownService : IMarkdownService
    {
        private readonly IValidationService _validationService;

        public MarkdownService(IValidationService validationService)
        {
            _validationService = validationService;
        }

        public (string html, ValidationResult validation) ConvertToHtml(string markdown, int rowNumber)
        {
            var validation = _validationService.CreateValidation();

            try
            {
                var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
                return (Markdown.ToHtml(markdown, pipeline), validation);
            }
            catch
            {
                validation.AddError(string.Format(DeksmartDomainResource.MarkdownConversionError, rowNumber, markdown));
                return (string.Empty, validation);
            }
        }
    }
}