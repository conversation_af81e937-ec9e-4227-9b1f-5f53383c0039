namespace DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[Keyless]
public class ProductEshopUnit
{
    [Column("PRODEJNI_MJ")]
    public string UnitSales { get; set; } = null!;

    [Column("BALENI_MJ")]
    public string UnitPackage { get; set; } = null!;

    [Column("PREP_BALtoPRODEJ")]
    public decimal SalesUnitsInPackage { get; set; }

    [Column("JEN_CELA_BALENI")] 
    public bool IsOnlyPackage { get; set; }

    [Column("MIN_OBJ_MNOZ")]
    public decimal MinimumUnitsSales { get; set; }
}
