using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorFieldCategoryRepository : IntIdDaoBase<ConfiguratorFieldCategory>, IConfiguratorFieldCategoryRepository
    {
        public ConfiguratorFieldCategoryRepository(ConfiguratorContext context) : base(context)
        {
        }

        /// <inheritdoc/>
        public async Task<List<ConfiguratorFieldCategory>> GetFieldCategoriesForConfiguratorAsync(int configuratorId)
        {
            return await _context.ConfiguratorFieldCategories
                .AsNoTracking()
                .AsSplitQuery()
                .Include(d => d.Visibility)
                .Include(d => d.ConfiguratorFields)
                    .ThenInclude(d => d.Visibility)
                .Include(d => d.ConfiguratorFields)
                    .ThenInclude(d => d.Expression)
                .Include(d => d.ConfiguratorFields)
                    .ThenInclude(d => d.ConfiguratorFieldValues)
                        .ThenInclude(d => d.Visibility)
                .Where(d => d.ConfiguratorId == configuratorId)
                .ToListAsync();
        }
    }
}
