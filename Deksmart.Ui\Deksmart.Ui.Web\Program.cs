using System.Text.Json;
using System.Text.Json.Serialization;
using System.Globalization;
using Microsoft.AspNetCore.HttpOverrides;
using Polly;
using Polly.Extensions.Http;
using Deksmart.Ui.Shared.Services;
using Deksmart.Ui.Shared.Factory;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();
builder.Services.AddHealthChecks();

// Configure JSON options
var jsonOptions = new JsonSerializerOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
    ReferenceHandler = ReferenceHandler.Preserve,
    Converters = { new JsonStringEnumConverter() }
};
builder.Services.AddSingleton(jsonOptions);


builder.Services.AddHttpClient("DeksmartApi")
.AddPolicyHandler(GetRetryPolicy())
.AddPolicyHandler(GetCircuitBreakerPolicy());

// Register services
builder.Services.AddTransient<ConfiguratorGridService>();
builder.Services.AddScoped<IConfiguratorGridServiceFactory, ConfiguratorGridServiceFactory>();
builder.Services.AddScoped<IConfiguratorApiService, ConfiguratorApiService>();
builder.Services.AddScoped<IConfiguratorStateService, ConfiguratorStateService>();
builder.Services.AddScoped<IConfiguratorValidationService, ConfiguratorValidationService>();
builder.Services.AddScoped<IChildServiceManager, ChildServiceManager>();
builder.Services.AddScoped<HttpService>();
builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<ConfiguratorNavigationService>();
builder.Services.AddScoped<HeaderStateService>();
builder.Services.AddScoped<MetaDescriptionService>();

// Add HttpClient for proxy
builder.Services.AddHttpClient();

// Set up localization with Czech as default
var cultureInfo = new CultureInfo("cs-CZ");
CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

// Configure WebAssembly hosting
builder.Services.AddResponseCompression(opts =>
{
    opts.MimeTypes = ["application/octet-stream"];
});

// Configure Razor Pages options
builder.Services.AddMvc().AddRazorPagesOptions(options =>
{
    options.RootDirectory = "/Pages";
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

// Use response compression
app.UseResponseCompression();

// Configure forwarded headers for running behind a reverse proxy in Docker
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});

app.UseHttpsRedirection();
app.UseBlazorFrameworkFiles();
app.UseStaticFiles();
app.UseRouting();

app.MapRazorPages();
app.MapControllers();
app.MapHealthChecks("/health");

// Add redirect from root to /deksmart
app.MapGet("/", context => {
    context.Response.Redirect("/deksmart");
    return Task.CompletedTask;
});

// Map fallback route for WebAssembly
app.MapFallbackToFile("index.html");

app.Run();

static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(3, retryAttempt =>
            TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
}

static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
}
