using Microsoft.AspNetCore.Mvc;
using System.Text;
using System.Text.RegularExpressions;

namespace Deksmart.Api.Controllers
{
    [ApiController]
    [Route("api/proxy")]
    public class ProxyController : DeksmartBaseController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public ProxyController(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }

        [HttpGet("eshop-homepage")]
        public async Task<IActionResult> GetEshopHomepage()
        {
            try
            {
                // Načítáme URL z konfigurace
                var eshopUrl = _configuration["EshopSettings:HomepageUrl"];
                
                if (string.IsNullOrEmpty(eshopUrl))
                {
                    return BadRequest("EshopSettings:HomepageUrl configuration is missing or empty");
                }

                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(eshopUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return Content(content, "text/html");
                }
                else
                {
                    return StatusCode((int)response.StatusCode, $"Failed to fetch eshop homepage: {response.ReasonPhrase}");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error fetching eshop homepage: {ex.Message}");
            }
        }

        [HttpGet("eshop-header")]
        public async Task<IActionResult> GetEshopHeader()
        {
            try
            {
                // Načítáme URL z konfigurace
                var eshopUrl = _configuration["EshopSettings:HomepageUrl"];

                if (string.IsNullOrEmpty(eshopUrl))
                {
                    return BadRequest("EshopSettings:HomepageUrl configuration is missing or empty");
                }

                var client = _httpClientFactory.CreateClient();
                var response = await client.GetAsync(eshopUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    
                    // Extrakce header a menu
                    var headerMatch = Regex.Match(content, "<header.*?</header>", RegexOptions.Singleline);
                    var menuMatch = Regex.Match(content, @"<div\s+id=[""']vue-megamenu[""'].*?</div>\s*</div>\s*</div>", RegexOptions.Singleline);
                    
                    // Vytvoření výsledného HTML
                    var resultBuilder = new StringBuilder();
                    
                    // Pokud najdeme header, použijeme ho, jinak vrátíme prázdný string
                    resultBuilder.Append(headerMatch.Success ? headerMatch.Value : "");
                    
                    // Pokud najdeme menu, použijeme ho, jinak vrátíme základní šablonu
                    if (menuMatch.Success)
                    {
                        resultBuilder.Append(menuMatch.Value);
                    }
                    else
                    {
                        resultBuilder.Append(@"<div id=""vue-megamenu"">
                            <div class=""vue-megamenu vue-megamenu-fallback"">
                                <div class=""vue-megamenu-navbar"">
                                    <div class=""vue-megamenu__section"">
                                        <a href=""/"" class=""vue-megamenu__section__link"">
                                            <span>Menu není dostupné</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>");
                    }
                    
                    return Content(resultBuilder.ToString(), "text/html");
                }
                else
                {
                    return StatusCode((int)response.StatusCode, $"Failed to fetch eshop homepage: {response.ReasonPhrase}");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Error extracting eshop header: {ex.Message}");
            }
        }
    }
}
