﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <InternalsVisibleTo>Deksmart.Domain.Tests</InternalsVisibleTo>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Markdig" Version="0.41.2" />
        <PackageReference Include="NCalcSync" Version="5.4.2" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Resource\DeksmartDomainResource.Designer.cs">
        <DependentUpon>DeksmartDomainResource.resx</DependentUpon>
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Resource\DeksmartDomainResource.cs-CZ.resx">
        <SubType>Designer</SubType>
      </EmbeddedResource>
      <EmbeddedResource Update="Resource\DeksmartDomainResource.resx">
        <SubType>Designer</SubType>
        <LastGenOutput>DeksmartDomainResource.Designer.cs</LastGenOutput>
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
    </ItemGroup>

</Project>
