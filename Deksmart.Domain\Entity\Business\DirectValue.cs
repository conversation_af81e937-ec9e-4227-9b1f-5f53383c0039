﻿using Deksmart.Domain.Entity.Db;

namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Represents a single numeric input field in the configurator that is not a multiple-choice option.
    /// Used to map, validate, and process direct user-entered values (with optional constraints and expressions) from the database entity ConfiguratorField.
    /// Supports value range validation, defaulting, and expression-based calculation as part of the configurator's field matching and evaluation logic.
    /// </summary>
    public record DirectValue
    {
        public int Id { get; set; }
        public string Ident { get; set; } = null!;
        public ConfiguratorExpression? Expression { get; set; }
        public decimal DefaultValue { get; set; }
        public int? Max { get; set; }
        public int? Min { get; set; }
    }
}
