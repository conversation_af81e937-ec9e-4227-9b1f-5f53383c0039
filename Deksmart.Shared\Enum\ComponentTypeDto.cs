namespace Deksmart.Shared.Enum
{
    /// <summary>
    /// Enumerates the types of UI components available for rendering user-editable fields in a configurator.
    /// Used in DTOs to specify how each field should be presented and interacted with in the UI (e.g., numeric input, slider, checkbox, select box, tile, expression, text, single choice, product selector).
    /// Supports dynamic UI generation, validation, and workflow logic by providing a standardized set of component types for configuration scenarios.
    /// Maps directly to the domain-level <c>ComponentType</c> enum and is transferred between backend, API, and UI to ensure consistent rendering and behavior.
    /// </summary>
    [Serializable]
    public enum ComponentTypeDto
    {
        Numeric = 1,
        Slider = 2,
        Checkbox = 3,
        Selectbox = 4,
        Tile = 5,
        Expression = 6,
        Text = 7,
        SingleChoice = 8,
        Product = 9
    }
} 