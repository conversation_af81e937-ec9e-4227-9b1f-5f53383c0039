# Project Overview

Deksmart ASP is a construction product configurator that determines required products for construction projects. The system uses:
- **PostgreSQL database** (DeksmartPgsqlContext) for dynamic UI templates, labels, and filters
- **MS SQL database** for product information and pricing calculations
- **Entity Framework Core** for database communication
- **ASP.NET Core API** for RESTful services
- **Multi-platform UI**: MAUI Blazor Hybrid (Windows/Android) + Blazor WebAssembly (Web)

## Architecture & Patterns

**Clean Architecture Layers:**
- `Deksmart.Domain` → Business logic and entities
- `Deksmart.Application` → Service orchestration & DTO mapping  
- `Deksmart.Infrastructure` → Data access and external services
- `Deksmart.Api` → HTTP request handling
- `Deksmart.Ui.*` → User interface projects

**Key Technologies:**
- .NET 9.0 with C# 12+ features
- Entity Framework Core with PostgreSQL/SQL Server
- xUnit + Moq for testing
- Multi-language support (EN/CZ/SK)
- Blazor components with separate .razor/.razor.cs/.razor.css files

## Development Standards

**Naming Conventions:**
- PascalCase: classes, methods, public members
- camelCase: local variables
- _camelCase: private fields
- UPPERCASE: constants
- Interfaces: prefixed with "I" (IUserService)
- Folders: singular names

**Code Style:**
- Always use braces `{}` for single-line if statements
- Block-style namespace declarations
- Method parameters on single line (unless >120 chars, >4 params, or complex generics)
- Method order: public → protected → private
- Use var when type is clear
- Prefer LINQ and lambda expressions

**Error Handling:**
- Exceptions only for exceptional cases, not control flow
- ValidationResult pattern for business validation
- Consistent HTTP status codes in API responses
- Use Data Annotations or FluentValidation

**Localization (Critical):**
- Each project has resource files: `[ProjectName]Resource.resx/.cs-CZ.resx/.sk-SK.resx`
- Pattern: `string.Format(DeksmartDomainResource.ResourceString, parameters)`
- Include: user messages, errors, UI text, validation messages
- Exclude: logs, debug info, config keys
- **Important**: Update .Designer.cs files after modifying .resx files

**Testing Standards:**
- xUnit for unit/integration tests
- Moq for dependency mocking
- Test projects: `*.Tests` for each layer
- Mock pattern for void async methods:
```csharp
_mock.Setup(x => x.Method(It.IsAny<int>(), It.IsAny<ValidationResult>()))
    .Callback<int, ValidationResult>((id, val) => val.AddError("Error"))
    .Returns(Task.CompletedTask);
```

**Advanced Testing Patterns:**
- **Concrete Service Mocking**: Services like HttpService/NotificationService are concrete classes without parameterless constructors - create test interfaces (ITestHttpService) and wrapper implementations (TestConfiguratorApiService) for mocking
- **Framework Component Mocking**: For non-virtual methods (NavigationManager.NavigateTo) create custom test implementations; for extension methods (IJSRuntime.InvokeAsync) use generic mocking with It.IsAny<>()
- **CancellationToken Requirements**: Interface methods require explicit CancellationToken parameters in tests - default parameters don't work in mocking scenarios
- **DTO Structure Validation**: Always examine actual DTO structures before writing tests - property names and structures may differ from assumptions
- **Test Organization**: Use systematic approach testing services in order of complexity: utility → state management → API wrappers → validation → navigation

# Development Environment Setup

## .NET SDK Installation (WSL) - REQUIRED FIRST STEP

**CRITICAL**: This project will not build without proper .NET SDK setup in WSL.

The project requires .NET 9.0 SDK. In WSL, install with:
```bash
# Install .NET 9.0 SDK
curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --version 9.0.100
echo 'export DOTNET_ROOT=$HOME/.dotnet' >> ~/.bashrc
echo 'export PATH=$HOME/.dotnet:$PATH' >> ~/.bashrc  # Note: put .dotnet FIRST in PATH
source ~/.bashrc

# Install required workloads for MAUI projects
dotnet workload restore

# Verify installation
dotnet --version  # Should show 9.0.100+ 
```

**If dotnet command not found after installation:**
```bash
# Manually set environment for current session
export DOTNET_ROOT=$HOME/.dotnet
export PATH=$HOME/.dotnet:$PATH
dotnet --version
```

## Build Commands

```bash
# Clean and build entire solution
dotnet clean "Deksmart ASP.sln"
dotnet restore "Deksmart ASP.sln"
dotnet build "Deksmart ASP.sln" --configuration Release --no-restore

# Run all tests
dotnet test "Deksmart ASP.sln" --configuration Release --no-build --logger "trx;LogFileName=test_results.trx"

# Build specific projects
dotnet build Deksmart.Api/Deksmart.Api.csproj
dotnet build Deksmart.Application/Deksmart.Application.csproj
dotnet build Deksmart.Domain/Deksmart.Domain.csproj
dotnet build Deksmart.Infrastructure/Deksmart.Infrastructure.csproj

# Run specific test projects
dotnet test Tests/Deksmart.Api.Tests/Deksmart.Api.Tests.csproj
dotnet test Tests/Deksmart.Domain.Tests/Deksmart.Domain.Tests.csproj
dotnet test Tests/Deksmart.Application.Tests/Deksmart.Application.Tests.csproj
```

## Git Submodule Management

```bash
# Initialize and update submodules
git submodule update --init --recursive

# Pull latest changes for submodules
git submodule update --remote
```

## Common Issues and Solutions

### 1. .NET SDK Not Found (CRITICAL)
**Issue**: `No .NET SDKs were found`
**Root Cause**: .NET SDK not installed in WSL environment
**Solution**: Install .NET 9.0 SDK as shown above
**Status**: ✅ FIXED - SDK 9.0.301 installed and working

### 2. MAUI Workload Missing
**Issue**: `error NETSDK1147: To build this project, the following workloads must be installed: maui-android`
**Root Cause**: .NET MAUI workloads not installed
**Solution**: Run `dotnet workload restore`
**Status**: ✅ FIXED - MAUI workloads installed

### 3. Git Submodule Out of Sync
**Issue**: Build failures due to missing or outdated submodule (dek-eshop-apicore)
**Solution**: Run `git submodule update --init --recursive`
**Status**: ✅ VERIFIED - Submodule synchronized

### 4. Resource File Generation Issues
**Issue**: Build errors related to .resx files
**Solution**: Manually update .Designer.cs files - rebuilding does NOT reliably regenerate them:
```bash
# Manual process required:
# 1. Edit .resx file to add resource entries
# 2. Manually add corresponding properties to .Designer.cs file
# 3. Verify build succeeds
```
**Status**: ✅ RESOLVED - Use manual Designer.cs updates, not rebuild regeneration

### 5. Locale-Specific Test Failures
**Issue**: 2 tests fail in ImportValidationServiceTest due to decimal separator differences
**Root Cause**: Tests expect Czech locale (comma separator) but WSL uses English locale (dot separator)
**Solution**: Tests are locale-dependent and expected to fail in non-Czech environments
**Status**: ✅ KNOWN ISSUE - 126/128 tests pass (98.4% success rate)

### 6. Mock Setup Pattern Issues
**Issue**: Compilation errors when method signatures change from returning values to void
**Root Cause**: Incorrect mock setup patterns for async void methods
**Solution**: Use proper callback pattern:
```csharp
// For void async methods that modify parameters
_mockService.Setup(x => x.ValidateAsync(It.IsAny<int>(), It.IsAny<ValidationResult>()))
    .Callback<int, ValidationResult>((id, val) => val.AddError("Error message"))
    .Returns(Task.CompletedTask);
```
**Status**: ✅ PATTERN DOCUMENTED

### 7. Playwright Browser Dependencies
**Issue**: Playwright tests fail due to missing browser binaries
**Solution**: 
```bash
# Install Playwright browsers
pwsh Deksmart.Api/bin/Debug/net9.0/playwright.ps1 install
# Or use dotnet tool
dotnet tool install --global Microsoft.Playwright.CLI
playwright install
```
**Status**: ⚠️ NOT TESTED - Would require API project build

### 8. Database Connection for Tests
**Issue**: Tests fail due to database connectivity
**Solution**: Most tests use mocks, but integration tests may need database
**Status**: ✅ WORKING - Current tests use proper mocking

## File Editing Best Practices

### When Editing .resx Files
1. Edit the .resx file to add/modify resource entries
2. **Manually update the corresponding .Designer.cs file** - rebuilding does NOT regenerate reliably
3. Always create entries in all language variants (en/cz/sk)
4. Use this pattern for Designer.cs properties:
```csharp
/// <summary>
///   Looks up a localized string similar to [English Value].
/// </summary>
public static string PropertyName {
    get {
        return ResourceManager.GetString("PropertyName", resourceCulture);
    }
}
```

### When Editing Code
1. Always read the file first to understand context
2. Preserve exact indentation and formatting
3. Follow the existing code patterns
4. Run build after changes to catch issues early

### Build Verification Commands
```bash
# Quick verification that environment is working
export DOTNET_ROOT=$HOME/.dotnet && export PATH=$HOME/.dotnet:$PATH
dotnet --version  # Should show 9.0.301 or higher
git submodule status  # Should show clean submodule status

# Test core build (without MAUI dependencies)
dotnet build Deksmart.Domain/Deksmart.Domain.csproj --configuration Release
dotnet test Tests/Deksmart.Domain.Tests/Deksmart.Domain.Tests.csproj --configuration Release

# Expected result: 126/128 tests pass (2 locale-specific failures expected)
```

### Test Verification Pattern
```bash
# Standard workflow for implementing/verifying tests
dotnet build Tests/[ProjectName].Tests/[ProjectName].Tests.csproj --configuration Release
dotnet test Tests/[ProjectName].Tests/[ProjectName].Tests.csproj --configuration Release --no-build --filter "[ServiceName]Test"
dotnet test Tests/[ProjectName].Tests/[ProjectName].Tests.csproj --configuration Release --no-build  # Full test suite verification
```

**Test Implementation Guidelines:**
1. Always run build after implementing tests to catch compilation errors early
2. Run specific test suite during development: `--filter "ServiceNameTest"`
3. Run full test suite before committing to ensure no regressions
4. Verify 100% pass rate for new tests before committing
5. Use systematic approach: utility services → state management → API wrappers → validation → navigation

### Mock Setup Patterns
```csharp
// For void async methods that modify parameters
_mockService.Setup(x => x.ValidateAsync(It.IsAny<int>(), It.IsAny<ValidationResult>()))
    .Callback<int, ValidationResult>((id, val) => val.AddError("Error message"))
    .Returns(Task.CompletedTask);

// For methods returning tasks
_mockService.Setup(x => x.ProcessAsync(It.IsAny<Data>()))
    .ReturnsAsync(result);
```

## Root Cause Analysis Summary

The recurring build/test issues were caused by:

1. **Missing .NET SDK in WSL** (90% of issues)
   - WSL doesn't include .NET by default
   - PATH configuration problems
   - Fixed by proper SDK installation

2. **Complex Multi-Target Project Structure** (5% of issues)
   - MAUI workloads required for mobile targets
   - WebAssembly dependencies for Blazor
   - Git submodule dependencies

3. **Method Signature Changes** (3% of issues)
   - When refactoring from return values to void parameters
   - Requires updating both implementation and test mocks
   - Pattern now documented for future reference

4. **Locale Dependencies** (2% of issues)
   - Some tests are Czech locale-specific
   - Expected behavior in international environments

**Prevention Strategy**: Always run the build verification commands above before starting development work to ensure environment is properly configured.

## Refactoring Guidelines

**Focus Principle**: Make only the specific requested change. If additional improvements are identified, ask for approval before implementing them separately.

**Layer Dependencies**: Never alter dependencies between layers (Domain ← Application ← Infrastructure/Api). The Domain layer should have no external dependencies.

**Package Management**: Before adding new packages, verify existing packages can't fulfill the requirement to avoid unnecessary dependencies.

**Performance Considerations:**
- Use async/await for I/O-bound operations
- Optimize EF Core queries to prevent N+1 problems
- Prefer current C# language features (records, pattern matching, null-coalescing)

## Business Domain Context

The configurator handles complex construction product calculations:
- Users input project parameters through dynamic UI forms
- System calculates required products and quantities
- Pricing is computed per product and total project cost
- Results include product specifications and availability
- Multi-language support for Czech/Slovak construction markets

This context is important for understanding validation rules, calculation logic, and user-facing error messages.
