﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>83efd37d-a73b-4872-a3d7-108c3f056fee</UserSecretsId>
    <IsPackable>false</IsPackable>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\Comments.xml</DocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn><!-- Vypnut<PERSON> zelených warningů vynucujících si komentáře -->
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DEK.Eshop.ApiCore\DEK.Eshop.ApiCore.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\" />
  </ItemGroup>

</Project>
