﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ImportError" xml:space="preserve">
    <value>An error occurred during the import process: {0}</value>
  </data>
  <data name="ImportSuccess" xml:space="preserve">
    <value>Import completed successfully.</value>
  </data>
  <data name="FailedToGetProducts" xml:space="preserve">
    <value>Failed to get products: {0}</value>
  </data>
  <data name="FailedToGetProductUnits" xml:space="preserve">
    <value>Failed to get product units: {0}</value>
  </data>
  <data name="FailedToGetProductPricing" xml:space="preserve">
    <value>Failed to get product pricing: {0}</value>
  </data>
  <data name="FailedToGetProductParameters" xml:space="preserve">
    <value>Failed to get product parameters: {0}</value>
  </data>
  <data name="FailedToGetUnitConversions" xml:space="preserve">
    <value>Failed to get unit conversions: {0}</value>
  </data>
  <data name="ProductCodeRequired" xml:space="preserve">
    <value>Product code is required</value>
  </data>
  <data name="FailedToGetProductDetails" xml:space="preserve">
    <value>Failed to get product details: {0}</value>
  </data>
  <data name="FailedToGetProductSpecifications" xml:space="preserve">
    <value>Failed to get product specifications: {0}</value>
  </data>
  <data name="ProductNotFound" xml:space="preserve">
    <value>Product not found</value>
  </data>
  <data name="PricingError" xml:space="preserve">
    <value>Pricing: {0}</value>
  </data>
  <data name="UnitsError" xml:space="preserve">
    <value>Units: {0}</value>
  </data>
  <data name="Configuration" xml:space="preserve">
    <value>Configuration</value>
  </data>
  <data name="SelectedProducts" xml:space="preserve">
    <value>Selected Products</value>
  </data>
  <data name="ProductCode" xml:space="preserve">
    <value>Product Code</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="PricePerPackage" xml:space="preserve">
    <value>Price per package</value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
    <value>Total Price</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="TotalPriceExclVat" xml:space="preserve">
    <value>Total Price (excl. VAT)</value>
  </data>
  <data name="Vat" xml:space="preserve">
    <value>VAT</value>
  </data>
  <data name="TotalPriceInclVat" xml:space="preserve">
    <value>Total Price (incl. VAT)</value>
  </data>
  <data name="ContactInformation" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="NameLabel" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="EmailLabel" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="PhoneLabel" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="CancelButtonText" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FieldNotFilledValidation" xml:space="preserve">
    <value>{0} is not filled.</value>
  </data>
  <data name="FailedToGeneratePdf" xml:space="preserve">
    <value>Failed to generate PDF: {0}</value>
  </data>
  <data name="FailedToGenerateHtml" xml:space="preserve">
    <value>Failed to generate HTML: {0}</value>
  </data>
  <data name="NoProductCodesProvided" xml:space="preserve">
    <value>No product codes provided</value>
  </data>
  <data name="ApiReturnedStatusCodeError" xml:space="preserve">
    <value>API returned {0}: {1}</value>
  </data>
  <data name="ApiReturnedItemCountMismatch" xml:space="preserve">
    <value>API returned {0} items, expected {1} items</value>
  </data>
  <data name="NetworkErrorWhileFetchingData" xml:space="preserve">
    <value>Network error while fetching data from {0}: {1}</value>
  </data>
  <data name="UnexpectedErrorWhileFetchingData" xml:space="preserve">
    <value>Unexpected error while fetching data from {0}: {1}</value>
  </data>
  <data name="CompositeIdRequired" xml:space="preserve">
    <value>Composite ID is required.</value>
  </data>
  <data name="CompositeIdInvalid" xml:space="preserve">
    <value>Composite ID must be a valid integer.</value>
  </data>
  <data name="ConfiguratorIdRequired" xml:space="preserve">
    <value>Configurator ID is required.</value>
  </data>
  <data name="ConfiguratorIdInvalid" xml:space="preserve">
    <value>Configurator ID must be a valid integer.</value>
  </data>
  <data name="CurrencySymbol" xml:space="preserve">
    <value>Kč</value>
  </data>
  <data name="UnitConversionParameterArrayLengthMismatch" xml:space="preserve">
    <value>Parameter arrays must be the same length</value>
  </data>
</root>