using System.Text.Json.Serialization;
using System.Text.Json;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Deksmart.Ui.Shared.Services;
using Deksmart.Ui.Shared;
using Polly;
using Polly.Extensions.Http;
using Deksmart.Ui.Shared.Factory;
using System.Globalization;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Add HttpClient for base address
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Add HttpClient named "DeksmartApi" with no proxy and Polly policies
builder.Services.AddHttpClient("DeksmartApi", client =>
{
    var apiUrl = builder.Configuration["ApiBaseUrl"]!;
    client.BaseAddress = new Uri(apiUrl);
})
.AddPolicyHandler(GetRetryPolicy())
.AddPolicyHandler(GetCircuitBreakerPolicy());

var jsonOptions = new JsonSerializerOptions
{
    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
    Converters = { new JsonStringEnumConverter() }
};

builder.Services.AddSingleton(jsonOptions);
builder.Services.AddTransient<IConfiguratorGridService, ConfiguratorGridService>();
builder.Services.AddScoped<IConfiguratorGridServiceFactory, ConfiguratorGridServiceFactory>();
builder.Services.AddScoped<IConfiguratorApiService, ConfiguratorApiService>();
builder.Services.AddScoped<IConfiguratorStateService, ConfiguratorStateService>();
builder.Services.AddScoped<IConfiguratorValidationService, ConfiguratorValidationService>();
builder.Services.AddScoped<IChildServiceManager, ChildServiceManager>();
builder.Services.AddScoped<HttpService>();
builder.Services.AddScoped<NotificationService>();
builder.Services.AddScoped<ConfiguratorNavigationService>();
builder.Services.AddScoped<HeaderStateService>();
builder.Services.AddScoped<MetaDescriptionService>();
builder.Services.AddScoped<TabDragDropService>();

// Set up localization with Czech as default
var cultureInfo = new CultureInfo("cs-CZ");
CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;

await builder.Build().RunAsync();

static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .WaitAndRetryAsync(3, retryAttempt =>
            TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
}

static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
{
    return HttpPolicyExtensions
        .HandleTransientHttpError()
        .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
}