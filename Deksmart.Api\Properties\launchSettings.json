{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/doc", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5144"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api/doc", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7276;http://localhost:5144"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "api/doc", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/api/doc", "publishAllPorts": true, "useSSL": true}, "WSL": {"commandName": "WSL2", "launchBrowser": true, "launchUrl": "https://localhost:7276/api/doc", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7276;http://localhost:5144"}, "distributionName": ""}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:60684/", "sslPort": 44300}}}