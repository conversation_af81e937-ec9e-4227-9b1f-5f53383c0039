using Microsoft.AspNetCore.Http;
using System;
using System.Globalization;

namespace DEK.Eshop.ApiCore.Loging;

public class Logger
{
    // EventHandler for uncatch exception logging
    private event EventHandler<ExceptionHandlerEventArgs>? UncatchExceptionEventHandler;

    // EventHander for regular exception logging
    private event EventHandler<ExceptionHandlerEventArgs>? ExceptionEventHandler;

    // EventHandler for HTTP access logging
    private event EventHandler<HttpAccessHandlerEventArgs>? HttpAccessEventHandler;

    public void SubscribeToUncatchExceptionHandler(IUncatchExceptionHandlerEvent adapter)
    {
        this.UncatchExceptionEventHandler += adapter.WriteUncatchException;
    }

    public void SubscribeToExceptionHandler(IExceptionHandlerEvent adapter)
    {
        this.ExceptionEventHandler += adapter.WriteException;
    }

    public void SubscribeToHttpAccessHandler(IHttpAccessEvent adapter)
    {
        this.HttpAccessEventHandler += adapter.WriteHttpAccess;
    }

    public void DispatchUncatchExceptionEvent(Exception exception)
    {
        var args = new ExceptionHandlerEventArgs(exception);
        this.UncatchExceptionEventHandler?.Invoke(this, args);
    }

    public void DispatchExceptionEvent(Exception exception)
    {
        var args = new ExceptionHandlerEventArgs(exception);
        this.ExceptionEventHandler?.Invoke(this, args);
    }

    public void DispatchHttpAccessEvent(HttpContext httpContext)
    {
        var args = (HttpAccessHandlerEventArgs)httpContext;
        Task.Run(() => this.HttpAccessEventHandler?.Invoke(this, args));
    }
}

public class ExceptionHandlerEventArgs : EventArgs
{
    public Exception Exception { get; set; }

    public ExceptionHandlerEventArgs(Exception exception)
    {
        this.Exception = exception;
    }
}

public class HttpAccessHandlerEventArgs : EventArgs
{
    public string Type { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Query { get; set; } = string.Empty;
    public string Time { get; set; } = string.Empty;
    public string ClientIp { get; set; } = string.Empty;
    public string Referrer { get; set; } = string.Empty;
    public string Agent { get; set; } = string.Empty;

    public static explicit operator HttpAccessHandlerEventArgs(HttpContext httpContext)
    {
        return new HttpAccessHandlerEventArgs() {
            Type = "http",
            Method = httpContext.Request.Method,
            Status = httpContext.Response.StatusCode.ToString(),
            Path = httpContext.Request.Path,
            Query = httpContext.Request.QueryString.ToString(),
            Time = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszzz", CultureInfo.InvariantCulture),
            ClientIp = httpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString() ?? string.Empty,
            Referrer = httpContext.Request.Headers["Referer"].ToString(),
            Agent = httpContext.Request.Headers["User-Agent"].ToString()
        };
    }
}
