﻿using Microsoft.AspNetCore.Components;

namespace Deksmart.Ui.Shared.Components.Utility
{
    public partial class TrimInputNumber : ComponentBase
    {
        [Parameter]
        public decimal Value { get; set; }

        [Parameter]
        public EventCallback<decimal> ValueChanged { get; set; }

        [Parameter]
        public decimal Min { get; set; } = decimal.MinValue;

        [Parameter]
        public decimal Max { get; set; } = decimal.MaxValue;

        [Parameter]
        public string Class { get; set; } = string.Empty;

        [Parameter]
        public string Placeholder { get; set; } = string.Empty;

        [Parameter]
        public string? Suffix { get; set; }

        private string _inputValue = string.Empty;
        private bool _isFocused = false;

        protected override void OnParametersSet()
        {
            if (!_isFocused)
            {
                _inputValue = Value == 0 ? "0" : Value.ToString("0.################");
            }
        }

        private void HandleFocus()
        {
            _isFocused = true;
            if (Value == 0)
            {
                _inputValue = string.Empty;
            }
        }

        private void HandleBlur()
        {
            _isFocused = false;
            if (string.IsNullOrWhiteSpace(_inputValue))
            {
                Value = 0;
                _inputValue = "0";
                ValueChanged.InvokeAsync(Value);
            }
            else if (decimal.TryParse(_inputValue, out var parsedValue))
            {
                Value = parsedValue;
                ValueChanged.InvokeAsync(Value);
            }
            else
            {
                // Invalid input, reset to previous value
                _inputValue = Value == 0 ? "0" : Value.ToString("0.################");
            }
        }
    }
}
