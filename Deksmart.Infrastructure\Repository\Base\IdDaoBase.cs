﻿using Deksmart.Domain.Entity.Db.Interface;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Interface.Base;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository.Base
{
    public class IdDaoBase<TEntity, TId> : RepositoryBase, IIdRepositoryBase<TEntity, TId>
        where TEntity : class, IIdEntity<TId>
        where TId : struct
    {
        public IdDaoBase(ConfiguratorContext context) : base(context)
        {
        }

        /// <inheritdoc/>
        public async virtual Task<IIdEntity<TId>?> GetEntityByIdAsync(TId id)
        {
            return await _context.Set<TEntity>().AsNoTracking().FirstOrDefaultAsync(e => e.Id.Equals(id));
        }

        /// <inheritdoc/>
        public async virtual Task InsertAsync(TEntity entity)
        {
            await _context.Set<TEntity>().AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public virtual void SetUpdated(TEntity entity)
        {
            _context.Set<TEntity>().Update(entity);
        }
    }
}
