using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Services
{
    public class ConfiguratorValidationService : IConfiguratorValidationService
    {
        public bool SetAllFieldsDirty(ConfiguratorWrapper? configuratorWrapper, List<IConfiguratorGridService> childServices)
        {
            if (configuratorWrapper == null)
                return false;

            foreach (var category in configuratorWrapper.FieldCategories)
            {
                foreach (var field in category.AllFields)
                {
                    field.IsDirty = true;
                }
            }

            // Also set fields in child services as dirty if this is a collection
            foreach (var childService in childServices)
            {
                // For child services, we want to show validation checkboxes
                childService.ShowValidationCheckboxes = true;
                childService.SetAllFieldsDirty();
            }

            return configuratorWrapper.IsValid;
        }

        public string? FindFirstInvalidCategoryId(ConfiguratorWrapper? configuratorWrapper, List<IConfiguratorGridService> childServices)
        {
            if (configuratorWrapper == null)
                return null;

            // First check this configurator's categories
            foreach (var category in configuratorWrapper.FieldCategories)
            {
                if (category.AllFields.Any(f => f.HasValidationError && f.IsDirty))
                {
                    // Return the category's HTML element ID
                    return $"category-{category.Category.Id}";
                }
            }

            // If no errors found in this configurator, check child configurators
            foreach (var childService in childServices)
            {
                var childCategoryId = childService.FindFirstInvalidCategoryId();
                if (childCategoryId != null)
                {
                    return childCategoryId;
                }
            }

            return null;
        }

        public bool UpdateParentValidationState(ConfiguratorWrapper? mainConfiguratorWrapper, List<IConfiguratorGridService> childServices)
        {
            if (mainConfiguratorWrapper == null)
                return false;

            // The parent is valid only if all children are valid
            bool allChildrenValid = childServices
                .Where(s => s.ConfiguratorWrapper != null)
                .All(s => s.ConfiguratorWrapper!.IsValid);

            // Update the parent's IsValid property directly
            mainConfiguratorWrapper.UpdateIsValid(allChildrenValid);

            return allChildrenValid;
        }
    }
}