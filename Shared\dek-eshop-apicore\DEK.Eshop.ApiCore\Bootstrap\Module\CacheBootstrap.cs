using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Cache;
using Microsoft.Extensions.Caching.Distributed;
using DEK.Eshop.ApiCore.Config.Dto;
using StackExchange.Redis;

namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class CacheBootstrap
{

    /// <exception cref="ConfigException"></exception>
    public static void AddCacheBootstrap(this IServiceCollection services, IConfiguration configuration)
    {
        var config = configuration.GetSection("ApiCore:Cache:Redis").Get<CacheRedis>();

        if (config is not null && config.Enabled) {

            services.AddSingleton<CacheManager>();

            // Add DevNullAdapter
            if (config.ReplaceRedisWithDevNullAdapter) {
                services.AddSingleton<IDistributedCache, DevNullCacheAdapter>();
                return;
            }
            // Add RedisAdapter
            services.AddStackExchangeRedisCache(options => {
                options.InstanceName = config.Prefix;
                options.ConfigurationOptions = new ConfigurationOptions() {
                    EndPoints = { config.Host },
                    ConnectTimeout = config.ConnectTimeout,
                    SyncTimeout = config.SyncTimeout,
                    CheckCertificateRevocation = config.CheckCertificateRevocation,
                    Password = config.Password,
                };
            });
        } 
    }
}
