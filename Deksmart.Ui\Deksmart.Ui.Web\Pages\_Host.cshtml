@page "/{*path:nonfile}"
@using Deksmart.Ui.Web.Client
@using Deksmart.Ui.Shared
@namespace Deksmart.Ui.Server.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="blazor-webassembly-prerendering" content="false" />
    <title>Deksmart</title>
    <base href="~/" />
    <link rel="stylesheet" href="_content/Deksmart.Ui.Shared/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="_content/Deksmart.Ui.Shared/css/app.css" />
    <link rel="stylesheet" href="_content/Deksmart.Ui.Shared/css/form-field-fixes.css" />
    <link href="Deksmart.Ui.Web.Client.styles.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://www.dek.cz/css-production/index.css?v=1747660134" />
</head>
<body>
    <div id="app">
        <div style="display: flex; justify-content: center; align-items: center; height: 100vh;">
            <div style="text-align: center;">
                <h1>DEKSMART</h1>
                <p>Loading application...</p>
                <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="_content/Deksmart.Ui.Shared/js/app.js"></script>
    <script src="_framework/blazor.webassembly.js"></script>
    <script>
        // Add custom error handling for Blazor WebAssembly
        window.addEventListener('unhandledrejection', function (event) {
            console.error('Unhandled promise rejection:', event.reason);
            if (window.handleComponentError) {
                window.handleComponentError(event.reason);
            }
        });

        window.setMetaDescription = function (description) {
            let tag = document.querySelector('meta[name="description"]');
            if (!tag) {
                tag = document.createElement('meta');
                tag.name = 'description';
                document.head.appendChild(tag);
            }
            tag.content = description;
        }
    </script>
</body>
</html>
