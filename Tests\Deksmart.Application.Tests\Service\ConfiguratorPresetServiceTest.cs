﻿using Moq;
using Deksmart.Application.Service;
using Deksmart.Shared.Dto;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Application.Mapping;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Shared.Enum;

namespace Deksmart.Application.Tests.Service
{
    public class ConfiguratorPresetServiceTest
    {
        private readonly Mock<IConfiguratorPresetRepository> _presetDaoMock;
        private readonly Mock<IConfiguratorMappingService> _mappingServiceMock;
        private readonly Mock<IConfiguratorValidationService> _validationServiceMock;
        private readonly Mock<IPresetConfiguratorCategoryStateRepository> _categoryStateDaoMock;
        private readonly Mock<IValidationService> _validationServiceBaseMock;
        private readonly ConfiguratorPresetService _service;

        public ConfiguratorPresetServiceTest()
        {
            _presetDaoMock = new Mock<IConfiguratorPresetRepository>();
            _mappingServiceMock = new Mock<IConfiguratorMappingService>();
            _validationServiceMock = new Mock<IConfiguratorValidationService>();
            _categoryStateDaoMock = new Mock<IPresetConfiguratorCategoryStateRepository>();
            _validationServiceBaseMock = new Mock<IValidationService>();
            _validationServiceBaseMock.Setup(x => x.CreateValidation()).Returns(new ValidationResult());

            _service = new ConfiguratorPresetService(
                _presetDaoMock.Object,
                _categoryStateDaoMock.Object,
                _mappingServiceMock.Object,
                _validationServiceMock.Object);
        }

        [Fact]
        public async Task SavePresetAsync_ShouldReturnError_WhenConfiguratorDoesNotExist()
        {
            // Arrange
            var configuratorId = 1;
            var fieldValues = new List<ClientFieldValueDto>();
            var selectedProducts = new List<ClientProductValueDto>();
            var existsValidation = new ValidationResult();
            existsValidation.AddError("Configurator does not exist");

            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Callback<int, ValidationResult>((id, val) => val.AddError("Configurator does not exist"))
                .Returns(Task.CompletedTask);

            // Act
            var (guid, validation) = await _service.SavePresetAsync(configuratorId, new ConfiguratorStateDto(configuratorId, fieldValues, selectedProducts));

            // Assert
            Assert.Null(guid);
            Assert.True(validation.HasErrors);
            Assert.Contains("Configurator does not exist", validation.GetErrors());
            _presetDaoMock.Verify(x => x.InsertAsync(It.IsAny<ConfiguratorPreset>()), Times.Never);
        }

        [Fact]
        public async Task SavePresetAsync_ShouldSavePreset_WhenValid()
        {
            // Arrange
            var configuratorId = 1;
            var fieldValues = new List<ClientFieldValueDto> { new ClientFieldValueDto { FieldId = 1, Value = 10 } };
            var selectedProducts = new List<ClientProductValueDto> { new ClientProductValueDto { ProductId = 1, Amount = 1 } };
            var existsValidation = new ValidationResult();

            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);
            _presetDaoMock.Setup(x => x.InsertAsync(It.IsAny<ConfiguratorPreset>())).Returns(Task.CompletedTask);

            // Act
            var (guid, validation) = await _service.SavePresetAsync(configuratorId, new ConfiguratorStateDto(configuratorId, fieldValues, selectedProducts));

            // Assert
            Assert.NotNull(guid);
            Assert.False(validation.HasErrors);
            _presetDaoMock.Verify(x => x.InsertAsync(It.IsAny<ConfiguratorPreset>()), Times.Once);
        }

        [Fact]
        public async Task GetConfiguratorStateAsync_ShouldReturnNull_WhenPresetNotFound()
        {
            // Arrange
            var presetId = Guid.NewGuid();
            _presetDaoMock.Setup(x => x.GetFieldPresetByIdAsync(presetId)).ReturnsAsync((ConfiguratorPreset?)null);

            // Act
            var result = await _service.GetConfiguratorStateAsync(presetId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetConfiguratorStateAsync_ShouldReturnState_WhenPresetFound()
        {
            // Arrange
            var presetId = Guid.NewGuid();
            var preset = new ConfiguratorPreset
            {
                ConfiguratorFieldCombinations = new List<ConfiguratorFieldCombination>
                {
                    new ConfiguratorFieldCombination { FieldId = 1, FieldValue = 10 },
                    new ConfiguratorFieldCombination { FieldId = 2, FieldValue = 20 }
                }
            };

            var productCombinations = new List<ConfiguratorProductCombination>
            {
                new ConfiguratorProductCombination { ProductId = 1, Amount = 1 },
                new ConfiguratorProductCombination { ProductId = 2, Amount = 2 }
            };

            _presetDaoMock.Setup(x => x.GetFieldPresetByIdAsync(presetId)).ReturnsAsync(preset);
            _presetDaoMock.Setup(x => x.GetProductPresetByIdAsync(presetId)).ReturnsAsync(productCombinations);
            _mappingServiceMock.Setup(x => x.MapToDto(It.IsAny<ConfiguratorFieldCombination>()))
                .Returns<ConfiguratorFieldCombination>(c => new ClientFieldValueDto { FieldId = c.FieldId, Value = c.FieldValue });

            // Act
            var result = await _service.GetConfiguratorStateAsync(presetId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.FieldValues.Count);
            Assert.Equal(2, result.SelectedProducts.Count);
            Assert.Equal(1, result.FieldValues[0].FieldId);
            Assert.Equal(10, result.FieldValues[0].Value);
            Assert.Equal(2, result.FieldValues[1].FieldId);
            Assert.Equal(20, result.FieldValues[1].Value);
            Assert.Equal(1, result.SelectedProducts[0].ProductId);
            Assert.Equal(1, result.SelectedProducts[0].Amount);
            Assert.Equal(2, result.SelectedProducts[1].ProductId);
            Assert.Equal(2, result.SelectedProducts[1].Amount);
        }

        [Fact]
        public async Task GetPresetIdByCatalogId_ShouldReturnNull_WhenCatalogIdIsEmpty()
        {
            // Arrange
            var catalogId = "";

            // Act
            var result = await _service.GetPresetIdByCatalogId(catalogId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPresetIdByCatalogId_ShouldReturnNull_WhenPresetNotFound()
        {
            // Arrange
            var catalogId = "TEST";
            _presetDaoMock.Setup(x => x.GetPresetIdByCatalogId(catalogId)).ReturnsAsync((ConfiguratorPreset?)null);

            // Act
            var result = await _service.GetPresetIdByCatalogId(catalogId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPresetIdByCatalogId_ShouldReturnPreset_WhenFound()
        {
            // Arrange
            var catalogId = "TEST";
            var preset = new ConfiguratorPreset { Id = Guid.NewGuid(), ConfiguratorId = 1 };
            _presetDaoMock.Setup(x => x.GetPresetIdByCatalogId(catalogId)).ReturnsAsync(preset);

            // Act
            var result = await _service.GetPresetIdByCatalogId(catalogId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(preset.ConfiguratorId, result.ConfiguratorId);
            Assert.Equal(preset.Id.ToString(), result.PresetId);
        }

        [Fact]
        public async Task SavePresetAsync_ShouldSaveCategoryStates()
        {
            // Arrange
            int configuratorId = 123;
            var categoryStates = new List<CategoryStateDto>
            {
                new() { CategoryId = 1, CollapseState = CategoryCollapseStateDto.Expanded },
                new() { CategoryId = 2, CollapseState = CategoryCollapseStateDto.Collapsed }
            };

            var state = new ConfiguratorStateDto
            {
                ConfiguratorId = configuratorId,
                FieldValues = [],
                SelectedProducts = [],
                CategoryStates = categoryStates
            };

            ConfiguratorPreset? savedPreset = null;
            _presetDaoMock.Setup(r => r.InsertAsync(It.IsAny<ConfiguratorPreset>()))
                .Callback<ConfiguratorPreset>(p => savedPreset = p)
                .Returns(Task.CompletedTask);

            var existsValidation = new ValidationResult();
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Act
            var (guid, validation) = await _service.SavePresetAsync(configuratorId, state);

            // Assert
            Assert.NotNull(guid);
            Assert.False(validation.HasErrors);
            Assert.NotNull(savedPreset);
            Assert.Equal(2, savedPreset.CategoryStates.Count);
            Assert.Equal(1, savedPreset.CategoryStates.First().CategoryId);
            Assert.Equal(CategoryCollapseStateDto.Expanded, (CategoryCollapseStateDto)savedPreset.CategoryStates.First().CollapseState);
            Assert.Equal(2, savedPreset.CategoryStates.Last().CategoryId);
            Assert.Equal(CategoryCollapseStateDto.Collapsed, (CategoryCollapseStateDto)savedPreset.CategoryStates.Last().CollapseState);
        }

        [Fact]
        public async Task GetConfiguratorStateAsync_ShouldLoadCategoryStates()
        {
            // Arrange
            var presetId = Guid.NewGuid();
            var categoryStates = new List<PresetConfiguratorCategoryState>
            {
                new() { CategoryId = 1, CollapseState = Deksmart.Domain.Enum.CategoryCollapseState.Expanded },
                new() { CategoryId = 2, CollapseState = Deksmart.Domain.Enum.CategoryCollapseState.Collapsed }
            };

            var preset = new ConfiguratorPreset
            {
                Id = presetId,
                ConfiguratorId = 123,
                ConfiguratorFieldCombinations = [],
                CategoryStates = categoryStates
            };

            _presetDaoMock.Setup(r => r.GetFieldPresetByIdAsync(presetId))
                .ReturnsAsync(preset);
            _presetDaoMock.Setup(r => r.GetProductPresetByIdAsync(presetId))
                .ReturnsAsync([]);
            _categoryStateDaoMock.Setup(r => r.GetCategoryStatesForPresetAsync(presetId))
                .ReturnsAsync(categoryStates);

            // Act
            var result = await _service.GetConfiguratorStateAsync(presetId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.CategoryStates.Count);
            Assert.Equal(1, result.CategoryStates[0].CategoryId);
            Assert.Equal(CategoryCollapseStateDto.Expanded, result.CategoryStates[0].CollapseState);
            Assert.Equal(2, result.CategoryStates[1].CategoryId);
            Assert.Equal(CategoryCollapseStateDto.Collapsed, result.CategoryStates[1].CollapseState);
        }
    }
}