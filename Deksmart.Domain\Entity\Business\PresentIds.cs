namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Tracks the IDs of products, compositions, fields, values, and categories present during a configurator import or mapping operation.
    /// Used to synchronize the imported data with the database, ensuring only entities with IDs in these sets are retained or updated, and others are marked for deletion.
    /// Updated dynamically as entities are processed during mapping.
    /// </summary>
    public class PresentIds
    {
        public HashSet<int> ProductIds { get; } = new();
        public HashSet<int> CompositionIds { get; } = new();
        public HashSet<int> FieldIds { get; } = new();
        public HashSet<int> ValuesIds { get; } = new();
        public HashSet<int> CategoryIds { get; } = new();
    }
}
