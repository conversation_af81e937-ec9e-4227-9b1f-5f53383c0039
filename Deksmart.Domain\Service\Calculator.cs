using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Resource;
using Microsoft.Extensions.Logging;

namespace Deksmart.Domain.Service
{
    public interface ICalculator
    {
        /// <summary>
        /// Evaluates a mathematical or logical expression using the provided parameters.
        /// </summary>
        /// <param name="expressionString">The expression to evaluate, as a string.</param>
        /// <param name="parameters">A collection of parameter values to use in the expression.</param>
        /// <returns>
        /// A tuple containing the calculation result (decimal) and validation result.
        /// The decimal will be 0 if validation fails.
        /// </returns>
        (decimal result, ValidationResult validation) CalculateExpression(string expressionString, IEnumerable<ValueIdent> parameters);
    }

    public class Calculator : ICalculator
    {
        private readonly ILogger<Calculator> _logger;

        public Calculator(ILogger<Calculator> logger)
        {
            _logger = logger;
        }

        public (decimal result, ValidationResult validation) CalculateExpression(string expressionString, IEnumerable<ValueIdent> parameters)
        {
            var validation = new ValidationResult();
            
            if (string.IsNullOrWhiteSpace(expressionString))
            {
                validation.AddError(DeksmartDomainResource.CalculatorExpressionEmpty);
                _logger.LogWarning("Expression string cannot be null or empty");
                return (0, validation);
            }

            try
            {
                var expression = new NCalc.Expression(expressionString);

                foreach (var parameter in parameters)
                {
                    expression.Parameters[parameter.Ident] = parameter.Value;
                }

                var result = expression.Evaluate();
                
                if (result == null)
                {
                    validation.AddError(string.Format(DeksmartDomainResource.CalculatorExpressionEvaluatedNull, expressionString));
                    _logger.LogWarning($"Expression '{expressionString}' evaluated to null");
                    return (0, validation);
                }

                if (!decimal.TryParse(result.ToString(), out decimal evaluated))
                {
                    validation.AddError(string.Format(DeksmartDomainResource.CalculatorResultParseError, result));
                    _logger.LogWarning($"Cannot parse expression result '{result}' to decimal for expression '{expressionString}'");
                    return (0, validation);
                }

                return (evaluated, validation);
            }
            catch (Exception ex) when (ex.GetType().Name.Contains("Evaluation"))
            {
                validation.AddError(string.Format(DeksmartDomainResource.CalculatorEvaluationError, ex.Message));
                _logger.LogError(ex, $"Expression evaluation failed for '{expressionString}'");
                return (0, validation);
            }
            catch (ArgumentException ex)
            {
                validation.AddError(string.Format(DeksmartDomainResource.CalculatorInvalidArgument, ex.Message));
                _logger.LogError(ex, $"Invalid expression or parameters for '{expressionString}'");
                return (0, validation);
            }
            catch (Exception ex)
            {
                validation.AddError(string.Format(DeksmartDomainResource.CalculatorUnexpectedError, ex.Message));
                _logger.LogError(ex, $"Unexpected error calculating expression '{expressionString}'");
                return (0, validation);
            }
        }
    }
}
