using System.Security.Principal;


namespace DEK.Eshop.ApiCore.Indentity;

public record HttpUser : IIdentity
{

    // required from jwt
    public string? UserEmail { get; init; } = null;

    public string? BranchCode { get; init; } = null;

    public string CartId { get; init; } = string.Empty;

    public string? EshopId { get; init; } = null; // muže být null u server to server na pulic endpointech

    public string? Database { get; init; } = null; // muže být null u server to server na pulic endpointech

    // IIdentity
    public string AuthenticationType { get; init; } = "Custom";

    public bool IsAuthenticated { get; init; }

    public string Name { get; init; } = string.Empty;
}
