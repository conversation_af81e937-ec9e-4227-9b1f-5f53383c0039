.notification-container {
    position: fixed;
    top: 0;
    right: 0;
    left: auto;
    width: auto;
    z-index: 2000;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.notification {
    margin-top: 1rem;
    min-width: 320px;
    max-width: 90vw;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    pointer-events: auto;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    font-size: 1rem;
    border-left: 5px solid #e74c3c;
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 400px;
}

.notification-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.notification-message {
    flex: 1;
    color: #333;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: pre-line;
    min-width: 0;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #888;
    margin-left: 1rem;
}

.notification.error {
    border-left-color: #e74c3c;
}

.notification.info {
    border-left-color: #3498db;
}

.notification.success {
    border-left-color: #27ae60;
} 