using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Moq;
using Deksmart.Ui.Shared.Services;
using Deksmart.Ui.Model;
using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class ConfiguratorNavigationServiceTest
    {
        private readonly TestNavigationManager _testNavigationManager;
        private readonly Mock<ILogger<ConfiguratorNavigationService>> _mockLogger;
        private readonly Mock<IJSRuntime> _mockJSRuntime;
        private readonly ConfiguratorNavigationService _service;

        public ConfiguratorNavigationServiceTest()
        {
            _testNavigationManager = new TestNavigationManager();
            _mockLogger = new Mock<ILogger<ConfiguratorNavigationService>>();
            _mockJSRuntime = new Mock<IJSRuntime>();
            _service = new ConfiguratorNavigationService(
                _testNavigationManager,
                _mockLogger.Object,
                _mockJSRuntime.Object);
        }

        private class TestNavigationManager : NavigationManager
        {
            public TestNavigationManager() : base()
            {
                Initialize("https://localhost/", "https://localhost/");
            }

            public List<(string uri, bool forceLoad)> NavigateCalls { get; } = new();

            protected override void NavigateToCore(string uri, bool forceLoad)
            {
                NavigateCalls.Add((uri, forceLoad));
            }
        }

        private IConfiguratorGridService CreateTestConfiguratorGridService(int id = 1, string title = "Test Configurator")
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = id,
                Title = title,
                Url = "test-url"
            };
            var wrapper = new ConfiguratorWrapper(configuratorDto);
            
            var mockService = new Mock<IConfiguratorGridService>();
            mockService.Setup(s => s.ConfiguratorWrapper).Returns(wrapper);
            return mockService.Object;
        }

        [Fact]
        public void Constructor_InitializesCorrectly()
        {
            var service = new ConfiguratorNavigationService(
                _testNavigationManager,
                _mockLogger.Object,
                _mockJSRuntime.Object);

            Assert.NotNull(service);
        }

        [Fact]
        public void SetActiveService_SetsActiveService()
        {
            var testService = CreateTestConfiguratorGridService(42, "Test Service");

            _service.SetActiveService(testService);

            var activeService = _service.GetActiveService();
            Assert.Equal(testService, activeService);
        }

        [Fact]
        public void SetActiveService_LogsInformation()
        {
            var testService = CreateTestConfiguratorGridService(42, "Test Service");

            _service.SetActiveService(testService);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Set active configurator service")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void SetActiveService_WithNullWrapper_LogsWithNullId()
        {
            var mockService = new Mock<IConfiguratorGridService>();
            mockService.Setup(s => s.ConfiguratorWrapper).Returns((ConfiguratorWrapper?)null);

            _service.SetActiveService(mockService.Object);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Set active configurator service")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public void GetActiveService_WhenNoServiceSet_ReturnsNull()
        {
            var activeService = _service.GetActiveService();

            Assert.Null(activeService);
        }

        [Fact]
        public void GetActiveService_AfterSettingService_ReturnsCorrectService()
        {
            var testService = CreateTestConfiguratorGridService(123, "My Service");
            _service.SetActiveService(testService);

            var activeService = _service.GetActiveService();

            Assert.Equal(testService, activeService);
        }

        [Fact]
        public void ClearActiveService_SetsActiveServiceToNull()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);
            Assert.NotNull(_service.GetActiveService());

            _service.ClearActiveService();

            Assert.Null(_service.GetActiveService());
        }

        [Fact]
        public async Task NavigateToConfigurator_WithActiveService_CallsJSRuntimeAndNavigates()
        {
            var testService = CreateTestConfiguratorGridService(456, "Nav Test Service");
            _service.SetActiveService(testService);
            var expectedScrollPosition = 150.5;
            _mockJSRuntime.Setup(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(expectedScrollPosition);

            await _service.NavigateToConfigurator(789);

            _mockJSRuntime.Verify(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()), Times.Once);
            Assert.Single(_testNavigationManager.NavigateCalls);
            Assert.Equal("/deksmart/789", _testNavigationManager.NavigateCalls[0].uri);
            Assert.False(_testNavigationManager.NavigateCalls[0].forceLoad);
            Assert.Equal(expectedScrollPosition, _service.GetSavedScrollPosition());
        }

        [Fact]
        public async Task NavigateToConfigurator_WithActiveService_LogsNavigation()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);
            _mockJSRuntime.Setup(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(100.0);

            await _service.NavigateToConfigurator(999);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Navigating to configurator ID: 999")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task NavigateToConfigurator_WithoutActiveService_DoesNotNavigate()
        {
            await _service.NavigateToConfigurator(123);

            _mockJSRuntime.Verify(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()), Times.Never);
            Assert.Empty(_testNavigationManager.NavigateCalls);
        }

        [Fact]
        public async Task NavigateToConfigurator_WithoutActiveService_DoesNotLog()
        {
            await _service.NavigateToConfigurator(123);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Navigating to configurator")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Never);
        }

        [Fact]
        public void GetSavedScrollPosition_InitialValue_ReturnsZero()
        {
            var scrollPosition = _service.GetSavedScrollPosition();

            Assert.Equal(0.0, scrollPosition);
        }

        [Fact]
        public async Task GetSavedScrollPosition_AfterNavigation_ReturnsCorrectValue()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);
            var expectedPosition = 275.8;
            _mockJSRuntime.Setup(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(expectedPosition);

            await _service.NavigateToConfigurator(123);

            Assert.Equal(expectedPosition, _service.GetSavedScrollPosition());
        }

        [Fact]
        public async Task NavigateToConfigurator_MultipleNavigations_UpdatesScrollPosition()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);

            // First navigation
            _mockJSRuntime.SetupSequence(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(100.0)
                .ReturnsAsync(250.5);

            await _service.NavigateToConfigurator(1);
            Assert.Equal(100.0, _service.GetSavedScrollPosition());

            await _service.NavigateToConfigurator(2);
            Assert.Equal(250.5, _service.GetSavedScrollPosition());
        }

        [Fact]
        public async Task NavigateToConfigurator_JSRuntimeThrowsException_DoesNotPreventNavigation()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);
            _mockJSRuntime.Setup(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ThrowsAsync(new JSException("JavaScript error"));

            var exception = await Record.ExceptionAsync(() => _service.NavigateToConfigurator(123));

            Assert.NotNull(exception);
            Assert.IsType<JSException>(exception);
            Assert.Empty(_testNavigationManager.NavigateCalls);
        }

        [Fact]
        public void SetActiveService_AfterClear_CanSetNewService()
        {
            var firstService = CreateTestConfiguratorGridService(1, "First");
            var secondService = CreateTestConfiguratorGridService(2, "Second");

            _service.SetActiveService(firstService);
            Assert.Equal(firstService, _service.GetActiveService());

            _service.ClearActiveService();
            Assert.Null(_service.GetActiveService());

            _service.SetActiveService(secondService);
            Assert.Equal(secondService, _service.GetActiveService());
        }

        [Fact]
        public void SetActiveService_ReplacingActiveService_UpdatesToNewService()
        {
            var firstService = CreateTestConfiguratorGridService(1, "First");
            var secondService = CreateTestConfiguratorGridService(2, "Second");

            _service.SetActiveService(firstService);
            Assert.Equal(firstService, _service.GetActiveService());

            _service.SetActiveService(secondService);
            Assert.Equal(secondService, _service.GetActiveService());
        }

        [Fact]
        public async Task NavigateToConfigurator_DifferentIds_GeneratesCorrectUrls()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);
            _mockJSRuntime.Setup(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(0.0);

            await _service.NavigateToConfigurator(42);
            await _service.NavigateToConfigurator(999);
            await _service.NavigateToConfigurator(0);

            Assert.Equal(3, _testNavigationManager.NavigateCalls.Count);
            Assert.Equal("/deksmart/42", _testNavigationManager.NavigateCalls[0].uri);
            Assert.Equal("/deksmart/999", _testNavigationManager.NavigateCalls[1].uri);
            Assert.Equal("/deksmart/0", _testNavigationManager.NavigateCalls[2].uri);
            Assert.All(_testNavigationManager.NavigateCalls, call => Assert.False(call.forceLoad));
        }

        [Fact]
        public async Task NavigateToConfigurator_ForceLoadParameterTest()
        {
            var testService = CreateTestConfiguratorGridService();
            _service.SetActiveService(testService);
            _mockJSRuntime.Setup(js => js.InvokeAsync<double>(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(0.0);

            await _service.NavigateToConfigurator(123);

            // Verify that forceLoad parameter is always false
            Assert.Single(_testNavigationManager.NavigateCalls);
            Assert.Equal("/deksmart/123", _testNavigationManager.NavigateCalls[0].uri);
            Assert.False(_testNavigationManager.NavigateCalls[0].forceLoad);
        }
    }
}