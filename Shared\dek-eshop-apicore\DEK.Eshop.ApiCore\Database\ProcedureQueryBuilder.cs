using Microsoft.Data.SqlClient;

namespace DEK.Eshop.ApiCore.Database;

/// <summary>
/// Query builder for stored procedures.
/// </summary>
public class ProcedureQueryBuilder
{
    public Dictionary<string, SqlParameter> Parameters { get; set; } = new Dictionary<string, SqlParameter>();

    public string ProcedureName { get; }

    public ProcedureQueryBuilder(string procedureName)
    {
        ProcedureName = procedureName;
    }

    /// <summary>
    /// If the value is null, adding of paramater is skipped, and the default value in the procedure definition is used.
    /// </summary>
    /// <exception cref="ArgumentException"></exception>
    public ProcedureQueryBuilder AddParam(string name, object? value)
    {
        if (string.IsNullOrWhiteSpace(name)) {
            throw new ArgumentException("Parametr name is empty string");
        }

        if (value is null) {
            return this;
        }
        // if the name starts with @, remove it
        name = name.First().ToString() == "@" ? name.Remove(0, 1) : name;
        Parameters.Add(name, new SqlParameter(name + '_', value));
        return this;
    }

    public string BuildSql()
    {
        var builder = new System.Text.StringBuilder();
        builder.Append("SET NOCOUNT ON; ");

        builder.Append("EXEC");
        builder.Append(' ');
        builder.Append(ProcedureName);

        var i = 0;
        foreach (var param in Parameters) {
            builder.Append(" @");
            builder.Append(param.Key);
            builder.Append(" = ");
            builder.Append('@');
            builder.Append(param.Key);
            builder.Append("_,");

            i++;

            // removes the comma after the last parameter
            if (Parameters.Count == i) {
                builder.Length--; 
            }
        }

        return builder.ToString();
    }
}
