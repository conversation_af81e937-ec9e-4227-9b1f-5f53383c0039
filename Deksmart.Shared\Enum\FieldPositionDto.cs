namespace Deksmart.Shared.Enum
{
    /// <summary>
    /// Enum defining the possible layout positions for a configurator field within a category.
    /// Used to control and transfer the visual arrangement of fields (left, right, full width, and their suffixes) between backend, API, and UI.
    /// Supports dynamic UI generation and field grouping logic in configuration workflows by specifying where each field should be rendered relative to others.
    /// </summary>
    [Serializable]
    public enum FieldPositionDto : short
    {
        Left = 0,
        LeftSuffix = 1,
        Right = 2,
        RightSuffix = 3,
        FullWidth = 4,
        FullWidthSuffix = 5
    }
} 