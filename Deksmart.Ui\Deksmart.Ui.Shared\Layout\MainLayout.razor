﻿@inherits LayoutComponentBase
@using Deksmart.Ui.Shared.Components
@using Deksmart.Ui.Shared.Components.Utility
@using Deksmart.Ui.Shared.Resources
@using Deksmart.Ui.Shared.Services
@using System.Diagnostics.CodeAnalysis
@implements IDisposable
@inject IJSRuntime JSRuntime
@inject HeaderStateService HeaderState

<div class="page">
    <header id="dek-header">
    DEK
    </header>
    <main>
        <div class="menu-toggle">
            <div class="header-left">
                <button class="navbar-toggler" @onclick="ToggleSidebar" title="Toggle menu">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <a class="header-brand" href="@($"/{UiSharedResource.Deksmart}")">@UiSharedResource.DeksmartBrand</a>
            </div>

            @if (!string.IsNullOrEmpty(HeaderState.Title))
            {
                <div class="header-center">
                    <h3 class="configurator-title">@HeaderState.Title</h3>
                </div>
            }

            @if (HeaderState.ShowSaveButton)
            {
                <div class="header-right">
                    <button class="save-button" @onclick="HandleSaveClick" disabled="@HeaderState.SaveButtonDisabled">@UiSharedResource.Save</button>
                </div>
            }
        </div>
        <div class="content-wrapper" style="position: relative; min-height: calc(100vh - var(--header-total-height, 0px));">
            <article class="content px-4">
                @Body
            </article>
            @if (sidebarVisible)
            {
                <div class="menu-backdrop" @onclick="ToggleSidebar"></div>
            }
        </div>
    </main>
    <div id="sidebar" class="@($"sidebar {(sidebarVisible ? "show" : "")}")">
        <NavMenu />
    </div>
</div>

<ErrorNotification />

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("fetchDekHeader");
            await JSRuntime.InvokeVoidAsync("adjustSidebarTop");
            // Initialize sidebar positioning after headers are loaded
            await Task.Delay(100); // Small delay to ensure headers are rendered
            await JSRuntime.InvokeVoidAsync("updateSidebarPosition");
        }
        await base.OnAfterRenderAsync(firstRender);
    }
    private bool sidebarVisible = false;

    protected override void OnInitialized()
    {
        // Subscribe to header state changes
        HeaderState.OnChange += StateHasChanged;
        base.OnInitialized();
    }

    private async Task ToggleSidebar()
    {
        sidebarVisible = !sidebarVisible;
        
        if (sidebarVisible)
        {
            // Update sidebar position before showing it
            await JSRuntime.InvokeVoidAsync("updateSidebarPosition");
        }
        
        StateHasChanged();
    }

    private async Task HandleSaveClick()
    {
        if (HeaderState.SaveCallback.HasDelegate)
        {
            await HeaderState.SaveCallback.InvokeAsync();
        }
    }

    // Clean up the subscription when the component is disposed
    void IDisposable.Dispose()
    {
        HeaderState.OnChange -= StateHasChanged;
    }
}
