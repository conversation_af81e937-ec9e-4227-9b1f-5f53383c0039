<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Deksmart</title>
    <base href="/" />
    <link rel="stylesheet" href="_content/Deksmart.Ui.Shared/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="_content/Deksmart.Ui.Shared/css/app.css" />
    <link rel="stylesheet" href="_content/Deksmart.Ui.Shared/css/form-field-fixes.css" />
    <link rel="icon" type="image/png" href="_content/Deksmart.Ui.Shared/favicon.png" />
    <link href="Deksmart.Ui.Web.Client.styles.css" rel="stylesheet" />
</head>

<body>
    <div id="app">
        <div style="display: flex; justify-content: center; align-items: center; height: 100vh;">
            <div style="text-align: center;">
                <h1>@UiSharedResource.DeksmartBrand</h1>
                <p>@UiSharedResource.LoadingApplication</p>
                <div class="spinner-border text-danger" role="status">
                    <span class="visually-hidden">@UiSharedResource.Loading</span>
                </div>
            </div>
        </div>
    </div>

    <div id="blazor-error-ui">
        @UiSharedResource.UnhandledError
        <a href="." class="reload">@UiSharedResource.Reload</a>
        <span class="dismiss">🗙</span>
    </div>

    <script src="_content/Deksmart.Ui.Shared/js/app.js"></script>
    <script src="_framework/blazor.webassembly.js"></script>
    <script>
        // Add custom error handling for Blazor WebAssembly
        window.addEventListener('unhandledrejection', function (event) {
            console.error('Unhandled promise rejection:', event.reason);
            if (window.handleComponentError) {
                window.handleComponentError(event.reason);
            }
        });
    </script>
</body>

</html>
