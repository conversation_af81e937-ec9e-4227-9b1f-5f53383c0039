using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Security.Cryptography;
using DEK.Eshop.ApiCore.Config.Dto;
using DEK.Eshop.ApiCore.Config;

namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class JwtBootstrap
{
    /// <exception cref="ConfigException"></exception>
    public static void AddJwtBootstrap(this IServiceCollection services, IConfiguration configuration)
    {
        // https://blog.devgenius.io/jwt-authentication-in-asp-net-core-e67dca9ae3e8
        // https://matteosonoio.it/aspnet-core-authentication-schemes/ - multiple authentication schemes

        var jwt = ConfigFactory.Create<Jwt>(configuration);

        // if config has asymmetric keys, add them to services
        if (jwt.AsymmetricKeys.Any()) {
            var rsaSecurityKeys = jwt.AsymmetricKeys.Select(key => {
                RSA rsa = RSA.Create();
                rsa.ImportRSAPublicKey(Convert.FromBase64String(key), out int _);
                return new RsaSecurityKey(rsa);
            });
            services.AddSingleton(rsaSecurityKeys);
        }

        // if config has symmetric keys, add them to services
        if (jwt.SymmetricKeys.Any()) {
            var symmetricSecurityKeys = jwt.SymmetricKeys.Select(key => new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key)));
            services.AddSingleton(symmetricSecurityKeys);
        }

        services.AddAuthentication("Bearer").AddJwtBearer(options => {
            var keys = Enumerable.Empty<SecurityKey>();

            if (jwt.AsymmetricKeys.Any()) {
                keys = keys.Concat(services.BuildServiceProvider().GetRequiredService<IEnumerable<RsaSecurityKey>>());
            }

            if (jwt.SymmetricKeys.Any()) {
                keys = keys.Concat(services.BuildServiceProvider().GetRequiredService<IEnumerable<SymmetricSecurityKey>>());
            }

            //o.IncludeErrorDetails = true;
            options.TokenValidationParameters = new TokenValidationParameters {
                ValidateIssuer = jwt.Issuers.Any(),
                ValidIssuers = jwt.Issuers,
                ValidateAudience = jwt.Issuers.Any(),
                ValidAudiences = jwt.Audiences,
                ValidateLifetime = jwt.Expiration, // https://www.rfc-editor.org/rfc/rfc7519#section-4.1.4 https://stackoverflow.com/a/39926886/4000826
                ValidateIssuerSigningKey = true,
                IssuerSigningKeys = keys,
            };

            // TODO: pridat vlastni respose code a message
            // https://stackoverflow.com/a/50451116/4000826
            //o.Events = new JwtBearerEvents {
            //    OnAuthenticationFailed = (context) =>
            //    {
            //        context.Response.WriteAsync("test test");
            //        return Task.CompletedTask;
            //    }
            //};
        });
    }

    /// <exception cref="ConfigException"></exception>
    public static void UseJwtBootstrap(this WebApplication app)
    {
        app.UseAuthentication();
    }
}
