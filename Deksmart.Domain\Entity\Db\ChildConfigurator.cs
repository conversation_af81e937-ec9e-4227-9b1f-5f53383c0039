using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Defines the relationship between a parent configurator and its child configurators, enabling composite configurators.
    /// Specifies which configurators can be selected as children of another configurator, allowing users to compose complex configurations.
    /// </summary>
    [Table("child_configurator", Schema = "dbo")]
    public class ChildConfigurator
    {
        /// <summary>
        /// Foreign key to the parent composite configurator.
        /// </summary>
        [Key]
        [Column("composite_id")]
        public int CompositeId { get; set; }

        /// <summary>
        /// The ID of the child configurator.
        /// </summary>
        [Key]
        [Column("configurator_id")]
        public int ConfiguratorId { get; set; }

        [Column("display_order")]
        public int DisplayOrder { get; set; }

        [ForeignKey("ConfiguratorId")]
        public virtual Configurator Configurator { get; set; } = null!;
    }
}
