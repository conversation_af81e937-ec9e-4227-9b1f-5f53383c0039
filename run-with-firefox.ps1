Write-Host "Starting API and Web application with Firefox..."

# Kill any existing dotnet processes
Write-Host "Stopping any existing dotnet processes..."
Get-Process -Name dotnet -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Clean the Web project
Write-Host "Cleaning Web project..."
Push-Location .\Deksmart.Ui\Deksmart.Ui.Web
Remove-Item -Path "bin" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "obj" -Recurse -Force -ErrorAction SilentlyContinue
Pop-Location

# Build the API project
Write-Host "Building API project..."
dotnet build .\Deksmart.Api\Deksmart.Api.csproj

# Build the Web project
Write-Host "Building Web project..."
dotnet build .\Deksmart.Ui\Deksmart.Ui.Web\Deksmart.Ui.Web.csproj

# Create the runtimeconfig.json file
Write-Host "Creating runtimeconfig.json file..."
$runtimeConfigContent = @"
{
  "runtimeOptions": {
    "tfm": "net9.0",
    "framework": {
      "name": "Microsoft.AspNetCore.App",
      "version": "9.0.0"
    },
    "configProperties": {
      "System.GC.Server": true,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false
    }
  }
}
"@

$runtimeConfigPath = ".\Deksmart.Ui\Deksmart.Ui.Web\bin\Debug\net9.0\Deksmart.Ui.Web.runtimeconfig.json"
New-Item -Path $runtimeConfigPath -Force -Value $runtimeConfigContent | Out-Null

# Start the API in the background
Write-Host "Starting API..."
$apiProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--project", ".\Deksmart.Api\Deksmart.Api.csproj", "--urls=https://localhost:7276;http://localhost:5144" -PassThru -NoNewWindow

# Start the Web application in the background
Write-Host "Starting Web application..."
$webProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--project", ".\Deksmart.Ui\Deksmart.Ui.Web\Deksmart.Ui.Web.csproj", "--urls=https://localhost:7209;http://localhost:5213" -PassThru -NoNewWindow

# Wait for the applications to start
Write-Host "Waiting for applications to start..."
Start-Sleep -Seconds 5

# Open Firefox
Write-Host "Opening Firefox..."
Start-Process "firefox" -ArgumentList "https://localhost:7209"

Write-Host "Press any key to stop the applications..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop the processes
Write-Host "Stopping applications..."
Stop-Process -Id $apiProcess.Id -Force -ErrorAction SilentlyContinue
Stop-Process -Id $webProcess.Id -Force -ErrorAction SilentlyContinue

Write-Host "Done!"
