﻿@using Deksmart.Ui.Shared.Layout
@using System.Runtime.InteropServices
@using Deksmart.Ui.Shared.Resources

<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>@UiSharedResource.PageNotFound</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <p role="alert">@UiSharedResource.PageNotFoundMessage</p>
        </LayoutView>
    </NotFound>
</Router>

@code {
    @inject NavigationManager NavigationManager

    protected override void OnAfterRender(bool firstRender)
    {
        base.OnAfterRender(firstRender);
        if (firstRender && !IsWeb())
        {
            NavigationManager.NavigateTo($"/{UiSharedResource.Deksmart}");
        }
    }

    private bool IsWeb()
    {
        return RuntimeInformation.IsOSPlatform(OSPlatform.Create("BROWSER"));
    }
}
