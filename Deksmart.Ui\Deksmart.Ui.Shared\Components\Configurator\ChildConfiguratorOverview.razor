@using Deksmart.Ui.Shared.Resources
@using Deksmart.Ui.Model
@using Deksmart.Ui.Shared.Components.Product
@using Microsoft.AspNetCore.Components
@inherits ComponentBase

<div class="accordion-item child-overview-item">
    <div class="accordion-header child-overview-header">
        <h4 class="accordion-title">@UiSharedResource.ChildConfiguratorOverview</h4>
    </div>
    <div class="accordion-content child-overview-content">
        <div class="excel-table-container">
            <table class="excel-table">
                <thead>
                    <tr>
                        <th>@UiSharedResource.ConfiguratorName</th>
                        <th>@UiSharedResource.Field</th>
                        <th>@UiSharedResource.Value</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var childService in MainConfiguratorGridService.ChildServices)
                    {
                        if (childService.ConfiguratorWrapper != null)
                        {
                            var configuratorName = childService.ConfiguratorWrapper.DisplayTabTitle;
                            bool isFirstRow = true;

                            @foreach (var category in childService.ConfiguratorWrapper.FieldCategories)
                            {
                                var fieldsWithValues = GetFieldsWithValues(category);

                                @foreach (var field in fieldsWithValues)
                                {
                                    var bgClass = GetRowBackgroundClass(childService, MainConfiguratorGridService.ChildServices);

                                    <tr class="@bgClass">
                                        <td data-label="@UiSharedResource.ConfiguratorName">
                                            @if (isFirstRow)
                                            {
                                                @configuratorName
                                                isFirstRow = false;
                                            }
                                        </td>
                                        <td data-label="@UiSharedResource.Field">@((MarkupString)field.Title)</td>
                                        <td data-label="@UiSharedResource.Value">@GetFieldValueDisplay(field)</td>
                                    </tr>
                                }
                            }

                        }
                    }
                </tbody>
            </table>
        </div>

        @* Aggregated Products Section - Use CompositionGrid in summary mode *@
        @if (MainConfiguratorGridService.ConfiguratorWrapper?.ConfiguratorCompositions?.Any() == true)
        {
            <div class="mt-3">
                <CompositionGrid ConfiguratorGridService="MainConfiguratorGridService" IsSummaryView="true" />
            </div>
        }
    </div>
</div>
