.tabbed-configurator {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tabs-header {
    display: flex;
    flex-wrap: wrap;
    border-bottom: 1px solid #e9e9e9;
    padding: 0 10px;
    background-color: white;
    min-height: 42px; /* Ensure consistent height even when empty */
    gap: 2px; /* Small gap between tabs */
}

.tab {
    padding: 10px 15px 10px 20px; /* Increased left padding for the stripe */
    cursor: pointer;
    border: none;
    border-left: 4px solid #ccc; /* Gray left stripe like categories */
    border-bottom: 1px solid #e9e9e9; /* Light bottom border */
    margin-right: 5px;
    margin-bottom: 0; /* Remove bottom margin */
    background-color: #f5f5f5; /* Very light gray background for inactive tabs */
    display: flex;
    align-items: center;
    user-select: none;
    touch-action: none;
    position: relative;
    min-width: 120px;
    max-width: 250px; /* Increased max width */
    transition: all 0.2s ease;
    z-index: 1; /* Ensure tabs stack properly */
    gap: 5px; /* Small gap between elements */
    box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* Subtle shadow like categories */
}

.tab:hover {
    background-color: #f0f0f0; /* Darker gray on hover for inactive tabs */
}

.tab.dragging {
    opacity: 0.5;
    cursor: grabbing;
}


.tab.active {
    background-color: white; /* White background for active tab */
    border-left: 4px solid var(--primary-color, #0d6efd); /* Primary color for active tab */
    font-weight: 500; /* Slightly bolder text */
    color: var(--secondary-color);
    z-index: 2; /* Ensure active tab is on top */
    position: relative;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08); /* Subtle shadow */
    border-bottom: 1px solid white; /* White bottom border to connect with content */
    margin-bottom: -1px; /* Overlap the header border */
}

/* Special styling for active tab in the last row */
.tab.active:last-child,
.tab.active:nth-last-child(-n+2) {
    margin-bottom: -1px;
}

.tab.active:hover {
    background-color: white; /* Keep white background on hover */
}

.tab-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0; /* Required for text-overflow to work in a flex container */
}

.tab-name-input {
    background: transparent;
    border: none;
    outline: none;
    font-size: inherit;
    font-family: inherit;
    color: inherit;
    padding: 0;
    margin: 0;
    width: 100%;
    min-width: 0;
}

.tab-name-input:focus {
    outline: none;
}

.tab.has-duplicates {
    border-right: 2px solid var(--warning-color, #ffc107);
}

.duplicate-badge {
    background-color: var(--warning-color, #ffc107);
    color: var(--warning-text-color, #000);
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 0.75em;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Small precision drop zones for exact positioning */
.precision-drop-zone {
    width: 4px; /* Very thin - just for precision */
    height: 42px; /* Match tab height */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.precision-drop-zone.active {
    width: 6px; /* Slightly wider when active */
}

.drop-indicator {
    width: 2px;
    height: 0;
    background-color: #dc3545;
    border-radius: 1px;
    transition: all 0.2s ease;
    position: relative;
}

.precision-drop-zone.active .drop-indicator {
    height: 42px; /* Full tab height for clear visibility */
    box-shadow: 0 0 6px rgba(220, 53, 69, 0.6);
}

.close-tab {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0 5px;
    font-size: 1.2em;
    flex-shrink: 0;
    margin-left: auto; /* Push to the right */
}

.close-tab:hover {
    color: #000;
}

.add-tab {
    background: none;
    border: none;
    border-left: 4px solid #ccc; /* Gray left stripe like tabs */
    padding: 10px 15px 10px 20px;
    cursor: pointer;
    margin-left: 5px;
    margin-bottom: 0;
    flex-shrink: 0;
    transition: all 0.2s ease;
    min-width: 40px;
    height: 42px; /* Match height with tabs */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #666;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05); /* Subtle shadow like tabs */
}

.add-tab:hover {
    background-color: #f8f8f8;
    border-left-color: #999;
}

/* Style for tabs in the last row to connect with content area */
.tabs-header > .tab:nth-last-child(-n+10):not(:nth-last-child(n+11)),
.tabs-header > .add-tab:last-child {
    margin-bottom: 0;
    position: relative;
    z-index: 1;
}

.tab-content {
    flex: 1;
    padding: 20px 0;
    overflow: auto;
    position: relative;
    z-index: 0;
    border-top: 1px solid #e9e9e9;
    background-color: white;
    box-shadow: inset 0 3px 3px -3px rgba(0,0,0,0.05); /* Subtle shadow */
}

/* Mobile-specific styles */
@media (max-width: 768px) {
    .tabs-header {
        padding: 0 5px;
    }

    .tab {
        padding: 8px 15px 8px 20px; /* Keep left padding for stripe */
        min-width: 100px;
        max-width: 150px;
        margin-bottom: 2px;
    }

    .add-tab {
        padding: 8px 15px 8px 20px; /* Keep left padding for stripe */
        height: 38px; /* Adjust height for mobile */
        margin-bottom: 2px;
    }
}

/* Validation error indicator */
.validation-error-indicator {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    margin-left: 6px;
    color: white;
    background-color: var(--danger-color, #dc3545);
    border-radius: 0.25rem;
    width: 18px;
    height: 18px;
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0; /* Prevent shrinking */
}

/* Touch device optimizations */
@media (hover: none) {
    .tab {
        padding: 12px 20px 12px 24px; /* Larger touch target with left padding for stripe */
    }

    .close-tab {
        padding: 8px; /* Larger touch target for close button */
    }

    .tab-title {
        max-width: 120px; /* Limit title width on touch devices */
    }
}