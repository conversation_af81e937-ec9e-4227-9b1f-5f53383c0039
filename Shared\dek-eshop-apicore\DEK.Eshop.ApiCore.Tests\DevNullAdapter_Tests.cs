using Xunit.Abstractions;
using DEK.Eshop.ApiCore.Cache;
using Microsoft.Extensions.Caching.Distributed;

namespace DEK.Eshop.ApiCore.Tests;

/// <summary>
/// dotnet watch test --logger:"console;verbosity=detailed"
/// </summary>
public class DevNullAdapter_Tests
{
    private readonly ITestOutputHelper _output;

    private readonly CacheManager cacheManager;

    public DevNullAdapter_Tests(ITestOutputHelper output)
    {
        _output = output;

        var cacheAdapter = new DevNullCacheAdapter();
        this.cacheManager = new CacheManager(cacheAdapter);

        //_output.WriteLine("My message.");
    }

    [Fact]
    /// <summary>
    /// </summary>
    public async Task Test1()
    {
        var adapter = new DevNullCacheAdapter();
        await adapter.SetAsync("dddd", new byte[] { 100, 200 }, new DistributedCacheEntryOptions());
        var result = await adapter.GetAsync("dddd");
        Assert.Null(result);
    }

}