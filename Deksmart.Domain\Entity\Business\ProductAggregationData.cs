using Deksmart.Domain.Entity.Db;

namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Represents product aggregation data with complete ordering information for composite configurator processing.
    /// Contains a product and all necessary ordering metadata to determine proper composition sequence.
    /// </summary>
    public class ProductAggregationData
    {
        /// <summary>
        /// The configurator product
        /// </summary>
        public ConfiguratorProduct Product { get; set; } = null!;

        /// <summary>
        /// The composition title from which this product comes
        /// </summary>
        public string CompositionTitle { get; set; } = null!;

        /// <summary>
        /// The ID of the configurator containing this product
        /// </summary>
        public int ConfiguratorId { get; set; }

        /// <summary>
        /// The tab order of the configurator (primary ordering)
        /// </summary>
        public int? TabOrder { get; set; }

        /// <summary>
        /// The order of the composition within the configurator (secondary ordering)
        /// </summary>
        public int CompositionOrder { get; set; }

        /// <summary>
        /// The order of the product within the composition (tertiary ordering)
        /// </summary>
        public int ProductOrder { get; set; }
        
        /// <summary>
        /// Gets the composite order key for sorting by TabOrder.CompositionOrder.ProductOrder
        /// Uses zero-padding to ensure proper lexicographic sorting
        /// </summary>
        public string CompositeOrderKey => $"{TabOrder ?? int.MaxValue:D10}.{CompositionOrder:D10}.{ProductOrder:D10}";
    }
}