﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Main domain entity representing a dynamic configurator definition.
    /// Serves as the top-level container for building and managing dynamic configuration UIs.
    /// Central to the application's configuration logic, holding metadata, field categories, compositions, and child configurators.
    /// </summary>
    [Table("configurator", Schema = "dbo")]
    public partial class Configurator : IIntIdEntity
    {
        /// <summary>
        /// KALK_ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// KALK_NAZEV
        /// </summary>
        [Column("title")]
        [StringLength(200)]
        public string Title { get; set; } = null!;

        /// <summary>
        /// INFO_TEXT
        /// </summary>
        [Column("description", TypeName = "character varying")]
        public string? Description { get; set; }

        [Column("end_description", TypeName = "character varying")]
        public string? EndDescription { get; set; }

        /// <summary>
        /// Indicates that this configurator is a composite (can contain multiple sub-configurators).
        /// </summary>
        [Column("is_composite")]
        public bool IsComposite { get; set; }

        /// <summary>
        /// Controls whether this configurator is visible in the main menu. Set to false to hide configurators that are only used as children in composites or for any other reason.
        /// </summary>
        [Column("show_in_menu")]
        public bool ShowInMenu { get; set; } = true;

        [Column("meta_description", TypeName = "character varying")]
        [StringLength(140)]
        public string? MetaDescription { get; set; }

        public virtual ICollection<ConfiguratorComposition> ConfiguratorCompositions { get; set; } = new List<ConfiguratorComposition>();

        public virtual ICollection<ConfiguratorFieldCategory> ConfiguratorFieldCategories { get; set; } = new List<ConfiguratorFieldCategory>();

        public virtual ICollection<ChildConfigurator> ChildConfigurators { get; set; } = new List<ChildConfigurator>();

        //Business logic
        [NotMapped]
        public decimal TotalPriceNoVat { get; set; }
        [NotMapped]
        public decimal TotalPriceVat { get; set; }
        [NotMapped]
        public decimal TotalVat { get; set; }
        [NotMapped]
        public List<Configurator> ChildUserConfigurators { get; set; } = new();
        [NotMapped]
        public string? TabTitle { get; set; }
        [NotMapped]
        public int? TabOrder { get; set; }
        [NotMapped]
        public bool IsValid { get; set; }
        [NotMapped]
        public bool HasProducts { get; set; }
        /// <summary>
        /// Indicates whether products can be added to shopping cart.
        /// False when any product has validation errors (pricing failures, missing data, etc.).
        /// Separate from IsValid which controls other form functionality.
        /// </summary>
        [NotMapped]
        public bool CanAddToCart { get; set; } = true;
    }
}
