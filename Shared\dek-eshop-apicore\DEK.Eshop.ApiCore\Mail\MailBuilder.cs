using MimeKit;

namespace DEK.Eshop.ApiCore.Mail;

public class MailBuilder
{
    private MimeMessage _message = new MimeMessage();

    private BodyBuilder _body = new BodyBuilder();

    public MailBuilder AddFrom(string mail, string? name = null)
    {
        _message.From.Add(new MailboxAddress(name, mail));
        return this;
    }

    public MailBuilder AddTo(string mail, string? name = null)
    {
        _message.To.Add(new MailboxAddress(name, mail));
        return this;
    }

    public MailBuilder SetSubject(string subject)
    {
        _message.Subject = subject;
        return this;
    }

    public MailBuilder SetTextBody(string body)
    {
        _body.TextBody = body;
        return this;
    }

    public MailBuilder SetHtmlBody(string body)
    {
        _body.HtmlBody = body;
        return this;
    }

    public MailBuilder AddAttachment(string fileName, Stream stream)
    {
        _body.Attachments.Add(fileName, stream);
        return this;
    }

    public MimeMessage Build()
    {
        _message.Body = _body.ToMessageBody();
        return _message;
    }
}

