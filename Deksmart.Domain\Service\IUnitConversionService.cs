using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;

namespace Deksmart.Domain.Service
{
    /// <summary>
    /// Service responsible for unit conversion operations including normalization, 
    /// compatibility checks, and conversion between different unit types.
    /// </summary>
    public interface IUnitConversionService
    {
        /// <summary>
        /// Normalizes a unit string for comparison by removing spaces, special characters, and converting to lowercase
        /// </summary>
        /// <param name="unit">The unit string to normalize</param>
        /// <returns>Normalized unit string</returns>
        string NormalizeUnitString(string? unit);

        /// <summary>
        /// Checks if two normalized unit strings are meter unit variations (compatible)
        /// </summary>
        /// <param name="normalizedUnit1">First normalized unit string</param>
        /// <param name="normalizedUnit2">Second normalized unit string</param>
        /// <returns>True if both units are compatible meter variations</returns>
        bool IsMeterUnitVariation(string normalizedUnit1, string normalizedUnit2);

        /// <summary>
        /// Determines the unit type (sales or package) based on product unit and e-shop unit data
        /// </summary>
        /// <param name="productUnit">The product unit string</param>
        /// <param name="eshopUnit">The e-shop unit information</param>
        /// <returns>Tuple indicating if using sales unit and if using package unit</returns>
        (bool isUsingSalesUnit, bool isUsingPackageUnit) DetermineUnitType(string productUnit, EshopProductUnit eshopUnit);

        /// <summary>
        /// Calculates package quantity from amount based on unit type
        /// </summary>
        /// <param name="amount">The amount to convert</param>
        /// <param name="isUsingSalesUnit">Whether the amount is in sales units</param>
        /// <param name="unitsInPackage">Number of units in a package</param>
        /// <returns>Package quantity (rounded up for sales units)</returns>
        decimal CalculatePackageQuantity(decimal amount, bool isUsingSalesUnit, decimal unitsInPackage);

        /// <summary>
        /// Validates unit compatibility and returns validation result
        /// </summary>
        /// <param name="productCode">Product code for error messages</param>
        /// <param name="productUnit">Product unit string</param>
        /// <param name="eshopUnit">E-shop unit information</param>
        /// <returns>Validation result with any errors</returns>
        ValidationResult ValidateUnitCompatibility(string productCode, string productUnit, EshopProductUnit eshopUnit);
    }
}
