﻿using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Service;
using Deksmart.Shared.Dto;
using StackExchange.Profiling.Internal;
using Deksmart.Application.Mapping;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Domain.Enum;
using Deksmart.Shared.Enum;

namespace Deksmart.Application.Service
{
    public interface IConfiguratorPresetService
    {
        /// <summary>
        /// Saves a new preset for the specified configurator, including its field values, selected products, category states, and any child configurator states.
        /// </summary>
        /// <param name="id">The ID of the configurator for which to save the preset.</param>
        /// <param name="configuratorState">The state of the configurator to be saved as a preset.</param>
        /// <returns>
        /// A tuple containing the GUID of the created preset (or null if validation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(Guid? guid, ValidationResult validation)> SavePresetAsync(int id, ConfiguratorStateDto configuratorState);

        /// <summary>
        /// Retrieves the preset DTO associated with the specified catalog ID.
        /// </summary>
        /// <param name="catalogId">The catalog ID to search for.</param>
        /// <returns>
        /// The <see cref="PresetDto"/> if found; otherwise, null.
        /// </returns>
        Task<PresetDto?> GetPresetIdByCatalogId(string catalogId);

        /// <summary>
        /// Retrieves the full configurator state for the specified preset, including field values, selected products, category states, and any child configurator states.
        /// </summary>
        /// <param name="presetId">The GUID of the preset.</param>
        /// <returns>
        /// The <see cref="ConfiguratorStateDto"/> representing the preset's state, or null if not found.
        /// </returns>
        Task<ConfiguratorStateDto?> GetConfiguratorStateAsync(Guid presetId);
    }

    public class ConfiguratorPresetService : IConfiguratorPresetService
    {
        private readonly IConfiguratorPresetRepository _presetDao;
        private readonly IPresetConfiguratorCategoryStateRepository _categoryStateDao;
        private readonly IConfiguratorMappingService _mappingService;
        private readonly IConfiguratorValidationService _validationService;

        public ConfiguratorPresetService(
            IConfiguratorPresetRepository presetDao,
            IPresetConfiguratorCategoryStateRepository categoryStateDao,
            IConfiguratorMappingService mappingService,
            IConfiguratorValidationService validationService)
        {
            _presetDao = presetDao;
            _categoryStateDao = categoryStateDao;
            _mappingService = mappingService;
            _validationService = validationService;
        }

        public async Task<(Guid? guid, ValidationResult validation)> SavePresetAsync(int id, ConfiguratorStateDto configuratorState)
        {
            var validation = new ValidationResult();

            // Validate configurator exists
            await _validationService.ValidateConfiguratorExistsAsync(id, validation);
            if (validation.HasErrors)
            {
                return (null, validation);
            }

            var newGuid = Guid.NewGuid();

            var preset = new ConfiguratorPreset
            {
                Id = newGuid,
                ConfiguratorId = id,
                ConfiguratorFieldCombinations = configuratorState.FieldValues
                    .Where(d => d.Value.HasValue)
                    .Select(fv => new ConfiguratorFieldCombination
                    {
                        FieldId = fv.FieldId,
                        FieldValue = fv.Value!.Value
                    })
                    .ToList(),
                ConfiguratorProductCombinations = configuratorState.SelectedProducts
                    .Select(pid => new ConfiguratorProductCombination
                    {
                        ProductId = pid.ProductId,
                        Amount = pid.Amount ?? 0
                    })
                    .ToList()
            };

            // Add category states directly to the preset
            if (configuratorState.CategoryStates?.Count > 0)
            {
                preset.CategoryStates = configuratorState.CategoryStates
                    .Select(cs => new PresetConfiguratorCategoryState
                    {
                        PresetId = preset.Id,
                        CategoryId = cs.CategoryId,
                        CollapseState = (CategoryCollapseState)cs.CollapseState
                    })
                    .ToList();
            }

            // Save the preset with all its collections
            await _presetDao.InsertAsync(preset);

            // Save child presets if they exist
            if (configuratorState.ChildConfiguratorStates?.Count > 0)
            {
                var childPresets = new List<ChildPreset>();
                foreach (var childState in configuratorState.ChildConfiguratorStates)
                {
                    var (childGuid, childValidation) = await SavePresetAsync(childState.ConfiguratorId, childState);
                    if (childGuid == null)
                    {
                        validation.AddError(childValidation.GetErrors());
                        return (null, validation);
                    }

                    childPresets.Add(new ChildPreset
                    {
                        CompositeId = preset.Id,
                        PresetId = childGuid.Value,
                        Order = childState.TabOrder ?? 1,
                        TabTitle = childState.TabTitle
                    });
                }

                preset.ChildPresets = childPresets;
                await _presetDao.UpdateAsync(preset);
            }

            return (newGuid, validation);
        }

        public async Task<PresetDto?> GetPresetIdByCatalogId(string catalogId)
        {
            if(catalogId.IsNullOrWhiteSpace())
                return null;

            var preset = await _presetDao.GetPresetIdByCatalogId(catalogId);

            if (preset == null)
                return null;

            return new PresetDto { ConfiguratorId = preset.ConfiguratorId, PresetId = preset.Id.ToString()};
        }

        public async Task<ConfiguratorStateDto?> GetConfiguratorStateAsync(Guid presetId)
        {
            var preset = await _presetDao.GetFieldPresetByIdAsync(presetId);
            if (preset == null)
                return null;

            var fieldValues = await GetFieldsForPresetAsync(presetId);
            var selectedProducts = await GetSelectedProducts(presetId);
            var categoryStates = await GetCategoryStatesAsync(presetId);

            var state = new ConfiguratorStateDto
            {
                ConfiguratorId = preset.ConfiguratorId,
                FieldValues = fieldValues,
                SelectedProducts = selectedProducts,
                CategoryStates = categoryStates
            };

            // Handle child presets if they exist
            if (preset.ChildPresets?.Count > 0)
            {
                state.ChildConfiguratorStates = [];
                foreach (var childPreset in preset.ChildPresets)
                {
                    var childFieldValues = await GetFieldsForPresetAsync(childPreset.PresetId);
                    var childSelectedProducts = await GetSelectedProducts(childPreset.PresetId);
                    var childCategoryStates = await GetCategoryStatesAsync(childPreset.PresetId);

                    var childState = new ChildConfiguratorStateDto(
                        childPreset.Preset.ConfiguratorId,
                        childFieldValues,
                        childSelectedProducts,
                        childPreset.Order,
                        childPreset.TabTitle
                    );

                    // Add category states for child configurator
                    childState.CategoryStates = childCategoryStates;

                    state.ChildConfiguratorStates.Add(childState);
                }
            }

            return state;
        }

        private async Task<List<CategoryStateDto>> GetCategoryStatesAsync(Guid presetId)
        {
            var categoryStates = await _categoryStateDao.GetCategoryStatesForPresetAsync(presetId);

            if (categoryStates == null || categoryStates.Count == 0)
                return [];

            return [.. categoryStates.Select(cs => new CategoryStateDto
            {
                CategoryId = cs.CategoryId,
                CollapseState = (CategoryCollapseStateDto)cs.CollapseState
            })];
        }

        private async Task<List<ClientProductValueDto>> GetSelectedProducts(Guid presetId)
        {
            var presetProducts = await _presetDao.GetProductPresetByIdAsync(presetId);

            if (presetProducts.Count == 0)
                return [];

            return [.. presetProducts.Select(p =>
                    new ClientProductValueDto { ProductId = p.ProductId, Amount = p.Amount })];
        }

        private async Task<List<ClientFieldValueDto>> GetFieldsForPresetAsync(Guid presetId)
        {
            var preset = await _presetDao.GetFieldPresetByIdAsync(presetId);

            if(preset == null)
                return [];

            return [.. preset.ConfiguratorFieldCombinations.Select(_mappingService.MapToDto)];
        }
    }
}