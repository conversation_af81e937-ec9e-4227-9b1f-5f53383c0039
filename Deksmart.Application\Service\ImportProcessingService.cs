using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Db.Interface;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Application.Service
{
    public interface IImportProcessingService
    {
        // Transaction management
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        
        // Common operations
        Task<Configurator?> GetConfiguratorAsync(int id);
        Task SaveConfiguratorAsync(Configurator configurator, bool isUpdate);
        
        // Type conversion cleanup
        void CleanupNormalConfiguratorData(Configurator configurator);
        void CleanupCompositeConfiguratorData(Configurator configurator);
        
        // Validation helper
        Task<bool> HandleValidationFailureAsync(ValidationResult result, ValidationResult main, bool forceFail = false);
        
        // Soft deletion helper
        void SetDeleteOnNotPresentItems(Configurator configurator, PresentIds presentIds);
    }

    public class ImportProcessingService : IImportProcessingService
    {
        private readonly IConfiguratorRepository _configuratorDao;
        private readonly IChildConfiguratorRepository _childConfiguratorDao;
        private readonly IConfiguratorFieldRepository _configuratorFieldDao;
        private readonly IConfiguratorCompositionRepository _configuratorCompositionDao;
        private readonly IConfiguratorProductRepository _configuratorItemDao;
        private readonly IConfiguratorFieldCategoryRepository _configuratorFieldCategoryDao;
        private readonly IConfiguratorFieldValueRepository _configuratorFieldValueDao;

        public ImportProcessingService(
            IConfiguratorRepository configuratorDao,
            IChildConfiguratorRepository childConfiguratorDao,
            IConfiguratorFieldRepository configuratorFieldDao,
            IConfiguratorCompositionRepository configuratorCompositionDao,
            IConfiguratorProductRepository configuratorItemDao,
            IConfiguratorFieldCategoryRepository configuratorFieldCategoryDao,
            IConfiguratorFieldValueRepository configuratorFieldValueDao)
        {
            _configuratorDao = configuratorDao;
            _childConfiguratorDao = childConfiguratorDao;
            _configuratorFieldDao = configuratorFieldDao;
            _configuratorCompositionDao = configuratorCompositionDao;
            _configuratorItemDao = configuratorItemDao;
            _configuratorFieldCategoryDao = configuratorFieldCategoryDao;
            _configuratorFieldValueDao = configuratorFieldValueDao;
        }

        public async Task BeginTransactionAsync()
        {
            await _configuratorDao.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            await _configuratorDao.CommitTransactionAsync();
        }

        public async Task RollbackTransactionAsync()
        {
            await _configuratorDao.RollbackTransactionAsync();
        }

        public async Task<Configurator?> GetConfiguratorAsync(int id)
        {
            return await _configuratorDao.GetCompleteConfiguratorAsync(id);
        }

        public async Task SaveConfiguratorAsync(Configurator configurator, bool isUpdate)
        {
            await _configuratorDao.SaveConfiguratorAsync(configurator, isUpdate);
        }

        public async Task<bool> HandleValidationFailureAsync(ValidationResult result, ValidationResult main, bool forceFail = false)
        {
            if (result.HasErrors || forceFail)
            {
                main.AddError(result.GetErrors());
                await RollbackTransactionAsync();
                return true;
            }
            return false;
        }

        public void CleanupNormalConfiguratorData(Configurator configurator)
        {
            // Create empty PresentIds to mark all normal configurator data for deletion
            var presentIds = new PresentIds();
            
            var compositions = configurator.ConfiguratorCompositions;
            SoftDeleteEntities(compositions, presentIds.CompositionIds, _configuratorCompositionDao);

            var items = compositions.SelectMany(c => c.ConfiguratorProducts);
            SoftDeleteEntities(items, presentIds.ProductIds, _configuratorItemDao);

            var fieldCategories = configurator.ConfiguratorFieldCategories;
            SoftDeleteEntities(fieldCategories, presentIds.CategoryIds, _configuratorFieldCategoryDao);

            var fields = fieldCategories.SelectMany(c => c.ConfiguratorFields);
            SoftDeleteEntities(fields, presentIds.FieldIds, _configuratorFieldDao);

            var fieldValues = fields.SelectMany(f => f.ConfiguratorFieldValues);
            SoftDeleteEntities(fieldValues, presentIds.ValuesIds, _configuratorFieldValueDao);
        }

        public void CleanupCompositeConfiguratorData(Configurator configurator)
        {
            // Cleanup child configurator relationships when converting to normal
            // ChildConfigurator doesn't implement IDeletableEntity, so handle it directly
            foreach (var childConfigurator in configurator.ChildConfigurators.ToList())
            {
                _childConfiguratorDao.Remove(childConfigurator);
            }
            
            // Clear the collection to prevent EF from trying to maintain relationships
            configurator.ChildConfigurators.Clear();
        }

        public void SetDeleteOnNotPresentItems(Configurator configurator, PresentIds presentIds)
        {
            var compositions = configurator.ConfiguratorCompositions;
            SoftDeleteEntities(compositions, presentIds.CompositionIds, _configuratorCompositionDao);

            var items = compositions.SelectMany(c => c.ConfiguratorProducts);
            SoftDeleteEntities(items, presentIds.ProductIds, _configuratorItemDao);

            var fieldCategories = configurator.ConfiguratorFieldCategories;
            SoftDeleteEntities(fieldCategories, presentIds.CategoryIds, _configuratorFieldCategoryDao);

            var fields = fieldCategories.SelectMany(c => c.ConfiguratorFields);
            SoftDeleteEntities(fields, presentIds.FieldIds, _configuratorFieldDao);

            var fieldValues = fields.SelectMany(f => f.ConfiguratorFieldValues);
            SoftDeleteEntities(fieldValues, presentIds.ValuesIds, _configuratorFieldValueDao);
        }

        private static void SoftDeleteEntities<TEntity>(IEnumerable<TEntity> entities, HashSet<int> presentIds, IIntIdRepositoryBase<TEntity> dao)
            where TEntity : class, IDeletableEntity
        {
            var entitiesToDelete = entities.DistinctBy(d => d.Id)
                .Where(e => e.Id > 0 && !presentIds.Contains(e.Id))
                .ToList();

            foreach (var entity in entitiesToDelete)
            {
                entity.IsDeleted = true;
                dao.SetUpdated(entity);
            }
        }
    }
}