using Deksmart.Ui.Model;
using Deksmart.Ui.Shared.Resources;
using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components;

namespace Deksmart.Ui.Shared.Components.Configurator
{
    public partial class ChildConfiguratorOverview : ComponentBase
    {
        [Parameter]
        public IConfiguratorGridService MainConfiguratorGridService { get; set; } = null!;

        /// <summary>
        /// Gets fields that have values set from a category, ordered by Order
        /// When there are distinct fields with the same Ident, only the first one is used
        /// </summary>
        /// <param name="category">The category to get fields from</param>
        /// <returns>List of fields with values, ordered by Order</returns>
        private static List<ConfiguratorFieldWrapper> GetFieldsWithValues(ConfiguratorFieldCategoryWrapper category)
        {
            // Get all fields from the category
            var allFields = category.AllFields;

            // Filter fields that have values, order by Order, and take only the first field for each Ident
            var result = allFields
                .Where(HasValue)
                .OrderBy(field => field.Order)
                .GroupBy(field => field.Ident)
                .Select(group => group.First())
                .ToList();

            return result;
        }

        /// <summary>
        /// Determines if a field has a value set
        /// Excludes ExpressionFieldWrapper and TextFieldWrapper as they should not be shown in the overview
        /// </summary>
        /// <param name="field">The field to check</param>
        /// <returns>True if the field has a value, false otherwise</returns>
        private static bool HasValue(ConfiguratorFieldWrapper field)
        {
            return field switch
            {
                SliderFieldWrapper sliderField => sliderField.Value != 0,
                NumericFieldWrapper numericField => numericField.Value != 0,
                CheckboxFieldWrapper checkboxField => checkboxField.Value,
                IdFieldWrapper idField => idField.Value.HasValue && idField.SelectedWrapper != null,
                _ => false
            };
        }

        /// <summary>
        /// Gets a display markup for a field's value
        /// </summary>
        /// <param name="field">The field to get the value for</param>
        /// <returns>A MarkupString representation of the field's value</returns>
        private static MarkupString GetFieldValueDisplay(ConfiguratorFieldWrapper field)
        {
            string valueHtml = field switch
            {
                SliderFieldWrapper sliderField => FormatNumericWithSuffix(sliderField.Value, sliderField.Suffix),
                NumericFieldWrapper numericField => FormatNumericWithSuffix(numericField.Value, numericField.Suffix),
                CheckboxFieldWrapper checkboxField => checkboxField.Value ? UiSharedResource.Yes : UiSharedResource.No,
                IdFieldWrapper idField => idField.SelectedWrapper?.Value?.Title ?? string.Empty,
                _ => string.Empty
            };

            return new MarkupString(valueHtml);
        }

        /// <summary>
        /// Formats a numeric value with its suffix, ensuring the suffix is properly displayed as HTML
        /// </summary>
        /// <param name="value">The numeric value</param>
        /// <param name="suffix">The suffix, which may contain HTML</param>
        /// <returns>A formatted string with the value and suffix</returns>
        private static string FormatNumericWithSuffix(decimal value, string? suffix)
        {
            if (string.IsNullOrEmpty(suffix))
            {
                return value.ToString();
            }

            // Check if the suffix contains paragraph tags that might cause line breaks
            string processedSuffix = suffix;
            if (suffix.Contains("<p>") || suffix.Contains("</p>"))
            {
                // Remove paragraph tags that might cause line breaks
                processedSuffix = suffix.Replace("<p>", "<span>").Replace("</p>", "</span>");
            }

            // Wrap the entire content in a span to ensure it stays on one line
            return $"<span class=\"numeric-with-suffix\">{value} {processedSuffix}</span>";
        }

        /// <summary>
        /// Gets the CSS class for alternating row backgrounds
        /// </summary>
        /// <param name="service">The current service</param>
        /// <param name="allServices">All child services</param>
        /// <returns>CSS class for the row background</returns>
        private static string GetRowBackgroundClass(IConfiguratorGridService service, List<IConfiguratorGridService> allServices)
        {
            int index = allServices.IndexOf(service);
            return index % 2 == 0 ? "child-overview-white" : "child-overview-lightgray";
        }
    }
}
