@using Deksmart.Ui.Model
@using Deksmart.Ui.Shared.Resources

@if (ConfiguratorGridService.ConfiguratorWrapper != null)
{
    @if (ShowPageTitle)
    {
        <PageTitle>@ConfiguratorGridService.ConfiguratorWrapper.Title</PageTitle>
    }

    @if (!string.IsNullOrEmpty(ConfiguratorGridService.ConfiguratorWrapper.Description))
    {
        <div class="card mb-3">
            <div class="card-body">
                @((MarkupString)ConfiguratorGridService.ConfiguratorWrapper.Description)
            </div>
        </div>
    }
}
