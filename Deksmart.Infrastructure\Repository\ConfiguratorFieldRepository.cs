using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorFieldRepository : IntIdDaoBase<ConfiguratorField>, IConfiguratorFieldRepository
    {
        public ConfiguratorFieldRepository(ConfiguratorContext context) : base(context)
        {
        }

        /// <inheritdoc/>
        public async Task<List<DirectValue>> GetFieldDirectValuesForConfiguratorAsync(int configuratorId)
        {
            return await _context.ConfiguratorFieldCategories
                .AsNoTracking()
                .Where(cat => cat.ConfiguratorId == configuratorId)
                .SelectMany(cat => cat.ConfiguratorFields)
                .Where(cf => !cf.ConfiguratorFieldValues.Any() && cf.Ident != null)
                .Select(cf => new DirectValue
                {
                    Id = cf.Id,
                    Ident = cf.Ident,
                    Expression = cf.Expression,
                    DefaultValue = cf.DefaultValue,
                    Max = cf.MaxValue,
                    Min = cf.MinValue
                })
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<List<MultipleChoiceValue>> GetFieldMultipleChoiceValuesIdForConfiguratorAsync(int configuratorId)
        {
            // TODO: filter component types if needed
            return await _context.ConfiguratorFieldCategories
                .AsNoTracking()
                .Where(cat => cat.ConfiguratorId == configuratorId)
                .SelectMany(cat => cat.ConfiguratorFields)
                .Include(cf => cf.ConfiguratorFieldValues)
                .Where(cf => cf.ConfiguratorFieldValues.Any())
                .Select(cf => new MultipleChoiceValue
                {
                    Id = cf.Id,
                    DefaultValue = cf.DefaultValue,
                    Ident = cf.Ident,
                    Values = cf.ConfiguratorFieldValues.Select(v => v.NumericValue).ToList()
                })
                .ToListAsync();
        }
    }
}
