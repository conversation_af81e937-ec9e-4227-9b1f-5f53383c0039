.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--header-bg);
    color: white;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
}

.page-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease-in-out;
    margin: var(--spacing-xs);
    background-color: white;
    color: var(--primary-color);
    border-color: white;
}

.button:hover {
    background-color: var(--light-color);
    color: var(--primary-dark);
    border-color: var(--light-color);
}

.button:active {
    transform: translateY(1px);
}

.button:disabled {
    background-color: var(--disabled-bg);
    border-color: var(--disabled-bg);
    color: var(--disabled-text);
    cursor: not-allowed;
    opacity: 0.8;
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--card-bg);
    background-clip: border-box;
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--light-border);
    font-weight: 500;
}

.card-body {
    flex: 1 1 auto;
    padding: 1rem;
}

.card-footer {
    padding: 0.75rem 1rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--light-border);
}

/* Touch-friendly button sizes for mobile */
@media (max-width: 768px) {
    .button {
        padding: 0.75rem 1rem;
        min-height: 44px; /* Minimum touch target size */
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
    }

    .page-header h3 {
        font-size: 1.25rem;
    }
}

/* Platform-specific optimizations */
@media (hover: none) {
    .button:hover {
        background-color: white;
        color: var(--primary-color);
        transform: none;
    }
}

.mb-3 {
    margin-bottom: 1rem;
}

/* Button group */
.button-group {
    display: flex;
    gap: 0.5rem;
}