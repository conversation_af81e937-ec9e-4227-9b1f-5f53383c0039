namespace DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;

using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

[Keyless]
public class ProductEshopPrice
{
    [Column("CENA_SLEVAsDPH")]
    public decimal PriceVatSales { get; set; }

    [Column("CENA_SLEVA")]
    public decimal PriceNoVatSales { get; set; }

    [Column("CENAsDPH")]
    public decimal PriceVatSalesOrigin { get; set; }

    [Column("CENAbezDPH")]
    public decimal PriceNoVatSalesOrigin { get; set; }

    [Column("CENA_BALENI_SLEVAsDPH")]
    public decimal PriceVatPackage { get; set; }

    [Column("CENA_BALENI_SLEVA")]
    public decimal PriceNoVatPackage { get; set; }

    [Column("CENA_BALENI_sDPH")]
    public decimal PriceVatPackageOrigin { get; set; }

    [Column("CENA_BALENI")]
    public decimal PriceNoVatPackageOrigin { get; set; }

    [Column("ZOBR_NEJNIZSI_CENA_30DNI")]
    public bool IsVisibleDiscountHistory { get; set; }

    [Column("NEJNIZSI_CENA_30DNI_sDPH")]
    public decimal PriceVatLowestHistory { get; set; }

    [Column("NEJNIZSI_CENA_30DNI")]
    public decimal PriceNoVatLowestHistory { get; set; }

    [Column("REC_POPL_bezDPH")]
    public bool HasTax { get; set; }

    [Column("DAN")]
    public decimal Vat { get; set; }

    [Column("SLEVA_DET")]
    public int DiscountPercent { get; set; }
}
