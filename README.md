# Deksmart Aplikace - Návod ke spuštění

Tento dokument obsahuje podrobný návod, jak nastavit a spustit Deksmart aplikaci ve vývojovém prostředí.




### Nastavení startup projektů

1. Klikněte pravým tlačítkem na solution v Solution Exploreru
2. Vyberte možnost "Configure Startup Projects..."
3. Zvolte "Multiple startup projects"
4. Nastavte následující projekty na "Start" s HTTPS:
   - Deksmart.Api
   - Deksmart.Ui.Web

   ![Konfigurace startup projektů](startup_projects_config.jpg)

## Spuštění pomocí Docker Compose

Při změně v kódu je potřeba smazat image a poté znovu spustit Docker Compose.
```bash
docker compose up -d
```
```bash
docker compose down
```
Web je dostupný na http://localhost:5000

## Přístup k aplikaci

Po úspěšném spuštění můžete přistupovat k aplikaci na následujících URL:

- **API dokumentace (Swagger)**: https://localhost:7276/api/doc
- **Webová aplikace**: https://localhost:7209/deksmart

### Redis (Cache)

- S `docker compose up -d` se Redis nastaví sám. Viz docker-compose.yml.
- Při spuštění přes Visual Studio je Redis v `Deksmart.Api\appsettings.json`: přednastaven na `localhost:6379` a je potřeba si v `Deksmart.Api\appsettings.Development.json` nastavit `ReplaceRedisWithDevNullAdapter` na `false`(vypne dev null adapter). A pustit Redis lokálně.

```json
"ApiCore" : {
    "Cache": {
        "Redis": {
            "ReplaceRedisWithDevNullAdapter": false
        }
    }
}
```
```
docker run -it -d --rm -p 6379:6379 --name redis_local docker-registry.dek.cz/redis:6.2
```


