using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Model
{
    public class ConfiguratorCompositionWrapper
    {
        public ConfiguratorCompositionDto ConfiguratorCompositions { get; }
        public ConfiguratorCompositionWrapper(ConfiguratorCompositionDto configuratorCompositions)
        {
            ConfiguratorCompositions = configuratorCompositions;
        }

        public int Id => ConfiguratorCompositions.Id;
        public string Title => ConfiguratorCompositions.Title;
        public bool IsMultipleProducts => ConfiguratorCompositions.IsMultipleProducts;
        public int Order => ConfiguratorCompositions.Order;
        public List<ConfiguratorProductWrapper> SelectedProducts { get; set; } = [];
        public List<ConfiguratorProductWrapper> Products { get; set; } = [];
        public bool IsLoading { get; set; }
    }
}
