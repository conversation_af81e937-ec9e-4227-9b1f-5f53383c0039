namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a reference to a configurator that is included as a child in a composite (parent) configurator.
    /// Used to define, transfer, and render the structure and order of nested configurators in composite configuration workflows.
    /// Enables dynamic composition of configurators, allowing the backend to specify which configurators are available as children and the UI to present and select among them.
    /// </summary>
    public class ChildConfiguratorDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public int Order { get; set; }
    }
}