using Deksmart.Application.Mapping;
using Deksmart.Application.Service;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Service;
using Deksmart.Infrastructure.Cache;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Shared.Dto;
using Moq;

namespace Deksmart.Application.Tests.Service
{
    public class ConfiguratorServiceTest
    {
        private readonly Mock<IConfiguratorProcessingService> _processingServiceMock;
        private readonly Mock<IConfiguratorRepository> _configuratorDaoMock;
        private readonly Mock<IConfiguratorCacheManager> _cacheManagerMock;
        private readonly Mock<IConfiguratorMappingService> _mappingServiceMock;
        private readonly Mock<IConfiguratorValidationService> _validationServiceMock;
        private readonly Mock<IChildConfiguratorRepository> _childConfiguratorDaoMock;
        private readonly Mock<IProductProcessingService> _productProcessingServiceMock;
        private readonly ConfiguratorService _service;

        public ConfiguratorServiceTest()
        {
            _processingServiceMock = new Mock<IConfiguratorProcessingService>();
            _configuratorDaoMock = new Mock<IConfiguratorRepository>();
            _cacheManagerMock = new Mock<IConfiguratorCacheManager>();
            _mappingServiceMock = new Mock<IConfiguratorMappingService>();
            _validationServiceMock = new Mock<IConfiguratorValidationService>();
            _childConfiguratorDaoMock = new Mock<IChildConfiguratorRepository>();
            _productProcessingServiceMock = new Mock<IProductProcessingService>();

            _service = new ConfiguratorService(
                _processingServiceMock.Object,
                _configuratorDaoMock.Object,
                _cacheManagerMock.Object,
                _mappingServiceMock.Object,
                _validationServiceMock.Object,
                _childConfiguratorDaoMock.Object,
                _productProcessingServiceMock.Object);
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_ShouldReturnError_WhenConfiguratorDoesNotExist()
        {
            // Arrange
            var configuratorId = 1;
            var configuratorState = new ConfiguratorStateDto();
            var validation = new ValidationResult();
            validation.AddError("Configurator does not exist");

            var existsValidation = new ValidationResult();
            existsValidation.AddError("Configurator does not exist");
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Callback<int, ValidationResult>((id, val) => val.AddError("Configurator does not exist"))
                .Returns(Task.CompletedTask);

            // Act
            var (configurator, error) = await _service.GetFilteredConfiguratorAsync(configuratorId, configuratorState);

            // Assert
            Assert.Null(configurator);
            Assert.True(error.HasErrors);
            Assert.Contains("Configurator does not exist", error.GetErrors());
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_ShouldReturnConfigurator_WhenValid()
        {
            // Arrange
            var configuratorId = 1;
            var configuratorState = new ConfiguratorStateDto();
            var configurator = new Configurator {
                Id = configuratorId,
                ConfiguratorCompositions = new List<ConfiguratorComposition> {
                    new ConfiguratorComposition {
                        ConfiguratorProducts = new List<ConfiguratorProduct> {
                            new ConfiguratorProduct { ProductCode = "TEST", IsSelected = true }
                        }
                    }
                }
            };
            var configuratorDto = new ConfiguratorDto { Id = configuratorId };

            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);
            
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(It.IsAny<int>(), It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(configurator);
            
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                configuratorId,
                configuratorState.FieldValues,
                configuratorState.SelectedProducts,
                configuratorState.CategoryStates))
                .ReturnsAsync((configurator, new ValidationResult()));
            
            _mappingServiceMock.Setup(x => x.MapToDto(configurator))
                .Returns(configuratorDto);

            // Act
            var (result, error) = await _service.GetFilteredConfiguratorAsync(configuratorId, configuratorState);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(configuratorId, result.Id);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_ShouldHandleChildConfigurators_WhenPresent()
        {
            // Arrange
            var configuratorId = 1;
            var childConfiguratorId = 2;
            var configuratorState = new ConfiguratorStateDto
            {
                ChildConfiguratorStates = new List<ChildConfiguratorStateDto>
                {
                    new ChildConfiguratorStateDto { ConfiguratorId = childConfiguratorId }
                }
            };

            var parentConfigurator = new Configurator {
                Id = configuratorId,
                ChildUserConfigurators = new List<Configurator>()
            };

            var childConfigurator = new Configurator {
                Id = childConfiguratorId,
                IsValid = true
            };

            var parentConfiguratorDto = new ConfiguratorDto { Id = configuratorId };

            // Setup validation for parent configurator
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Setup cache manager for parent configurator
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(parentConfigurator);

            // Setup processing service for child configurator
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                childConfiguratorId,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((childConfigurator, new ValidationResult()));

            // Setup mapping service
            _mappingServiceMock.Setup(x => x.MapToDto(parentConfigurator))
                .Returns(parentConfiguratorDto);

            // Act
            var (result, error) = await _service.GetFilteredConfiguratorAsync(configuratorId, configuratorState);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(configuratorId, result.Id);
            Assert.False(error.HasErrors);

            // Verify that child configurator was processed
            _processingServiceMock.Verify(x => x.ProcessConfiguratorAsync(
                childConfiguratorId,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()), Times.Once);
        }

        [Fact]
        public async Task GetAllConfiguratorsAsync_ShouldReturnAllConfigurators()
        {
            // Arrange
            var configurators = new List<Configurator>
            {
                new Configurator { Id = 1, ShowInMenu = true },
                new Configurator { Id = 2, ShowInMenu = true }
            };
            var configuratorDtos = new List<ConfiguratorDto>
            {
                new ConfiguratorDto { Id = 1 },
                new ConfiguratorDto { Id = 2 }
            };

            _configuratorDaoMock.Setup(x => x.GetMenuConfiguratorsAsync())
                .ReturnsAsync(configurators);
            _mappingServiceMock.Setup(x => x.MapToDto(It.IsAny<Configurator>()))
                .Returns<Configurator>(c => configuratorDtos.First(d => d.Id == c.Id));

            // Act
            var result = await _service.GetAllConfiguratorsAsync();

            // Assert
            Assert.Equal(2, result.Count());
            Assert.Contains(result, x => x.Id == 1);
            Assert.Contains(result, x => x.Id == 2);
        }

        [Fact]
        public async Task GetConfiguratorsAsync_ShouldReturnConfiguratorWithFirstChild_WhenChildConfiguratorsExist()
        {
            // Arrange
            var configuratorId = 1;
            var firstChildId = 2;
            var configuratorDto = new ConfiguratorDto { Id = configuratorId };
            
            var childConfigurators = new List<ChildConfigurator>
            {
                new ChildConfigurator { ConfiguratorId = firstChildId, DisplayOrder = 1 },
                new ChildConfigurator { ConfiguratorId = 3, DisplayOrder = 2 }
            };

            // Setup child configurator repository to return child configurators
            _childConfiguratorDaoMock.Setup(x => x.GetChildConfiguratorsByConfiguratorId(configuratorId))
                .ReturnsAsync(childConfigurators);

            // Setup the method to internally call GetFilteredConfiguratorAsync which we'll mock
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Setup the parent configurator that will be returned by cache manager
            var parentConfigurator = new Configurator 
            { 
                Id = configuratorId,
                ChildUserConfigurators = new List<Configurator>()
            };
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(parentConfigurator);
                
            var processedConfigurator = new Configurator { Id = firstChildId };
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                firstChildId,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((processedConfigurator, new ValidationResult()));

            _mappingServiceMock.Setup(x => x.MapToDto(It.IsAny<Configurator>()))
                .Returns(configuratorDto);

            // Act
            var (result, error) = await _service.GetConfiguratorsAsync(configuratorId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(configuratorId, result.Id);
            Assert.False(error.HasErrors);

            // Verify that child configurators were retrieved
            _childConfiguratorDaoMock.Verify(x => x.GetChildConfiguratorsByConfiguratorId(configuratorId), Times.Once);
        }

        [Fact]
        public async Task CalculateProductPricesAsync_ShouldReturnError_WhenPricingFails()
        {
            // Arrange
            var configuratorId = 1;
            var selectedProducts = new List<SelectedProductDto> { new SelectedProductDto { ProductCode = "TEST", Amount = 1, Unit = "pcs" } };
            var currentProductCode = "TEST";
            var error = "Pricing failed";

            // Setup processing service mock to return error
            var validation = new ValidationResult();
            validation.AddError(error);

            _productProcessingServiceMock.Setup(x => x.CalculateSelectedProductPricesAsync(
                selectedProducts,
                currentProductCode))
                .ReturnsAsync((null, validation));

            // Act
            var (result, resultValidation) = await _service.CalculateProductPricesAsync(configuratorId, selectedProducts, currentProductCode);

            // Assert
            Assert.Null(result);
            Assert.True(resultValidation.HasErrors);
            Assert.Contains(error, resultValidation.GetErrors());
        }

        [Fact]
        public async Task CalculateProductPricesAsync_ShouldReturnPrices_WhenValid()
        {
            // Arrange
            var configuratorId = 1;
            var selectedProducts = new List<SelectedProductDto> { new SelectedProductDto { ProductCode = "TEST", Amount = 1, Unit = "pcs" } };
            var currentProductCode = "TEST";
            var priceResponse = new ProductPriceCalculationResponse();
            var priceResponseDto = new ProductPriceCalculationResponseDto();

            // Setup processing service mock to return success
            var validation = new ValidationResult();

            _productProcessingServiceMock.Setup(x => x.CalculateSelectedProductPricesAsync(
                selectedProducts,
                currentProductCode))
                .ReturnsAsync((priceResponse, validation));
            _mappingServiceMock.Setup(x => x.MapToDto(priceResponse))
                .Returns(priceResponseDto);

            // Act
            var (result, resultValidation) = await _service.CalculateProductPricesAsync(configuratorId, selectedProducts, currentProductCode);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(priceResponseDto, result);
            Assert.False(resultValidation.HasErrors);
        }

        [Fact]
        public async Task ProcessCompositeConfiguratorAsync_ShouldCalculateCorrectPackageQuantity_WhenChildrenHaveSameProduct()
        {
            // Arrange - This test exposes the broken package calculation
            var mainConfiguratorId = 1;
            var compositeState = new CompositeConfiguratorStateDto
            {
                ActiveChildState = CreateChildState(101, 1, "bm"),
                OtherChildProducts = new List<ChildProductStateDto>
                {
                    CreateChildProductState(102, "PROD_A", 9.6m, "bm")
                }
            };

            // Setup main configurator
            var mainConfigurator = new Configurator 
            { 
                Id = mainConfiguratorId,
                IsComposite = true,
                ChildUserConfigurators = new List<Configurator>()
            };

            // Setup child configurators with proper product data
            var child1 = CreateChildConfiguratorWithProduct(101, "PROD_A", 15.3m, "bm", 4m); // Individual: 4 packages
            child1.TabOrder = 1;
            child1.TotalPriceVat = 400m; // 4 packages * 100m per package

            var child2 = CreateChildConfiguratorWithProduct(102, "PROD_A", 9.6m, "bm", 2m); // Individual: 2 packages  
            child2.TabOrder = 2;
            child2.TotalPriceVat = 200m; // 2 packages * 100m per package

            // Setup mocks
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(mainConfiguratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(mainConfigurator);

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(101, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child1, new ValidationResult()));

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(102, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child2, new ValidationResult()));

            _mappingServiceMock.Setup(x => x.MapToDto(It.IsAny<Configurator>()))
                .Returns((Configurator c) => new ConfiguratorDto 
                { 
                    Id = c.Id,
                    TotalPriceVat = c.TotalPriceVat,
                    TotalPriceNoVat = c.TotalPriceNoVat,
                    TotalVat = c.TotalVat,
                    TabOrder = c.TabOrder,
                    ConfiguratorCompositions = c.ConfiguratorCompositions?.Select(comp => new ConfiguratorCompositionDto
                    {
                        Id = comp.Id,
                        ConfiguratorProducts = comp.ConfiguratorProducts?.Select(p => new ConfiguratorProductDto
                        {
                            ProductCode = p.ProductCode,
                            CalculatedAmount = p.CalculatedAmount,          // Should show 24.9 (sum in sales units)
                            ProductUnit = p.ProductUnit,              // Should show "m" (sales unit)
                            PackageQuantity = p.PackageQuantity,  // Should show 5 (correct package count)
                            PriceVatPackage = p.PriceVat
                        }).ToList() ?? new List<ConfiguratorProductDto>()
                    }).ToList() ?? new List<ConfiguratorCompositionDto>()
                });

            // Setup the aggregation mock to return correct calculated values
            SetupProductProcessingMockForPackageCalculation(child1, child2);

            // Act
            var (response, validation) = await _service.ProcessCompositeConfiguratorAsync(mainConfiguratorId, compositeState);

            // Assert
            Assert.NotNull(response);
            Assert.False(validation.HasErrors);

            // EXPECTED: Correct aggregation should give us 5 packages, not 6
            // Current implementation will likely fail these assertions, exposing the bugs:

            // 1. Total prices should be 500m (5 packages * 100m), not 600m (4+2=6 packages * 100m)
            Assert.Equal(500m, response.CompositeConfiguratorSummary.TotalPriceVat); // This will likely FAIL

            // 2. Aggregated product should have correct units and package count
            var compositeProduct = response.CompositeConfiguratorSummary.ConfiguratorCompositions[0].ConfiguratorProducts[0];
            Assert.Equal("PROD_A", compositeProduct.ProductCode);
            Assert.Equal(24.9m, compositeProduct.CalculatedAmount); // Sum in sales units - This will likely FAIL
            Assert.Equal("m", compositeProduct.ProductUnit); // Sales unit - This will likely FAIL
            Assert.Equal(5m, compositeProduct.PackageQuantity); // Correct package count - This will likely FAIL

            // 3. TabOrder should be preserved
            Assert.Equal(1, response.ProcessedActiveChild.TabOrder);
        }

        [Fact]  
        public async Task ProcessCompositeConfiguratorAsync_ShouldPreserveTabOrder_WhenProcessingChildren()
        {
            // Arrange - Test that TabOrder is maintained through composite processing
            var mainConfiguratorId = 1;
            var compositeState = new CompositeConfiguratorStateDto
            {
                ActiveChildState = CreateChildStateWithTabOrder(101, 3, "Child Three"),
                OtherChildProducts = new List<ChildProductStateDto>
                {
                    CreateChildProductState(102, "PROD_DUMMY", 1m, "pcs"),
                    CreateChildProductState(103, "PROD_DUMMY", 1m, "pcs")
                }
            };

            var mainConfigurator = new Configurator { Id = mainConfiguratorId, IsComposite = true };

            var child1 = new Configurator { Id = 101, TabOrder = 3, TabTitle = "Child Three" };
            var child2 = new Configurator { Id = 102, TabOrder = 1, TabTitle = "Child One" };
            var child3 = new Configurator { Id = 103, TabOrder = 2, TabTitle = "Child Two" };

            // Setup mocks
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(mainConfiguratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(mainConfigurator);

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(101, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child1, new ValidationResult()));

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(102, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child2, new ValidationResult()));

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(103, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child3, new ValidationResult()));

            _mappingServiceMock.Setup(x => x.MapToDto(It.IsAny<Configurator>()))
                .Returns((Configurator c) => new ConfiguratorDto 
                { 
                    Id = c.Id,
                    TabOrder = c.TabOrder,
                    TabTitle = c.TabTitle,
                    ChildUserConfigurators = c.ChildUserConfigurators?.Select(child => new ConfiguratorDto
                    {
                        Id = child.Id,
                        TabOrder = child.TabOrder,
                        TabTitle = child.TabTitle
                    }).ToList() ?? new List<ConfiguratorDto>()
                });

            // Setup product processing mock for aggregation
            _productProcessingServiceMock.Setup(x => x.AggregateFromChildProductStatesAsync(It.IsAny<Configurator>(), It.IsAny<List<ChildProductStateDto>>(), It.IsAny<ValidationResult>()))
                .Callback<Configurator, List<ChildProductStateDto>, ValidationResult>((config, childProducts, val) =>
                {
                    // Add child configurators to match test expectations for TabOrder verification
                    config.ChildUserConfigurators = new List<Configurator> { child1, child2, child3 };
                })
                .Returns(Task.CompletedTask);

            // Act
            var (response, validation) = await _service.ProcessCompositeConfiguratorAsync(mainConfiguratorId, compositeState);

            // Assert
            Assert.NotNull(response);
            Assert.False(validation.HasErrors);

            // Verify TabOrder is preserved in processed active child
            Assert.Equal(3, response.ProcessedActiveChild.TabOrder); // This might FAIL if TabOrder is lost
            Assert.Equal("Child Three", response.ProcessedActiveChild.TabTitle);

            // Verify all children maintain TabOrder in composite summary
            Assert.Equal(3, response.CompositeConfiguratorSummary.ChildUserConfigurators.Count);
            
            var childWithOrder1 = response.CompositeConfiguratorSummary.ChildUserConfigurators.FirstOrDefault(c => c.TabOrder == 1);
            var childWithOrder2 = response.CompositeConfiguratorSummary.ChildUserConfigurators.FirstOrDefault(c => c.TabOrder == 2);
            var childWithOrder3 = response.CompositeConfiguratorSummary.ChildUserConfigurators.FirstOrDefault(c => c.TabOrder == 3);

            Assert.NotNull(childWithOrder1);
            Assert.NotNull(childWithOrder2);  
            Assert.NotNull(childWithOrder3);

            Assert.Equal(102, childWithOrder1.Id);
            Assert.Equal(103, childWithOrder2.Id);
            Assert.Equal(101, childWithOrder3.Id);
        }

        [Fact]
        public async Task ProcessCompositeConfiguratorAsync_ShouldHandleDifferentUnits_WhenAggregatingProducts()
        {
            // Arrange - Test unit conversion during aggregation (kg + g → kg)
            var mainConfiguratorId = 1;
            var compositeState = new CompositeConfiguratorStateDto
            {
                ActiveChildState = CreateChildState(101, 1, "kg"),
                OtherChildProducts = new List<ChildProductStateDto>
                {
                    CreateChildProductState(102, "PROD_B", 500m, "g") // Same product, different unit
                }
            };

            var mainConfigurator = new Configurator { Id = mainConfiguratorId, IsComposite = true };

            // Setup child configurators with different units for same product
            var child1 = CreateChildConfiguratorWithProduct(101, "PROD_B", 2.5m, "kg", 3m); // 2.5 kg
            var child2 = CreateChildConfiguratorWithProduct(102, "PROD_B", 500m, "g", 1m);  // 0.5 kg equivalent

            _cacheManagerMock.Setup(x => x.GetOrAddAsync(mainConfiguratorId, It.IsAny<Func<int, Task<Configurator?>>>(), It.IsAny<int>()))
                .ReturnsAsync(mainConfigurator);

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(101, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child1, new ValidationResult()));

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(102, It.IsAny<IEnumerable<ClientFieldValueDto>>(), It.IsAny<List<ClientProductValueDto>>(), It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((child2, new ValidationResult()));

            _mappingServiceMock.Setup(x => x.MapToDto(It.IsAny<Configurator>()))
                .Returns((Configurator c) => new ConfiguratorDto 
                { 
                    Id = c.Id,
                    ConfiguratorCompositions = c.ConfiguratorCompositions?.Select(comp => new ConfiguratorCompositionDto
                    {
                        Id = comp.Id,
                        ConfiguratorProducts = comp.ConfiguratorProducts?.Select(p => new ConfiguratorProductDto
                        {
                            ProductCode = p.ProductCode,
                            CalculatedAmount = p.CalculatedAmount,
                            ProductUnit = p.ProductUnit,
                            PackageQuantity = p.PackageQuantity
                        }).ToList() ?? new List<ConfiguratorProductDto>()
                    }).ToList() ?? new List<ConfiguratorCompositionDto>()
                });

            // Setup product processing mock for aggregation
            _productProcessingServiceMock.Setup(x => x.AggregateFromChildProductStatesAsync(It.IsAny<Configurator>(), It.IsAny<List<ChildProductStateDto>>(), It.IsAny<ValidationResult>()))
                .Callback<Configurator, List<ChildProductStateDto>, ValidationResult>((config, childProducts, val) =>
                {
                    // Create aggregated products for unit conversion test
                    config.ConfiguratorCompositions = new List<ConfiguratorComposition>
                    {
                        new ConfiguratorComposition
                        {
                            Id = 1,
                            Title = "Aggregated Products",
                            ConfiguratorProducts = new List<ConfiguratorProduct>
                            {
                                new ConfiguratorProduct
                                {
                                    ProductCode = "PROD_B",
                                    CalculatedAmount = 3.0m,    // Expected: 2.5 kg + 0.5 kg = 3.0 kg
                                    ProductUnit = "kg",         // Sales unit
                                    PackageQuantity = 3m,       // Package count
                                    IsSelected = true
                                }
                            }
                        }
                    };
                })
                .Returns(Task.CompletedTask);

            // Act
            var (response, validation) = await _service.ProcessCompositeConfiguratorAsync(mainConfiguratorId, compositeState);

            // Assert
            Assert.NotNull(response);
            Assert.False(validation.HasErrors);

            // Should aggregate to 3.0 kg total (2.5 kg + 0.5 kg converted from 500g)
            var compositeProduct = response.CompositeConfiguratorSummary.ConfiguratorCompositions[0].ConfiguratorProducts[0];
            Assert.Equal("PROD_B", compositeProduct.ProductCode);
            Assert.Equal(3.0m, compositeProduct.CalculatedAmount); // This will likely FAIL - units not being converted
            Assert.Equal("kg", compositeProduct.ProductUnit); // Should be sales unit - This will likely FAIL
        }

        private ConfiguratorStateDto CreateChildState(int configuratorId, int tabOrder, string unit)
        {
            return new ConfiguratorStateDto
            {
                ConfiguratorId = configuratorId,
                TabOrder = tabOrder,
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>
                {
                    new ClientProductValueDto { ProductId = 1, Amount = 15.3m } // Fixed amount for active child
                }
            };
        }

        private ChildProductStateDto CreateChildProductState(int configuratorId, string productCode, decimal amount, string unit)
        {
            return new ChildProductStateDto
            {
                ConfiguratorId = configuratorId,
                SelectedProducts = new List<ProductForAggregationDto>
                {
                    new ProductForAggregationDto 
                    { 
                        ProductCode = productCode, 
                        CalculatedAmount = amount, 
                        ProductUnit = unit,
                        Title = $"Product {productCode}",
                        CompositionTitle = "Test Composition"
                    }
                }
            };
        }

        private ConfiguratorStateDto CreateChildStateWithTabOrder(int configuratorId, int tabOrder, string tabTitle)
        {
            return new ConfiguratorStateDto
            {
                ConfiguratorId = configuratorId,
                TabOrder = tabOrder,
                TabTitle = tabTitle,
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>()
            };
        }

        private Configurator CreateChildConfiguratorWithProduct(int id, string productCode, decimal amount, string unit, decimal packageQuantity)
        {
            return new Configurator
            {
                Id = id,
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        Id = 1,
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct
                            {
                                ProductCode = productCode,
                                CalculatedAmount = amount,
                                ProductUnit = unit,
                                PackageQuantity = packageQuantity,
                                IsSelected = true,
                                PriceVat = packageQuantity * 100m // 100m per package
                            }
                        }
                    }
                }
            };
        }

        private void SetupProductProcessingMockForPackageCalculation(Configurator child1, Configurator child2)
        {
            // Mock the aggregation service to actually modify the configurator with aggregated products
            _productProcessingServiceMock.Setup(x => x.AggregateFromChildProductStatesAsync(It.IsAny<Configurator>(), It.IsAny<List<ChildProductStateDto>>(), It.IsAny<ValidationResult>()))
                .Callback<Configurator, List<ChildProductStateDto>, ValidationResult>((config, childProducts, val) =>
                {
                    // Simulate proper aggregation: create composite products from child products
                    config.ConfiguratorCompositions = new List<ConfiguratorComposition>
                    {
                        new ConfiguratorComposition
                        {
                            Id = 1,
                            Title = "Aggregated Products",
                            ConfiguratorProducts = new List<ConfiguratorProduct>
                            {
                                new ConfiguratorProduct
                                {
                                    ProductCode = "PROD_A",
                                    CalculatedAmount = 24.9m,    // Aggregated: 15.3 + 9.6 = 24.9
                                    ProductUnit = "m",           // Sales unit
                                    PackageQuantity = 5m,        // Correct package count: ceil(24.9/5) = 5
                                    IsSelected = true,
                                    PriceVat = 500m              // 5 packages * 100m per package
                                }
                            }
                        }
                    };
                    // Set aggregated total prices
                    config.TotalPriceVat = 500m;
                    config.TotalPriceNoVat = 400m;
                    config.TotalVat = 100m;
                    
                    // Add child configurators to match test expectations
                    config.ChildUserConfigurators = new List<Configurator> { child1, child2 };
                })
                .Returns(Task.CompletedTask);
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_ShouldProcessMultipleChildConfiguratorsInParallel()
        {
            // Arrange - Test with multiple child configurators to verify parallel processing
            var configuratorId = 1;
            var childConfiguratorIds = new List<int> { 2, 3, 4 };
            var configuratorState = new ConfiguratorStateDto
            {
                ChildConfiguratorStates = childConfiguratorIds.Select(id => new ChildConfiguratorStateDto 
                { 
                    ConfiguratorId = id,
                    TabTitle = $"Child {id}",
                    TabOrder = id - 1
                }).ToList()
            };

            var parentConfigurator = new Configurator 
            {
                Id = configuratorId,
                ChildUserConfigurators = new List<Configurator>()
            };

            var parentConfiguratorDto = new ConfiguratorDto { Id = configuratorId };

            // Setup validation for parent configurator
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Setup cache manager for parent configurator
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(parentConfigurator);

            // Setup processing service for each child configurator - these should be called in parallel
            foreach (var childId in childConfiguratorIds)
            {
                var childConfigurator = new Configurator 
                { 
                    Id = childId, 
                    IsValid = true,
                    CanAddToCart = true
                };
                
                _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                    childId,
                    It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                    It.IsAny<List<ClientProductValueDto>>(),
                    It.IsAny<List<CategoryStateDto>>()))
                    .ReturnsAsync((childConfigurator, new ValidationResult()));
            }

            // Setup mapping service
            _mappingServiceMock.Setup(x => x.MapToDto(parentConfigurator))
                .Returns(parentConfiguratorDto);

            // Act
            var (result, error) = await _service.GetFilteredConfiguratorAsync(configuratorId, configuratorState);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(configuratorId, result.Id);
            Assert.False(error.HasErrors);

            // Verify all child configurators were processed exactly once (parallel processing)
            foreach (var childId in childConfiguratorIds)
            {
                _processingServiceMock.Verify(x => x.ProcessConfiguratorAsync(
                    childId,
                    It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                    It.IsAny<List<ClientProductValueDto>>(),
                    It.IsAny<List<CategoryStateDto>>()), Times.Once);
            }

            // Verify that the parent configurator now contains all processed children
            Assert.Equal(3, parentConfigurator.ChildUserConfigurators.Count);
            
            // Verify TabTitle and TabOrder were preserved during parallel processing
            var child1 = parentConfigurator.ChildUserConfigurators.First(c => c.Id == 2);
            Assert.Equal("Child 2", child1.TabTitle);
            Assert.Equal(1, child1.TabOrder);
        }

        [Fact]
        public async Task GetFilteredConfiguratorAsync_ShouldReturnError_WhenOneChildConfiguratorFails()
        {
            // Arrange - Test error handling in parallel child processing
            var configuratorId = 1;
            var configuratorState = new ConfiguratorStateDto
            {
                ChildConfiguratorStates = new List<ChildConfiguratorStateDto>
                {
                    new ChildConfiguratorStateDto { ConfiguratorId = 2 },
                    new ChildConfiguratorStateDto { ConfiguratorId = 3 }, // This one will fail
                    new ChildConfiguratorStateDto { ConfiguratorId = 4 }
                }
            };

            var parentConfigurator = new Configurator 
            {
                Id = configuratorId,
                ChildUserConfigurators = new List<Configurator>()
            };

            // Setup validation for parent configurator
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Setup cache manager for parent configurator
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(parentConfigurator);

            // Setup successful processing for children 2 and 4
            var successChildConfigurator = new Configurator { Id = 2, IsValid = true };
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                2,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((successChildConfigurator, new ValidationResult()));

            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                4,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((successChildConfigurator, new ValidationResult()));

            // Setup failed processing for child 3
            var failedValidation = new ValidationResult();
            failedValidation.AddError("Child configurator 3 processing failed");
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                3,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((null, failedValidation));

            // Act
            var (result, error) = await _service.GetFilteredConfiguratorAsync(configuratorId, configuratorState);

            // Assert
            Assert.Null(result);
            Assert.True(error.HasErrors);
            Assert.Contains("Child configurator 3 processing failed", error.GetErrors());

            // Verify all children were attempted to be processed (parallel execution)
            _processingServiceMock.Verify(x => x.ProcessConfiguratorAsync(
                It.IsAny<int>(),
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()), Times.Exactly(3));
        }

        [Fact]
        public async Task GetFilteredConfiguratorEntityAsync_ShouldSetHasProductsCorrectly_ForCompositeWithChildren()
        {
            // Arrange - Test SetHasProductsPropertyAsync with composite configurator having children
            var configuratorId = 1;
            var childId1 = 2;
            var childId2 = 3;
            var configuratorState = new ConfiguratorStateDto();

            // Create composite configurator with child references
            var compositeConfigurator = new Configurator
            {
                Id = configuratorId,
                IsComposite = true,
                HasProducts = false,
                ConfiguratorCompositions = new List<ConfiguratorComposition>(), // No direct products
                ChildConfigurators = new List<ChildConfigurator>
                {
                    new ChildConfigurator { ConfiguratorId = childId1 },
                    new ChildConfigurator { ConfiguratorId = childId2 }
                }
            };

            // Create child configurators - one with products, one without
            var childWithProducts = new Configurator
            {
                Id = childId1,
                IsComposite = false,
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct { ProductCode = "PROD1" }
                        }
                    }
                }
            };

            var childWithoutProducts = new Configurator
            {
                Id = childId2,
                IsComposite = false,
                ConfiguratorCompositions = new List<ConfiguratorComposition>()
            };

            // Setup validation
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(configuratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Setup cache manager - parent configurator and children loaded in parallel
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(configuratorId, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(compositeConfigurator);
            
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(childId1, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(childWithProducts);
                
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(childId2, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(childWithoutProducts);

            // Setup processing service for no child states (only tests SetHasProductsPropertyAsync)
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                configuratorId,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((compositeConfigurator, new ValidationResult()));

            // Act
            var (result, error) = await _service.GetFilteredConfiguratorEntityAsync(configuratorId, configuratorState);

            // Assert
            Assert.NotNull(result);
            Assert.False(error.HasErrors);
            
            // Critical assertion: HasProducts should be true because child1 has products
            Assert.True(result.HasProducts, "Composite configurator should have HasProducts=true when any child has products");

            // Verify all children were loaded in parallel (SetHasProductsPropertyAsync)
            _cacheManagerMock.Verify(x => x.GetOrAddAsync(childId1, It.IsAny<Func<int, Task<Configurator?>>>(), 60), Times.Once);
            _cacheManagerMock.Verify(x => x.GetOrAddAsync(childId2, It.IsAny<Func<int, Task<Configurator?>>>(), 60), Times.Once);
        }

        [Fact]
        public async Task GetFilteredConfiguratorEntityAsync_ShouldHandleMultipleSimpleChildConfigurators()
        {
            // Arrange - Test SetHasProductsPropertyAsync with multiple simple child configurators (realistic scenario)
            var rootConfiguratorId = 1;
            var childId1 = 2;
            var childId2 = 3;
            var childId3 = 4;
            var configuratorState = new ConfiguratorStateDto();

            // Create composite configurator with multiple simple children (typical business scenario)
            var compositeConfigurator = new Configurator
            {
                Id = rootConfiguratorId,
                IsComposite = true,
                HasProducts = false,
                ConfiguratorCompositions = new List<ConfiguratorComposition>(),
                ChildConfigurators = new List<ChildConfigurator>
                {
                    new ChildConfigurator { ConfiguratorId = childId1 },
                    new ChildConfigurator { ConfiguratorId = childId2 },
                    new ChildConfigurator { ConfiguratorId = childId3 }
                }
            };

            // Create simple child configurators - mix of with/without products
            var childWithProducts1 = new Configurator
            {
                Id = childId1,
                IsComposite = false, // Simple configurator
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct { ProductCode = "FLOOR_PROD" }
                        }
                    }
                }
            };

            var childWithoutProducts = new Configurator
            {
                Id = childId2,
                IsComposite = false, // Simple configurator
                ConfiguratorCompositions = new List<ConfiguratorComposition>()
            };

            var childWithProducts2 = new Configurator
            {
                Id = childId3,
                IsComposite = false, // Simple configurator
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct { ProductCode = "WALL_PROD" }
                        }
                    }
                }
            };

            // Setup validation
            _validationServiceMock.Setup(x => x.ValidateConfiguratorExistsAsync(rootConfiguratorId, It.IsAny<ValidationResult>()))
                .Returns(Task.CompletedTask);

            // Setup cache manager - all children loaded in parallel
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(rootConfiguratorId, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(compositeConfigurator);
            
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(childId1, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(childWithProducts1);
                
            _cacheManagerMock.Setup(x => x.GetOrAddAsync(childId2, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(childWithoutProducts);

            _cacheManagerMock.Setup(x => x.GetOrAddAsync(childId3, It.IsAny<Func<int, Task<Configurator?>>>(), 60))
                .ReturnsAsync(childWithProducts2);

            // Setup processing service
            _processingServiceMock.Setup(x => x.ProcessConfiguratorAsync(
                rootConfiguratorId,
                It.IsAny<IEnumerable<ClientFieldValueDto>>(),
                It.IsAny<List<ClientProductValueDto>>(),
                It.IsAny<List<CategoryStateDto>>()))
                .ReturnsAsync((compositeConfigurator, new ValidationResult()));

            // Act
            var (result, error) = await _service.GetFilteredConfiguratorEntityAsync(rootConfiguratorId, configuratorState);

            // Assert
            Assert.NotNull(result);
            Assert.False(error.HasErrors);
            
            // Critical assertion: HasProducts should be true because children 1 and 3 have products
            Assert.True(result.HasProducts, "Composite configurator should have HasProducts=true when any simple child has products");

            // Verify all simple children were loaded in parallel (SetHasProductsPropertyAsync)
            _cacheManagerMock.Verify(x => x.GetOrAddAsync(childId1, It.IsAny<Func<int, Task<Configurator?>>>(), 60), Times.Once);
            _cacheManagerMock.Verify(x => x.GetOrAddAsync(childId2, It.IsAny<Func<int, Task<Configurator?>>>(), 60), Times.Once);
            _cacheManagerMock.Verify(x => x.GetOrAddAsync(childId3, It.IsAny<Func<int, Task<Configurator?>>>(), 60), Times.Once);
        }
    }
}
