using Deksmart.Domain.Entity.Business;

namespace Deksmart.Domain.Service.Base
{
    /// <summary>
    /// Base interface for services that perform validation
    /// </summary>
    public interface IValidationService
    {
        /// <summary>
        /// Creates a new ValidationResult instance
        /// </summary>
        /// <returns>A new ValidationResult instance</returns>
        ValidationResult CreateValidation();

        /// <summary>
        /// Creates a ValidationResult with a single error
        /// </summary>
        /// <param name="errorMessage">The error message</param>
        /// <returns>A ValidationResult with the specified error</returns>
        ValidationResult CreateValidationWithError(string errorMessage);

        /// <summary>
        /// Creates a ValidationResult with multiple errors
        /// </summary>
        /// <param name="errorMessages">The error messages</param>
        /// <returns>A ValidationResult with the specified errors</returns>
        ValidationResult CreateValidationWithErrors(IEnumerable<string> errorMessages);
    }
}
