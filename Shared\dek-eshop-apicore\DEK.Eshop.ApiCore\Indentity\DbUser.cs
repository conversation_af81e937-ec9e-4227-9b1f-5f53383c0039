using DEK.Eshop.ApiCore.Config.Dto;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace DEK.Eshop.ApiCore.Indentity;

[Keyless]
public record DbUser
{

    [Column("subjectName")]
    public string SubjectName { get; init; } = null!;

    [Column("homeStoreId")]
    public string BranchHomeCode { get; init; } = null!;

    [Column("priceLevelEshop")]
    public string PriceLevelEshop { get; init; } = null!;

    [Column("priceLevelRentalNew")]
    public int PriceLevelRental { get; init; } = -1;

    [Column("rentalBan")]
    public bool HasRentalBan { get; init; }

    [Column("firmId")]
    public int CompanyId { get; init; }

    [Column("isB2B")]
    public bool IsB2B { get; init; }

    [Column("isAdmin")]
    //[NotMapped]
    public bool IsCompanyAdmin { get; init; } = false;
}
