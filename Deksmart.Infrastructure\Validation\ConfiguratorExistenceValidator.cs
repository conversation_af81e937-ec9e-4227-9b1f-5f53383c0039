using Deksmart.Domain.Service;
using Deksmart.Infrastructure.Repository.Interface;

namespace Deksmart.Infrastructure.Validation
{
    public class ConfiguratorExistenceValidator : IConfiguratorExistenceValidator
    {
        private readonly IConfiguratorRepository _configuratorRepository;

        public ConfiguratorExistenceValidator(IConfiguratorRepository configuratorRepository)
        {
            _configuratorRepository = configuratorRepository;
        }

        public async Task<bool> DoesConfiguratorExistAsync(int configuratorId)
        {
            return await _configuratorRepository.DoesConfiguratorExistAsync(configuratorId);
        }
    }
} 