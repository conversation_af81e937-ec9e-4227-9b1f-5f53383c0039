using Deksmart.Application.Resource;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Shared.Dto;
using Microsoft.Playwright;
using System.Text;
using System.Text.RegularExpressions;

namespace Deksmart.Application.Service
{
    public interface IConfiguratorViewGenerator
    {
        /// <summary>
        /// Generates an HTML representation of the given configurator, optionally including contact information.
        /// </summary>
        /// <param name="configurator">The configurator entity to render as HTML.</param>
        /// <param name="contactInfo">Optional contact information to include in the HTML output.</param>
        /// <returns>
        /// A string containing the generated HTML.
        /// </returns>
        string GenerateHtml(Configurator configurator, ContactInfoDto? contactInfo = null);

        /// <summary>
        /// Generates a PDF document from the provided HTML content.
        /// </summary>
        /// <param name="htmlContent">The HTML content to convert to PDF.</param>
        /// <returns>
        /// A tuple containing the PDF as a byte array (or null if generation fails) and a <see cref="ValidationResult"/> with any validation errors.
        /// </returns>
        Task<(byte[]? pdfBytes, ValidationResult validation)> GeneratePdfFromHtmlAsync(string htmlContent);
    }

    public class ConfiguratorViewGenerator : IConfiguratorViewGenerator
    {
        private readonly string _playwrightServerUrl;

        public ConfiguratorViewGenerator()
        {
            _playwrightServerUrl = $"ws://{Environment.GetEnvironmentVariable("Playwright__Host")}";
        }

        public string GenerateHtml(Configurator configurator, ContactInfoDto? contactInfo = null)
        {
            return GenerateHtmlContent(configurator, contactInfo);
        }

        public async Task<(byte[]? pdfBytes, ValidationResult validation)> GeneratePdfFromHtmlAsync(string htmlContent)
        {
            var validation = new ValidationResult();

            try
            {
                Console.WriteLine($"Playwright url: {_playwrightServerUrl}");
                using var playwright = await Playwright.CreateAsync();
                await using var browser = await playwright.Chromium.ConnectAsync(_playwrightServerUrl);
                var page = await browser.NewPageAsync();
                await page.SetContentAsync(htmlContent);
                var pdfBytes = await page.PdfAsync(new PagePdfOptions
                {
                    Format = "A4",
                    PrintBackground = true,
                    Margin = new Margin { Top = "20mm", Right = "20mm", Bottom = "20mm", Left = "20mm" }
                });
                return (pdfBytes, validation);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PDF generation failed: {ex.Message}");
                validation.AddError(string.Format(DeksmartApplicationResource.FailedToGeneratePdf, ex.Message));
                return (null, validation);
            }
        }

        private string GenerateHtmlContent(Configurator configurator, ContactInfoDto? contactInfo)
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("    <meta charset=\"UTF-8\">");
            html.AppendLine("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">");
            html.AppendLine("    <title>Configurator View</title>");
            html.AppendLine("</head>");
            html.AppendLine("<div style=\"font-family: Arial, sans-serif; margin: 20px; color: #333333;\">");

            GenerateContactInfo(html, contactInfo);

            // Generate HTML for the main configurator
            GenerateConfiguratorHtml(html, configurator, contactInfo);

            // Generate HTML for child configurators if they exist
            if (configurator.ChildUserConfigurators?.Any() == true)
            {
                foreach (var childConfigurator in configurator.ChildUserConfigurators.OrderBy(c => c.TabOrder))
                {
                    html.AppendLine("<div style=\"margin-top: 30px; border-top: 2px solid #dddddd; padding-top: 20px;\">");
                    GenerateConfiguratorHtml(html, childConfigurator, contactInfo);
                    html.AppendLine("</div>");
                }
            }

            html.AppendLine("</div>");
            html.AppendLine("</html>");

            return html.ToString();
        }

        private void GenerateConfiguratorHtml(StringBuilder html, Configurator configurator, ContactInfoDto? contactInfo)
        {
            GenerateHeader(html, configurator);
            GenerateConfigurationSection(html, configurator);

            // Check if there are any products in the configurator
            bool hasProducts = configurator.ConfiguratorCompositions?.Any(c =>
                c.ConfiguratorProducts?.Any(p => p.IsSelected) == true) == true;

            if (hasProducts)
            {
                GenerateSelectedProductsSection(html, configurator);
                GenerateSummarySection(html, configurator);
            }
        }

        private void GenerateHeader(StringBuilder html, Configurator configurator)
        {
            html.AppendLine("    <div style=\"text-align: center; margin-bottom: 30px;\">");
            html.AppendLine($"        <h1 style=\"margin: 0; padding: 0;\">{RemoveHtmlTags(!string.IsNullOrEmpty(configurator.TabTitle) ? configurator.TabTitle : configurator.Title)}</h1>");
            html.AppendLine("    </div>");
        }

        private void GenerateContactInfo(StringBuilder html, ContactInfoDto? contactInfo)
        {
            if (contactInfo != null)
            {
                html.AppendLine("    <div style=\"margin-bottom: 20px;\">");
                html.AppendLine($"        <div style=\"font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #dddddd; padding-bottom: 5px;\">{DeksmartApplicationResource.ContactInformation}</div>");
                html.AppendLine("        <div style=\"padding: 8px; border: 1px solid #dddddd;\">");
                if (!string.IsNullOrEmpty(contactInfo.Name))
                {
                    html.AppendLine($"            <div style=\"margin-bottom: 5px;\">{DeksmartApplicationResource.NameLabel}: {contactInfo.Name}</div>");
                }
                if (!string.IsNullOrEmpty(contactInfo.Email))
                {
                    html.AppendLine($"            <div style=\"margin-bottom: 5px;\">{DeksmartApplicationResource.EmailLabel}: {contactInfo.Email}</div>");
                }
                if (!string.IsNullOrEmpty(contactInfo.Phone))
                {
                    html.AppendLine($"            <div style=\"margin-bottom: 5px;\">{DeksmartApplicationResource.PhoneLabel}: {contactInfo.Phone}</div>");
                }
                html.AppendLine("        </div>");
                html.AppendLine("    </div>");
            }
        }

        private void GenerateConfigurationSection(StringBuilder html, Configurator configurator)
        {
            if (configurator.ConfiguratorFieldCategories?.Any() == true)
            {
                // Track which Ident values have already been processed
                var processedIdents = new HashSet<string>();

                html.AppendLine("    <div style=\"margin-bottom: 20px;\">");
                html.AppendLine($"        <div style=\"font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #dddddd; padding-bottom: 5px;\">{DeksmartApplicationResource.Configuration}</div>");
                foreach (var category in configurator.ConfiguratorFieldCategories.OrderBy(c => c.Order))
                {
                    var fieldsWithValues = category.ConfiguratorFields?
                        .Where(HasValue)
                        .OrderBy(f => f.Order)
                        .ToList() ?? new List<ConfiguratorField>();

                    if (fieldsWithValues.Any())
                    {
                        html.AppendLine("        <div style=\"padding: 8px; border: 1px solid #dddddd; margin-bottom: 15px;\">");
                        html.AppendLine($"            <div style=\"font-weight: bold; margin-bottom: 5px;\">{RemoveHtmlTags(category.Title)}</div>");
                        html.AppendLine("            <div style=\"margin-left: 15px;\">");
                        foreach (var field in fieldsWithValues)
                        {
                            // Skip fields with Ident that has already been processed
                            // Only process fields with non-null Ident values
                            if (!string.IsNullOrEmpty(field.Ident))
                            {
                                // If this Ident has already been processed, skip this field
                                if (processedIdents.Contains(field.Ident))
                                {
                                    continue;
                                }

                                // Mark this Ident as processed
                                processedIdents.Add(field.Ident);
                            }

                            var value = GetFieldValue(field);
                            if (!string.IsNullOrEmpty(value))
                            {
                                html.AppendLine($"                <div style=\"margin-bottom: 5px;\">{RemoveHtmlTags(field.Title)}: {value}</div>");
                            }
                        }
                        html.AppendLine("            </div>");
                        html.AppendLine("        </div>");
                    }
                }
                html.AppendLine("    </div>");
            }
        }

        private void GenerateSelectedProductsSection(StringBuilder html, Configurator configurator)
        {
            if (configurator.ConfiguratorCompositions?.Any() == true)
            {
                html.AppendLine("    <div style=\"margin-bottom: 20px;\">");
                html.AppendLine($"        <div style=\"font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #dddddd; padding-bottom: 5px;\">{DeksmartApplicationResource.SelectedProducts}</div>");
                html.AppendLine("        <table style=\"width: 100%; border-collapse: collapse; margin-bottom: 20px;\" border=\"1\" cellpadding=\"8\" cellspacing=\"0\">");
                html.AppendLine("            <thead>");
                html.AppendLine("                <tr style=\"background-color: #f5f5f5;\">");
                html.AppendLine($"                    <th style=\"text-align: left;\">{DeksmartApplicationResource.Configuration}</th>");
                html.AppendLine($"                    <th style=\"text-align: left;\">{DeksmartApplicationResource.SelectedProducts}</th>");
                html.AppendLine($"                    <th style=\"text-align: left;\">{DeksmartApplicationResource.ProductCode}</th>");
                html.AppendLine($"                    <th style=\"text-align: left;\">{DeksmartApplicationResource.Amount}</th>");
                html.AppendLine($"                    <th style=\"text-align: left;\">{DeksmartApplicationResource.PricePerPackage}</th>");
                html.AppendLine($"                    <th style=\"text-align: left;\">{DeksmartApplicationResource.TotalPrice}</th>");
                html.AppendLine("                </tr>");
                html.AppendLine("            </thead>");
                html.AppendLine("            <tbody>");
                foreach (var composition in configurator.ConfiguratorCompositions.OrderBy(c => c.Order))
                {
                    var selectedProducts = composition.ConfiguratorProducts?
                        .Where(p => p.IsSelected)
                        .ToList() ?? new List<ConfiguratorProduct>();

                    foreach (var product in selectedProducts)
                    {
                        html.AppendLine("                <tr>");
                        html.AppendLine($"                    <td>{RemoveHtmlTags(composition.Title)}</td>");
                        html.AppendLine($"                    <td>{RemoveHtmlTags(product.Title)}</td>");
                        html.AppendLine($"                    <td>{product.ProductCode}</td>");
                        html.AppendLine($"                    <td>{product.CalculatedAmount.ToString("G29")} {RemoveHtmlTags(product.ProductUnit)}</td>");
                        html.AppendLine($"                    <td>{product.PriceVatPackage} {RemoveHtmlTags(product.PackageUnit)}/{DeksmartApplicationResource.CurrencySymbol}</td>");
                        html.AppendLine($"                    <td>{product.PriceVat} {DeksmartApplicationResource.CurrencySymbol}</td>");
                        html.AppendLine("                </tr>");
                    }
                }
                html.AppendLine("            </tbody>");
                html.AppendLine("        </table>");
                html.AppendLine("    </div>");
            }
        }

        private void GenerateSummarySection(StringBuilder html, Configurator configurator)
        {
            html.AppendLine("    <div style=\"margin-top: 20px; padding: 10px; background-color: #f5f5f5;\">");
            html.AppendLine($"        <div style=\"font-weight: bold; margin-bottom: 10px;\">{DeksmartApplicationResource.Summary}</div>");
            html.AppendLine($"        <div style=\"margin-bottom: 5px;\">{DeksmartApplicationResource.TotalPriceExclVat}: {configurator.TotalPriceNoVat} {DeksmartApplicationResource.CurrencySymbol}</div>");
            html.AppendLine($"        <div style=\"margin-bottom: 5px;\">{DeksmartApplicationResource.Vat}: {configurator.TotalVat} {DeksmartApplicationResource.CurrencySymbol}</div>");
            html.AppendLine($"        <div style=\"margin-bottom: 5px;\">{DeksmartApplicationResource.TotalPriceInclVat}: {configurator.TotalPriceVat} {DeksmartApplicationResource.CurrencySymbol}</div>");
            html.AppendLine("    </div>");
        }

        private static string RemoveHtmlTags(string? input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return Regex.Replace(input, "<.*?>", string.Empty);
        }

        private static bool HasValue(ConfiguratorField field)
        {
            if (field.ConfiguratorFieldValues?.Any() == true)
            {
                return field.ConfiguratorFieldValues.Any(v => v.NumericValue == field.Value);
            }

            if (field.Value.HasValue)
            {
                return true;
            }

            return false;
        }

        private static string GetFieldValue(ConfiguratorField field)
        {
            if (field.ConfiguratorFieldValues?.Any() == true)
            {
                var selectedValue = field.ConfiguratorFieldValues.FirstOrDefault(v => v.NumericValue == field.Value);
                if (selectedValue != null)
                {
                    return RemoveHtmlTags(selectedValue.Title);
                }
            }

            if (field.Value.HasValue && field.Value.Value != 0)
            {
                var value = field.Value.Value;
                if (!string.IsNullOrEmpty(field.Suffix))
                {
                    return $"{value:G29} {RemoveHtmlTags(field.Suffix)}";
                }
                return value.ToString("G29");
            }

            return string.Empty;
        }

        #region EMERGENCY_PLAYWRIGHT_LOCAL
        //public async Task<(byte[]? pdfBytes, ValidationResult validation)> GeneratePdfFromHtmlAsync(string htmlContent)
        // {
        //     var validation = new ValidationResult();

        //     // Create a simple temporary directory
        //     string tempDir = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());

        //     try
        //     {
        //         // Create minimal directory structure
        //         Directory.CreateDirectory(tempDir);

        //         // Create Playwright instance
        //         using var playwright = await Playwright.CreateAsync();

        //         // Configure browser launch options with minimal settings
        //         var launchOptions = new BrowserTypeLaunchOptions
        //         {
        //             Headless = true,
        //             // Explicitly set Chrome path for Docker environment
        //             ExecutablePath = "/usr/bin/google-chrome",
        //             Args = ["--no-sandbox", "--disable-dev-shm-usage"],
        //             Env = new Dictionary<string, string>
        //             {
        //                 { "HOME", tempDir }
        //             },
        //             Timeout = 60000 // 60 seconds timeout
        //         };

        //         // Launch browser
        //         await using var browser = await playwright.Chromium.LaunchAsync(launchOptions);

        //         // Create page with minimal settings
        //         var page = await browser.NewPageAsync();

        //         // Set page content
        //         await page.SetContentAsync(htmlContent);

        //         // Generate PDF with minimal settings
        //         var pdfBytes = await page.PdfAsync(new PagePdfOptions
        //         {
        //             Format = "A4",
        //             PrintBackground = true,
        //             Margin = new Margin { Top = "20mm", Right = "20mm", Bottom = "20mm", Left = "20mm" }
        //         });

        //         return (pdfBytes, validation);
        //     }
        //     catch (Exception ex)
        //     {
        //         Console.WriteLine($"PDF generation failed: {ex.Message}");

        //         // Check if Chrome is available
        //         if (!File.Exists("/usr/bin/google-chrome"))
        //         {
        //             Console.WriteLine("Google Chrome not found at /usr/bin/google-chrome");
        //         }

        //         validation.AddError(string.Format(DeksmartApplicationResource.FailedToGeneratePdf, ex.Message));
        //         return (null, validation);
        //     }
        //     finally
        //     {
        //         // Clean up temporary directory
        //         try
        //         {
        //             if (Directory.Exists(tempDir))
        //             {
        //                 Directory.Delete(tempDir, true);
        //             }
        //         }
        //         catch
        //         {
        //             // Ignore cleanup errors
        //         }
        //     }
        // }
        #endregion
    }
}