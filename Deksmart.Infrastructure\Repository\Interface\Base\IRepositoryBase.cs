﻿namespace Deksmart.Infrastructure.Repository.Interface.Base
{
    /// <summary>
    /// Base repository interface providing transaction management operations.
    /// </summary>
    public interface IRepositoryBase
    {
        /// <summary>
        /// Begins a new database transaction asynchronously.
        /// </summary>
        Task BeginTransactionAsync();

        /// <summary>
        /// Commits the current database transaction asynchronously.
        /// </summary>
        Task CommitTransactionAsync();

        /// <summary>
        /// Rolls back the current database transaction asynchronously.
        /// </summary>
        Task RollbackTransactionAsync();
    }
}
