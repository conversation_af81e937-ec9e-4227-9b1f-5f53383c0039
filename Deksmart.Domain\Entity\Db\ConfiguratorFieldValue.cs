﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// CZ_ESHOP_KONFIGURATOR_FILTRY_HODNOTY
    /// Represents an option or selectable value for configurator fields that support multiple choices (e.g., selectbox, tile, radiobutton).
    /// Defines the display name, order, numeric value, visibility logic, and optional image for each selectable option within a field.
    /// </summary>
    [Table("configurator_field_value", Schema = "dbo")]
    [Index("VisibilityId", Name = "idx_configurator_field_value_visibility_id")]
    public partial class ConfiguratorFieldValue : IDeletableEntity
    {
        /// <summary>
        /// HODNOTA_ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// HODNOTA_NAZEV
        /// </summary>
        [Column("title")]
        [StringLength(200)]
        public string Title { get; set; } = null!;

        /// <summary>
        /// POZNAMKA
        /// </summary>
        [Column("description")]
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// PORADI
        /// </summary>
        [Column("order")]
        public int Order { get; set; }

        [Column("is_deleted")]
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Numeric value of the field value used in expressions.
        /// </summary>
        [Column("numeric_value")]
        public decimal NumericValue { get; set; }

        [Column("visibility_id")]
        public int? VisibilityId { get; set; }

        /// <summary>
        /// for tile component
        /// </summary>
        [Column("image")]
        [StringLength(200)]
        public string? Image { get; set; }

        [ForeignKey("VisibilityId")]
        public virtual ConfiguratorExpression? Visibility { get; set; }
    }
}
