namespace DEK.Eshop.ApiCore.Config.Dto;

/// <summary>
/// https://stackexchange.github.io/StackExchange.Redis/Configuration.html
/// </summary>
public class CacheRedis
{
    public bool Enabled { get; set; } = false;

    public bool ReplaceRedisWithDevNullAdapter { get; set; } = false;

    public string? Prefix { get; set; } = null;

    public string Host { get; set; } = null!;

    public int ConnectTimeout { get; set; } = 200;

    public int SyncTimeout { get; set; } = 200;

    public string? Password { get; set; } = null;

    public bool CheckCertificateRevocation { get; set; } = false;
}
