using System.Data;
using Deksmart.Application.Resource;
using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Repository.Interface;
using ExcelDataReader;
using Microsoft.AspNetCore.Http;
using Deksmart.Domain.Service;
using Deksmart.Domain.Entity.Business;

namespace Deksmart.Application.Service
{
    public interface ICompositeImportService
    {
        /// <summary>
        /// Imports a composite from the provided Excel file, validates the data, and updates the configurator and its child configurators in the database.
        /// </summary>
        /// <param name="file">The Excel file containing the composite data to import.</param>
        /// <returns>
        /// A tuple indicating whether the import was successful and a <see cref="ValidationResult"/> containing any validation errors.
        /// </returns>
        Task<(bool success, ValidationResult validation)> ImportCompositeAsync(IFormFile file);
    }

    public class CompositeImportService : ICompositeImportService
    {
        private readonly IImportProcessingService _importProcessingService;
        private readonly IChildConfiguratorRepository _childConfiguratorDao;
        private readonly IConfiguratorImportParser _configuratorImportParser;
        private readonly IImportValidationService _importValidationService;

        public CompositeImportService(
            IImportProcessingService importProcessingService,
            IChildConfiguratorRepository childConfiguratorDao,
            IConfiguratorImportParser configuratorImportParser,
            IImportValidationService importValidationService)
        {
            _importProcessingService = importProcessingService;
            _childConfiguratorDao = childConfiguratorDao;
            _configuratorImportParser = configuratorImportParser;
            _importValidationService = importValidationService;
        }

        public async Task<(bool success, ValidationResult validation)> ImportCompositeAsync(IFormFile file)
        {
            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0;

                var validation = new ValidationResult();

                try
                {
                    await _importProcessingService.BeginTransactionAsync();

                    using (var reader = ExcelReaderFactory.CreateReader(stream))
                    {
                        var result = reader.AsDataSet();
                        var mainSheet = result.Tables[0];

                        Configurator? composite = null;
                        var childConfiguratorIds = new List<int>();
                        var displayOrders = new List<int>();
                        var isUpdate = false;

                        for (int rowIndex = 1; rowIndex < mainSheet.Rows.Count; rowIndex++)
                        {
                            var row = mainSheet.Rows[rowIndex];
                            if (rowIndex == 1) // Main configurator row
                            {
                                // Validate compositeId is not empty
                                var emptyValidation = _importValidationService.ValidateCellEmpty(row[0], DeksmartApplicationResource.CompositeIdRequired);
                                if (await _importProcessingService.HandleValidationFailureAsync(emptyValidation, validation))
                                    return (false, validation);
                                // Validate compositeId is integer
                                var (isValidInt, compositeId, intValidation) = _importValidationService.ValidateCellInteger(row[0]?.ToString(), DeksmartApplicationResource.CompositeIdInvalid);
                                if (await _importProcessingService.HandleValidationFailureAsync(intValidation, validation, !isValidInt))
                                    return (false, validation);
                                var existing = await _importProcessingService.GetConfiguratorAsync(compositeId);
                                var parseResult = _configuratorImportParser.ParseConfigurator(compositeId, existing, row, validation);
                                composite = parseResult.configurator;
                                isUpdate = parseResult.isUpdated;
                                
                                // Capture original composite state before modification
                                var wasNormal = isUpdate && existing != null && !existing.IsComposite;
                                composite.IsComposite = true;
                                
                                // Handle normal→composite conversion: cleanup normal configurator data
                                if (wasNormal)
                                {
                                    _importProcessingService.CleanupNormalConfiguratorData(composite);
                                }
                                
                                if (validation.HasErrors)
                                    return (false, validation);
                            }

                            if (composite != null) // Child configurator row
                            {
                                var childId = Convert.ToInt32(row[6]);
                                var order = Convert.ToInt32(row[7]);
                                childConfiguratorIds.Add(childId);
                                displayOrders.Add(order);
                            }
                        }
                        if (composite != null)
                        {
                            if (isUpdate)
                            {
                                // For updates, clear all existing child configurators first
                                _childConfiguratorDao.RemoveRange(composite.ChildConfigurators.ToList());
                                composite.ChildConfigurators.Clear();
                            }

                            // Add all child configurators from the import
                            for (int i = 0; i < childConfiguratorIds.Count; i++)
                            {
                                var childId = childConfiguratorIds[i];
                                var order = displayOrders[i];
                                composite.ChildConfigurators.Add(new ChildConfigurator
                                {
                                    CompositeId = composite.Id,
                                    ConfiguratorId = childId,
                                    DisplayOrder = order
                                });
                            }
                            await _importProcessingService.SaveConfiguratorAsync(composite, isUpdate);
                        }
                    }
                    await _importProcessingService.CommitTransactionAsync();
                    return (true, validation);
                }
                catch (Exception ex)
                {
                    await _importProcessingService.RollbackTransactionAsync();
                    validation.AddError(string.Format(DeksmartApplicationResource.ImportError, ex.Message));
                    return (false, validation);
                }
            }
        }

    }
}
