<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Description>Core package pro api eshopu</Description>
    <IsPackable>true</IsPackable>
    <PackageProjectUrl>https://gitlab.dek.cz/aspnet/package-eshop/dek-eshop-apicore</PackageProjectUrl>
    <IsTestProject>false</IsTestProject> <!-- Prevent run tests If "dotnet test" command runs from root dir (.sln) -->
  </PropertyGroup>

    <ItemGroup>
        <None Include="../readme.md" />
        <None Include="../.gitlab-ci.yml" />
        <EmbeddedResource Include="Static/swagger-ui-inject.js" />

        <PackageReference Include="MailKit" Version="4.12.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
        <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.5.4" />
        <PackageReference Include="MiniProfiler.EntityFrameworkCore" Version="4.5.4" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="8.1.1" />
        <PackageReference Include="Swashbuckle.AspNetCore.ReDoc" Version="8.1.1" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.2" />
        <PackageReference Include="FluentValidation" Version="12.0.0" />
    </ItemGroup>

</Project>
