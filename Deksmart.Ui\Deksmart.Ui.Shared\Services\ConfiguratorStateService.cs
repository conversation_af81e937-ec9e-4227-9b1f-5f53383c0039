using Deksmart.Shared.Dto;
using Deksmart.Shared.Enum;
using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Services
{
    public class ConfiguratorStateService : IConfiguratorStateService
    {
        public T GetConfiguratorState<T>(ConfiguratorWrapper configuratorWrapper, List<IConfiguratorGridService> childServices) where T : ConfiguratorStateDto, new()
        {
            var configuratorState = new T
            {
                ConfiguratorId = configuratorWrapper.Id,
                FieldValues = GetFieldValues(configuratorWrapper),
                SelectedProducts = GetSelectedProducts(configuratorWrapper),
                CategoryStates = GetCategoryStates(configuratorWrapper),
                TabTitle = configuratorWrapper.TabTitle,
                TabOrder = configuratorWrapper.TabOrder
            };

            if (configuratorWrapper.IsComposite)
            {
                var childStates = new List<ChildConfiguratorStateDto>();
                for (int i = 0; i < childServices.Count; i++)
                {
                    var service = childServices[i];
                    if (service.ConfiguratorWrapper != null)
                    {
                        var childState = new ChildConfiguratorStateDto(
                            service.ConfiguratorWrapper.Id,
                            GetFieldValues(service.ConfiguratorWrapper),
                            GetSelectedProducts(service.ConfiguratorWrapper),
                            service.ConfiguratorWrapper.TabOrder,
                            service.ConfiguratorWrapper.TabTitle);

                        // Add category states for child configurator
                        childState.CategoryStates = GetCategoryStates(service.ConfiguratorWrapper);

                        childStates.Add(childState);
                    }
                }

                configuratorState.ChildConfiguratorStates = childStates;
            }

            return configuratorState;
        }

        public CompositeConfiguratorStateDto GetCompositeConfiguratorState(ConfiguratorWrapper activeChildWrapper, IConfiguratorGridService mainConfiguratorService)
        {
            var compositeState = new CompositeConfiguratorStateDto(mainConfiguratorService.ConfiguratorWrapper!.Id);

            // Set the current child as the active child
            compositeState.ActiveChildState = GetConfiguratorState<ConfiguratorStateDto>(activeChildWrapper, new List<IConfiguratorGridService>());

            // Get products from other children (excluding the active one)
            // Use both Id and TabOrder to properly identify the unique active child instance
            var otherChildServices = mainConfiguratorService.ChildServices
                .Where(cs => cs.ConfiguratorWrapper?.Id != activeChildWrapper.Id || 
                            cs.ConfiguratorWrapper?.TabOrder != activeChildWrapper.TabOrder)
                .ToList();
            
            // Debug logging to help identify potential filtering issues
            Console.WriteLine($"[DEBUG] Active child: Id={activeChildWrapper.Id}, TabOrder={activeChildWrapper.TabOrder}");
            Console.WriteLine($"[DEBUG] Total child services: {mainConfiguratorService.ChildServices.Count}");
            
            // Log all child services before filtering
            Console.WriteLine($"[DEBUG] All child services:");
            foreach (var service in mainConfiguratorService.ChildServices)
            {
                Console.WriteLine($"[DEBUG]   Service: Id={service.ConfiguratorWrapper?.Id}, TabOrder={service.ConfiguratorWrapper?.TabOrder}");
            }
            
            Console.WriteLine($"[DEBUG] Other child services after filtering: {otherChildServices.Count}");
            foreach (var service in otherChildServices)
            {
                Console.WriteLine($"[DEBUG]   Filtered service: Id={service.ConfiguratorWrapper?.Id}, TabOrder={service.ConfiguratorWrapper?.TabOrder}");
            }
            
            compositeState.OtherChildProducts = GetAllChildProducts(otherChildServices);

            return compositeState;
        }

        public List<ClientFieldValueDto> GetFieldValues(ConfiguratorWrapper configuratorWrapper)
        {
            return configuratorWrapper.FieldCategories
                            .SelectMany(category => category.AllFields)
                            .Select(GetFieldValueDataContract)
                            .ToList();
        }

        public List<ClientProductValueDto> GetSelectedProducts(ConfiguratorWrapper configuratorWrapper)
        {
            return configuratorWrapper.ConfiguratorCompositions!.SelectMany(p => p.SelectedProducts)
                                .Select(p => new ClientProductValueDto { ProductId = p.Id, Amount = p.IsPackageAmountDirty ? p.PackageQuantity : null }).ToList();
        }

        public List<CategoryStateDto> GetCategoryStates(ConfiguratorWrapper configuratorWrapper)
        {
            return configuratorWrapper.FieldCategories
                .Where(c => c.CollapseState != CategoryCollapseStateDto.NotCollapsible) // Only include collapsible categories
                .Select(c => new CategoryStateDto
                {
                    CategoryId = c.Category.Id,
                    CollapseState = c.Category.CollapseState
                })
                .ToList();
        }

        /// <summary>
        /// Gets product data from all child configurators for composite product aggregation.
        /// Collects selected products with their essential information from each child service.
        /// </summary>
        /// <param name="childServices">List of child configurator services</param>
        /// <returns>List of child product states for aggregation</returns>
        public List<ChildProductStateDto> GetAllChildProducts(List<IConfiguratorGridService> childServices)
        {
            var allChildProducts = new List<ChildProductStateDto>();

            foreach (var childService in childServices)
            {
                if (childService.ConfiguratorWrapper?.ConfiguratorCompositions?.Any() == true)
                {
                    var childProductState = new ChildProductStateDto
                    {
                        ConfiguratorId = childService.ConfiguratorWrapper.Id,
                        TabOrder = childService.ConfiguratorWrapper.TabOrder,
                        SelectedProducts = GetProductsForAggregation(childService.ConfiguratorWrapper)
                    };

                    allChildProducts.Add(childProductState);
                }
            }

            return allChildProducts;
        }

        /// <summary>
        /// Extracts product information needed for aggregation from a configurator wrapper.
        /// Creates ProductForAggregationDto objects with all necessary data for server-side aggregation.
        /// </summary>
        /// <param name="configuratorWrapper">The configurator wrapper to extract products from</param>
        /// <returns>List of products formatted for aggregation</returns>
        private List<ProductForAggregationDto> GetProductsForAggregation(ConfiguratorWrapper configuratorWrapper)
        {
            var productsForAggregation = new List<ProductForAggregationDto>();

            if (configuratorWrapper.ConfiguratorCompositions == null)
                return productsForAggregation;

            foreach (var composition in configuratorWrapper.ConfiguratorCompositions)
            {
                foreach (var product in composition.SelectedProducts)
                {
                    var productForAggregation = new ProductForAggregationDto
                    {
                        ProductCode = product.ProductCode,
                        CalculatedAmount = product.CalculatedAmountDecimal,
                        UserAmount = product.IsPackageAmountDirty ? product.PackageQuantity : null,
                        ProductUnit = product.ProductUnit,
                        Title = product.Title,
                        CompositionTitle = composition.Title,
                        CompositionOrder = composition.Order,
                        ProductOrder = product.Order
                    };

                    productsForAggregation.Add(productForAggregation);
                }
            }

            return productsForAggregation;
        }

        public Dictionary<string, bool> SaveFieldStates(ConfiguratorWrapper? configuratorWrapper)
        {
            if (configuratorWrapper == null)
                return new Dictionary<string, bool>();

            var fieldStates = new Dictionary<string, bool>();

            foreach (var category in configuratorWrapper.FieldCategories)
            {
                foreach (var field in category.AllFields)
                {
                    // Use the field's Ident as the key since it's unique and persists across reloads
                    // We only need to track if a field should be marked as dirty
                    fieldStates[field.Ident] = field.IsDirty || field.IsPendingValidation;
                }
            }

            return fieldStates;
        }

        public void RestoreFieldStates(ConfiguratorWrapper? configuratorWrapper, Dictionary<string, bool> fieldStates)
        {
            if (configuratorWrapper == null)
                return;

            foreach (var category in configuratorWrapper.FieldCategories)
            {
                foreach (var field in category.AllFields)
                {
                    // Restore saved state if available
                    if (fieldStates.TryGetValue(field.Ident, out var shouldBeDirty))
                    {
                        field.IsDirty = shouldBeDirty;
                    }

                    // Always reset pending validation state
                    field.IsPendingValidation = false;
                }
            }
        }

        public ClientFieldValueDto GetFieldValueDataContract(ConfiguratorFieldWrapper wrapper)
        {
            var result = new ClientFieldValueDto() { FieldId = wrapper.Field.Id };

            switch (wrapper)
            {
                case NumericFieldWrapper numericField:
                    result.Value = numericField.Value;
                    break;
                case CheckboxFieldWrapper checkboxField:
                    result.Value = checkboxField.Value == true ? 1 : 0;
                    break;
                case IdFieldWrapper selectBoxField:
                    if (selectBoxField.Value != null)
                        result.Value = selectBoxField.FieldValues.Single(d => d.Id == selectBoxField.Value).Value.NumericValue;
                    break;
                default:
                    break;
            }

            return result;
        }
    }
}