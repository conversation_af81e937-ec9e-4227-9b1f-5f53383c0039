using Deksmart.Domain.Entity.Db.Interface;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Base
{
    public abstract class IntIdDaoBase<TEntity> : IdDaoBase<TEntity, int>, IIntIdRepositoryBase<TEntity>
            where TEntity : class, IIntIdEntity
    {
        protected IntIdDaoBase(ConfiguratorContext context) : base(context)
        {
        }
    }
}
