using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Service.Base;

namespace Deksmart.Domain.Service
{
    /// <summary>
    /// Service responsible for unit conversion operations including normalization, 
    /// compatibility checks, and conversion between different unit types.
    /// </summary>
    public class UnitConversionService : IUnitConversionService
    {
        private readonly IValidationService _validationService;

        public UnitConversionService(IValidationService validationService)
        {
            _validationService = validationService;
        }

        public string NormalizeUnitString(string? unit)
        {
            if (string.IsNullOrWhiteSpace(unit)) return string.Empty;

            return unit
                .Trim()
                .ToLowerInvariant()
                .Replace(" ", "")
                .Replace("-", "")
                .Replace("_", "")
                .Replace(".", "")
                .Replace(",", "");
        }

        public bool IsMeterUnitVariation(string normalizedUnit1, string normalizedUnit2)
        {
            // Common variations for meter units
            var meterVariations = new[]
            {
                "m", "bm", "metr", "metrů",
                "m2", "m²", "bm2", "bm²",
                "m3", "m³", "bm3", "bm³", "kubík", "kubik"
            };

            // Check if both units are meter variations
            bool isUnit1Meter = meterVariations.Contains(normalizedUnit1);
            bool isUnit2Meter = meterVariations.Contains(normalizedUnit2);

            // If both are meter variations, they're compatible
            return isUnit1Meter && isUnit2Meter;
        }

        public (bool isUsingSalesUnit, bool isUsingPackageUnit) DetermineUnitType(string productUnit, EshopProductUnit eshopUnit)
        {
            // Normalize unit strings for comparison
            var normalizedProductUnit = NormalizeUnitString(productUnit);
            var normalizedPackageUnit = NormalizeUnitString(eshopUnit.UnitPackage);
            var normalizedSalesUnit = NormalizeUnitString(eshopUnit.UnitSales);

            // Determine if we're using sales or package units based on current ProductUnit
            bool isUsingSalesUnit = normalizedProductUnit == normalizedSalesUnit;
            bool isUsingPackageUnit = normalizedProductUnit == normalizedPackageUnit;

            // Special case handling for common unit variations
            if (!isUsingSalesUnit && !isUsingPackageUnit)
            {
                // Handle meter variations
                if (IsMeterUnitVariation(normalizedProductUnit, normalizedSalesUnit))
                {
                    isUsingSalesUnit = true;
                }
                else if (IsMeterUnitVariation(normalizedProductUnit, normalizedPackageUnit))
                {
                    isUsingPackageUnit = true;
                }
            }

            return (isUsingSalesUnit, isUsingPackageUnit);
        }

        public decimal CalculatePackageQuantity(decimal amount, bool isUsingSalesUnit, decimal unitsInPackage)
        {
            // Calculate package quantity based on unit type
            return isUsingSalesUnit 
                ? Math.Ceiling(amount / unitsInPackage) 
                : amount;
        }

        public ValidationResult ValidateUnitCompatibility(string productCode, string productUnit, EshopProductUnit eshopUnit)
        {
            var validation = _validationService.CreateValidation();
            
            var (isUsingSalesUnit, isUsingPackageUnit) = DetermineUnitType(productUnit, eshopUnit);
            
            if (!isUsingSalesUnit && !isUsingPackageUnit)
            {
                validation.AddError(string.Format(DeksmartDomainResource.InvalidProductUnit, 
                    productCode, productUnit, eshopUnit.UnitSales, eshopUnit.UnitPackage));
            }

            return validation;
        }
    }
}
