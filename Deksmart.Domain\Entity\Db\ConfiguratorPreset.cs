﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Represents a saved state of a configurator, allowing users to store and reuse specific configurations.
    /// Captures selected field values, product combinations, and category states for efficient retrieval and application of previously defined configurations.
    /// </summary>
    [Table("configurator_preset", Schema = "dbo")]
    [Index("ConfiguratorId", Name = "idx_configurator_preset_configurator_id")]
    public partial class ConfiguratorPreset : IIdEntity<Guid>
    {
        [Key]
        [Column("id")]
        public Guid Id { get; set; }

        [Column("catalog_id")]
        [StringLength(20)]
        public string? CatalogId { get; set; }

        [Column("configurator_id")]
        public int ConfiguratorId { get; set; }

        [ForeignKey("ConfiguratorId")]
        public virtual Configurator Configurator { get; set; } = null!;

        public virtual ICollection<ConfiguratorFieldCombination> ConfiguratorFieldCombinations { get; set; } = new List<ConfiguratorFieldCombination>();

        public virtual ICollection<ConfiguratorProductCombination> ConfiguratorProductCombinations { get; set; } = new List<ConfiguratorProductCombination>();

        public virtual ICollection<PresetConfiguratorCategoryState> CategoryStates { get; set; } = new List<PresetConfiguratorCategoryState>();
        
        public virtual ICollection<ChildPreset> ChildPresets { get; set; } = new List<ChildPreset>();
    }
}