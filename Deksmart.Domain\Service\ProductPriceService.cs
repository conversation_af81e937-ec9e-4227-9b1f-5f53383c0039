using Microsoft.Extensions.Logging;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Service.Base;

namespace Deksmart.Domain.Service
{
    public interface IProductPriceService
    {
        /// <summary>
        /// Calculates prices for the given products using the provided enrichment data
        /// </summary>
        /// <param name="products">The products to calculate prices for</param>
        /// <param name="enrichmentData">The enrichment data to use for calculation</param>
        /// <returns>A validation result</returns>
        ValidationResult CalculatePrices(IEnumerable<ConfiguratorProduct> products, IEnumerable<ProductPriceData> enrichmentData);

        /// <summary>
        /// Sets the total prices on the configurator based on the given products
        /// </summary>
        /// <param name="configurator">The configurator to set prices on</param>
        /// <param name="products">The products to calculate prices from</param>
        void SetConfiguratorTotalPrices(Configurator configurator, IEnumerable<ConfiguratorProduct> products);

        /// <summary>
        /// Calculates prices for the selected products
        /// </summary>
        /// <param name="selectedProducts">The selected products</param>
        /// <param name="currentProductCode">The current product code</param>
        /// <param name="enrichmentData">The enrichment data to use for calculation</param>
        /// <returns>The calculation result and validation result</returns>
        (ProductPriceCalculationResponse? result, ValidationResult validation) CalculateSelectedProductPrices(
            List<SelectedProduct> selectedProducts,
            string currentProductCode,
            IEnumerable<ProductPriceData> enrichmentData);
    }

    public class ProductPriceService : IProductPriceService
    {
        private readonly ILogger<ProductPriceService> _logger;
        private readonly IValidationService _validationService;

        public ProductPriceService(ILogger<ProductPriceService> logger, IValidationService validationService)
        {
            _logger = logger;
            _validationService = validationService;
        }

        public ValidationResult CalculatePrices(IEnumerable<ConfiguratorProduct> products, IEnumerable<ProductPriceData> enrichmentData)
        {
            var validation = _validationService.CreateValidation();

            if (products == null || !products.Any()) return validation;

            var enrichmentByCode = enrichmentData.ToLookup(x => x.ProductCode);

            foreach (var product in products)
            {
                // Clear any previous validation error
                product.ValidationError = null;
                
                var productEnrichmentData = enrichmentByCode[product.ProductCode];
                if (productEnrichmentData.Any())
                {
                    // Take the first matching enrichment data since they should all have the same pricing/unit info
                    var data = productEnrichmentData.First();
                    var calculation = CalculateSingleProductPrices(product.ProductCode, product.CalculatedAmount, product.UserAmount, product.ProductUnit, data.Pricing, data.Unit, data.ConvertedAmount);
                    if (!calculation.Validation.HasErrors)
                    {
                        calculation.ApplyTo(product);
                    }
                    else
                    {
                        // Use soft validation - set error on product instead of failing globally
                        product.ValidationError = calculation.Validation.GetErrors();
                        _logger.LogWarning("Price calculation failed for product {ProductCode}: {Errors}", product.ProductCode, product.ValidationError);
                    }
                }
                else
                {
                    // Use soft validation - set error on product instead of failing globally
                    product.ValidationError = string.Format(DeksmartDomainResource.NoEnrichmentDataFound, product.ProductCode);
                    _logger.LogWarning("No enrichment data found for product {ProductCode}", product.ProductCode);
                }
            }

            return validation;
        }

        public void SetConfiguratorTotalPrices(Configurator configurator, IEnumerable<ConfiguratorProduct> products)
        {
            // Filter out products with validation errors - only include products with valid pricing
            var validProducts = products?.Where(p => !p.HasValidationError) ?? Enumerable.Empty<ConfiguratorProduct>();
            
            // If there are no valid products, set all prices to 0
            if (!validProducts.Any())
            {
                configurator.TotalPriceNoVat = 0;
                configurator.TotalPriceVat = 0;
                configurator.TotalVat = 0;
                return;
            }

            var totalPriceNoVat = validProducts.Sum(p => p.PriceNoVat);
            var totalPriceVat = validProducts.Sum(p => p.PriceVat);
            var totalVat = validProducts.Sum(p => p.Vat);

            configurator.TotalPriceNoVat = totalPriceNoVat;
            configurator.TotalPriceVat = totalPriceVat;
            configurator.TotalVat = totalVat;
        }

        public (ProductPriceCalculationResponse? result, ValidationResult validation) CalculateSelectedProductPrices(
            List<SelectedProduct> selectedProducts,
            string currentProductCode,
            IEnumerable<ProductPriceData> enrichmentData)
        {
            var validation = _validationService.CreateValidation();

            // If there are no selected products, return a response with zero prices
            if (selectedProducts == null || selectedProducts.Count == 0)
            {
                return (new ProductPriceCalculationResponse
                {
                    PriceVat = 0,
                    TotalPriceNoVat = 0,
                    TotalPriceVat = 0,
                    TotalVat = 0
                }, validation);
            }

            // Calculate prices for all selected products
            var products = selectedProducts.Select(p => new ConfiguratorProduct
            {
                ProductCode = p.ProductCode,
                CalculatedAmount = p.Amount,
                ProductUnit = p.Unit
            }).ToList();

            var validationResult = CalculatePrices(products, enrichmentData);
            if (validationResult.HasErrors)
            {
                validation.AddError(validationResult.GetErrors());
                return (null, validation);
            }

            // Calculate total prices
            var (totalPriceNoVat, totalPriceVat, totalVat) = CalculateTotalPrices(products);

            // Find current product's prices
            var currentProduct = products.FirstOrDefault(p => p.ProductCode == currentProductCode);
            if (currentProduct == null)
            {
                // If the current product code is not found, return an error
                validation.AddError(string.Format(DeksmartDomainResource.ProductNotFoundInSelection, currentProductCode));
                return (null, validation);
            }

            return (new ProductPriceCalculationResponse
            {
                PriceVat = currentProduct.PriceVat,
                TotalPriceNoVat = totalPriceNoVat,
                TotalPriceVat = totalPriceVat,
                TotalVat = totalVat
            }, validation);
        }

        /// <summary>
        /// Calculates product prices with proper package rounding logic.
        /// </summary>
        /// <remarks>
        /// LESSON LEARNED: The UnitsInPackage divide-then-multiply pattern is NOT redundant!
        /// 
        /// Both API and manual conversion paths use the same business logic:
        /// 1. Convert sales units to whole packages using Math.Ceiling(amount / UnitsInPackage)
        /// 2. Price the whole packages using packageCount * PriceNoVatSales * UnitsInPackage
        /// 
        /// This ensures customers pay for complete packages even when they need fractional amounts.
        /// Removing this pattern would cause incorrect pricing and break business requirements.
        /// 
        /// Tests verify this logic works correctly for both conversion approaches.
        /// </remarks>
        private static ProductPriceCalculation CalculateSingleProductPrices(
            string productCode,
            decimal calculatedAmount,
            decimal? userAmount,
            string productUnit,
            EshopProductPricing pricing,
            EshopProductUnit unit,
            decimal? convertedAmount = null)
        {
            // Single decision point: use API conversion or fallback to manual conversion
            if (convertedAmount.HasValue)
            {
                return CalculatePricesWithApiConversion(productCode, calculatedAmount, userAmount, convertedAmount.Value, pricing, unit);
            }
            else
            {
                return CalculatePricesWithManualConversion(productCode, calculatedAmount, userAmount, productUnit, pricing, unit);
            }
        }

        /// <summary>
        /// Calculates prices using API-provided unit conversion data.
        /// The API returns convertedAmount in sales units, which we convert to package quantities for pricing.
        /// </summary>
        /// <remarks>
        /// IMPORTANT: The divide-then-multiply pattern with UnitsInPackage is intentional and necessary.
        /// It handles package rounding logic where customers must buy whole packages.
        /// 
        /// Example: Customer needs 15.3 m² of material
        /// - Package contains 10 m² (UnitsInPackage = 10)
        /// - Math.Ceiling(15.3 / 10) = 2 packages required
        /// - Customer pays for 2 × 10 = 20 m² worth (not 15.3 m²)
        /// - Price: 2 packages × PriceNoVatSales × 10 UnitsInPackage
        /// 
        /// DO NOT "simplify" by removing the divide/multiply - it would break package rounding business logic!
        /// </remarks>
        private static ProductPriceCalculation CalculatePricesWithApiConversion(
            string productCode,
            decimal originalCalculatedAmount,
            decimal? userAmount,
            decimal convertedAmount,
            EshopProductPricing pricing,
            EshopProductUnit unit)
        {
            var validation = new ValidationResult();

            // API returns amount in sales units, convert to packages by dividing by UnitsInPackage
            // This ensures customers pay for whole packages (package rounding logic)
            var packageQuantity = Math.Ceiling(convertedAmount / unit.UnitsInPackage);

            return CalculateFinalPrices(productCode, originalCalculatedAmount, userAmount, packageQuantity, pricing, unit, validation);
        }

        private static ProductPriceCalculation CalculatePricesWithManualConversion(
            string productCode,
            decimal calculatedAmount,
            decimal? userAmount,
            string productUnit,
            EshopProductPricing pricing,
            EshopProductUnit unit)
        {
            var validation = new ValidationResult();

            // Normalize unit strings for comparison
            var normalizedProductUnit = NormalizeUnitString(productUnit);
            var normalizedPackageUnit = NormalizeUnitString(unit.UnitPackage);
            var normalizedSalesUnit = NormalizeUnitString(unit.UnitSales);

            // Determine if we're using sales or package units based on current ProductUnit
            bool isUsingSalesUnit = normalizedProductUnit == normalizedSalesUnit;
            bool isUsingPackageUnit = normalizedProductUnit == normalizedPackageUnit;

            // Special case handling for common unit variations
            if (!isUsingSalesUnit && !isUsingPackageUnit)
            {
                // Handle meter variations
                if (IsMeterUnitVariation(normalizedProductUnit, normalizedSalesUnit) ||
                    IsMeterUnitVariation(normalizedProductUnit, normalizedPackageUnit))
                {
                    isUsingSalesUnit = normalizedProductUnit == normalizedSalesUnit;
                }
                else
                {
                    validation.AddError(string.Format(DeksmartDomainResource.InvalidProductUnit, productCode, productUnit, unit.UnitSales, unit.UnitPackage));
                    return new ProductPriceCalculation(0, 0, 0, 0, 0, null, null) { Validation = validation };
                }
            }

            // Calculate package quantity based on unit type
            var packageQuantity = isUsingSalesUnit 
                ? Math.Ceiling(calculatedAmount / unit.UnitsInPackage) 
                : calculatedAmount;

            return CalculateFinalPrices(productCode, calculatedAmount, userAmount, packageQuantity, pricing, unit, validation);
        }

        private static ProductPriceCalculation CalculateFinalPrices(
            string productCode,
            decimal originalCalculatedAmount,
            decimal? userAmount,
            decimal packageQuantity,
            EshopProductPricing pricing,
            EshopProductUnit unit,
            ValidationResult validation)
        {
            var amountToCalculate = packageQuantity;

            // If user amount is set and greater than calculated amount, use user amount
            if (userAmount.HasValue && userAmount.Value > amountToCalculate)
            {
                amountToCalculate = userAmount.Value;
            }
            else
            {
                userAmount = null;
            }

            if (amountToCalculate < 0)
            {
                validation.AddError(string.Format(DeksmartDomainResource.InvalidProductAmount, productCode, amountToCalculate));
                return new ProductPriceCalculation(0, 0, 0, 0, 0, null, null) { Validation = validation };
            }

            // CRITICAL: Package-based pricing calculation with UnitsInPackage multiplication
            // This completes the divide-then-multiply pattern for package rounding business logic
            // 
            // Flow: Sales Units → Packages (via Math.Ceiling) → Sales Units Equivalent (via UnitsInPackage)
            // Example: 15.3 m² → 2 packages → 20 m² pricing
            // 
            // The multiplication by UnitsInPackage ensures customers pay for the full package content,
            // not just the fractional amount they actually need.
            var totalPriceNoVat = Math.Round(pricing.PriceNoVatSales * amountToCalculate * unit.UnitsInPackage, 2);
            var totalVat = Math.Round(totalPriceNoVat * pricing.Vat / 100, 2);
            var totalPriceVat = Math.Round(totalPriceNoVat + totalVat, 2);

            return new ProductPriceCalculation(
                totalPriceNoVat,
                totalPriceVat,
                totalVat,
                Math.Round(pricing.PriceVatPackage, 2),
                packageQuantity,
                userAmount,
                unit.UnitPackage
            ) { Validation = validation };
        }

        private static (decimal TotalPriceNoVat, decimal TotalPriceVat, decimal TotalVat) CalculateTotalPrices(IEnumerable<ConfiguratorProduct> products)
        {
            if (products == null || !products.Any())
            {
                return (0, 0, 0);
            }

            var totalPriceNoVat = products.Sum(p => p.PriceNoVat);
            var totalPriceVat = products.Sum(p => p.PriceVat);
            var totalVat = products.Sum(p => p.Vat);

            return (totalPriceNoVat, totalPriceVat, totalVat);
        }

        private static bool IsMeterUnitVariation(string normalizedUnit1, string normalizedUnit2)
        {
            // Common variations for meter units
            var meterVariations = new[]
            {
                "m", "bm", "metr", "metrů",
                "m2", "m²", "bm2", "bm²",
                "m3", "m³", "bm3", "bm³", "kubík", "kubik"
            };

            // Check if both units are meter variations
            bool isUnit1Meter = meterVariations.Contains(normalizedUnit1);
            bool isUnit2Meter = meterVariations.Contains(normalizedUnit2);

            // If both are meter variations, they're compatible
            return isUnit1Meter && isUnit2Meter;
        }

        private static string NormalizeUnitString(string? unit)
        {
            if (string.IsNullOrWhiteSpace(unit)) return string.Empty;

            return unit
                .Trim()
                .ToLowerInvariant()
                .Replace(" ", "")
                .Replace("-", "")
                .Replace("_", "")
                .Replace(".", "")
                .Replace(",", "");
        }
    }
}