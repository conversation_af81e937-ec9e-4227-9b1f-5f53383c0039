.navbar-toggler {
    background-color: transparent;
    border: none;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
}

.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cpath stroke='rgba(255, 255, 255, 0.9)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
    background-size: 100% 100%;
}

.top-row {
    min-height: 3.5rem;
    background-color: white;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--light-border);
}

.container-fluid {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center; /* Center the brand text */
}

.nav-header {
    font-size: 1.1rem;
    font-weight: 600; /* Bold but not too heavy */
    color: var(--secondary-color); /* Use secondary color for section headers */
    letter-spacing: 0.5px; /* Slight letter spacing for better readability */
    padding: 0.5rem 1rem; /* Consistent padding */
    text-transform: uppercase; /* Uppercase for section headers */
}

.bi {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.bi-thick-dot-nav-menu {
    margin-left: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333333' class='bi bi-dot' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='4'/%3E%3C/svg%3E");
}

/* Active state icons */
.nav-item ::deep a.active .bi-thick-dot-nav-menu {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23c00c18' class='bi bi-dot' viewBox='0 0 16 16'%3E%3Ccircle cx='8' cy='8' r='4'/%3E%3C/svg%3E");
}

.nav-item {
    font-size: 0.9rem;
    padding-bottom: 0;
    white-space: normal; /* Allow text to wrap */
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2; /* Adjust line height to control spacing between rows */
}

.nav-item:first-of-type {
    padding-top: 0;
}

.nav-item:last-of-type {
    padding-bottom: 0;
}

.nav-item ::deep a {
    color: var(--secondary-color); /* Changed from white to dark color */
    height: auto;
    display: flex;
    align-items: center;
    line-height: 1.2;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s ease-in-out;
    opacity: 0.9;
}

@media (max-width: 640px) {
    .nav-item ::deep a {
        padding: 1rem 1.25rem; /* Larger touch targets on mobile */
        font-size: 1rem; /* Slightly larger font for better readability */
    }
}

.nav-item ::deep a.active {
    background-color: var(--active-bg);
    color: var(--primary-color);
    opacity: 1;
    border-left: 3px solid var(--primary-color);
    padding-left: calc(1rem - 3px);
    font-weight: 500;
}

.nav-item ::deep a:hover {
    background-color: var(--hover-bg);
    color: var(--primary-color);
    opacity: 1;
}

/* Mobile menu styles */
@media (max-width: 640px) {
    /* Active link styling */
    .nav-item ::deep a.active {
        padding-left: calc(1.25rem - 3px); /* Adjust for the larger padding on mobile */
        background-color: rgba(192, 12, 24, 0.05); /* Subtle background for active items */
    }

    /* Menu structure */
    .nav-scrollable {
        display: block;
        background-color: white;
        height: 100%; /* Fill available parent height */
        overflow-y: auto;
        width: 100%;
        padding-top: 0.5rem; /* Add a little padding at the top */
        padding-bottom: 0.5rem; /* Add padding at bottom for better UX */
    }

    .container-fluid {
        padding: 0;
        width: 100%;
    }

    .nav-item {
        border-bottom: 1px solid var(--light-border);
    }

    .nav-item:last-child {
        border-bottom: none;
    }

    /* No extra margin needed for first item now */
    .nav-item:first-child {
        margin-top: 0;
    }
}

@media (min-width: 641px) {
    .navbar-toggler {
        display: none;
    }

    .collapse {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }

    .nav-scrollable {
        /* Allow sidebar to scroll for tall menus */
        height: 100%; /* Fill available parent height */
        overflow-y: auto;
        background-color: white;
        border-right: 1px solid var(--light-border);
        padding-top: 0.5rem; /* Add a little padding at the top */
        padding-bottom: 0.5rem; /* Add padding at bottom for better UX */
    }
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 0;
}

.nav-link span[aria-hidden="true"] {
    flex-shrink: 0;
}

.nav-link .configurator-title {
    overflow: visible;
    min-width: 0;
    flex: 1 1 0%;
    word-break: break-word;
}
