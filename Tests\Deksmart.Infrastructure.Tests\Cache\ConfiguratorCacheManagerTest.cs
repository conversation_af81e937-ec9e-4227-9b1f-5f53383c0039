using System.Text;
using System.Text.Json;
using DEK.Eshop.ApiCore.Cache;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Cache;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Deksmart.Infrastructure.Tests.Cache
{
    public class ConfiguratorCacheManagerTest
    {
        private readonly Mock<IDistributedCache> _distributedCacheMock;
        private readonly Mock<ILogger<ConfiguratorCacheManager>> _loggerMock;
        private readonly CacheManager _cacheManager;
        private readonly ConfiguratorCacheManager _configuratorCacheManager;

        public ConfiguratorCacheManagerTest()
        {
            _distributedCacheMock = new Mock<IDistributedCache>();
            _loggerMock = new Mock<ILogger<ConfiguratorCacheManager>>();
            _cacheManager = new CacheManager(_distributedCacheMock.Object);
            _configuratorCacheManager = new ConfiguratorCacheManager(_cacheManager, _loggerMock.Object);
        }

        #region Helper Methods

        private byte[] SerializeToBytes<T>(T obj)
        {
            var json = JsonSerializer.Serialize(obj);
            return Encoding.UTF8.GetBytes(json);
        }

        private void SetupCacheGet<T>(string key, T value)
        {
            var serialized = SerializeToBytes(value);
            var fullKey = key + "-" + typeof(T).Name;
            _distributedCacheMock.Setup(x => x.GetAsync(fullKey, It.IsAny<CancellationToken>()))
                .ReturnsAsync(serialized);
        }

        private void SetupCacheGetNull<T>(string key)
        {
            var fullKey = key + "-" + typeof(T).Name;
            _distributedCacheMock.Setup(x => x.GetAsync(fullKey, It.IsAny<CancellationToken>()))
                .ReturnsAsync((byte[]?)null);
        }

        private void SetupCacheGetException<T>(string key, Exception exception)
        {
            var fullKey = key + "-" + typeof(T).Name;
            _distributedCacheMock.Setup(x => x.GetAsync(fullKey, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);
        }

        #endregion

        #region GetOrAddAsync Tests

        [Fact]
        public async Task GetOrAddAsync_CacheHit_ShouldReturnCachedData()
        {
            // Arrange
            var testProduct = new ConfiguratorProduct { Id = 1, Title = "Test Product" };
            SetupCacheGet("C:CP:1", testProduct);

            // Act
            var result = await _configuratorCacheManager.GetOrAddAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult(new ConfiguratorProduct()));

            // Assert
            Assert.Equal(testProduct.Id, result?.Id);
            Assert.Equal(testProduct.Title, result?.Title);
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetOrAddAsync_CacheMiss_ShouldLoadFromDbAndSaveToCache()
        {
            // Arrange
            var dbProduct = new ConfiguratorProduct { Id = 1, Title = "DB Product" };
            SetupCacheGetNull<ConfiguratorProduct>("C:CP:1");

            // Act
            var result = await _configuratorCacheManager.GetOrAddAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult(dbProduct));

            // Assert
            Assert.Equal(dbProduct.Id, result?.Id);
            Assert.Equal(dbProduct.Title, result?.Title);
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetOrAddAsync_CacheError_ShouldLoadFromDbButNotSaveToCache()
        {
            // Arrange
            var dbProduct = new ConfiguratorProduct { Id = 1, Title = "DB Product" };
            var cacheError = new Exception("Cache is down");
            SetupCacheGetException<ConfiguratorProduct>("C:CP:1", cacheError);

            // Act
            var result = await _configuratorCacheManager.GetOrAddAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult(dbProduct));

            // Assert
            Assert.Equal(dbProduct.Id, result?.Id);
            Assert.Equal(dbProduct.Title, result?.Title);
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Never);
            _loggerMock.Verify(
                x => x.Log(LogLevel.Error, It.IsAny<EventId>(), It.IsAny<It.IsAnyType>(), It.IsAny<Exception>(), It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetOrAddAsync_DbReturnsNull_ShouldReturnDefaultAndLogWarning()
        {
            // Arrange
            SetupCacheGetNull<ConfiguratorProduct>("C:CP:1");

            // Act
            var result = await _configuratorCacheManager.GetOrAddAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult<ConfiguratorProduct?>(null));

            // Assert
            Assert.Null(result);
            _loggerMock.Verify(
                x => x.Log(LogLevel.Warning, It.IsAny<EventId>(), It.IsAny<It.IsAnyType>(), It.IsAny<Exception?>(), It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion

        #region GetOrAddListAsync Tests

        [Fact]
        public async Task GetOrAddListAsync_CacheHit_ShouldReturnCachedList()
        {
            // Arrange
            var testProducts = new List<ConfiguratorProduct> { new() { Id = 1, Title = "Test Product" } };
            SetupCacheGet("CL:CP:1", testProducts);

            // Act
            var result = await _configuratorCacheManager.GetOrAddListAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult(new List<ConfiguratorProduct>()));

            // Assert
            Assert.Single(result);
            Assert.Equal(testProducts[0].Id, result[0].Id);
            Assert.Equal(testProducts[0].Title, result[0].Title);
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetOrAddListAsync_CacheError_ShouldLoadFromDbButNotSaveToCache()
        {
            // Arrange
            var dbProducts = new List<ConfiguratorProduct> { new() { Id = 1, Title = "DB Product" } };
            var cacheError = new Exception("Cache is down");
            SetupCacheGetException<List<ConfiguratorProduct>>("CL:CP:1", cacheError);

            // Act
            var result = await _configuratorCacheManager.GetOrAddListAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult(dbProducts));

            // Assert
            Assert.Single(result);
            Assert.Equal(dbProducts[0].Id, result[0].Id);
            Assert.Equal(dbProducts[0].Title, result[0].Title);
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetOrAddListAsync_DbReturnsNull_ShouldReturnEmptyList()
        {
            // Arrange
            SetupCacheGetNull<List<ConfiguratorProduct>>("CL:CP:1");

            // Act
            var result = await _configuratorCacheManager.GetOrAddListAsync<ConfiguratorProduct, int>(1, _ => Task.FromResult<List<ConfiguratorProduct>?>(null));

            // Assert
            Assert.Empty(result);
        }

        #endregion

        #region GetOrAddListByItemAndSaveAsync Tests

        [Fact]
        public async Task GetOrAddListByItemAndSaveAsync_EmptyInput_ShouldReturnEmptyList()
        {
            // Act
            var result = await _configuratorCacheManager.GetOrAddListByItemAndSaveAsync<ConfiguratorProduct, int>(
                new List<int>(), _ => Task.FromResult(new List<ConfiguratorProduct>()));

            // Assert
            Assert.Empty(result);
            _distributedCacheMock.Verify(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetOrAddListByItemAndSaveAsync_NullInput_ShouldReturnEmptyList()
        {
            // Act
            var result = await _configuratorCacheManager.GetOrAddListByItemAndSaveAsync<ConfiguratorProduct, int>(
                null!, _ => Task.FromResult(new List<ConfiguratorProduct>()));

            // Assert
            Assert.Empty(result);
            _distributedCacheMock.Verify(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetOrAddListByItemAndSaveAsync_MixedCacheHitsAndMisses_ShouldPreserveOrder()
        {
            // Arrange
            var ids = new List<int> { 1, 2, 3 };
            var cachedProduct1 = new ConfiguratorProduct { Id = 1, Title = "Cached 1" };
            var cachedProduct3 = new ConfiguratorProduct { Id = 3, Title = "Cached 3" };
            var dbProduct2 = new ConfiguratorProduct { Id = 2, Title = "DB 2" };

            // Set up explicit cache responses for each key
            var serialized1 = SerializeToBytes(cachedProduct1);
            var serialized3 = SerializeToBytes(cachedProduct3);
            
            _distributedCacheMock.Setup(x => x.GetAsync("C:CP:1-ConfiguratorProduct", It.IsAny<CancellationToken>()))
                .ReturnsAsync(serialized1);
            _distributedCacheMock.Setup(x => x.GetAsync("C:CP:2-ConfiguratorProduct", It.IsAny<CancellationToken>()))
                .ReturnsAsync((byte[]?)null);
            _distributedCacheMock.Setup(x => x.GetAsync("C:CP:3-ConfiguratorProduct", It.IsAny<CancellationToken>()))
                .ReturnsAsync(serialized3);

            // Act
            var result = await _configuratorCacheManager.GetOrAddListByItemAndSaveAsync<ConfiguratorProduct, int>(
                ids, missingIds => 
                {
                    // Should only be called with missing ID 2
                    Assert.Single(missingIds);
                    Assert.Equal(2, missingIds[0]);
                    return Task.FromResult(new List<ConfiguratorProduct> { dbProduct2 });
                });

            // Assert
            Assert.Equal(3, result.Count);
            Assert.Equal("Cached 1", result[0].Title);
            Assert.Equal("DB 2", result[1].Title);
            Assert.Equal("Cached 3", result[2].Title);
            
            // Should save the missing item
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetOrAddListByItemAndSaveAsync_CacheErrorOnFirstItem_ShouldEarlyBreakAndLoadAll()
        {
            // Arrange
            var ids = new List<int> { 1, 2, 3 };
            var cacheError = new Exception("Cache is down");
            var dbProducts = new List<ConfiguratorProduct>
            {
                new() { Id = 1, Title = "DB 1" },
                new() { Id = 2, Title = "DB 2" },
                new() { Id = 3, Title = "DB 3" }
            };

            SetupCacheGetException<ConfiguratorProduct>("C:CP:1", cacheError);

            // Act
            var result = await _configuratorCacheManager.GetOrAddListByItemAndSaveAsync<ConfiguratorProduct, int>(
                ids, missingIds => 
                {
                    Assert.Equal(3, missingIds.Count); // All IDs should be in missing after early break
                    return Task.FromResult(dbProducts);
                });

            // Assert
            Assert.Equal(3, result.Count);
            Assert.Equal("DB 1", result[0].Title);
            Assert.Equal("DB 2", result[1].Title);
            Assert.Equal("DB 3", result[2].Title);
            
            // Should only check cache for first item due to early break
            _distributedCacheMock.Verify(x => x.GetAsync("C:CP:1-ConfiguratorProduct", It.IsAny<CancellationToken>()), Times.Once);
            
            // Should not save anything due to cache error
            _distributedCacheMock.Verify(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetOrAddListByItemAndSaveAsync_DbReturnsFewerItemsThanExpected_ShouldThrowException()
        {
            // Arrange
            var ids = new List<int> { 1, 2, 3 };
            var dbProducts = new List<ConfiguratorProduct> { new() { Id = 1, Title = "DB 1" } }; // Only 1 item instead of 3

            SetupCacheGetNull<ConfiguratorProduct>("C:CP:1");
            SetupCacheGetNull<ConfiguratorProduct>("C:CP:2");
            SetupCacheGetNull<ConfiguratorProduct>("C:CP:3");

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _configuratorCacheManager.GetOrAddListByItemAndSaveAsync<ConfiguratorProduct, int>(
                    ids, _ => Task.FromResult(dbProducts)));

            Assert.Contains("Failed to load all requested items", exception.Message);
        }

        #endregion

        #region Key Generation Tests

        [Fact]
        public async Task KeyGeneration_ConfiguratorProduct_ShouldUseCorrectAbbreviation()
        {
            // Arrange
            SetupCacheGet("C:CP:123", new ConfiguratorProduct());

            // Act
            await _configuratorCacheManager.GetOrAddAsync<ConfiguratorProduct, int>(123, _ => Task.FromResult(new ConfiguratorProduct()));

            // Assert - verify the correct key was used
            _distributedCacheMock.Verify(x => x.GetAsync("C:CP:123-ConfiguratorProduct", It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task KeyGeneration_EshopProduct_ShouldUseCorrectAbbreviation()
        {
            // Arrange
            SetupCacheGet("C:EP:456", new EshopProduct());

            // Act
            await _configuratorCacheManager.GetOrAddAsync<EshopProduct, int>(456, _ => Task.FromResult(new EshopProduct()));

            // Assert
            _distributedCacheMock.Verify(x => x.GetAsync("C:EP:456-EshopProduct", It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task KeyGeneration_List_ShouldUseListPrefix()
        {
            // Arrange
            SetupCacheGet("CL:CF:789", new List<ConfiguratorField>());

            // Act
            await _configuratorCacheManager.GetOrAddListAsync<ConfiguratorField, int>(789, _ => Task.FromResult(new List<ConfiguratorField>()));

            // Assert
            _distributedCacheMock.Verify(x => x.GetAsync("CL:CF:789-List`1", It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task KeyGeneration_UnknownType_ShouldUseFullTypeName()
        {
            // Arrange
            SetupCacheGet("C:String:test", "cached");

            // Act
            await _configuratorCacheManager.GetOrAddAsync<string, string>("test", _ => Task.FromResult("value"));

            // Assert
            _distributedCacheMock.Verify(x => x.GetAsync("C:String:test-String", It.IsAny<CancellationToken>()), Times.Once);
        }


        #endregion
    }
}