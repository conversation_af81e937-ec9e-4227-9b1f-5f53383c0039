using Moq;
using Deksmart.Ui.Shared.Services;
using Deksmart.Shared.Dto;
using Deksmart.Shared.Enum;
using Deksmart.Ui.Model;
using Deksmart.Domain.Entity.Business;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class ConfiguratorStateServiceTest
    {
        private readonly ConfiguratorStateService _service;

        public ConfiguratorStateServiceTest()
        {
            _service = new ConfiguratorStateService();
        }

        private ConfiguratorWrapper CreateTestConfiguratorWrapper(
            int id = 1, 
            string tabTitle = "Test Tab", 
            bool isComposite = false,
            int? tabOrder = null)
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = id,
                Title = "Test Configurator",
                TabTitle = tabTitle,
                IsComposite = isComposite,
                Url = "test-url",
                TabOrder = tabOrder
            };

            var wrapper = new ConfiguratorWrapper(configuratorDto);

            // Initialize ConfiguratorCompositions
            wrapper.ConfiguratorCompositions = [];

            // Add field categories
            var category = new ConfiguratorFieldCategoryWrapper(new ConfiguratorFieldCategoryDto
            {
                Id = 1,
                Title = "Test Category",
                CollapseState = CategoryCollapseStateDto.Expanded
            });

            // Add a numeric field
            var numericField = new NumericFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 1,
                Title = "Numeric Field",
                Ident = "numeric_field"
            });
            numericField.Value = 10.5m;
            numericField.IsDirty = true;

            // Add a checkbox field
            var checkboxField = new CheckboxFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 2,
                Title = "Checkbox Field",
                Ident = "checkbox_field"
            });
            checkboxField.Value = true;

            category.Fields.Add(numericField);
            category.Fields.Add(checkboxField);
            wrapper.FieldCategories.Add(category);

            // Add compositions with selected products
            var composition = new ConfiguratorCompositionWrapper(new ConfiguratorCompositionDto
            {
                Id = 1,
                Title = "Test Composition"
            });

            var selectedProduct = new ConfiguratorProductWrapper(new ConfiguratorProductDto
            {
                Id = 100,
                Title = "Test Product",
                ProductCode = "PROD100",
                PackageQuantity = 1.0m,
                UserPackageQuantity = 5.0m
            });

            composition.SelectedProducts.Add(selectedProduct);
            wrapper.ConfiguratorCompositions.Add(composition);

            return wrapper;
        }

        private Mock<IConfiguratorGridService> CreateMockChildService(int id, string tabTitle, int? tabOrder = null)
        {
            var mockService = new Mock<IConfiguratorGridService>();
            var childWrapper = CreateTestConfiguratorWrapper(id, tabTitle, false, tabOrder);
            mockService.Setup(s => s.ConfiguratorWrapper).Returns(childWrapper);
            return mockService;
        }

        [Fact]
        public void GetConfiguratorState_WithBasicWrapper_ReturnsCorrectState()
        {
            var wrapper = CreateTestConfiguratorWrapper(42, "My Tab");
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.GetConfiguratorState<ConfiguratorStateDto>(wrapper, childServices);

            Assert.Equal(42, result.ConfiguratorId);
            Assert.Equal("My Tab", result.TabTitle);
            Assert.Equal(2, result.FieldValues.Count);
            Assert.Single(result.SelectedProducts);
            Assert.Single(result.CategoryStates);
        }

        [Fact]
        public void GetConfiguratorState_WithCompositeWrapper_IncludesChildStates()
        {
            var wrapper = CreateTestConfiguratorWrapper(1, "Parent", true);
            var childServices = new List<IConfiguratorGridService>
            {
                CreateMockChildService(2, "Child 1", 1).Object,
                CreateMockChildService(3, "Child 2", 2).Object
            };

            var result = _service.GetConfiguratorState<ConfiguratorStateDto>(wrapper, childServices);

            Assert.Equal(1, result.ConfiguratorId);
            Assert.Equal("Parent", result.TabTitle);
            Assert.NotNull(result.ChildConfiguratorStates);
            Assert.Equal(2, result.ChildConfiguratorStates.Count);
            
            Assert.Equal(2, result.ChildConfiguratorStates[0].ConfiguratorId);
            Assert.Equal("Child 1", result.ChildConfiguratorStates[0].TabTitle);
            Assert.Equal(1, result.ChildConfiguratorStates[0].TabOrder);
            
            Assert.Equal(3, result.ChildConfiguratorStates[1].ConfiguratorId);
            Assert.Equal("Child 2", result.ChildConfiguratorStates[1].TabTitle);
            Assert.Equal(2, result.ChildConfiguratorStates[1].TabOrder);
        }

        [Fact]
        public void GetConfiguratorState_WithChildServiceWithNullWrapper_SkipsChild()
        {
            var wrapper = CreateTestConfiguratorWrapper(1, "Parent", true);
            var mockChildService = new Mock<IConfiguratorGridService>();
            mockChildService.Setup(s => s.ConfiguratorWrapper).Returns((ConfiguratorWrapper?)null);

            var childServices = new List<IConfiguratorGridService>
            {
                CreateMockChildService(2, "Child 1").Object,
                mockChildService.Object
            };

            var result = _service.GetConfiguratorState<ConfiguratorStateDto>(wrapper, childServices);

            Assert.Single(result.ChildConfiguratorStates);
            Assert.Equal(2, result.ChildConfiguratorStates[0].ConfiguratorId);
        }

        [Fact]
        public void GetFieldValues_ReturnsCorrectFieldValues()
        {
            var wrapper = CreateTestConfiguratorWrapper();

            var result = _service.GetFieldValues(wrapper);

            Assert.Equal(2, result.Count);
            
            var numericField = result.First(f => f.FieldId == 1);
            Assert.Equal(10.5m, numericField.Value);
            
            var checkboxField = result.First(f => f.FieldId == 2);
            Assert.Equal(1m, checkboxField.Value); // true converted to 1
        }

        [Fact]
        public void GetSelectedProducts_ReturnsCorrectProducts()
        {
            var wrapper = CreateTestConfiguratorWrapper();

            var result = _service.GetSelectedProducts(wrapper);

            Assert.Single(result);
            Assert.Equal(100, result[0].ProductId);
            Assert.Equal(5, result[0].Amount);
        }

        [Fact]
        public void GetSelectedProducts_WithCleanPackageAmount_ReturnsNullAmount()
        {
            var wrapper = CreateTestConfiguratorWrapper();
            // Create a new product with clean package amount (PackageQuantity == UserPackageQuantity)
            var cleanProduct = new ConfiguratorProductWrapper(new ConfiguratorProductDto
            {
                Id = 101,
                Title = "Clean Product",
                ProductCode = "PROD101",
                PackageQuantity = 3.0m,
                UserPackageQuantity = 3.0m // Same as PackageQuantity, so IsPackageAmountDirty = false
            });
            wrapper.ConfiguratorCompositions[0].SelectedProducts.Clear();
            wrapper.ConfiguratorCompositions[0].SelectedProducts.Add(cleanProduct);

            var result = _service.GetSelectedProducts(wrapper);

            Assert.Single(result);
            Assert.Equal(101, result[0].ProductId);
            Assert.Null(result[0].Amount);
        }

        [Fact]
        public void GetCategoryStates_ReturnsOnlyCollapsibleCategories()
        {
            var wrapper = CreateTestConfiguratorWrapper();
            
            // Add a non-collapsible category
            var nonCollapsibleCategory = new ConfiguratorFieldCategoryWrapper(new ConfiguratorFieldCategoryDto
            {
                Id = 2,
                Title = "Non-Collapsible",
                CollapseState = CategoryCollapseStateDto.NotCollapsible
            });
            wrapper.FieldCategories.Add(nonCollapsibleCategory);

            var result = _service.GetCategoryStates(wrapper);

            Assert.Single(result);
            Assert.Equal(1, result[0].CategoryId);
            Assert.Equal(CategoryCollapseStateDto.Expanded, result[0].CollapseState);
        }

        [Fact]
        public void SaveFieldStates_WithNullWrapper_ReturnsEmptyDictionary()
        {
            var result = _service.SaveFieldStates(null);

            Assert.Empty(result);
        }

        [Fact]
        public void SaveFieldStates_WithFields_ReturnsDirtyStates()
        {
            var wrapper = CreateTestConfiguratorWrapper();
            // Set one field as pending validation
            wrapper.FieldCategories[0].Fields[1].IsPendingValidation = true;

            var result = _service.SaveFieldStates(wrapper);

            Assert.Equal(2, result.Count);
            Assert.True(result["numeric_field"]); // IsDirty = true
            Assert.True(result["checkbox_field"]); // IsPendingValidation = true
        }

        [Fact]
        public void RestoreFieldStates_WithNullWrapper_DoesNotThrow()
        {
            var fieldStates = new Dictionary<string, bool> { { "test", true } };

            var exception = Record.Exception(() => _service.RestoreFieldStates(null, fieldStates));

            Assert.Null(exception);
        }

        [Fact]
        public void RestoreFieldStates_RestoresFieldStatesAndResetsPendingValidation()
        {
            var wrapper = CreateTestConfiguratorWrapper();
            var field1 = wrapper.FieldCategories[0].Fields[0];
            var field2 = wrapper.FieldCategories[0].Fields[1];

            // Set initial states
            field1.IsDirty = false;
            field1.IsPendingValidation = true;
            field2.IsDirty = true;
            field2.IsPendingValidation = true;

            var fieldStates = new Dictionary<string, bool>
            {
                { "numeric_field", true },
                { "checkbox_field", false }
            };

            _service.RestoreFieldStates(wrapper, fieldStates);

            Assert.True(field1.IsDirty); // Restored to true
            Assert.False(field1.IsPendingValidation); // Always reset to false
            Assert.False(field2.IsDirty); // Restored to false
            Assert.False(field2.IsPendingValidation); // Always reset to false
        }

        [Fact]
        public void RestoreFieldStates_WithMissingFieldInStates_OnlyResetsPendingValidation()
        {
            var wrapper = CreateTestConfiguratorWrapper();
            var field = wrapper.FieldCategories[0].Fields[0];
            field.IsDirty = true;
            field.IsPendingValidation = true;

            var fieldStates = new Dictionary<string, bool>(); // Empty states

            _service.RestoreFieldStates(wrapper, fieldStates);

            Assert.True(field.IsDirty); // Unchanged
            Assert.False(field.IsPendingValidation); // Always reset
        }

        [Fact]
        public void GetFieldValueDataContract_WithNumericField_ReturnsNumericValue()
        {
            var numericField = new NumericFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 1,
                Title = "Numeric",
                Ident = "numeric"
            });
            numericField.Value = 42.7m;

            var result = _service.GetFieldValueDataContract(numericField);

            Assert.Equal(1, result.FieldId);
            Assert.Equal(42.7m, result.Value);
        }

        [Fact]
        public void GetFieldValueDataContract_WithCheckboxFieldTrue_ReturnsOne()
        {
            var checkboxField = new CheckboxFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 2,
                Title = "Checkbox",
                Ident = "checkbox"
            });
            checkboxField.Value = true;

            var result = _service.GetFieldValueDataContract(checkboxField);

            Assert.Equal(2, result.FieldId);
            Assert.Equal(1m, result.Value);
        }

        [Fact]
        public void GetFieldValueDataContract_WithCheckboxFieldFalse_ReturnsZero()
        {
            var checkboxField = new CheckboxFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 3,
                Title = "Checkbox",
                Ident = "checkbox"
            });
            checkboxField.Value = false;

            var result = _service.GetFieldValueDataContract(checkboxField);

            Assert.Equal(3, result.FieldId);
            Assert.Equal(0m, result.Value);
        }

        [Fact]
        public void GetFieldValueDataContract_WithIdFieldWrapper_ReturnsSelectedValue()
        {
            var selectField = new SelectBoxFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 4,
                Title = "Select",
                Ident = "select"
            });

            // Add field values
            var fieldValue = new ConfiguratorFieldValueWrapper(new ConfiguratorFieldValueDto
            {
                Id = 10,
                Title = "Option 1",
                NumericValue = 25.5m
            });
            selectField.FieldValues.Add(fieldValue);
            selectField.Value = 10;

            var result = _service.GetFieldValueDataContract(selectField);

            Assert.Equal(4, result.FieldId);
            Assert.Equal(25.5m, result.Value);
        }

        [Fact]
        public void GetFieldValueDataContract_WithIdFieldWrapperNullValue_ReturnsNullValue()
        {
            var selectField = new SelectBoxFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 5,
                Title = "Select",
                Ident = "select"
            });
            selectField.Value = null;

            var result = _service.GetFieldValueDataContract(selectField);

            Assert.Equal(5, result.FieldId);
            Assert.Null(result.Value);
        }

        [Fact]
        public void GetFieldValueDataContract_WithUnknownFieldType_ReturnsNullValue()
        {
            var mockField = new Mock<ConfiguratorFieldWrapper>(new ConfiguratorFieldDto
            {
                Id = 6,
                Title = "Unknown",
                Ident = "unknown"
            });

            var result = _service.GetFieldValueDataContract(mockField.Object);

            Assert.Equal(6, result.FieldId);
            Assert.Null(result.Value);
        }

        [Fact]
        public void GetConfiguratorState_WithNonCompositeWrapper_DoesNotIncludeChildStates()
        {
            var wrapper = CreateTestConfiguratorWrapper(1, "Parent", false);
            var childServices = new List<IConfiguratorGridService>
            {
                CreateMockChildService(2, "Child").Object
            };

            var result = _service.GetConfiguratorState<ConfiguratorStateDto>(wrapper, childServices);

            Assert.Empty(result.ChildConfiguratorStates);
        }

        [Fact]
        public void GetConfiguratorState_WithEmptyChildServices_HasEmptyChildStates()
        {
            var wrapper = CreateTestConfiguratorWrapper(1, "Parent", true);
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.GetConfiguratorState<ConfiguratorStateDto>(wrapper, childServices);

            Assert.NotNull(result.ChildConfiguratorStates);
            Assert.Empty(result.ChildConfiguratorStates);
        }
    }
}