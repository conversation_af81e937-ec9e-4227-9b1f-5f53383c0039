﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Deksmart.Ui.Shared.Resources {
    using System;


    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class UiSharedResource {

        private static global::System.Resources.ResourceManager resourceMan;

        private static global::System.Globalization.CultureInfo resourceCulture;

        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal UiSharedResource() {
        }

        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Deksmart.Ui.Shared.Resources.UiSharedResource", typeof(UiSharedResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }

        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to An unexpected error occurred: {0}.
        /// </summary>
        internal static string AnUnexpectedErrorOccurred {
            get {
                return ResourceManager.GetString("AnUnexpectedErrorOccurred", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to There was a problem with the request parameters. Please check your input and try again..
        /// </summary>
        internal static string ArgumentException {
            get {
                return ResourceManager.GetString("ArgumentException", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to calculate-product-prices.
        /// </summary>
        internal static string CalculateProductPricesUrl {
            get {
                return ResourceManager.GetString("CalculateProductPricesUrl", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to category-{0}-collapsed.
        /// </summary>
        internal static string CategoryCollapsed {
            get {
                return ResourceManager.GetString("CategoryCollapsed", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }



        /// <summary>
        ///   Looks up a localized string similar to Configurators.
        /// </summary>
        internal static string Configurators {
            get {
                return ResourceManager.GetString("Configurators", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to configurators.
        /// </summary>
        internal static string ConfiguratorsUrl {
            get {
                return ResourceManager.GetString("ConfiguratorsUrl", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to deksmart.
        /// </summary>
        internal static string Deksmart {
            get {
                return ResourceManager.GetString("Deksmart", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to DEKSMART.
        /// </summary>
        internal static string DeksmartBrand {
            get {
                return ResourceManager.GetString("DeksmartBrand", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Download PDF.
        /// </summary>
        internal static string DownloadConfigurationPdf {
            get {
                return ResourceManager.GetString("DownloadConfigurationPdf", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Enter the composition number from the catalog:.
        /// </summary>
        internal static string EnterCompositionNumber {
            get {
                return ResourceManager.GetString("EnterCompositionNumber", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to generate PDF.
        /// </summary>
        internal static string FailedToGeneratePdf {
            get {
                return ResourceManager.GetString("FailedToGeneratePdf", resourceCulture);
            }
        }



        /// <summary>
        ///   Looks up a localized string similar to Final price including VAT.
        /// </summary>
        internal static string FinalPrice {
            get {
                return ResourceManager.GetString("FinalPrice", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The resulting price for the structure excluding VAT.
        /// </summary>
        internal static string FinalPriceNoVat {
            get {
                return ResourceManager.GetString("FinalPriceNoVat", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Find.
        /// </summary>
        internal static string Find {
            get {
                return ResourceManager.GetString("Find", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to view/{0}/pdf.
        /// </summary>
        internal static string GenerateConfigurationPdfUrl {
            get {
                return ResourceManager.GetString("GenerateConfigurationPdfUrl", resourceCulture);
            }
        }



        /// <summary>
        ///   Looks up a localized string similar to The operation could not be completed. Please try again or contact support if the problem persists..
        /// </summary>
        internal static string InvalidOperationError {
            get {
                return ResourceManager.GetString("InvalidOperationError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The server returned data in an unexpected format. Please try again or contact support if the problem persists..
        /// </summary>
        internal static string JsonFormatError {
            get {
                return ResourceManager.GetString("JsonFormatError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to {0}/configurator.
        /// </summary>
        internal static string LoadConfiguratorUrl {
            get {
                return ResourceManager.GetString("LoadConfiguratorUrl", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to {0}/configurator/{1}.
        /// </summary>
        internal static string LoadPresetUrl {
            get {
                return ResourceManager.GetString("LoadPresetUrl", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to New Tab.
        /// </summary>
        internal static string NewTab {
            get {
                return ResourceManager.GetString("NewTab", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Next.
        /// </summary>
        internal static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to No configurator selected.
        /// </summary>
        internal static string NoConfiguratorSelected {
            get {
                return ResourceManager.GetString("NoConfiguratorSelected", resourceCulture);
            }
        }


        /// <summary>
        ///   Looks up a localized string similar to Preview.
        /// </summary>
        internal static string Preview {
            get {
                return ResourceManager.GetString("Preview", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Price with VAT.
        /// </summary>
        internal static string PriceVAT {
            get {
                return ResourceManager.GetString("PriceVAT", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Product listing.
        /// </summary>
        internal static string ProductListing {
            get {
                return ResourceManager.GetString("ProductListing", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Product name.
        /// </summary>
        internal static string ProductName {
            get {
                return ResourceManager.GetString("ProductName", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        internal static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Product number.
        /// </summary>
        internal static string ProductNumber {
            get {
                return ResourceManager.GetString("ProductNumber", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Required quantity per structure.
        /// </summary>
        internal static string QuantityPerStructure {
            get {
                return ResourceManager.GetString("QuantityPerStructure", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        internal static string Remove {
            get {
                return ResourceManager.GetString("Remove", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to {0}/save.
        /// </summary>
        internal static string SavePresetUrl {
            get {
                return ResourceManager.GetString("SavePresetUrl", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to You don&apos;t have permission to perform this action. Please check your credentials and try again..
        /// </summary>
        internal static string UnauthorizedAccessError {
            get {
                return ResourceManager.GetString("UnauthorizedAccessError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to VAT.
        /// </summary>
        internal static string VAT {
            get {
                return ResourceManager.GetString("VAT", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to view/{0}/email.
        /// </summary>
        internal static string SendInquiryUrl {
            get {
                return ResourceManager.GetString("SendInquiryUrl", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Send inquiry.
        /// </summary>
        internal static string SendInquiryButtonText {
            get {
                return ResourceManager.GetString("SendInquiryButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Inquiry has been sent successfully.
        /// </summary>
        internal static string SendInquirySuccess {
            get {
                return ResourceManager.GetString("SendInquirySuccess", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Failed to send inquiry.
        /// </summary>
        internal static string SendInquiryError {
            get {
                return ResourceManager.GetString("SendInquiryError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string NameLabel {
            get {
                return ResourceManager.GetString("NameLabel", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string EmailLabel {
            get {
                return ResourceManager.GetString("EmailLabel", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Phone.
        /// </summary>
        public static string PhoneLabel {
            get {
                return ResourceManager.GetString("PhoneLabel", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Pricing Error.
        /// </summary>
        public static string PricingError {
            get {
                return ResourceManager.GetString("PricingError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string CancelButtonText {
            get {
                return ResourceManager.GetString("CancelButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Child Configurator Overview.
        /// </summary>
        internal static string ChildConfiguratorOverview {
            get {
                return ResourceManager.GetString("ChildConfiguratorOverview", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Configurator Name.
        /// </summary>
        internal static string ConfiguratorName {
            get {
                return ResourceManager.GetString("ConfiguratorName", resourceCulture);
            }
        }


        /// <summary>
        ///   Looks up a localized string similar to Field.
        /// </summary>
        internal static string Field {
            get {
                return ResourceManager.GetString("Field", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        internal static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Validation error occurred while sending data to {0}: {1}.
        /// </summary>
        public static string ValidationError {
            get {
                return ResourceManager.GetString("ValidationError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to An unknown error occurred. Please try again later.
        /// </summary>
        public static string UnknownError {
            get {
                return ResourceManager.GetString("UnknownError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to This tab contains validation errors.
        /// </summary>
        internal static string ValidationErrorsInTab {
            get {
                return ResourceManager.GetString("ValidationErrorsInTab", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The requested {0} was not found.
        /// </summary>
        public static string ResourceNotFound {
            get {
                return ResourceManager.GetString("ResourceNotFound", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to You don't have permission to access this resource.
        /// </summary>
        public static string ForbiddenError {
            get {
                return ResourceManager.GetString("ForbiddenError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to The request was invalid. Please check your input and try again.
        /// </summary>
        public static string BadRequestError {
            get {
                return ResourceManager.GetString("BadRequestError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to A server error occurred. Please try again later or contact support if the problem persists.
        /// </summary>
        public static string ServerError {
            get {
                return ResourceManager.GetString("ServerError", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Loading application....
        /// </summary>
        internal static string LoadingApplication {
            get {
                return ResourceManager.GetString("LoadingApplication", resourceCulture);
            }
        }




        /// <summary>
        ///   Looks up a localized string similar to Not found.
        /// </summary>
        internal static string PageNotFound {
            get {
                return ResourceManager.GetString("PageNotFound", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Sorry, there's nothing at this address.
        /// </summary>
        internal static string PageNotFoundMessage {
            get {
                return ResourceManager.GetString("PageNotFoundMessage", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Select....
        /// </summary>
        internal static string SelectOption {
            get {
                return ResourceManager.GetString("SelectOption", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Select Configurator.
        /// </summary>
        internal static string SelectConfigurator {
            get {
                return ResourceManager.GetString("SelectConfigurator", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to DEKSMART - Professional product configurator. Design, customize and calculate exact pricing for your projects. Save and share your configurations..
        /// </summary>
        internal static string MetaDescription_MainPage {
            get {
                return ResourceManager.GetString("MetaDescription_MainPage", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Configure your {0} with DEKSMART. Design, customize and calculate exact pricing. Professional solution for your needs..
        /// </summary>
        internal static string MetaDescription_ConfiguratorTemplate {
            get {
                return ResourceManager.GetString("MetaDescription_ConfiguratorTemplate", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Kč.
        /// </summary>
        internal static string CurrencySymbol {
            get {
                return ResourceManager.GetString("CurrencySymbol", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Add to agenda.
        /// </summary>
        internal static string AddToAgendaButtonText {
            get {
                return ResourceManager.GetString("AddToAgendaButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Add to shopping cart.
        /// </summary>
        internal static string AddToCartButtonText {
            get {
                return ResourceManager.GetString("AddToCartButtonText", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Some products could not be priced. Shopping cart is disabled until errors are resolved..
        /// </summary>
        internal static string ProductPricingErrorMessage {
            get {
                return ResourceManager.GetString("ProductPricingErrorMessage", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to Totals exclude products with pricing errors.
        /// </summary>
        internal static string TotalsExcludeErrorsMessage {
            get {
                return ResourceManager.GetString("TotalsExcludeErrorsMessage", resourceCulture);
            }
        }

        /// <summary>
        ///   Looks up a localized string similar to {0}/composite.
        /// </summary>
        internal static string ProcessCompositeConfiguratorUrl {
            get {
                return ResourceManager.GetString("ProcessCompositeConfiguratorUrl", resourceCulture);
            }
        }
    }
}
