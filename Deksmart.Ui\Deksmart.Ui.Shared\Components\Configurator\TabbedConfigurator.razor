@using Deksmart.Shared.Dto
@using Deksmart.Ui.Model
@using Deksmart.Ui.Shared.Resources
@using Deksmart.Ui.Shared.Components
@using Deksmart.Ui.Shared.Services
@inherits ComponentBase
@inject HeaderStateService HeaderState

@if (MainConfiguratorGridService.ConfiguratorWrapper != null)
{
    <PageTitle>@MainConfiguratorGridService.ConfiguratorWrapper.Title</PageTitle>
}

<div class="tabbed-configurator">
    <div class="tabs-header">
        @* Drop zone before first tab *@
        <div class="precision-drop-zone @(_draggedService != null && _dropZoneIndex == 0 ? "active" : "")"
             @ondragover:preventDefault="true"
             @ondragover="() => OnDropZoneOver(0)"
             @ondragleave="() => OnDropZoneLeave(0)"
             @ondrop:preventDefault="true"
             @ondrop="() => OnDropZoneDrop(0)">
            <div class="drop-indicator"></div>
        </div>
        
        @for (int i = 0; i < MainConfiguratorGridService.ChildServices.Count; i++)
        {
            var service = MainConfiguratorGridService.ChildServices[i];
            var serviceIndex = i;
            
            @* Tab *@
            
            <div class="tab @(service == ActiveConfiguratorService ? "active" : "") @(_draggedService == service ? "dragging" : "")"
                 draggable="@(_editingService == null ? "true" : "false")"
                 @ondragstart="() => OnDragStart(service)"
                 @ondragend="OnDragEnd"
                 @ondragover:preventDefault="true"
                 @ondragover="(e) => OnTabDragOver(service, serviceIndex, e)"
                 @ondragleave="() => OnTabDragLeave(service)"
                 @ondrop:preventDefault="true"
                 @ondrop="() => OnTabDrop(service, serviceIndex)"
                 @onclick="() => SetActiveConfigurator(service)">
                
                @if (_editingService == service)
                {
                    <input class="tab-name-input"
                           type="text"
                           value="@service.ConfiguratorWrapper?.TabTitle"
                           @onchange="(e) => UpdateTabName(service, e.Value?.ToString())"
                           @onblur="() => StopEditing()"
                           @onkeydown="(e) => HandleKeyDown(e, service)"
                           @onclick:stopPropagation="true"
                           autofocus />
                }
                else
                {
                    <span class="tab-title" @ondblclick="() => StartEditing(service)">
                        @service.ConfiguratorWrapper?.DisplayTabTitle
                    </span>
                    @if (service.ConfiguratorWrapper != null && !service.ConfiguratorWrapper.IsValid && service.ShowValidationCheckboxes)
                    {
                        <span class="validation-error-indicator" title="@UiSharedResource.ValidationErrorsInTab">!</span>
                    }
                }
                @if (MainConfiguratorGridService.ChildServices.Count > 1)
                {
                    <button class="close-tab" @onclick:stopPropagation="true" @onclick="() => CloseTab(service)">×</button>
                }
            </div>
            
            @* Precision drop zone after each tab *@
            <div class="precision-drop-zone @(_draggedService != null && _dropZoneIndex == serviceIndex + 1 ? "active" : "")"
                 @ondragover:preventDefault="true"
                 @ondragover="() => OnDropZoneOver(serviceIndex + 1)"
                 @ondragleave="() => OnDropZoneLeave(serviceIndex + 1)"
                 @ondrop:preventDefault="true"
                 @ondrop="() => OnDropZoneDrop(serviceIndex + 1)">
                <div class="drop-indicator"></div>
            </div>
        }
        
        <button class="add-tab"
                @onclick="async () => {
                    if (_draggedService == null)
                    {
                        if (MainConfiguratorGridService.ConfiguratorWrapper?.ChildConfigurators?.Count > 1)
                        {
                            ShowConfiguratorSelection();
                        }
                        else if (MainConfiguratorGridService.ConfiguratorWrapper?.ChildConfigurators?.Count == 1)
                        {
                            await OnConfiguratorSelected(MainConfiguratorGridService.ConfiguratorWrapper.ChildConfigurators.Single());
                        }
                    }
                }"
                @ondragover:preventDefault="true"
                @ondragover="() => OnAddButtonDragOver()"
                @ondragleave="() => OnAddButtonDragLeave()"
                @ondrop:preventDefault="true"
                @ondrop="() => OnAddButtonDrop()">+</button>

    </div>

    <div class="tab-content">
        @if (ActiveConfiguratorService?.ConfiguratorWrapper != null)
        {
            <ConfiguratorGridBase Id="@ActiveConfiguratorService.ConfiguratorWrapper.Id"
            ConfiguratorGridService="@ActiveConfiguratorService"
            ShowSaveButton="false"
            ShowPageTitle="false"
            UpdateHeader="false"/>
        }
    </div>

    @if (MainConfiguratorGridService.ChildServices.Any())
    {
        <ChildConfiguratorOverview MainConfiguratorGridService="MainConfiguratorGridService" />
    }

    @if (!string.IsNullOrEmpty(MainEndDescription))
    {
        <div class="configurator-footer">
            <p class="end-description">@MainEndDescription</p>
        </div>
    }
</div>

<ConfiguratorSelectionDialog
    IsVisible="_showConfiguratorSelection"
    Configurators="MainConfiguratorGridService.ConfiguratorWrapper?.ChildConfigurators"
    OnSelected="OnConfiguratorSelected"
    OnCanceled="HideConfiguratorSelection" />
