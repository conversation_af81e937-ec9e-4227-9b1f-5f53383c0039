using System.Linq.Expressions;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Db.Interface;
using Deksmart.Infrastructure.Config;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Npgsql;

namespace Deksmart.Infrastructure.Context;

public partial class ConfiguratorContext : DbContext
{
    private readonly IConfiguration? _configuration;

    public ConfiguratorContext()
    {
    }

    public ConfiguratorContext(DbContextOptions<ConfiguratorContext> options)
        : base(options)
    {
    }

    public ConfiguratorContext(DbContextOptions<ConfiguratorContext> options, IConfiguration configuration)
        : base(options)
    {
        _configuration = configuration;
    }

    public virtual DbSet<Configurator> Configurators { get; set; }

    public virtual DbSet<ConfiguratorComposition> ConfiguratorCompositions { get; set; }

    public virtual DbSet<ConfiguratorExpression> ConfiguratorExpressions { get; set; }

    public virtual DbSet<ConfiguratorField> ConfiguratorFields { get; set; }

    public virtual DbSet<ConfiguratorFieldCategory> ConfiguratorFieldCategories { get; set; }

    public virtual DbSet<ConfiguratorFieldCombination> ConfiguratorFieldCombinations { get; set; }

    public virtual DbSet<ConfiguratorProductCombination> ConfiguratorProductCombinations { get; set; }

    public virtual DbSet<ConfiguratorFieldValue> ConfiguratorFieldValues { get; set; }

    public virtual DbSet<ConfiguratorPreset> ConfiguratorPresets { get; set; }

    public virtual DbSet<ConfiguratorProduct> ConfiguratorProducts { get; set; }

    public virtual DbSet<ChildConfigurator> ChildConfigurators { get; set; }

    public virtual DbSet<ChildPreset> ChildPresets { get; set; }

    public virtual DbSet<PresetConfiguratorCategoryState> PresetConfiguratorCategoryStates { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            if (_configuration != null)
            {
                var config = _configuration.GetSection("ConfiguratorDb").Get<ConfiguratorDbConfig>();
                if (config != null)
                {
                    var connectionString = new NpgsqlConnectionStringBuilder
                    {
                        Host = config.Host,
                        Port = config.Port,
                        Database = config.Database,
                        Username = config.Username,
                        Password = config.Password,
                        Pooling = config.Pooling,
                        IncludeErrorDetail = true
                    }.ConnectionString;

                    optionsBuilder.UseNpgsql(connectionString);
                }
                else
                {
                    // Fallback to default connection string if configuration is not available
                    optionsBuilder.UseNpgsql("Host=dev12.dek.cz:5432;Database=configurator;Username=configurator;Password=******");
                }
            }
            else
            {
                // Fallback to default connection string if configuration is not available
                optionsBuilder.UseNpgsql("Host=dev12.dek.cz:5432;Database=configurator;Username=configurator;Password=******");
            }
        }
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasPostgresExtension("tds_fdw");

        // Apply global filter for IDeletableEntity
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(IDeletableEntity).IsAssignableFrom(entityType.ClrType))
            {
                var parameter = Expression.Parameter(entityType.ClrType, "e");
                var filter = Expression.Lambda(
                    Expression.Equal(
                        Expression.Property(parameter, nameof(IDeletableEntity.IsDeleted)),
                        Expression.Constant(false)
                    ),
                    parameter
                );
                modelBuilder.Entity(entityType.ClrType).HasQueryFilter(filter);
            }
        }

        modelBuilder.Entity<Configurator>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator");

            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasComment("KALK_ID");
            entity.Property(e => e.Description).HasComment("INFO_TEXT");
            entity.Property(e => e.Title).HasComment("KALK_NAZEV");
            entity.Property(e => e.IsComposite)
                .HasDefaultValue(false)
                .HasComment("Indicates if this configurator is a collection of other configurators");
            entity.HasMany(c => c.ConfiguratorCompositions)
                .WithOne() // No navigation property on ConfiguratorComposition
                .HasForeignKey(cc => cc.ConfiguratorId)
                .OnDelete(DeleteBehavior.ClientSetNull);
            entity.HasMany(c => c.ConfiguratorFieldCategories)
                .WithOne() // No navigation property on ConfiguratorProduct
                .HasForeignKey(cc => cc.ConfiguratorId)
                .OnDelete(DeleteBehavior.ClientSetNull);
            entity.HasMany(d => d.ChildConfigurators)
                .WithOne()
                .HasForeignKey(d => d.CompositeId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("fk_child_configurator_collection");
        });

        modelBuilder.Entity<ChildConfigurator>(entity =>
        {
            entity.HasKey(e => new { e.CompositeId, e.ConfiguratorId })
                .HasName("pk_child_configurator");

            entity.ToTable("child_configurator", "dbo", tb =>
                tb.HasComment("Defines which configurators can be used in a collection"));

            entity.Property(e => e.CompositeId)
                .HasComment("ID of the collection configurator");
            entity.Property(e => e.ConfiguratorId)
                .HasComment("ID of the allowed configurator");
            entity.Property(e => e.DisplayOrder)
                .HasComment("Order in which configurators should be displayed");

            entity.HasOne(d => d.Configurator)
                .WithMany()
                .HasForeignKey(d => d.ConfiguratorId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("fk_child_configurator_configurator");

            entity.HasIndex(e => new { e.CompositeId, e.DisplayOrder })
                .IsUnique()
                .HasDatabaseName("ix_child_configurator_order");
        });

        modelBuilder.Entity<ConfiguratorComposition>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator_composition");

            entity.ToTable("configurator_composition", "dbo", tb => tb.HasComment("CZ_ESHOP_KONFIGURATOR_SKLADBY"));

            entity.Property(e => e.Id).HasComment("SKL_ID");
            entity.Property(e => e.ConfiguratorId).HasComment("KALK_ID");
            entity.Property(e => e.Order).HasComment("PORADI");
            entity.Property(e => e.Title).HasComment("SKL_NAZEV");
            entity.HasMany(c => c.ConfiguratorProducts)
                .WithOne() // No navigation property on ConfiguratorProduct
                .HasForeignKey(cc => cc.CompositionId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });

        modelBuilder.Entity<ConfiguratorExpression>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_item_expressions");
        });

        modelBuilder.Entity<ConfiguratorField>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator_field");

            entity.ToTable("configurator_field", "dbo", tb => tb.HasComment("CZ_ESHOP_KONFIGURATOR_FILTRY"));

            entity.Property(e => e.Id).HasComment("FILTR_ID");
            entity.Property(e => e.Description).HasComment("POZNAMKA");
            entity.Property(e => e.Order).HasComment("PORADI");
            entity.Property(e => e.Title).HasComment("FILTR_NAZEV");
            entity.Property(e => e.ComponentType).HasConversion<int>();
            entity.Property(e => e.FieldPosition).HasConversion<short>();

            entity.HasMany(f => f.ConfiguratorFieldValues)
                .WithMany() // No navigation property on ConfiguratorFieldValue
                .UsingEntity<Dictionary<string, object>>(
                    "ConfiguratorFieldToFieldValue",
                    r => r.HasOne<ConfiguratorFieldValue>().WithMany().HasForeignKey("FieldValueId"),
                    l => l.HasOne<ConfiguratorField>().WithMany().HasForeignKey("FieldId"),
                    j =>
                    {
                        j.HasKey("FieldId", "FieldValueId");
                        j.ToTable("configurator_field_to_field_value", "dbo");
                        j.IndexerProperty<int>("FieldId").HasColumnName("field_id");
                        j.IndexerProperty<int>("FieldValueId").HasColumnName("field_value_id");
                    });
        });

        modelBuilder.Entity<ConfiguratorFieldCategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator_field_group");

            entity.Property(e => e.ConfiguratorId).HasComment("KALK_ID");
            entity.Property(e => e.CollapseState).HasConversion<short>();

            entity.HasMany(c => c.ConfiguratorFields)
                .WithOne() // No navigation property on ConfiguratorField
                .HasForeignKey(cc => cc.FieldCategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull);
        });

        modelBuilder.Entity<ConfiguratorFieldCombination>(entity =>
        {
            entity.HasKey(e => new { e.PresetId, e.FieldId }).HasName("pk_field_combination");
        });

        modelBuilder.Entity<ConfiguratorProductCombination>(entity =>
        {
            entity.HasKey(e => new { e.PresetId, e.ProductId }).HasName("pk_configurator_product_combination");
        });

        modelBuilder.Entity<ConfiguratorFieldValue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator_field_values");

            entity.ToTable("configurator_field_value", "dbo", tb => tb.HasComment("CZ_ESHOP_KONFIGURATOR_FILTRY_HODNOTY"));

            entity.Property(e => e.Id).HasComment("HODNOTA_ID");
            entity.Property(e => e.Description).HasComment("POZNAMKA");
            entity.Property(e => e.Order).HasComment("PORADI");
            entity.Property(e => e.Title).HasComment("HODNOTA_NAZEV");
        });

        modelBuilder.Entity<ConfiguratorPreset>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator_preset");

            entity.Property(e => e.Id).ValueGeneratedNever();

            entity.HasOne(d => d.Configurator).WithMany()
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_configurator_preset_configurator");

            entity.HasMany(c => c.ConfiguratorFieldCombinations)
                .WithOne() // No navigation property on ConfiguratorProduct
                .HasForeignKey(cc => cc.PresetId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasMany(c => c.ConfiguratorProductCombinations)
                .WithOne() // No navigation property on ConfiguratorProduct
                .HasForeignKey(cc => cc.PresetId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasMany(c => c.CategoryStates)
                .WithOne() // No navigation property on ConfiguratorProduct
                .HasForeignKey(cc => cc.PresetId)
                .OnDelete(DeleteBehavior.ClientSetNull);

            entity.HasMany(c => c.ChildPresets)
                .WithOne() // No navigation property on ConfiguratorProduct
                .HasForeignKey(cc => cc.CompositeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("fk_child_preset_collection");
        });

        modelBuilder.Entity<ConfiguratorProduct>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_configurator_product");

            entity.ToTable("configurator_product", "dbo", tb => tb.HasComment("CZ_ESHOP_KONFIGURATOR_POLOZKY"));

            entity.Property(e => e.CompositionId).HasComment("SKL_ID");
            entity.Property(e => e.Order).HasComment("PORADI");
            entity.Property(e => e.ProductCode).HasComment("ID_POL");
            entity.Property(e => e.ProductUnit)
                .IsFixedLength()
                .HasComment("PROD_MJ");
            entity.Property(e => e.ProductVolume).HasComment("VEL_SPOTREBA_PROD_MJ");
            entity.Property(e => e.Title).HasComment("POL_NAZEV");
        });

        modelBuilder.Entity<ChildPreset>(entity =>
        {
            entity.HasKey(e => new { e.CompositeId, e.PresetId })
                .HasName("pk_child_preset");

            entity.ToTable("child_preset", "dbo");

            entity.HasOne(d => d.Preset)
                .WithMany()
                .HasForeignKey(d => d.PresetId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("fk_child_preset_preset");
        });

        modelBuilder.Entity<PresetConfiguratorCategoryState>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("pk_preset_configurator_category_state");

            entity.ToTable("preset_configurator_category_state", "dbo");

            entity.Property(e => e.Id).ValueGeneratedOnAdd();
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
