namespace DEK.Eshop.ApiCore.Validation;

public record class ErrorsResult : ValidationResult
{
    public ErrorsResult(bool success, IEnumerable<Error> errors) : base(success, errors)
    {
    }
}

//@todo : replace with ErrorsResult. Useage is not only for validation
public record class ValidationResult
{
    public IEnumerable<Error> Errors { get; init; }

    public bool Success { get; init; }

    public ValidationResult(bool success, IEnumerable<Error> errors)
    {
        Success = success;
        Errors = errors;
    }
}

public record class Error
{
    public string? Code { get; init; } = null;

    private string? property;
    public string? Property {
        get { return property; }
        init { property = !string.IsNullOrEmpty(value) ? char.<PERSON><PERSON><PERSON>er(value.First()) + value.Substring(1) : null; }
    }
    public string Message { get; init; } = null!;
}
