using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Services
{
    public interface IConfiguratorValidationService
    {
        /// <summary>
        /// Sets all fields in the configurator as dirty to trigger validation display
        /// </summary>
        /// <param name="configuratorWrapper">The configurator wrapper to mark fields as dirty</param>
        /// <param name="childServices">Child services to also mark as dirty</param>
        /// <returns>True if the configurator is valid after setting all fields as dirty</returns>
        bool SetAllFieldsDirty(ConfiguratorWrapper? configuratorWrapper, List<IConfiguratorGridService> childServices);

        /// <summary>
        /// Finds the first category with validation errors
        /// </summary>
        /// <param name="configuratorWrapper">The configurator wrapper to search</param>
        /// <param name="childServices">Child services to also search</param>
        /// <returns>The ID of the first category with validation errors, or null if no errors found</returns>
        string? FindFirstInvalidCategoryId(ConfiguratorWrapper? configuratorWrapper, List<IConfiguratorGridService> childServices);

        /// <summary>
        /// Updates validation state for parent-child configurator relationships
        /// </summary>
        /// <param name="mainConfiguratorWrapper">The main configurator wrapper</param>
        /// <param name="childServices">Child services to validate</param>
        /// <returns>True if all children are valid</returns>
        bool UpdateParentValidationState(ConfiguratorWrapper? mainConfiguratorWrapper, List<IConfiguratorGridService> childServices);
    }
}