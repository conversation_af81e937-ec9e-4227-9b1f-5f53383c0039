
namespace DEK.Eshop.ApiCore.Extension;

public static class DateTimeExtension
{
    /// <summary>
    /// Sets the Kind property of a DateTime to DateTimeKind.Utc
    /// But there is no time covertion, only sets the Kind property
    /// You may want use DateTime.ToUniversalTime() instead
    /// https://learn.microsoft.com/en-us/dotnet/api/system.datetime.kind?view=net-8.0
    /// </summary>
    public static DateTime Dek_SetKindUtc(this DateTime dateTime)
    {
        if (dateTime.Kind == DateTimeKind.Utc) { return dateTime; }
        return DateTime.SpecifyKind(dateTime, DateTimeKind.Utc);
    }
}
