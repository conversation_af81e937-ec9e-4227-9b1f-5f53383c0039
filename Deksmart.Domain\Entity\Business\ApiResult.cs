namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Represents a standardized API result wrapper containing data and validation information.
    /// </summary>
    public class ApiResult<T>
    {
        public T Data { get; private set; }
        public ValidationResult Validation { get; private set; }

        public ApiResult(T data)
        {
            Data = data;
            Validation = new ValidationResult();
        }

        public ApiResult(ValidationResult validation)
        {
            Validation = validation;
        }

        public static ApiResult<T> Success(T data) => new(data);
        public static ApiResult<T> Error(string error)
        {
            var validation = new ValidationResult();
            validation.AddError(error);
            return new(validation);
        }
    }
}
