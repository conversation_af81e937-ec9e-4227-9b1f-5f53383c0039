{"profiles": {"DEK.Eshop.ApiCore.Sandbox": {"commandName": "Project", "launchBrowser": true, "launchUrl": "core/doc", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7067;http://localhost:5067"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "core/doc", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/core/doc", "publishAllPorts": true, "useSSL": true}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:15458", "sslPort": 44322}}}