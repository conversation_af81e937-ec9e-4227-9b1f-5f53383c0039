using Microsoft.Extensions.Configuration;

namespace DEK.Eshop.ApiCore.Config;

public class ConfigFactory
{
    /// <summary>
    /// Creates an instance of the specified type from the appsettings.json file.
    /// Key parameter is optional. If not specified, the name of the type is used as configuration key.
    /// </summary>
    /// <param name="key">The key of the configuration section.</param>
    /// <exception cref="ConfigException"></exception>
    public static T Create<T>(IConfiguration configuration, string? key = null)
    {
        key = key ?? typeof(T).Name;

        var config = configuration.GetSection(key).Get<T>();

        if (config is null)
            throw new ConfigException("V appsettings.json chybí nastavení pro $." + key + ".");

        return config;
    }
}
