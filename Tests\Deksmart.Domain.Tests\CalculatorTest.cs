using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Service;
using Microsoft.Extensions.Logging;
using Moq;

namespace Deksmart.Domain.Tests
{
    public class CalculatorTest
    {
        private readonly Mock<ILogger<Calculator>> _loggerMock;
        private readonly Calculator _calculator;
        private readonly List<ValueIdent> _fieldValues;

        public CalculatorTest()
        {
            _loggerMock = new Mock<ILogger<Calculator>>();
            _calculator = new Calculator(_loggerMock.Object);

            // Setup test parameters
            _fieldValues = new List<ValueIdent>
            {
                new() { Ident = "V5", Value = 5 },
                new() { Ident = "F10", Value = 10 },
                new() { Ident = "V11", Value = 999 },
                new() { Ident = "F11", Value = int.MaxValue },
                new() { Ident = "V0", Value = 0 },
                new() { Ident = "V05", Value = 0.55555m },
                new() { Ident = "F1.1", Value = 50 },
                new() { Ident = "F1_1", Value = 66 },
                new() { Ident = "V_NEG", Value = -5 },
                new() { Ident = "V_DEC", Value = 3.14159m }
            };
        }

        [Theory]
        [InlineData("if(V5>V0;1;0)", 1)]
        [InlineData("if(V0>V11;1;0)", 0)]
        [InlineData("V5", 5)]
        [InlineData("0.5", 0.5)]
        [InlineData("(V5 + F10) / (F10 + V5)", 1)]
        [InlineData("F10 + V05", 10.55555)]
        [InlineData("F11", int.MaxValue)]
        [InlineData("[F1.1]", 50)]
        [InlineData("F1_1", 66)]
        [InlineData("0", 0)]
        [InlineData("if(5 = 10; 1; 0)", 0)]
        [InlineData("if(F1_1 IN (66, 25); 1; 0)", 0)] // IN with parameter doesn't work
        [InlineData("if(66 IN (66, 25); 1; 0)", 1)]
        [InlineData("F10 - V5", 5)]
        [InlineData("if(if(1 = 1; 1; 0) = 1; 1; 0)", 1)]
        [InlineData("if((1 = 1) && (2 = 2); 1; 0)", 1)]
        [InlineData("if(1 = 1 && 2 = 2; 1; 0)", 1)]
        [InlineData("if((1 = 1) && (2 = 3); 1; 0)", 0)]
        [InlineData("if(F1_1 = 5 || F1_1 = 66; 1; 0)", 1)]
        [InlineData("if(F1_1 = 5 || F1_1 = 55; 1; 0)", 0)]
        [InlineData("if(F1_1 = 5 || 55; 1; 0)", 1)] // or with just number does not work, always returns true
        public void CalculateExpression_WithValidExpressions_ReturnsCorrectResult(string expression, decimal expectedResult)
        {
            // Act
            var (result, validation) = _calculator.CalculateExpression(expression, _fieldValues);

            // Assert
            Assert.Equal(expectedResult, result);
            Assert.True(validation.IsValid);
        }

        [Theory]
        [InlineData("V_NEG + V5", 0)] // -5 + 5 = 0
        [InlineData("V_NEG * V5", -25)] // -5 * 5 = -25
        [InlineData("V_DEC * V5", 15.70795)] // 3.14159 * 5 = 15.70795
        [InlineData("V_DEC / V5", 0.628318)] // 3.14159 / 5 = 0.628318
        public void CalculateExpression_WithDecimalAndNegativeValues_ReturnsCorrectResult(string expression, decimal expectedResult)
        {
            // Act
            var (result, validation) = _calculator.CalculateExpression(expression, _fieldValues);

            // Assert
            Assert.Equal(expectedResult, result, 6); // Allow for small floating point differences
            Assert.True(validation.IsValid);
        }

        [Theory]
        [InlineData("F1_5")] // Non-existent parameter
        [InlineData("F10 V5")] // Invalid minus character
        [InlineData("")] // Empty expression
        [InlineData("F10 / V0")] // Division by zero
        [InlineData("F10 +")] // Incomplete expression
        [InlineData("F10 + V5)")] // Unmatched parentheses
        public void CalculateExpression_WithInvalidInput_ReturnsZero(string expression)
        {
            // Act
            var (result, validation) = _calculator.CalculateExpression(expression, _fieldValues);

            // Assert
            Assert.Equal(0, result);
            Assert.True(validation.HasErrors);
        }

        [Fact]
        public void CalculateExpression_WithNullInput_ReturnsZero()
        {
            // Act
            string? nullExpression = null;
            var (result, validation) = _calculator.CalculateExpression(nullExpression!, _fieldValues);

            // Assert
            Assert.Equal(0, result);
            Assert.True(validation.HasErrors);
        }

        [Fact]
        public void CalculateExpression_WithInvalidInput_LogsError()
        {
            // Arrange
            var invalidExpression = "F10 +";

            // Act
            var (result, validation) = _calculator.CalculateExpression(invalidExpression, _fieldValues);

            // Assert
            Assert.Equal(0, result);
            Assert.True(validation.HasErrors);
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString()!.Contains($"Expression evaluation failed for '{invalidExpression}'") || o.ToString()!.Contains($"Invalid expression or parameters for '{invalidExpression}'") || o.ToString()!.Contains($"Unexpected error calculating expression '{invalidExpression}'") ),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Theory]
        [InlineData("if(V5 > V0 AND F10 > V5; 1; 0)", 1)] // AND operator
        [InlineData("if(V5 > V0 OR V0 > V11; 1; 0)", 1)] // OR operator
        [InlineData("if(NOT(V0 > V11); 1; 0)", 1)] // NOT operator
        [InlineData("if(V5 >= V0; 1; 0)", 1)] // Greater than or equal
        [InlineData("if(V0 <= V5; 1; 0)", 1)] // Less than or equal
        [InlineData("if(V5 != V0; 1; 0)", 1)] // Not equal
        public void CalculateExpression_WithLogicalOperators_ReturnsCorrectResult(string expression, decimal expectedResult)
        {
            // Act
            var (result, validation) = _calculator.CalculateExpression(expression, _fieldValues);

            // Assert
            Assert.Equal(expectedResult, result);
            Assert.True(validation.IsValid);
        }

        [Theory]
        [InlineData("F10 * (V5 + V0)", 50)] // Nested parentheses
        [InlineData("(F10 + V5) * (V5 + V0)", 75)] // Multiple parentheses
        [InlineData("((F10 + V5) * V5) + V0", 75)] // Deeply nested parentheses
        public void CalculateExpression_WithComplexParentheses_ReturnsCorrectResult(string expression, decimal expectedResult)
        {
            // Act
            var (result, validation) = _calculator.CalculateExpression(expression, _fieldValues);

            // Assert
            Assert.Equal(expectedResult, result);
            Assert.True(validation.IsValid);
        }
    }
}
