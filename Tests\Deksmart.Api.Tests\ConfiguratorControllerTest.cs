﻿using Moq;
using Deksmart.Api.Controllers;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Deksmart.Api.Resource;
using Xunit;
using Deksmart.Application.Service;
using Deksmart.Shared.Dto;
using Deksmart.Application.Service.Http;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;

namespace Deksmart.Api.Tests
{
    public class ConfiguratorControllerTest
    {
        private readonly Mock<IConfiguratorService> _configuratorServiceMock;
        private readonly Mock<IConfiguratorPresetService> _presetSaverServiceMock;
        private readonly Mock<ILogger<ConfiguratorController>> _loggerMock;
        private readonly Mock<IEshopApiService> _eshopApiServiceMock;
        private readonly ConfiguratorController _controller;

        public ConfiguratorControllerTest()
        {
            _configuratorServiceMock = new Mock<IConfiguratorService>();
            _presetSaverServiceMock = new Mock<IConfiguratorPresetService>();
            _loggerMock = new Mock<ILogger<ConfiguratorController>>();
            _eshopApiServiceMock = new Mock<IEshopApiService>();

            _controller = new ConfiguratorController(
                _configuratorServiceMock.Object,
                _loggerMock.Object,
                _presetSaverServiceMock.Object,
                _eshopApiServiceMock.Object
            );
        }

        [Fact]
        public async Task GetConfigurators_ReturnsOkResult_WithListOfConfigurators()
        {
            // Arrange
            var configurators = new List<ConfiguratorDto> { new ConfiguratorDto { Id = 1, Title = "Test" } };
            _configuratorServiceMock.Setup(x => x.GetAllConfiguratorsAsync()).ReturnsAsync(configurators);

            // Act
            var result = await _controller.GetConfigurators();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<IEnumerable<ConfiguratorDto>>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Single(apiResponse.Data);
        }

        [Fact]
        public async Task GetConfigurator_ReturnsBadRequest_WhenErrorOccurs()
        {
            // Arrange
            int id = 1;
            var validation = new ValidationResult();
            validation.AddError("Error message");
            _configuratorServiceMock.Setup(x => x.GetConfiguratorsAsync(id))
                .ReturnsAsync((null, validation));

            // Act
            var result = await _controller.GetConfigurator(id);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Error message", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task GetConfigurator_ReturnsOkResult_WithConfigurator()
        {
            // Arrange
            int id = 1;
            var configurator = new ConfiguratorDto { Id = id };
            var validation = new ValidationResult();
            _configuratorServiceMock.Setup(x => x.GetConfiguratorsAsync(id))
                .ReturnsAsync((configurator, validation));

            // Act
            var result = await _controller.GetConfigurator(id);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<ConfiguratorDto>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(id, apiResponse.Data?.Id);
        }

        [Fact]
        public async Task GetFilteredConfigurator_ReturnsBadRequest_WhenErrorOccurs()
        {
            // Arrange
            int id = 1;
            var configuratorState = new ConfiguratorStateDto();
            var validation = new ValidationResult();
            validation.AddError("Error message");
            _configuratorServiceMock.Setup(x => x.GetFilteredConfiguratorAsync(id, configuratorState))
                .ReturnsAsync((null, validation));

            // Act
            var result = await _controller.GetFilteredConfigurator(id, configuratorState);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Error message", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task GetFilteredConfigurator_ReturnsOkResult_WithConfigurator()
        {
            // Arrange
            int id = 1;
            var configuratorState = new ConfiguratorStateDto();
            var configurator = new ConfiguratorDto { Id = id };
            var validation = new ValidationResult();
            _configuratorServiceMock.Setup(x => x.GetFilteredConfiguratorAsync(id, configuratorState))
                .ReturnsAsync((configurator, validation));

            // Act
            var result = await _controller.GetFilteredConfigurator(id, configuratorState);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<ConfiguratorDto>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(id, apiResponse.Data?.Id);
        }

        [Fact]
        public async Task SaveConfiguratorPreset_ReturnsBadRequest_WhenErrorOccurs()
        {
            // Arrange
            int id = 1;
            var products = new List<ClientProductValueDto>();
            var fields = new List<ClientFieldValueDto>();
            var preset = new ConfiguratorStateDto(id, fields, products);
            var validation = new ValidationResult();
            validation.AddError("Error message");
            _presetSaverServiceMock.Setup(x => x.SavePresetAsync(id, preset))
                .ReturnsAsync((null, validation));

            // Act
            var result = await _controller.SaveConfiguratorPreset(id, preset);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Error message", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task SaveConfiguratorPreset_ReturnsOkResult_WithGuid()
        {
            // Arrange
            int id = 1;
            var products = new List<ClientProductValueDto>();
            var fields = new List<ClientFieldValueDto>();
            var preset = new ConfiguratorStateDto(id, fields, products);
            var guid = Guid.NewGuid();
            var validation = new ValidationResult();
            _presetSaverServiceMock.Setup(x => x.SavePresetAsync(id, preset))
                .ReturnsAsync((guid, validation));

            // Act
            var result = await _controller.SaveConfiguratorPreset(id, preset);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<Guid?>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(guid, apiResponse.Data);
        }

        [Fact]
        public async Task GetConfiguratorByPreset_ReturnsNotFound_WhenPresetDoesNotExist()
        {
            // Arrange
            int id = 1;
            var presetId = Guid.NewGuid().ToString();
            _presetSaverServiceMock.Setup(x => x.GetConfiguratorStateAsync(It.IsAny<Guid>()))
                .ReturnsAsync((ConfiguratorStateDto?)null);

            // Act
            var result = await _controller.GetConfiguratorByPreset(id, presetId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(string.Format(DeksmartApiResource.PresetDoesNotExist, presetId), apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task GetConfiguratorByPreset_ReturnsBadRequest_WhenErrorOccurs()
        {
            // Arrange
            int id = 1;
            var presetId = Guid.NewGuid().ToString();
            var state = new ConfiguratorStateDto
            {
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>()
            };
            var validation = new ValidationResult();
            validation.AddError("Error message");
            _presetSaverServiceMock.Setup(x => x.GetConfiguratorStateAsync(It.IsAny<Guid>()))
                .ReturnsAsync(state);
            _configuratorServiceMock.Setup(x => x.GetFilteredConfiguratorAsync(id, state))
                .ReturnsAsync((null, validation));

            // Act
            var result = await _controller.GetConfiguratorByPreset(id, presetId);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Error message", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task GetConfiguratorByPreset_ReturnsOkResult_WithConfigurator()
        {
            // Arrange
            int id = 1;
            var presetId = Guid.NewGuid().ToString();
            var state = new ConfiguratorStateDto
            {
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>()
            };
            var configurator = new ConfiguratorDto { Id = id };
            var validation = new ValidationResult();

            _presetSaverServiceMock.Setup(x => x.GetConfiguratorStateAsync(It.IsAny<Guid>()))
                .ReturnsAsync(state);
            _configuratorServiceMock.Setup(x => x.GetFilteredConfiguratorAsync(id, state))
                .ReturnsAsync((configurator, validation));

            // Act
            var result = await _controller.GetConfiguratorByPreset(id, presetId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<ConfiguratorDto>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(id, apiResponse.Data?.Id);
        }

        [Fact]
        public async Task GetPresetByCatalogId_ReturnsNotFound_WhenPresetDoesNotExist()
        {
            // Arrange
            var catalogId = "test-catalog";
            _presetSaverServiceMock.Setup(x => x.GetPresetIdByCatalogId(catalogId))
                .ReturnsAsync((PresetDto?)null);

            // Act
            var result = await _controller.GetPresetByCatalogId(catalogId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(string.Format(DeksmartApiResource.PresetDoesNotExist, catalogId), apiResponse.ErrorMessage);
        }

        [Fact]
        public async Task GetPresetByCatalogId_ReturnsOkResult_WithPreset()
        {
            // Arrange
            var catalogId = "test-catalog";
            var preset = new PresetDto { ConfiguratorId = 1, PresetId = Guid.NewGuid().ToString() };
            _presetSaverServiceMock.Setup(x => x.GetPresetIdByCatalogId(catalogId))
                .ReturnsAsync(preset);

            // Act
            var result = await _controller.GetPresetByCatalogId(catalogId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<PresetDto>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(preset.ConfiguratorId, apiResponse.Data?.ConfiguratorId);
            Assert.Equal(preset.PresetId, apiResponse.Data?.PresetId);
        }

        [Fact]
        public async Task GetProductDetails_WithValidCode_ReturnsOk()
        {
            // Arrange
            var productCode = "TEST123";
            var productDetails = new ProductDetails(new EshopProduct(), new List<EshopProductSpecItem>());
            _eshopApiServiceMock.Setup(x => x.GetProductDetailsAsync(productCode))
                .ReturnsAsync(ApiResult<ProductDetails>.Success(productDetails));

            // Act
            var result = await _controller.GetProductDetails(productCode);

            // Assert
            Assert.IsType<OkObjectResult>(result);
            var okObjectResult = result as OkObjectResult;
            Assert.NotNull(okObjectResult?.Value);
        }

        [Fact]
        public async Task GetProductDetails_WithInvalidCode_ReturnsBadRequest()
        {
            // Arrange
            var productCode = "INVALID";
            _eshopApiServiceMock.Setup(x => x.GetProductDetailsAsync(productCode))
                .ReturnsAsync(ApiResult<ProductDetails>.Error("Product not found"));

            // Act
            var result = await _controller.GetProductDetails(productCode);

            // Assert
            var badRequestObjectResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestObjectResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestObjectResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Product not found", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task CalculateProductPrices_ReturnsBadRequest_WhenErrorOccurs()
        {
            // Arrange
            var request = new ProductPriceCalculationRequestDto();
            var validation = new ValidationResult();
            validation.AddError("Error message");
            _configuratorServiceMock.Setup(x => x.CalculateProductPricesAsync(
                    It.IsAny<int>(), It.IsAny<List<SelectedProductDto>>(), It.IsAny<string>()))
                .ReturnsAsync((null, validation));

            // Act
            var result = await _controller.CalculateProductPrices(request);

            // Assert
            var badRequestResult = Assert.IsType<ObjectResult>(result, exactMatch: false);
            Assert.Equal(422, badRequestResult.StatusCode); // Unprocessable Entity
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.NotNull(apiResponse.ValidationErrors);
            Assert.Contains("Error message", apiResponse.ValidationErrors!);
        }

        [Fact]
        public async Task CalculateProductPrices_ReturnsOkResult_WithPriceCalculation()
        {
            // Arrange
            var request = new ProductPriceCalculationRequestDto();
            var response = new ProductPriceCalculationResponseDto();
            var validation = new ValidationResult();
            _configuratorServiceMock.Setup(x => x.CalculateProductPricesAsync(
                    It.IsAny<int>(), It.IsAny<List<SelectedProductDto>>(), It.IsAny<string>()))
                .ReturnsAsync((response, validation));

            // Act
            var result = await _controller.CalculateProductPrices(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<ProductPriceCalculationResponseDto>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(response, apiResponse.Data);
        }
    }
}