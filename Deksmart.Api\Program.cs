using DEK.Eshop.ApiCore.Bootstrap;
using Deksmart.Infrastructure.Context;
using Deksmart.Application.Mapping;
using System.Text;
using Deksmart.Infrastructure.Repository;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Deksmart.Application.Service.Base;
using System.Net.Http.Headers;
using Polly;
using Polly.Extensions.Http;
using System.Text.Json.Serialization;
using System.Text.Json;
using Deksmart.Application.Service;
using Deksmart.Application.Service.Http;
using Deksmart.Api.Validator;
using Deksmart.Infrastructure.Cache;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Infrastructure.Validation;
using Deksmart.Api.HealthChecks;
using Deksmart.Infrastructure.Config;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Deksmart.Api
{
    public partial class Program
    {
        private static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            builder.Services.AddCoreBootstrap(builder);

            RegisterRepositories(builder);

            RegisterServices(builder);

            builder.Services.AddControllers()
                .AddJsonOptions(options =>
                {
                    options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                    options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
                });

            builder.Services.AddScoped<IConfiguratorRouteValidator, ConfiguratorRouteValidator>();
            builder.Services.AddScoped<IPresetRouteValidator, PresetRouteValidator>();
            builder.Services.AddScoped<IEmailConfiguratorStateValidator, EmailConfiguratorStateValidator>();

            // Add health checks
            builder.Services.AddHealthChecks()
                .AddCheck<PostgresHealthCheck>("postgres-db")
                .AddCheck<RedisHealthCheck>("redis-cache");

            // Add response compression
            builder.Services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
            });

            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            var app = builder.Build();

            var supportedCultures = new[] { "cs-CZ", "en-US", "sk-SK" };
            var defaultCulture = builder.Configuration.GetValue<string>("Application:DefaultCulture") ?? supportedCultures[0];
            var localizationOptions = new RequestLocalizationOptions()
                .SetDefaultCulture(defaultCulture)
                .AddSupportedCultures(supportedCultures)
                .AddSupportedUICultures(supportedCultures);

            app.UseRequestLocalization(localizationOptions);

            // Configure HTTPS redirection in non-development environments
            if (!app.Environment.IsDevelopment())
            {
                app.UseHsts();
                app.UseHttpsRedirection();
            }

            app.UseCoreBootstrap();

            // Map health check endpoint
            app.MapHealthChecks("/health");

            app.Run();
    }

        private static void RegisterRepositories(WebApplicationBuilder builder)
        {
            // Register ConfiguratorContext with configuration
            builder.Services.AddDbContext<ConfiguratorContext>((serviceProvider, options) =>
            {
                var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                var config = configuration.GetSection("ConfiguratorDb").Get<ConfiguratorDbConfig>();

                if (config != null)
                {
                    var connectionString = new NpgsqlConnectionStringBuilder
                    {
                        Host = config.Host,
                        Port = config.Port,
                        Database = config.Database,
                        Username = config.Username,
                        Password = config.Password,
                        Pooling = config.Pooling,
                        IncludeErrorDetail = true
                    }.ConnectionString;

                    options.UseNpgsql(connectionString);
                }
            });
            builder.Services.AddScoped<IConfiguratorRepository, ConfiguratorRepository>();
            builder.Services.AddScoped<IConfiguratorCompositionRepository, ConfiguratorCompositionRepository>();
            builder.Services.AddScoped<IConfiguratorFieldRepository, ConfiguratorFieldRepository>();
            builder.Services.AddScoped<IConfiguratorProductRepository, ConfiguratorProductRepository>();
            builder.Services.AddScoped<IConfiguratorFieldValueRepository, ConfiguratorFieldValueRepository>();
            builder.Services.AddScoped<IConfiguratorFieldCategoryRepository, ConfiguratorFieldCategoryRepository>();
            builder.Services.AddScoped<IConfiguratorPresetRepository, ConfiguratorPresetRepository>();
            builder.Services.AddScoped<IPresetConfiguratorCategoryStateRepository, PresetConfiguratorCategoryStateRepository>();
            builder.Services.AddScoped<IChildConfiguratorRepository, ChildConfiguratorRepository>();
    }

        private static void RegisterServices(WebApplicationBuilder builder)
        {
            builder.Services.AddLogging();

            builder.Services.AddLocalization();

            // Register base validation services
            builder.Services.AddScoped<IValidationService, ValidationService>();
            builder.Services.AddScoped<IApplicationService, ApplicationService>();

            builder.Services.AddScoped<IConfiguratorProcessingService, ConfiguratorProcessingService>();
            builder.Services.AddScoped<IProductProcessingService, ProductProcessingService>();
            builder.Services.AddScoped<IConfiguratorService, ConfiguratorService>();
            builder.Services.AddScoped<ICalculator, Calculator>();
            builder.Services.AddScoped<IConfiguratorExpressionCalculator, ConfiguratorExpressionCalculator>();
            builder.Services.AddScoped<IFieldValueMatcher, FieldValueMatcherService>();
            builder.Services.AddScoped<IConfiguratorCacheManager, ConfiguratorCacheManager>();
            builder.Services.AddScoped<IConfiguratorPresetService, ConfiguratorPresetService>();
            builder.Services.AddScoped<IConfiguratorExistenceValidator, ConfiguratorExistenceValidator>();
            builder.Services.AddScoped<IConfiguratorValidationService, ConfiguratorValidationService>();

            builder.Services.AddScoped<IImportProcessingService, ImportProcessingService>();
            builder.Services.AddScoped<IConfiguratorImportService, ConfiguratorImportService>();
            builder.Services.AddScoped<IImportValidationService, ImportValidationService>();
            builder.Services.AddScoped<IConfiguratorImportParser, ConfiguratorImportParser>();
            builder.Services.AddScoped<IMarkdownService, MarkdownService>();
            builder.Services.AddScoped<IConfiguratorViewGenerator, ConfiguratorViewGenerator>();
            builder.Services.AddScoped<IConfiguratorViewOrchestrator, ConfiguratorViewOrchestrator>();

            builder.Services.AddHttpClient("EShop", client =>
            {
                client.BaseAddress = new Uri("https://api-eshop.delta.dek.cz/");
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************D1U_P0NUWJl90X7-v0jApj2tbxiF7JIqGo5bcWTOOXbF3AvG16hVCKgEChYqT1bQ5qjoydRmQkjJuvI82xs9Y09XGnkfuvGHy_4HEpPLsKRo1e6bu-4yjlHi6orC5qHlY-8ynOqE80KWXm8qEtvwhPvofn6wkfw0xCfOMJQ0C6eDIYjBOURvtSL2W8tpZYHSJBDE7Xma3afuH1p05nS1XaLqLeWA_PT94B7-RJ1frrzVzhyJJYMoK5BLKjNsBTnCxYWrdohC4EAU8kOpZ1xmg-UL3gjMl_k-mJMllBK5FMoybWc1Z6jevQcwqqwCD3HUyOqWpiPziYoL-ss8NnkfRfi2HGIqoiItX00PJEDeoJ_5D6QblxGBtX2m64SDRD877Bg4nrbLS9DM3q5wcQDUhDQmtC559JFA-ju63BNrrf");
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.Timeout = TimeSpan.FromSeconds(30);
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

            builder.Services.AddScoped<IEshopApiService, EshopApiService>();
            builder.Services.AddScoped<IEshopMappingService, EshopMappingService>();
            builder.Services.AddScoped<IConfiguratorMappingService, ConfiguratorMappingService>();
            builder.Services.AddScoped<IProductAggregationMappingService, ProductAggregationMappingService>();
            builder.Services.AddScoped<IProductPriceService, ProductPriceService>();
            builder.Services.AddScoped<ICompositeImportService, CompositeImportService>();
    }

        private static Polly.Retry.AsyncRetryPolicy<HttpResponseMessage> GetRetryPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(3, retryAttempt =>
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }

        private static Polly.CircuitBreaker.AsyncCircuitBreakerPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
        }
    }
}
