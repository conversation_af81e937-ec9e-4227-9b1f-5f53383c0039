﻿.accordion-item {
    background: white;
    margin-bottom: 0;
    overflow: hidden;
    border: none;
    position: relative;
    border-bottom: 1px solid #e9e9e9;
}

.accordion-header {
    padding: 0.75rem 1rem;
    background-color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    position: relative;
    border-left: 4px solid #ccc;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    user-select: none;
}

/* Product Listing Header - Make it visually distinct from regular categories */
.product-listing-header {
    background-color: var(--secondary-color);
    border-left: 4px solid var(--primary-color);
    margin-top: var(--spacing-lg);
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
}

.product-listing-header .accordion-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
}

.product-listing-item {
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.product-listing-content {
    background-color: #f9f9f9;
    padding: var(--spacing-lg);
}

.accordion-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--secondary-color);
    flex-grow: 1;
}

/* Arrow indicator - matches field categories behavior */
.arrow {
    position: absolute;
    right: 1rem;
    display: inline-block;
    transition: transform 0.3s ease;
    color: var(--primary-color, #c00c18);
    font-size: 0.8rem;
    font-weight: bold;
}

.arrow.collapsed {
    transform: rotate(-90deg);
}

/* Ensure accordion button has proper positioning for arrow */
.accordion-button {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.accordion-content {
    padding: 1.5rem 1.25rem;
    background-color: white;
    border-top: none;
    box-shadow: inset 0 5px 5px -5px rgba(0,0,0,0.05);
}

.composition-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.composition-table {
    display: grid;
    grid-template-columns: auto minmax(200px, 1fr) auto auto auto auto;
    gap: 0;
    margin-bottom: 0;
    border: none;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.composition-header {
    display: contents;
}

.composition-header-cell {
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-size: 0.9rem;
    border-bottom: 1px solid var(--light-border);
    background-color: var(--secondary-color);
    font-weight: 600;
    color: white;
}

.composition-header-cell[style*="grid-column: 3 / 5"],
.composition-header-cell[style*="grid-column: 5 / 7"] {
    background-color: var(--secondary-color);
}

.composition-row {
    display: contents;
}

.composition-white .composition-property {
    background-color: white;
}

.composition-lightgray .composition-property {
    background-color: var(--light-color);
}

.composition-property {
    padding: 0.5rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    line-height: 1.2;
    border-bottom: 1px solid var(--light-border);
}

.composition-property:nth-child(3),
.composition-property:nth-child(5) {
    justify-content: flex-end;
}

.composition-property .button {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    margin: 0 0.25rem;
    min-height: 24px;
    background-color: white;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
}

.composition-property .button:hover {
    background-color: var(--primary-color);
    color: white;
}

.composition-property select.form-control {
    padding: 0.25rem 0.5rem;
    font-size: 0.9rem;
    height: 30px;
    width: 100%;
    min-width: 120px;
    margin: 0;
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
}

.composition-property input[type="number"] {
    width: 80px;
    padding: 0.25rem 0.5rem;
    font-size: 0.9rem;
    height: 30px;
    margin: 0;
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
}

.single-line-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--spacing-md);
    padding: 0.75rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
    margin-top: var(--spacing-sm);
    font-size: 0.9rem;
    box-shadow: var(--shadow-sm);
}

.single-line-right b {
    margin-right: var(--spacing-sm);
    width: 85%;
    text-align: right;
    color: var(--secondary-color);
    font-weight: 600;
}

.single-line-right span {
    width: 15%;
    text-align: right;
}

.desktop-only {
    display: contents;
}

.mobile-only {
    display: none;
}

@media (max-width: 767px) {
    .desktop-only {
        display: none !important;
    }

    .mobile-only {
        display: block;
    }

    .composition-table {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-sm);
        box-shadow: none;
        border: none;
    }

    .composition-header {
        display: none;
    }

    .composition-mobile {
        margin-bottom: var(--spacing-md);
        border-radius: var(--border-radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--light-border);
    }

    .composition-mobile:nth-child(odd) {
        background-color: var(--light-color);
    }

    .composition-mobile:nth-child(even) {
        background-color: white;
    }

    .composition-mobile-header {
        padding: 0.75rem 1rem;
        background-color: rgba(0, 0, 0, 0.03);
        border-bottom: 1px solid var(--light-border);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .composition-mobile-header .button {
        padding: 0.25rem 0.5rem;
        font-size: 0.85rem;
        margin: 0 0 0 var(--spacing-sm);
        min-height: 24px;
        border-radius: var(--border-radius-sm);
        background-color: white;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
    }

    .composition-mobile-header .button:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .composition-mobile-title {
        font-weight: 600;
        font-size: 1rem;
        color: var(--secondary-color);
    }

    .composition-mobile-row {
        padding: var(--spacing-md);
    }

    .composition-mobile-flex-row {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .composition-mobile-property {
        display: grid;
        grid-template-columns: 120px 1fr;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 0.9rem;
        line-height: 1.2;
        margin-bottom: var(--spacing-sm);
    }

    .composition-mobile-property:last-child {
        margin-bottom: 0;
    }

    .composition-mobile-property::before {
        content: attr(data-label);
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--secondary-color);
    }

    .mobile-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
    }

    .mobile-content .button {
        padding: 0.25rem 0.5rem;
        font-size: 0.85rem;
        margin: 0 0.25rem;
        min-height: 24px;
        border-radius: var(--border-radius-sm);
        background-color: white;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
        transition: all 0.2s ease;
    }

    .mobile-content .button:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .mobile-content select.form-control {
        width: 100%;
        min-width: unset;
        padding: 0.25rem 0.5rem;
        font-size: 0.9rem;
        height: 30px;
        border-radius: var(--border-radius-sm);
        border: 1px solid var(--light-border);
    }

    .mobile-content input[type="number"] {
        width: 80px;
        padding: 0.25rem 0.5rem;
        font-size: 0.9rem;
        height: 30px;
        border-radius: var(--border-radius-sm);
        border: 1px solid var(--light-border);
    }

    .product-number-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        width: 100%;
        justify-content: space-between;
    }

    .product-number-group span {
        justify-content: flex-start;
    }

    .product-number-group .button {
        justify-content: flex-end;
    }

    .quantity-package-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        width: 100%;
        justify-content: space-between;
    }

    .quantity-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        white-space: nowrap;
        justify-content: flex-start;
    }

    .package-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        white-space: nowrap;
        justify-content: flex-end;
    }

    .price-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        width: 100%;
        justify-content: space-between;
    }

    .price-per-package-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        white-space: nowrap;
        justify-content: flex-start;
    }

    .total-price-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        white-space: nowrap;
        justify-content: flex-end;
    }

    .composition-mobile-property[data-label="ProductNumber"] .mobile-content,
    .composition-mobile-property[data-label="ProductName"] .mobile-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .composition-mobile-property[data-label="QuantityPerStructure"] .mobile-content,
    .composition-mobile-property[data-label="PriceVAT"] .mobile-content {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        width: 100%;
    }

    .single-line-right {
        margin-top: var(--spacing-md);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.composition-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
}

.composition-loading-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    font-size: 0.9rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.composition-loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Product error styling for better UX visibility */
.composition-error {
    background-color: #fff3cd !important; /* Light warning background */
    border-left: 4px solid #ffc107 !important; /* Warning border */
}

.composition-error .composition-property {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107;
}

/* Mobile error styling */
.composition-mobile.composition-error {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    border-color: #ffc107 !important;
}

.composition-mobile.composition-error .composition-mobile-header {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

/* Error icon styling */
.text-warning {
    color: #ffc107 !important;
}

/* Tooltip styling for error icons */
.fas.fa-exclamation-triangle[title]:hover {
    cursor: help;
}

/* Error text styling in price cells */
.text-danger {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}
