using Microsoft.Extensions.Logging;
using Moq;
using Deksmart.Ui.Shared.Services;
using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Factory;
using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class ChildServiceManagerTest
    {
        private readonly Mock<ILogger> _mockLogger;
        private readonly Mock<IConfiguratorGridServiceFactory> _mockServiceFactory;
        private readonly Mock<IConfiguratorGridService> _mockParentService;
        private readonly ChildServiceManager _manager;
        private readonly List<IConfiguratorGridService> _childServices;

        public ChildServiceManagerTest()
        {
            _mockLogger = new Mock<ILogger>();
            _mockServiceFactory = new Mock<IConfiguratorGridServiceFactory>();
            _mockParentService = new Mock<IConfiguratorGridService>();
            _manager = new ChildServiceManager();
            _childServices = new List<IConfiguratorGridService>();
        }

        private ConfiguratorDto CreateTestConfiguratorDto(bool withChildren = true)
        {
            var dto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                IsValid = true,
                Url = "test-url"
            };

            if (withChildren)
            {
                dto.ChildUserConfigurators = new List<ConfiguratorDto>
                {
                    new ConfiguratorDto
                    {
                        Id = 2,
                        Title = "Child 1",
                        Description = "Child Description 1",
                        TabOrder = 1,
                        IsValid = true,
                        Url = "child-1-url"
                    },
                    new ConfiguratorDto
                    {
                        Id = 3,
                        Title = "Child 2",
                        Description = "Child Description 2",
                        TabOrder = 2,
                        IsValid = true,
                        Url = "child-2-url"
                    }
                };
            }

            return dto;
        }

        private Mock<IConfiguratorGridService> CreateMockChildService()
        {
            var mockService = new Mock<IConfiguratorGridService>();
            mockService.SetupAllProperties();
            return mockService;
        }

        [Fact]
        public void InitializeChildServices_WithNullChildConfigurators_DoesNotCreateServices()
        {
            var dto = CreateTestConfiguratorDto(withChildren: false);
            dto.ChildUserConfigurators = null;

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            Assert.Empty(_childServices);
            _mockServiceFactory.Verify(f => f.CreateService(), Times.Never);
        }

        [Fact]
        public void InitializeChildServices_WithEmptyChildConfigurators_DoesNotCreateServices()
        {
            var dto = CreateTestConfiguratorDto(withChildren: false);
            dto.ChildUserConfigurators = new List<ConfiguratorDto>();

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            Assert.Empty(_childServices);
            _mockServiceFactory.Verify(f => f.CreateService(), Times.Never);
        }

        [Fact]
        public void InitializeChildServices_WithChildConfigurators_CreatesServices()
        {
            var dto = CreateTestConfiguratorDto();
            var mockChild1 = CreateMockChildService();
            var mockChild2 = CreateMockChildService();

            _mockServiceFactory.SetupSequence(f => f.CreateService())
                .Returns(mockChild1.Object)
                .Returns(mockChild2.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            Assert.Equal(2, _childServices.Count);
            _mockServiceFactory.Verify(f => f.CreateService(), Times.Exactly(2));
        }

        [Fact]
        public void InitializeChildServices_SetsParentServiceOnChildren()
        {
            var dto = CreateTestConfiguratorDto();
            var mockChild1 = CreateMockChildService();
            var mockChild2 = CreateMockChildService();

            _mockServiceFactory.SetupSequence(f => f.CreateService())
                .Returns(mockChild1.Object)
                .Returns(mockChild2.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            mockChild1.VerifySet(s => s.MainConfiguratorGridService = _mockParentService.Object, Times.Once);
            mockChild2.VerifySet(s => s.MainConfiguratorGridService = _mockParentService.Object, Times.Once);
        }

        [Fact]
        public void InitializeChildServices_SetsConfiguratorWrapperOnChildren()
        {
            var dto = CreateTestConfiguratorDto();
            var mockChild1 = CreateMockChildService();
            var mockChild2 = CreateMockChildService();

            _mockServiceFactory.SetupSequence(f => f.CreateService())
                .Returns(mockChild1.Object)
                .Returns(mockChild2.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            mockChild1.VerifySet(s => s.ConfiguratorWrapper = It.IsNotNull<ConfiguratorWrapper>(), Times.Once);
            mockChild2.VerifySet(s => s.ConfiguratorWrapper = It.IsNotNull<ConfiguratorWrapper>(), Times.Once);
        }

        [Fact]
        public void InitializeChildServices_WithValidationHandler_SubscribesToEvents()
        {
            var dto = CreateTestConfiguratorDto();
            var mockChild1 = CreateMockChildService();
            var mockChild2 = CreateMockChildService();
            EventHandler validationHandler = (sender, e) => { };

            _mockServiceFactory.SetupSequence(f => f.CreateService())
                .Returns(mockChild1.Object)
                .Returns(mockChild2.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, validationHandler, _mockLogger.Object);

            mockChild1.VerifyAdd(s => s.ValidationStateChanged += validationHandler, Times.Once);
            mockChild2.VerifyAdd(s => s.ValidationStateChanged += validationHandler, Times.Once);
        }

        [Fact]
        public void InitializeChildServices_WithNullValidationHandler_DoesNotSubscribeToEvents()
        {
            var dto = CreateTestConfiguratorDto();
            var mockChild = CreateMockChildService();

            _mockServiceFactory.Setup(f => f.CreateService()).Returns(mockChild.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            mockChild.VerifyAdd(s => s.ValidationStateChanged += It.IsAny<EventHandler>(), Times.Never);
        }

        [Fact]
        public void InitializeChildServices_OrdersByTabOrder()
        {
            var dto = CreateTestConfiguratorDto(withChildren: false);
            dto.ChildUserConfigurators = new List<ConfiguratorDto>
            {
                new ConfiguratorDto { Id = 3, Title = "Third", TabOrder = 3, IsValid = true, Url = "third-url" },
                new ConfiguratorDto { Id = 1, Title = "First", TabOrder = 1, IsValid = true, Url = "first-url" },
                new ConfiguratorDto { Id = 2, Title = "Second", TabOrder = 2, IsValid = true, Url = "second-url" }
            };

            var mockChildren = new List<Mock<IConfiguratorGridService>>
            {
                CreateMockChildService(),
                CreateMockChildService(),
                CreateMockChildService()
            };

            _mockServiceFactory.SetupSequence(f => f.CreateService())
                .Returns(mockChildren[0].Object)
                .Returns(mockChildren[1].Object)
                .Returns(mockChildren[2].Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            Assert.Equal(3, _childServices.Count);
            _mockServiceFactory.Verify(f => f.CreateService(), Times.Exactly(3));
        }

        [Fact]
        public void InitializeChildServices_WithNullTabOrder_OrdersAtEnd()
        {
            var dto = CreateTestConfiguratorDto(withChildren: false);
            dto.ChildUserConfigurators = new List<ConfiguratorDto>
            {
                new ConfiguratorDto { Id = 2, Title = "HasOrder", TabOrder = 1, IsValid = true, Url = "has-order-url" },
                new ConfiguratorDto { Id = 1, Title = "NoOrder", TabOrder = null, IsValid = true, Url = "no-order-url" }
            };

            var mockChildren = new List<Mock<IConfiguratorGridService>>
            {
                CreateMockChildService(),
                CreateMockChildService()
            };

            _mockServiceFactory.SetupSequence(f => f.CreateService())
                .Returns(mockChildren[0].Object)
                .Returns(mockChildren[1].Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            Assert.Equal(2, _childServices.Count);
        }

        [Fact]
        public void InitializeChildServices_DisposesExistingServices()
        {
            var existingMockService = CreateMockChildService();
            _childServices.Add(existingMockService.Object);

            var dto = CreateTestConfiguratorDto();
            var newMockService = CreateMockChildService();
            _mockServiceFactory.Setup(f => f.CreateService()).Returns(newMockService.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            existingMockService.Verify(s => s.Dispose(), Times.Once);
        }

        [Fact]
        public void InitializeChildServices_LogsInformation()
        {
            var dto = CreateTestConfiguratorDto();
            var mockChild = CreateMockChildService();

            _mockServiceFactory.Setup(f => f.CreateService()).Returns(mockChild.Object);

            _manager.InitializeChildServices(dto, _childServices, _mockServiceFactory.Object, _mockParentService.Object, null, _mockLogger.Object);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Initializing child services")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((o, t) => o.ToString().Contains("Added child service")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Exactly(2));
        }

        [Fact]
        public void DisposeChildServices_DisposesAllServices()
        {
            var mockService1 = CreateMockChildService();
            var mockService2 = CreateMockChildService();
            _childServices.AddRange(new[] { mockService1.Object, mockService2.Object });

            _manager.DisposeChildServices(_childServices, null);

            mockService1.Verify(s => s.Dispose(), Times.Once);
            mockService2.Verify(s => s.Dispose(), Times.Once);
        }

        [Fact]
        public void DisposeChildServices_ClearsServicesList()
        {
            var mockService = CreateMockChildService();
            _childServices.Add(mockService.Object);

            _manager.DisposeChildServices(_childServices, null);

            Assert.Empty(_childServices);
        }

        [Fact]
        public void DisposeChildServices_WithValidationHandler_UnsubscribesFromEvents()
        {
            var mockService = CreateMockChildService();
            _childServices.Add(mockService.Object);
            EventHandler validationHandler = (sender, e) => { };

            _manager.DisposeChildServices(_childServices, validationHandler);

            mockService.VerifyRemove(s => s.ValidationStateChanged -= validationHandler, Times.Once);
        }

        [Fact]
        public void DisposeChildServices_WithNullValidationHandler_DoesNotUnsubscribe()
        {
            var mockService = CreateMockChildService();
            _childServices.Add(mockService.Object);

            _manager.DisposeChildServices(_childServices, null);

            mockService.VerifyRemove(s => s.ValidationStateChanged -= It.IsAny<EventHandler>(), Times.Never);
        }

        [Fact]
        public void DisposeChildServices_WithEmptyList_DoesNotThrow()
        {
            var exception = Record.Exception(() => _manager.DisposeChildServices(_childServices, null));

            Assert.Null(exception);
            Assert.Empty(_childServices);
        }
    }
}