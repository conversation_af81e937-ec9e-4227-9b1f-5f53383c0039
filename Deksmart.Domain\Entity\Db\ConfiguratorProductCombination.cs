﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Represents the saved state of a single product selection within a configurator preset.
    /// Links a preset to a specific product and stores the selected amount, enabling restoration of product choices and quantities when reloading a configurator preset.
    /// </summary>
    [PrimaryKey("PresetId", "ProductId")]
    [Table("configurator_product_combination", Schema = "dbo")]
    [Index("PresetId", Name = "idx_configurator_product_combination_product_id")]
    public partial class ConfiguratorProductCombination
    {
        [Key]
        [Column("preset_id")]
        public Guid PresetId { get; set; }

        [Key]
        [Column("product_id")]
        public int ProductId { get; set; }

        [Column("amount")]
        public decimal Amount { get; set; }
    }
}
