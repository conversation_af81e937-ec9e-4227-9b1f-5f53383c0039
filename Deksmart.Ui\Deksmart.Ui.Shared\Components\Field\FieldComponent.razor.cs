﻿using Deksmart.Ui.Model;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;

namespace Deksmart.Ui.Shared.Components.Field
{
    public partial class FieldComponent : ComponentBase
    {
        [Parameter]
        public ConfiguratorFieldWrapper Field { get; set; } = default!;

        [Parameter]
        public EventCallback OnDataLoaded { get; set; }

        [Parameter]
        public EventCallback<(int, IdFieldWrapper)> OnIdWrapperValueChange { get; set; }

        [Inject]
        public ILogger<FieldComponent> Logger { get; set; } = default!;

        [Inject]
        public IJSRuntime JSRuntime { get; set; } = default!;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && !string.IsNullOrEmpty(Field.JavaScriptAfterRender))
            {
                await JSRuntime.InvokeVoidAsync("eval", Field.JavaScriptAfterRender);
            }
        }

        protected async Task OnTileClick(IdFieldWrapper tileField, ConfiguratorFieldValueWrapper value)
        {
            Logger.LogInformation($"OnTileClick method called: {value}.");
            tileField.IsPendingValidation = true;
            await OnIdWrapperValueChange.InvokeAsync((value.Id, tileField));
            StateHasChanged();
        }

        protected string GetTileClass(TileFieldWrapper tileField, ConfiguratorFieldValueWrapper value)
        {
            return tileField.Value == value.Id ? "selected-tile" : string.Empty;
        }

        protected async Task OnTooltipShow(int id)
        {
            Logger.LogInformation($"Mouse over info button {id}");
            await JSRuntime.InvokeVoidAsync("updateTooltipPosition", $"info-button-{id}", $"tooltip-{id}");
        }

        protected async Task OnTooltipHide(int id)
        {
            Logger.LogInformation($"Mouse out info button {id}");
            await JSRuntime.InvokeVoidAsync("hideTooltip", $"tooltip-{id}");
        }
    }
}
