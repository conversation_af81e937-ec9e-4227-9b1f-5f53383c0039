using Deksmart.Shared.Enum;

namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing the persisted and transferable UI state (collapsed/expanded) of a specific field category in a configurator.
    /// Used to save, restore, and synchronize the user's category visibility preferences across sessions, devices, and API boundaries.
    /// Supports both preset persistence and dynamic UI rendering by capturing the collapse/expand state for each category.
    /// </summary>
    [Serializable]
    public class CategoryStateDto
    {
        public int CategoryId { get; set; }
        /// <summary>
        /// Represents the collapse/expand state of the category for UI and API.
        /// </summary>
        public CategoryCollapseStateDto CollapseState { get; set; }
    }
}
