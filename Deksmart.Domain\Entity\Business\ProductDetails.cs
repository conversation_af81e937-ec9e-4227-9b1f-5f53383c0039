using Deksmart.Domain.Entity.Business.Eshop;

namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Aggregates all relevant details and specifications for a single product, combining core product data and its technical/specification attributes.
    /// Used to transfer, present, and process detailed product information in the configurator, especially when a field is tied to a specific product.
    /// Typically constructed from e-shop API data and used in API responses, business logic, and UI mapping.
    /// </summary>
    public class ProductDetails
    {
        public EshopProduct Product { get; set; }
        public List<EshopProductSpecItem> Specifications { get; set; }

        public ProductDetails(EshopProduct product, List<EshopProductSpecItem> specifications)
        {
            Product = product;
            Specifications = specifications;
        }
    }
} 