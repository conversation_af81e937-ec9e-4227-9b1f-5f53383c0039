using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Config.Dto;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using System.Reflection;

namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class SwaggerBootstrap
{
    /// <exception cref="ConfigException"></exception>
    public static void AddSwaggerBootstrap(this IServiceCollection services, IHostEnvironment env, IConfiguration configuration)
    {
        // Skip in tests because of Comments.xml
        if (env.EnvironmentName == Environment.Test) {
            return;
        }

        var configSwagger = ConfigFactory.Create<Swagger>(configuration, "ApiCore:Swagger");

        if (!configSwagger.Enabled) {
            return;
        }

        services.AddSwaggerGen(o => {
            o.SwaggerDoc("spec", new OpenApiInfo { Title = configSwagger.ProjectName, Version = configSwagger.Version });
            o.EnableAnnotations();
            o.SupportNonNullableReferenceTypes();
            o.AddSecurityDefinition("bearerAuth", new OpenApiSecurityScheme {
                Type = SecuritySchemeType.Http,
                Scheme = "bearer",
                BearerFormat = "JWT",
                Description = "JWT Authorization header using the Bearer scheme."
            });
            o.AddSecurityRequirement(new OpenApiSecurityRequirement {
                {
                    new OpenApiSecurityScheme {
                        Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "bearerAuth" }
                    },
                    Array.Empty<string>()
                }
            });
            o.CustomSchemaIds(type => type.ToString()); // FullNames for schema names

            // https://learn.microsoft.com/en-us/aspnet/core/tutorials/getting-started-with-swashbuckle?view=aspnetcore-7.0&tabs=visual-studio#xml-comments
            var xmlFilename = $"Comments.xml";
            o.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
        });
    }

    /// <exception cref="ConfigException"></exception>
    public static void UseSwaggerBootstrap(this WebApplication app, IHostEnvironment env)
    {
        // Skip in tests because of Comments.xml
        if (env.EnvironmentName == Environment.Test) {
            return;
        }

        var configRoute = ConfigFactory.Create<Route>(app.Configuration, "ApiCore:Route");
        var configSwagger = ConfigFactory.Create<Swagger>(app.Configuration, "ApiCore:Swagger");

        if (!configSwagger.Enabled) {
            return;
        }

        var basePath = configRoute.BasePath.TrimStart('/');

        app.UseSwagger(c => {
            c.RouteTemplate = configRoute.BasePath + "/doc/{documentName}/swagger.json";
        });


        app.UseSwaggerUI(c => {
            c.RoutePrefix = basePath + "/doc";
            c.SwaggerEndpoint("spec/swagger.json", configSwagger.ProjectName);
            c.ConfigObject.AdditionalItems.Add("persistAuthorization", "true"); // remember front-end login
            c.InjectJavascript('/' + basePath + "/swagger-ui-inject.js");
        });

        var assembly = Assembly.Load("DEK.Eshop.ApiCore");
        var embeddedProvider = new EmbeddedFileProvider(assembly, "DEK.Eshop.ApiCore.Static");
        app.UseStaticFiles(new StaticFileOptions() {
            FileProvider = embeddedProvider,
            RequestPath = new PathString('/' + basePath)
        });

        app.UseReDoc(c => {
            c.RoutePrefix = basePath + "/redoc";
            c.SpecUrl('/' + basePath + "/doc/spec/swagger.json");
        });
    }
}
