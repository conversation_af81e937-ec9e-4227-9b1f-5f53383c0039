
using DEK.Eshop.ApiCore.Config.Dto;
using DEK.Eshop.ApiCore.Indentity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

namespace DEK.Eshop.ApiCore.Config;

public class ConfigService : IConfigService
{
    protected readonly IConfiguration _config;
    protected readonly IHostEnvironment _env;
    protected readonly HttpUserFactory _httpUserFactory;

    protected IApplication _configApp;

    public ConfigService(IConfiguration config, IHostEnvironment env, HttpUserFactory httpUserFactory)
    {
        this._config = config;
        this._env = env;
        this._httpUserFactory = httpUserFactory;
        this._configApp = this.Create<Application>();
    }

    public T Create<T>(string? key = null)
    {
        return ConfigFactory.Create<T>(this._config, key);
    }

    virtual public string GetEshopId()
    {
        var user = this._httpUserFactory.Create();
        return user.EshopId ?? string.Empty;
    }

    virtual public Mssql GetMssql()
    {
        var user = this._httpUserFactory.Create();
        var confing = ConfigFactory.Create<Mssql>(this._config);
        confing.Database = user.Database ?? confing.Database;
        return confing;
    }

}

