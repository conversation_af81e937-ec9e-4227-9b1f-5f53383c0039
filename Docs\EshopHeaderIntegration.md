# Integrace e-shop hlavičky a menu

Tento dokument popisuje technickou implementaci řešení pro získávání a zobrazování hlavičky a menu z externího e-shopu v Deksmart aplikaci. Hlavním cílem je integrovat konzistentní branding a navigaci z e-shopu do Deksmart aplikace.

## Základní princip

Aplikace při načtení získá HTML kód z nakonfigurovaného e-shopu (aktuálně dek.cz), extrahuje potřebné elementy (header a megamenu) a vloží je do Deksmart UI. Toto zajistí vizuální konzistenci s hlavním e-shopem.

## Architektura řešení

Řešení je implementováno pomocí serverového proxy mechanismu, který zajišťuje:
1. Stažení HTML obsahu z externího e-shopu
2. Extrakci specifických elementů hlavičky a menu z HTML obsahu
3. <PERSON><PERSON><PERSON><PERSON><PERSON> pouze potřebných elementů klientské aplikaci
4. Integraci prvků do UI aplikace bez CORS problémů

### Serverová část

Serverová část je implementována v `ProxyController.cs` s následujícími endpointy:

- **GET /api/proxy/eshop-homepage** - Vrací celý HTML obsah e-shop stránky
- **GET /api/proxy/eshop-header** - Vrací pouze vyextrahované elementy hlavičky a menu

Konfigurace e-shopu je umístěna v `appsettings.json` v sekci `EshopSettings`:

```json
"EshopSettings": {
  "HomepageUrl": "https://www.dek.cz/",
  "HeaderSelector": "header",
  "MenuSelector": "div#vue-megamenu"
}
```

#### Klíčové části serverové implementace

1. **Konfigurace** - URL e-shopu a CSS selektory pro hlavičku a menu jsou konfigurovatelné
2. **Extrakce HTML elementů** - Implementovány jsou dvě metody:
   - Regulární výrazy pro základní extrakci
   - Záložní metoda s manuálním vyhledáváním při selhání regulárních výrazů
3. **Logování** - Podrobné logování pro diagnostiku a ladění
4. **Ošetření chyb** - Robustní zpracování chybových stavů

### Klientská část

Klientská část je implementována v JavaScript souboru `app.js` a blazor komponentě `MainLayout.razor`.

#### Inicializace a načtení

Hlavička se načítá automaticky při prvním renderování stránky v metodě `OnAfterRenderAsync`:

```csharp
protected override async Task OnAfterRenderAsync(bool firstRender)
{
    if (firstRender)
    {
        await JSRuntime.InvokeVoidAsync("fetchDekHeader");
    }
    await base.OnAfterRenderAsync(firstRender);
}
```

#### JavaScript funkce

Funkce `fetchDekHeader` v souboru `app.js`:
1. Volá endpoint `/api/proxy/eshop-header`
2. Zpracovává vrácené HTML
3. Vkládá ho do cílového elementu s ID `dek-header`
4. Opravuje případné problémy s externími skripty

## Technické detaily implementace

### Extrakce HTML elementů pomocí regex

Extrakce HTML elementů na serveru používá dvouúrovňovou strategii:

#### Úroveň 1: Základní extrakce pomocí regulárních výrazů
```csharp
// Primární vyhledávání pomocí jednoduchého regex
var headerMatch = Regex.Match(content, "<header.*?</header>", RegexOptions.Singleline);
var menuMatch = Regex.Match(content, "<div\\s+id=[\"']vue-megamenu[\"'].*?</div>", RegexOptions.Singleline);
```

Tento přístup funguje pro většinu případů, kdy HTML struktura je relativně jednoduchá a nekombinuje více úrovní stejných tagů.

#### Úroveň 2: Záložní extrakce v případě selhání regex
```csharp
// Záložní metoda při selhání regex
if (!headerMatch.Success)
{
    int headerStartIndex = content.IndexOf("<header");
    if (headerStartIndex >= 0)
    {
        int headerEndIndex = content.IndexOf("</header>", headerStartIndex);
        if (headerEndIndex >= 0)
        {
            headerMatch = new Regex(".*").Match(
                content.Substring(headerStartIndex, headerEndIndex - headerStartIndex + 9)
            );
        }
    }
}
```

Tato záložní metoda používá základní string indexování a substring operace v případě, že regulární výraz selže. Je to robustnější přístup pro složitější HTML struktury, i když méně elegantní.

### JavaScript zpracování

Po obdržení HTML obsahu z proxy provádí klientská strana následující operace:

1. **Vložení HTML do cílového elementu**
   ```javascript
   targetHeader.innerHTML = html;
   ```

2. **Oprava externích skriptů**
   ```javascript
   // Fix any broken script tags or other elements
   const scripts = targetHeader.querySelectorAll('script');
   scripts.forEach(oldScript => {
       if (oldScript.src) {
           // Pro externí skripty vytvoříme a nahradíme novou instancí
           const newScript = document.createElement('script');
           Array.from(oldScript.attributes).forEach(attr => {
               newScript.setAttribute(attr.name, attr.value);
           });
           oldScript.parentNode.replaceChild(newScript, oldScript);
       }
   });
   ```

Toto opravné řešení je potřeba, protože při vložení skriptových elementů pomocí innerHTML prohlížeč tyto skripty automaticky nespustí. Vytvoření nového elementu a jeho nahrazení zajistí, že externí skripty budou načteny a spuštěny.

## Řešení běžných problémů

### CORS omezení

CORS (Cross-Origin Resource Sharing) omezení jsou v našem řešení elegantně obejita:

- **Problém**: Prohlížeče standardně blokují přímé AJAX požadavky na jiné domény z bezpečnostních důvodů.
- **Řešení**: Veškerá komunikace s externím e-shopem probíhá na serveru přes náš vlastní backend.
- **Výhoda**: Klientská aplikace nemusí řešit CORS, jelikož komunikuje pouze s naším API.

### Změny ve struktuře e-shop stránky

Pokud se změní struktura externího e-shopu:

| Typ změny | Vliv na naše řešení | Potřebná akce |
|-----------|---------------------|---------------|
| Drobné změny obsahu | Žádný vliv | Žádná akce |
| Změna CSS tříd/stylů | Žádný vliv na extrakci | Pravděpodobně žádná akce |
| Změna ID elementů | Může selhat extrakce menu | Aktualizace `MenuSelector` v konfiguraci |
| Kompletní změna struktury | Selhání extrakce | Úprava regulárních výrazů v kódu a konfigurace |

### Diagnostika a monitorování

Pro diagnostiku problémů máme implementováno robustní logování:

#### Serverová strana
```csharp
_logger.LogInformation("Retrieved content from eshop, length: {Length}", content?.Length ?? 0);
_logger.LogInformation("Header match found, length: {Length}", headerMatch.Value.Length);
```

#### Klientská strana
```javascript
console.log('Received HTML length:', html.length);
console.log('Eshop header and megamenu injected successfully');
```

**Postup při diagnostice**:
1. Zkontrolujte aplikační logy - obsahují detailní informace o procesu extrakce
2. Zkontrolujte konzoli prohlížeče - zobrazuje informace o zpracování na straně klienta
3. Ověřte konfiguraci v `appsettings.json` - zda URL a selektory odpovídají aktuální struktuře e-shopu

## Možnosti rozšíření 

### Multi-tenant podpora

Pro podporu více e-shopů můžeme rozšířit konfiguraci:

```json
"EshopSettings": {
  "Eshops": [
    {
      "Name": "DEK",
      "HomepageUrl": "https://www.dek.cz/",
      "HeaderSelector": "header",
      "MenuSelector": "div#vue-megamenu",
      "Active": true
    },
    {
      "Name": "Stavebniny",
      "HomepageUrl": "https://www.stavebniny.cz/",
      "HeaderSelector": "header.main-header",
      "MenuSelector": "nav.main-navigation",
      "Active": false
    }
  ],
  "ActiveEshop": "DEK"
}
```

### Pokročilé úpravy obsahu

V budoucnu můžeme implementovat pokročilejší zpracování:

- **CSS transformace** - úprava CSS tříd a stylů pro lepší integraci
- **Filtrování obsahu** - odstranění nežádoucích elementů z hlavičky
- **Lokalizace** - podpora vícejazyčného obsahu hlavičky
- **Caching** - ukládání extrahované hlavičky do cache pro zvýšení výkonu

### Integrace s dalšími částmi e-shopu

Stejný princip lze rozšířit pro integraci dalších částí e-shopu:

- Patička (footer)
- Košík
- Vyhledávání
- Přihlašovací formulář

## Bezpečnostní aspekty

Implementace zajišťuje:

1. **Izolaci** - JavaScript z e-shopu je izolován v rámci hlavičky
2. **Sanitizaci** - HTML je zpracováno a není automaticky vykonáváno
3. **Zabezpečení** - Citlivá data nejsou sdílena s externími servery