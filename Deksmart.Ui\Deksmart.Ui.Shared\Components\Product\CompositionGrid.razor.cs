﻿using Deksmart.Ui.Model;
using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components;

namespace Deksmart.Ui.Shared.Components.Product
{
    public partial class CompositionGrid : ComponentBase
    {
        [Parameter]
        public EventCallback OnDataLoaded { get; set; }

        [Parameter]
        public IConfiguratorGridService ConfiguratorGridService { get; set; } = null!;

        [Parameter]
        public bool IsSummaryView { get; set; } = false;

        private bool IsCollapsed { get; set; } = false;

        private async Task LoadDataAsync()
        {
            await OnDataLoaded.InvokeAsync();
        }

        private async Task LoadProductDetail(ConfiguratorProductWrapper product)
        {
            await ConfiguratorGridService.LoadProductDetail(product);
        }

        private void CloseProductDetail(ConfiguratorProductWrapper product)
        {
            product.Detail = null;
        }

        private async Task OnNumericPackageAmountChanged(decimal newValue, ConfiguratorProductWrapper product)
        {
            newValue = newValue < 0 ? 0 : Math.Round(newValue, 0);
            product.PackageQuantity = newValue;
            await ConfiguratorGridService.CalculateProductPrices(product);
            StateHasChanged();
        }

        private async Task OnSelectedItemChanged(int id, ConfiguratorCompositionWrapper composition, int index)
        {
            var selectedProduct = composition.Products?.FirstOrDefault(item => item.Id == id);
            if (selectedProduct != null)
            {
                composition.IsLoading = true;
                StateHasChanged();
                
                composition.SelectedProducts[index] = selectedProduct;
                await LoadDataAsync();
            }
        }

        private async Task AddProductIntoComposition(int compositionId)
        {
            ConfiguratorGridService.AddProductIntoComposition(compositionId);
            await LoadDataAsync();
            StateHasChanged();
        }

        private void RemoveProductFromComposition(int compositionId, int index)
        {
            ConfiguratorGridService.RemoveProductFromComposition(compositionId, index);
            StateHasChanged();
        }

        private void ToggleCollapse()
        {
            IsCollapsed = !IsCollapsed;
            StateHasChanged();
        }
    }
}
