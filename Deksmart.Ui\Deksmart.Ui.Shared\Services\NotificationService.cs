using Microsoft.Extensions.Logging;

namespace Deksmart.Ui.Shared.Services
{
    public class NotificationService
    {
        private readonly List<Notification> _notifications = new();
        private readonly ILogger<NotificationService> _logger;
        public event Action? OnChange;

        public IReadOnlyList<Notification> Notifications => _notifications.AsReadOnly();

        public NotificationService(ILogger<NotificationService> logger)
        {
            _logger = logger;
        }

        public void ShowError(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
            {
                _logger.LogWarning("Attempted to show empty error notification");
                return;
            }

            _logger.LogInformation("Showing error notification: {Message}", message);
            _notifications.Add(new Notification(message, NotificationType.Error));

            if (OnChange == null)
            {
                _logger.LogWarning("No subscribers to OnChange event when showing error notification");
            }

            OnChange?.Invoke();
            _logger.LogDebug("Error notification added, current count: {Count}", _notifications.Count);
        }

        public void ShowSuccess(string message)
        {
            if (string.IsNullOrWhiteSpace(message))
            {
                _logger.LogWarning("Attempted to show empty success notification");
                return;
            }

            _logger.LogInformation("Showing success notification: {Message}", message);
            _notifications.Add(new Notification(message, NotificationType.Success));
            OnChange?.Invoke();
        }

        public void RemoveNotification(Notification notification)
        {
            _logger.LogDebug("Removing notification: {Message}", notification.Message);
            _notifications.Remove(notification);
            OnChange?.Invoke();
        }
    }

    public class Notification
    {
        public string Message { get; }
        public NotificationType Type { get; }
        public DateTime Timestamp { get; }

        public Notification(string message, NotificationType type)
        {
            Message = message;
            Type = type;
            Timestamp = DateTime.Now;
        }
    }

    public enum NotificationType
    {
        Error,
        Success
    }
}