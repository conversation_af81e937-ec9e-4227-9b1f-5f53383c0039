using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Deksmart.Infrastructure.Context;
using Deksmart.Application.Service.Http;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Application.Mapping;
using Moq;
using System.Text.Json;
using System.Text;
using Deksmart.Api;
using Xunit;

namespace Deksmart.Integration.Tests.Fixtures
{
    /// <summary>
    /// Web application factory for configurator integration tests.
    /// Sets up in-memory database, mocks external services, and provides test environment configuration.
    /// </summary>
    public class ConfiguratorWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
    {
        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureAppConfiguration((context, config) =>
            {
                var integrationConfig = new ConfigurationBuilder()
                    .AddInMemoryCollection(new Dictionary<string, string?>
                    {
                        // Use in-memory database for testing
                        ["ConfiguratorDb:Host"] = "localhost",
                        ["ConfiguratorDb:Port"] = "5432", 
                        ["ConfiguratorDb:Database"] = "configurator_test",
                        ["ConfiguratorDb:Username"] = "test",
                        ["ConfiguratorDb:Password"] = "test",
                        ["ConfiguratorDb:Pooling"] = "false",

                        // Disable external dependencies
                        ["ApiCore:Cache:Redis:Enabled"] = "false",
                        ["ApiCore:Cache:Redis:ReplaceRedisWithDevNullAdapter"] = "true",
                        ["ApiCore:Logger:MailExceptionEnabled"] = "false",
                        ["ApiCore:Swagger:Enabled"] = "false",
                        
                        // Test environment settings
                        ["Application:EshopId"] = "test_eshop",
                        ["Application:DefaultCulture"] = "en-US"
                    })
                    .Build();

                config.AddConfiguration(integrationConfig);
            });

            builder.ConfigureServices(services =>
            {
                // Remove ALL Entity Framework related services to avoid conflicts
                var descriptorsToRemove = services
                    .Where(d => 
                        d.ServiceType == typeof(ConfiguratorContext) ||
                        d.ServiceType == typeof(DbContextOptions<ConfiguratorContext>) ||
                        d.ServiceType.FullName?.Contains("EntityFramework") == true ||
                        d.ServiceType.FullName?.Contains("Npgsql") == true ||
                        (d.ServiceType.IsGenericType && 
                         (d.ServiceType.GetGenericTypeDefinition() == typeof(DbContextOptions<>) ||
                          d.ServiceType.GetGenericTypeDefinition() == typeof(DbContext))))
                    .ToList();

                foreach (var descriptor in descriptorsToRemove)
                {
                    services.Remove(descriptor);
                }

                // Create a shared in-memory database that persists across all context instances
                var sharedDbOptions = new DbContextOptionsBuilder<ConfiguratorContext>()
                    .UseInMemoryDatabase("SharedTestDb")
                    .EnableSensitiveDataLogging()
                    .Options;

                // Register the shared options as singleton
                services.AddSingleton(sharedDbOptions);
                
                // Register context as scoped, but using the shared options
                services.AddScoped<ConfiguratorContext>(provider =>
                {
                    var options = provider.GetRequiredService<DbContextOptions<ConfiguratorContext>>();
                    return new ConfiguratorContext(options);
                });

                // Mock external HTTP services with correct interface
                var eshopDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(IEshopApiService));
                if (eshopDescriptor != null)
                {
                    services.Remove(eshopDescriptor);
                }
                services.AddScoped<IEshopApiService>(provider => CreateMockEshopApiService());

                // Register ConfiguratorMappingService that was added for unit conversion API integration
                services.AddScoped<IConfiguratorMappingService, ConfiguratorMappingService>();

                // Reduce logging noise in tests
                services.AddLogging(builder => builder.SetMinimumLevel(LogLevel.Warning));
            });

            builder.UseEnvironment("Testing");
        }

        private IEshopApiService CreateMockEshopApiService()
        {
            var mock = new Mock<IEshopApiService>();
            
            // Mock the actual interface methods that exist - return properly sized lists matching input
            mock.Setup(x => x.GetEshopProductsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync((List<string> productCodes) => 
                {
                    var products = productCodes.Select(code => new EshopProduct
                    {
                        Code = code,
                        Title = $"Test Product {code}",
                        ManufacturerTitle = "Test Manufacturer",
                        ShortDescription = "Test product for integration testing",
                        CategoryId = 1
                    }).ToList();
                    return ApiResult<List<EshopProduct>>.Success(products);
                });

            mock.Setup(x => x.GetEshopProductUnitsAsync(It.IsAny<List<string>>()))
                .ReturnsAsync((List<string> productCodes) => 
                {
                    var units = productCodes.Select(code => new EshopProductUnit
                    {
                        UnitSales = "m2",
                        UnitPackage = "m2",
                        UnitsInPackage = 5.0m,
                        IsPackagePrimary = true,
                        UnitPrimary = "m2",
                        UnitSecondary = "m2",
                        MinimumQuantitySales = 1.0m,
                        MinimumQuantityPrimary = 1
                    }).ToList();
                    return ApiResult<List<EshopProductUnit>>.Success(units);
                });

            mock.Setup(x => x.GetEshopProductPricingAsync(It.IsAny<List<string>>()))
                .ReturnsAsync((List<string> productCodes) => 
                {
                    var pricing = productCodes.Select(code => new EshopProductPricing
                    {
                        Vat = 21.0m,
                        PriceNoVatSales = 100.0m,
                        PriceVatSales = 121.0m,
                        PriceNoVatPackage = 100.0m,
                        PriceVatPackage = 121.0m,
                        DiscountPercent = 0.0m,
                        IsPackagePrimary = true,
                        HasTax = true
                    }).ToList();
                    return ApiResult<List<EshopProductPricing>>.Success(pricing);
                });

            mock.Setup(x => x.GetEshopProductParametersAsync(It.IsAny<List<string>>()))
                .ReturnsAsync((List<string> productCodes) => 
                {
                    var parameters = productCodes.Select(code => new List<EshopProductSpecItem>()).ToList();
                    return ApiResult<List<List<EshopProductSpecItem>>>.Success(parameters);
                });

            mock.Setup(x => x.GetProductDetailsAsync(It.IsAny<string>()))
                .ReturnsAsync((string productCode) => 
                {
                    var product = new EshopProduct 
                    {
                        Code = productCode,
                        Title = $"Test Product {productCode}",
                        ManufacturerTitle = "Test Manufacturer",
                        ShortDescription = "Test product for integration testing",
                        CategoryId = 1
                    };
                    return ApiResult<ProductDetails>.Success(new ProductDetails(
                        product, 
                        new List<EshopProductSpecItem>()
                    ));
                });

            // Mock unit conversion API method that was added for unit conversion integration
            mock.Setup(x => x.GetProductUnitConversionsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<List<decimal>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<string>>()))
                .ReturnsAsync((List<string> productCodes, List<decimal> quantities, List<string> inputUnits, List<string> outputUnits) => 
                {
                    var conversions = productCodes.Select((code, index) => new EshopProductUnitConversion
                    {
                        ProductCode = code,
                        QuantityOutput = quantities[index] // 1:1 conversion for test simplicity
                    }).ToList();
                    return ApiResult<List<EshopProductUnitConversion>>.Success(conversions);
                });

            return mock.Object;
        }

        public async Task InitializeAsync()
        {
            // Initialize database with simple test data - this must succeed for real integration testing
            await IntegrationTestDataSeeder.SeedSimpleTestDataAsync(Services);
        }

        public string GetTestJwtToken()
        {
            // Create a simple test JWT token for authentication
            var payload = new
            {
                iss = "web.eshop",
                aud = new[] { "api.cart", "api.account", "api.article", "api.product", "api.form" },
                eshopId = "test_eshop",
                priceLevelEshop = "ESHOP",
                priceLevelRental = 1,
                cartId = "test-cart-id",
                seasonId = "2025_SD",
                instance = "TEST_ESHOP",
                role = new[] { "eshop", "guest" },
                exp = DateTimeOffset.UtcNow.AddHours(1).ToUnixTimeSeconds()
            };

            var header = new { alg = "none", typ = "JWT" };
            
            var encodedHeader = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(header)))
                .TrimEnd('=').Replace('+', '-').Replace('/', '_');
            
            var encodedPayload = Convert.ToBase64String(Encoding.UTF8.GetBytes(JsonSerializer.Serialize(payload)))
                .TrimEnd('=').Replace('+', '-').Replace('/', '_');

            return $"{encodedHeader}.{encodedPayload}.";
        }

        public new async Task DisposeAsync()
        {
            await base.DisposeAsync();
        }
    }
}