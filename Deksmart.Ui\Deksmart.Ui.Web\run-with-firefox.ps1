Write-Host "Cleaning bin and obj directories..."
Remove-Item -Path "bin" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "obj" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Restoring packages..."
dotnet restore

Write-Host "Building project..."
dotnet build

Write-Host "Starting Blazor Server..."
$serverProcess = Start-Process -FilePath "dotnet" -ArgumentList "run" -PassThru -NoNewWindow

Write-Host "Waiting for server to start..."
Start-Sleep -Seconds 5

Write-Host "Opening Firefox..."
Start-Process "firefox" -ArgumentList "https://localhost:7209"

Write-Host "Press any key to stop the server..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Kill the server process when done
Stop-Process -Id $serverProcess.Id -Force
