namespace DEK.Eshop.ApiCore.Extension;

using DEK.Eshop.ApiCore.Database;
using Microsoft.EntityFrameworkCore;

public static class BdSetExtension
{
    public static Task<List<TEntity>> GetListAsync<TEntity>(this DbSet<TEntity> source, ProcedureQueryBuilder builder) where TEntity : class
    {
        return source.FromSqlRaw(builder.BuildSql(), builder.Parameters.Values.ToArray()).ToListAsync();
    }
}
