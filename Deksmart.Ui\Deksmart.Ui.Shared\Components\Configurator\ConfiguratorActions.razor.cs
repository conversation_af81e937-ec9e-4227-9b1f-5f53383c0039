using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Deksmart.Ui.Shared.Components.Configurator
{
    public partial class ConfiguratorActions : ComponentBase, IDisposable
    {
        [Parameter]
        public IConfiguratorGridService ConfiguratorGridService { get; set; } = default!;

        [Inject]
        public IJSRuntime JSRuntime { get; set; } = default!;

        private bool ShowInquiryForm { get; set; }
        private bool IsDownloadLoading { get; set; }
        private bool IsAddToAgendaLoading { get; set; }
        private bool IsAddToCartLoading { get; set; }

        private bool IsInquiryLoading { get; set; }
        private bool IsConfiguratorLoading => ConfiguratorGridService.IsLoading;

        protected override void OnInitialized()
        {
            ConfiguratorGridService.ValidationStateChanged += OnValidationStateChanged;
            ConfiguratorGridService.LoadingStateChanged += OnLoadingStateChanged;
        }


        private void OnValidationStateChanged(object? sender, EventArgs e)
        {
            InvokeAsync(StateHasChanged);
        }

        private void OnLoadingStateChanged(object? sender, EventArgs e)
        {
            InvokeAsync(StateHasChanged);
        }

        protected async Task HandleDownloadClick()
        {
            // Set loading state
            IsDownloadLoading = true;
            StateHasChanged();

            try
            {
                // Set all fields as dirty and check if the configurator is valid
                bool isValid = ConfiguratorGridService.SetAllFieldsDirty();

                // Only proceed if the configurator is valid
                if (isValid)
                {
                    var (pdfBytes, fileName) = await ConfiguratorGridService.GenerateConfigurationPdf();
                    if (pdfBytes != null)
                    {
                        await JSRuntime.InvokeVoidAsync("downloadPdf", pdfBytes, fileName);
                    }
                }
                else
                {
                    // Find the first category with validation errors and scroll to it
                    await ScrollToFirstInvalidCategory();
                }
            }
            finally
            {
                // Reset loading state
                IsDownloadLoading = false;
                StateHasChanged();
            }
        }

        protected async Task HandleInquiryClick()
        {
            // Set loading state
            IsInquiryLoading = true;
            StateHasChanged();

            try
            {
                // Set all fields as dirty and check if the configurator is valid
                bool isValid = ConfiguratorGridService.SetAllFieldsDirty();

                // Only show the inquiry form if the configurator is valid
                if (isValid)
                {
                    ShowInquiryForm = true;
                }
                else
                {
                    // Find the first category with validation errors and scroll to it
                    await ScrollToFirstInvalidCategory();
                }
            }
            finally
            {
                // Reset loading state
                IsInquiryLoading = false;
                StateHasChanged();
            }
        }

        private async Task ScrollToFirstInvalidCategory()
        {
            // For configurators with children, implement the new logic
            if (ConfiguratorGridService.ChildServices.Count > 0)
            {
                // Check if any hidden tab has validation errors
                bool anyHiddenTabHasErrors = ConfiguratorGridService.ChildServices
                    .Where(service => service != ConfiguratorGridService.ActiveChildConfiguratorService)
                    .Any(service => service.ConfiguratorWrapper != null && !service.ConfiguratorWrapper.IsValid);

                if (anyHiddenTabHasErrors)
                {
                    // Case 2: Any hidden tab has errors - scroll to the top
                    await JSRuntime.InvokeVoidAsync("window.scrollTo", 0, 0);
                    return;
                }
            }

            // Case 1: Only current tab has errors or this is not a tabbed configurator
            // Find the first category with validation errors (current logic)
            string? categoryId = ConfiguratorGridService.FindFirstInvalidCategoryId();

            if (!string.IsNullOrEmpty(categoryId))
            {
                // Use a very simple approach with a fixed offset
                // This is the most reliable method
                await JSRuntime.InvokeVoidAsync("scrollToElementWithFixedOffset", categoryId, 80);
            }
        }

        protected async Task HandleAddToAgendaClick()
        {
            // Set loading state
            IsAddToAgendaLoading = true;
            StateHasChanged();

            try
            {
                // Set all fields as dirty and check if the configurator is valid
                bool isValid = ConfiguratorGridService.SetAllFieldsDirty();

                // Only proceed if the configurator is valid
                if (isValid)
                {
                    // TODO: Implement add to agenda functionality
                    await Task.Delay(1000); // Placeholder for actual implementation
                }
                else
                {
                    // Find the first category with validation errors and scroll to it
                    await ScrollToFirstInvalidCategory();
                }
            }
            finally
            {
                // Reset loading state
                IsAddToAgendaLoading = false;
                StateHasChanged();
            }
        }

        protected async Task HandleAddToCartClick()
        {
            // Set loading state
            IsAddToCartLoading = true;
            StateHasChanged();

            try
            {
                // Set all fields as dirty and check if the configurator is valid
                bool isValid = ConfiguratorGridService.SetAllFieldsDirty();

                // Check both field validation and cart availability
                if (isValid && ConfiguratorGridService.ConfiguratorWrapper?.CanAddToCart == true)
                {
                    // TODO: Implement add to shopping cart functionality
                    await Task.Delay(1000); // Placeholder for actual implementation
                }
                else
                {
                    // Find the first category with validation errors and scroll to it
                    await ScrollToFirstInvalidCategory();
                }
            }
            finally
            {
                // Reset loading state
                IsAddToCartLoading = false;
                StateHasChanged();
            }
        }

        private async Task<bool> HandleInquirySubmit((string? name, string? email, string? phone) contactInfo)
        {
            // The InquiryForm component now handles its own loading state
            // We just need to pass through the result from the service
            return await ConfiguratorGridService.SendInquiry(contactInfo.name, contactInfo.email, contactInfo.phone);
        }

        public void Dispose()
        {
            ConfiguratorGridService.ValidationStateChanged -= OnValidationStateChanged;
            ConfiguratorGridService.LoadingStateChanged -= OnLoadingStateChanged;
        }
    }
}
