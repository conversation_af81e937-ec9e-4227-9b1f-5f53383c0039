﻿using DEK.Eshop.ApiCore.Loging;

namespace DEK.Eshop.ApiCore.Tests;

public class LogingManager_Tests
{
    private readonly ITestOutputHelper _output;

    private readonly LogingManager managerLoging;

    public static bool Touched { get; set; } = false;

    public LogingManager_Tests(ITestOutputHelper output)
    {
        _output = output;

        //var cacheAdapter = new DevNullCacheAdapter();
        var logger = new Logger();
        var testAdapter = new TestAdapter();
        logger.SubscribeToExceptionHandler(testAdapter);
        this.managerLoging = new LogingManager(logger);

        //_output.WriteLine("My message.");
    }

    /// <summary>
    /// Test if log exception event is fired
    /// </summary>
    [Fact]
    public void Test_IExceptionHandlerEvent_WriteException_Fired()
    {
        var exception = new Exception("Test exception");
        this.managerLoging.LogException(exception);
        Assert.True(Touched);
    }
}

public class TestAdapter : IExceptionHandlerEvent
{
    public void WriteException(object? sender, ExceptionHandlerEventArgs args)
    {
        LogingManager_Tests.Touched = true;
    }
}
