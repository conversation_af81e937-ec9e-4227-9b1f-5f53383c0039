Write-Host "Cleaning bin and obj directories..."
Remove-Item -Path "bin" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "obj" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Restoring packages..."
dotnet restore

Write-Host "Building project..."
dotnet build

Write-Host "Creating runtimeconfig.json file..."
$runtimeConfigContent = @"
{
  "runtimeOptions": {
    "tfm": "net9.0",
    "framework": {
      "name": "Microsoft.AspNetCore.App",
      "version": "9.0.0"
    },
    "configProperties": {
      "System.GC.Server": true,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false
    }
  }
}
"@

$runtimeConfigPath = "bin\Debug\net9.0\Deksmart.Ui.Web.Client.runtimeconfig.json"
New-Item -Path $runtimeConfigPath -Force -Value $runtimeConfigContent | Out-Null

Write-Host "Running application..."
dotnet run
