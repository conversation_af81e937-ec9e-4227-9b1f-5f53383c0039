﻿namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a lightweight reference to a saved configurator preset, containing the unique preset identifier and its associated configurator.
    /// Used to transfer preset identity and linkage information between backend, API, and UI.
    /// Supports preset retrieval, navigation, and workflow continuity without exposing full preset state or data.
    /// </summary>
    public class PresetDto
    {
        public int ConfiguratorId { get; set; }

        public string PresetId { get; set; } = null!;
    }
}
