# Api Core
Postavené na Service Collection Extension Pattern(Options pattern)

- [Simple example](https://dotnetcoretutorials.com/2017/01/24/servicecollection-extension-pattern)
- [Microsoft Docu](https://learn.microsoft.com/en-us/dotnet/core/extensions/options-library-authors)

Program.cs
- `ErrorValidationDictionaryFactory.Create()` je optional. Vrací Dictionary errorů pro validace. Pouze pokud budou POST a PUT metody.
- `MssqlRepository`, `PqsqlRepository`, `MssqlContext` a `PgsqlContext` implementace v aplikaci. *
- `MssqlContextFactory` a `PgsqlContextFactory` implementace v knihovně. *
```csharp

using DEK.Eshop.ApiCore.Extension;
using DEK.Eshop.ApiCore.Factory;
using DEK.Eshop.ApiCore.Sandbox.Context;
using DEK.Eshop.ApiCore.Sandbox.Factory;

builder.Services.AddDekEshopCore(builder, ErrorValidationDictionaryFactory.Create());

// Repository
builder.Services.AddScoped<MssqlRepository>();
builder.Services.AddScoped<PqsqlRepository>();

// Factory
builder.Services.AddScoped<MssqlContextFactory<MssqlContext>>();
builder.Services.AddScoped<PgsqlContextFactory<PgsqlContext>>();
```

## Nastavení configu

- [Global](docs/global.md)
- [Jwt](docs/jwt.md)
- [Databáze](docs/database.md)

## Třídy

- [ConfigFactory](docs/config-factory.md): Objekty konfigurace.
- [MssqlContextFactory (PgsqlContextFactory)](docs/config-factory.md): Továrny DB contextu a commandu.
- [UserFactory](docs/user-factory.md): Továrna na objekt usera z JWT

---
\* Ukázka implementace/použití je videt zde v projektu `DEK.Eshop.ApiCore.Sandbox` nebo ve výchozím stacku pro Api projekty.

