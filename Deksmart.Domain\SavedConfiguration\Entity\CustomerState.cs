using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Deksmart.Domain.SavedConfiguration.Entity;

public enum CustomerStateSortBy
{
    ASC = 1,
    DESC = 0,
}

public record CustomerState
{

    [Keyless]
    public record Select
    {
        public int StateId { get; init; }

        public int ConfiguratorId { get; init; }

        public long Hash { get; init; }

        public string Email { get; init; } = null!;

        public string Title { get; init; } = null!;

        public DateTime DateCreated { get; init; }
    }

    [Table("dtcz_kalkulacky_firma", Schema = "deksmart")]
    public record Delete
    {
        [Key]
        [Column("ai")]
        public int Id { get; init; }

        public long Hash { get; init; }

        public string Email { get; init; } = null!;
    }
}
