using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Service.Base;
using System.Data;
using Deksmart.Domain.Enum;

namespace Deksmart.Domain.Service
{
    public interface IConfiguratorImportParser
    {
        /// <summary>
        /// Parses a configurator from a data row, updating an existing configurator if provided, and validates the parsed data.
        /// </summary>
        /// <param name="configuratorId">The ID of the configurator to parse.</param>
        /// <param name="existingConfigurator">The existing configurator to update, or null to create a new one.</param>
        /// <param name="configuratorRow">The data row containing configurator information.</param>
        /// <param name="validation">The validation result to accumulate errors into.</param>
        /// <returns>
        /// A tuple containing the parsed <see cref="Configurator"/>, a flag indicating if it was updated.
        /// </returns>
        (Configurator configurator, bool isUpdated) ParseConfigurator(
            int configuratorId, Configurator? existingConfigurator, DataRow configuratorRow, ValidationResult validation);

        /// <summary>
        /// Maps and validates fields from the provided dataset to the configurator, updating present IDs and using the expression cache.
        /// </summary>
        /// <param name="configurator">The configurator to update.</param>
        /// <param name="result">The dataset containing field data.</param>
        /// <param name="presentIds">The set of present IDs to update.</param>
        /// <param name="expressionCache">A cache of parsed expressions for reuse.</param>
        /// <param name="validation">The validation result to accumulate errors into.</param>
        void MapFields(Configurator configurator, DataSet result, PresentIds presentIds,
            Dictionary<string, ConfiguratorExpression> expressionCache, ValidationResult validation);

        /// <summary>
        /// Maps and validates products from the provided dataset to the configurator, updating present IDs and using the expression cache.
        /// </summary>
        /// <param name="configurator">The configurator to update.</param>
        /// <param name="result">The dataset containing product data.</param>
        /// <param name="presentIds">The set of present IDs to update.</param>
        /// <param name="expressionCache">A cache of parsed expressions for reuse.</param>
        /// <param name="validation">The validation result to accumulate errors into.</param>
        void MapProducts(Configurator configurator, DataSet result, PresentIds presentIds,
            Dictionary<string, ConfiguratorExpression> expressionCache, ValidationResult validation);
    }

    public class ConfiguratorImportParser : IConfiguratorImportParser
    {
        private readonly IImportValidationService _importValidationService;
        private readonly IMarkdownService _markdownService;
        private readonly IValidationService _validationService;

        public ConfiguratorImportParser(
            IImportValidationService importValidationService,
            IMarkdownService markdownService,
            IValidationService validationService)
        {
            _importValidationService = importValidationService;
            _markdownService = markdownService;
            _validationService = validationService;
        }

        public (Configurator configurator, bool isUpdated) ParseConfigurator(
            int configuratorId, Configurator? existingConfigurator, DataRow configuratorRow, ValidationResult validation)
        {
            var isUpdated = existingConfigurator != null;
            var configurator = existingConfigurator ?? new Configurator { Id = configuratorId };

            // Validate configurator title is not empty
            var titleValidation = _importValidationService.ValidateCellEmpty(configuratorRow[1], DeksmartDomainResource.ConfiguratorTitleRequired);
            if (titleValidation.HasErrors)
            {
                validation.AddError(titleValidation.GetErrors());
                return (configurator, isUpdated);
            }
            configurator.Title = configuratorRow[1].ToString()!;

            if (configuratorRow.ItemArray.Length > 2)
            {
                var (isValidBool, showInMenu, boolValidation) = _importValidationService.ValidateCellBool(configuratorRow[2].ToString(), 2);
                if (boolValidation.HasErrors)
                {
                    validation.AddError(boolValidation.GetErrors());
                }

                if (isValidBool)
                    configurator.ShowInMenu = showInMenu;
            }

            if (configuratorRow.ItemArray.Length > 3)
            {
                var (html, markdownValidation) = _markdownService.ConvertToHtml(configuratorRow[3].ToString()!, 2);
                configurator.Description = html;

                if (markdownValidation.HasErrors)
                {
                    validation.AddError(markdownValidation.GetErrors());
                }
            }
            else
            {
                configurator.Description = string.Empty;
            }

            if (configuratorRow.ItemArray.Length > 4)
            {
                var (html, markdownValidation) = _markdownService.ConvertToHtml(configuratorRow[4].ToString()!, 2);
                configurator.EndDescription = html;

                if (markdownValidation.HasErrors)
                {
                    validation.AddError(markdownValidation.GetErrors());
                }
            }
            else
            {
                configurator.EndDescription = string.Empty;
            }

            if (configuratorRow.ItemArray.Length > 5)
            {
                configurator.MetaDescription = configuratorRow[5]?.ToString();
            }

            return (configurator, isUpdated);
        }

        public void MapFields(Configurator configurator, DataSet result, PresentIds presentIds,
            Dictionary<string, ConfiguratorExpression> expressionCache, ValidationResult validation)
        {
            var fieldValues = new Dictionary<string, List<ConfiguratorFieldValue>>();
            ConfiguratorFieldCategory? currentCategory = null;
            ConfiguratorField? currentField = null;
            ConfiguratorFieldValue? currentValue = null;
            string? currentIdent = null;

            var fieldTable = result.Tables[1];

            for (int i = 1; i < fieldTable.Rows.Count; i++)
            {
                var row = fieldTable.Rows[i];
                var isCategory = !string.IsNullOrWhiteSpace(row[0]?.ToString());
                var isField = !string.IsNullOrWhiteSpace(row[5]?.ToString());

                for (int j = 0; j < row.ItemArray.Length; j++)
                {
                    var cellValue = row[j]?.ToString()?.Trim();

                    switch (j)
                    {
                        case 0:
                            if (!string.IsNullOrWhiteSpace(cellValue))
                            {
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[5], DeksmartDomainResource.MissingFieldForCategory, i + 1), validation))
                                    return;
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[1], DeksmartDomainResource.ImportCategoryOrderRequired, i + 1), validation))
                                    return;
                                currentCategory = GetOrCreateCategory(configurator, cellValue!, presentIds.CategoryIds);
                            }
                            break;
                        case 1:
                            if (isCategory)
                            {
                                var intResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                                AddValidationAndReturnIfError(intResult, validation);
                                if (intResult.isValid)
                                    currentCategory!.Order = intResult.integer;
                            }
                            break;
                        case 2:
                            if (isCategory)
                            {
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                    currentCategory!.Visibility = GetExpression(cellValue!, expressionCache);
                                else
                                {
                                    currentCategory!.Visibility = null;
                                    currentCategory.VisibilityId = null;
                                }
                            }
                            break;
                        case 3:
                            if (isCategory)
                            {
                                var collapseStateResult = _importValidationService.ValidateCellEnum<CategoryCollapseState>(cellValue, DeksmartDomainResource.CollapseStateDoesNotExist, i + 1);
                                AddValidationAndReturnIfError(collapseStateResult, validation);
                                if (collapseStateResult.isValid)
                                    currentCategory!.CollapseState = collapseStateResult.enumValue;
                                else
                                    currentCategory!.CollapseState = CategoryCollapseState.NotCollapsible;
                            }
                            break;
                        case 4:
                            if (!string.IsNullOrWhiteSpace(cellValue))
                            {
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[5], DeksmartDomainResource.ImportFieldsTitleRequired, i + 1), validation))
                                    return;
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[6], DeksmartDomainResource.ImportFieldsComponentTypeRequired, i + 1), validation))
                                    return;
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[8], DeksmartDomainResource.ImportFieldOrderRequired, i + 1), validation))
                                    return;
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateIdentMaxLength(cellValue), validation))
                                    return;
                                currentIdent = cellValue;

                                var componentTypeStr = row[6].ToString()?.Trim();
                                var compTypeResult = _importValidationService.ValidateCellEnum<ComponentType>(componentTypeStr, DeksmartDomainResource.ComponentTypeDoesNotExist, i + 1);
                                if (AddValidationAndReturnIfError(compTypeResult, validation))
                                    return;
                                
                                var (field, fieldValidation) = GetOrCreateField(currentCategory!, currentIdent, compTypeResult.enumValue, presentIds.FieldIds);
                                if (AddValidationAndReturnIfError(fieldValidation, validation))
                                    return;
                                currentField = field;
                            }
                            break;
                        case 5:
                            if (!string.IsNullOrWhiteSpace(cellValue))
                            {
                                var (html, markdownValidation) = _markdownService.ConvertToHtml(cellValue!, i + 1);
                                currentField!.Title = html;
                                AddValidationAndReturnIfError(markdownValidation, validation);
                            }
                            break;
                        case 6:
                            if (isField)
                            {
                                var compTypeResult2 = _importValidationService.ValidateCellEnum<ComponentType>(cellValue, DeksmartDomainResource.ComponentTypeDoesNotExist, i + 1);
                                AddValidationAndReturnIfError(compTypeResult2, validation);
                                if (compTypeResult2.isValid)
                                {
                                    currentField!.ComponentType = compTypeResult2.enumValue;
                                    if (currentField.ComponentType == ComponentType.SingleChoice || currentField.ComponentType == ComponentType.Tile || currentField.ComponentType == ComponentType.Selectbox)
                                        AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[19], DeksmartDomainResource.ImportFieldsValueRequired, i + 1), validation);
                                    if (currentField.ComponentType == ComponentType.Expression)
                                        AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[14], DeksmartDomainResource.ImportFieldsExpressionRequired, i + 1), validation);
                                    if (currentField.ComponentType == ComponentType.Product)
                                        AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[13], DeksmartDomainResource.ImportFieldsProductRequired, i + 1), validation);
                                }
                            }
                            break;
                        case 7:
                            if (isField)
                            {
                                var posResult = _importValidationService.ValidateCellEnum<FieldPosition>(cellValue, DeksmartDomainResource.FieldPositionDoesNotExist, i + 1);
                                AddValidationAndReturnIfError(posResult, validation);
                                if (posResult.isValid)
                                    currentField!.FieldPosition = posResult.enumValue;
                                else
                                    currentField!.FieldPosition = FieldPosition.Left;
                            }
                            break;
                        case 8:
                            var fieldOrderResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                            AddValidationAndReturnIfError(fieldOrderResult, validation);
                            if (fieldOrderResult.isValid)
                                currentField!.Order = fieldOrderResult.integer;
                            break;
                        case 9:
                            if (isField)
                                currentField!.Description = !string.IsNullOrWhiteSpace(cellValue) ? cellValue : null;
                            break;
                        case 10:
                            if (isField)
                            {
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                {
                                    var (html, markdownValidation) = _markdownService.ConvertToHtml(cellValue!, i + 1);
                                    currentField!.Suffix = html;
                                    AddValidationAndReturnIfError(markdownValidation, validation);
                                }
                                else
                                    currentField!.Suffix = null;
                            }
                            break;
                        case 11:
                            if (isField)
                            {
                                var minResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                                AddValidationAndReturnIfError(minResult, validation);
                                currentField!.MinValue = minResult.isValid ? minResult.integer : null;
                            }
                            break;
                        case 12:
                            if (isField)
                            {
                                var maxResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                                AddValidationAndReturnIfError(maxResult, validation);
                                currentField!.MaxValue = maxResult.isValid ? maxResult.integer : null;
                            }
                            break;
                        case 13:
                            if (isField)
                            {
                                var compOrderResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                                AddValidationAndReturnIfError(compOrderResult, validation);
                                if (compOrderResult.isValid)
                                    currentField!.CompositionOrder = compOrderResult.integer;
                            }
                            break;
                        case 14:
                            if (isField)
                            {
                                var exprFieldValidation = _importValidationService.ValidateExpressionField(cellValue, currentField, i + 1);
                                AddValidationAndReturnIfError(exprFieldValidation, validation);
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                    currentField!.Expression = GetExpression(cellValue!, expressionCache);
                                else
                                {
                                    currentField!.Expression = null;
                                    currentField.ExpressionId = null;
                                }
                            }
                            break;
                        case 15:
                            if (isField)
                            {
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                    currentField!.Visibility = GetExpression(cellValue!, expressionCache);
                                else
                                {
                                    currentField!.Visibility = null;
                                    currentField.VisibilityId = null;
                                }
                            }
                            break;
                        case 16:
                            if (isField)
                            {
                                var defValResult = _importValidationService.ValidateCellNumeric(cellValue, i + 1);
                                AddValidationAndReturnIfError(defValResult, validation);
                                currentField!.DefaultValue = !string.IsNullOrWhiteSpace(cellValue) ? defValResult.numeric : 0;
                            }
                            break;
                        case 17:
                            if (isField)
                            {
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                {
                                    var boolResult = _importValidationService.ValidateCellBool(cellValue, i + 1);
                                    if (boolResult.isCellNull && boolResult.boolean)
                                    {
                                        var validationExpression = $"if([{currentField!.Ident}] > 0; 1; 0)";
                                        currentField.Validation = GetExpression(validationExpression, expressionCache);
                                    }
                                    else
                                    {
                                        currentField!.Validation = GetExpression(cellValue!, expressionCache);
                                    }
                                }
                                else
                                {
                                    currentField!.Validation = null;
                                    currentField.ValidationId = null;
                                }
                            }
                            break;
                        case 18:
                            if (isField)
                                currentField!.ValidationMessage = !string.IsNullOrWhiteSpace(cellValue) ? cellValue : null;
                            break;
                        case 19:
                            if (!string.IsNullOrWhiteSpace(cellValue) && !string.IsNullOrWhiteSpace(currentField!.Ident))
                            {
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[21], DeksmartDomainResource.ImportFieldsNumericValueRequired, i + 1), validation))
                                    return;
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[22], DeksmartDomainResource.ImportFieldsValueOrderRequired, i + 1), validation))
                                    return;
                                if (fieldValues.TryGetValue(currentField!.Ident, out var fieldValuesList))
                                {
                                    var fieldValue = fieldValuesList.FirstOrDefault(fv => fv.Title == cellValue);
                                    if (fieldValue != null)
                                    {
                                        currentValue = fieldValue;
                                        currentField.ConfiguratorFieldValues.Add(currentValue);
                                        continue;
                                    }
                                    else
                                    {
                                        currentValue = GetOrCreateFieldValue(currentField!, cellValue!, presentIds.ValuesIds);
                                        fieldValuesList.Add(currentValue);
                                    }
                                }
                                else
                                {
                                    currentValue = GetOrCreateFieldValue(currentField!, cellValue!, presentIds.ValuesIds);
                                    fieldValues[currentField!.Ident] = [currentValue];
                                }
                            }
                            else
                            {
                                currentValue = null;
                            }
                            break;
                        case 20:
                            if(currentValue != null)
                            {
                                var imgResult = _importValidationService.ValidateCellImage(cellValue, i + 1);
                                AddValidationAndReturnIfError(imgResult, validation);
                                if (imgResult.isCellNull)
                                    currentValue.Image = imgResult.image;
                            }
                            break;
                        case 21:
                            if(currentValue != null)
                            {
                                var numValResult = _importValidationService.ValidateCellNumeric(cellValue, i + 1);
                                AddValidationAndReturnIfError(numValResult, validation);
                                if (numValResult.isValid)
                                    currentValue.NumericValue = numValResult.numeric;
                            }
                            break;
                        case 22:
                            if (currentValue != null)
                            {
                                var valOrderResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                                AddValidationAndReturnIfError(valOrderResult, validation);
                                if (valOrderResult.isValid)
                                    currentValue.Order = valOrderResult.integer;
                            }
                            break;
                        case 23:
                            if (currentValue != null)
                            {
                                if (!string.IsNullOrWhiteSpace(cellValue))
                                    currentValue.Visibility = GetExpression(cellValue!, expressionCache);
                                else
                                    currentValue.Visibility = null;
                            }
                            break;
                    }
                }
            }
            EnsureCheckboxValues(configurator, presentIds);
        }

        public void MapProducts(Configurator configurator, DataSet result, PresentIds presentIds,
            Dictionary<string, ConfiguratorExpression> expressionCache, ValidationResult validation)
        {           
            if (result.Tables.Count < 3)
                return;

            ConfiguratorComposition? currentComposition = null;
            ConfiguratorProduct? currentItem = null;
            ConfiguratorExpression? amount = null;
            ConfiguratorExpression? visibility = null;

            var itemsTable = result.Tables[2];

            if (itemsTable.Rows.Count <= 1)
                return;

            for (int i = 1; i < itemsTable.Rows.Count; i++)
            {
                var row = itemsTable.Rows[i];
                var isComposition = !string.IsNullOrWhiteSpace(row[0]?.ToString());

                for (int j = 0; j < row.ItemArray.Length; j++)
                {
                    var cellValue = row[j]?.ToString()?.Trim();

                    switch (j)
                    {
                        case 0:
                            if (!string.IsNullOrWhiteSpace(cellValue))
                            {
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[1], DeksmartDomainResource.ImportCompositionsOrderRequired, i + 1), validation))
                                    return;
                                if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(row[4], DeksmartDomainResource.MissingProductForComposition, i + 1), validation))
                                    return;
                                currentComposition = GetOrCreateComposition(configurator, cellValue!, presentIds.CompositionIds);
                            }
                            break;
                        case 1:
                            var compOrderResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                            AddValidationAndReturnIfError(compOrderResult, validation);
                            if (compOrderResult.isValid)
                                currentComposition!.Order = compOrderResult.integer;
                            break;
                        case 2:
                            if (!string.IsNullOrWhiteSpace(cellValue))
                                currentComposition!.Visibility = GetExpression(cellValue!, expressionCache);
                            else if (isComposition)
                            {
                                currentComposition!.Visibility = null;
                                currentComposition.VisibilityId = null;
                            }
                            break;
                        case 3:
                            var multiProdResult = _importValidationService.ValidateCellBool(cellValue, i + 1);
                            AddValidationAndReturnIfError(multiProdResult, validation);
                            if (multiProdResult.isCellNull)
                                currentComposition!.IsMultipleProducts = multiProdResult.boolean;
                            else if (isComposition)
                                currentComposition!.IsMultipleProducts = false;
                            break;
                        case 4:
                            if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(cellValue, DeksmartDomainResource.ImportProductNameRequired, i + 1), validation))
                                return;
                            currentItem = GetOrCreateItem(currentComposition!, cellValue!, presentIds.ProductIds);
                            break;
                        case 5:
                            if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(cellValue, DeksmartDomainResource.ImportProductCodeRequired, i + 1), validation))
                                return;
                            currentItem!.ProductCode = cellValue!;
                            break;
                        case 6:
                            if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(cellValue, DeksmartDomainResource.ImportProductOrderRequired, i + 1), validation))
                                return;
                            var itemOrderResult = _importValidationService.ValidateCellInteger(cellValue, i + 1);
                            AddValidationAndReturnIfError(itemOrderResult, validation);
                            if (itemOrderResult.isValid)
                                currentItem!.Order = itemOrderResult.integer;
                            break;
                        case 7:
                            if (amount is null && AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(cellValue, DeksmartDomainResource.MissingAmountForProduct, i + 1), validation))
                                return;

                            if (!string.IsNullOrWhiteSpace(cellValue))
                            {
                                var expr = GetExpression(cellValue!, expressionCache);
                                amount = expr;
                                visibility = expr;
                            }

                            currentItem!.Quantity = amount!;
                            break;
                        case 8:
                            if (!string.IsNullOrWhiteSpace(cellValue))
                                visibility = GetExpression(cellValue!, expressionCache);
                            if (visibility is not null)
                                currentItem!.Visibility = visibility;
                            else 
                                validation.AddError(string.Format(DeksmartDomainResource.MissingVisibilityForProduct, i + 1));
                            break;
                        case 9:
                            if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(cellValue, DeksmartDomainResource.ImportProductVolumeRequired, i + 1), validation))
                                return;
                            var prodVolResult = _importValidationService.ValidateCellNumeric(cellValue, i + 1);
                            AddValidationAndReturnIfError(prodVolResult, validation);
                            if (prodVolResult.isValid)
                                currentItem!.ProductVolume = prodVolResult.numeric;
                            break;
                        case 10:
                            if (AddValidationAndReturnIfError(_importValidationService.ValidateCellEmpty(cellValue, DeksmartDomainResource.ImportProductUnitRequired, i + 1), validation))
                                return;
                            currentItem!.ProductUnit = cellValue!;
                            break;
                    }
                }
            }
        }

        private (ConfiguratorField? field, ValidationResult validation) GetOrCreateField(
            ConfiguratorFieldCategory category, string ident, ComponentType componentType, HashSet<int> presentFieldIds)
        {
            var validation = _validationService.CreateValidation();

            var field = category.ConfiguratorFields
                .FirstOrDefault(f =>
                    !f.IsDeleted &&
                    f.Ident == ident &&
                    f.ComponentType == componentType);

            if (field == null)
            {
                field = new ConfiguratorField
                {
                    Ident = ident,
                    ComponentType = componentType
                };
                category.ConfiguratorFields.Add(field);
            }
            else
            {
                presentFieldIds.Add(field.Id);
            }

            return (field, validation);
        }

        private static ConfiguratorExpression GetExpression(string expression, Dictionary<string, ConfiguratorExpression> expressionCache)
        {
            if (expressionCache.TryGetValue(expression, out var existingExpression))
            {
                return existingExpression;
            }

            // Create a new expression and add it to the cache
            var newExpression = new ConfiguratorExpression
            {
                Expression = expression
            };

            expressionCache[expression] = newExpression;
            return newExpression;
        }

        private static ConfiguratorFieldCategory GetOrCreateCategory(Configurator configurator, string name, HashSet<int> presentCategoryIds)
        {
            var category = configurator.ConfiguratorFieldCategories
                .FirstOrDefault(c => c.Title == name && !c.IsDeleted);

            if (category == null)
            {
                category = new ConfiguratorFieldCategory { ConfiguratorId = configurator.Id };
                configurator.ConfiguratorFieldCategories.Add(category);
            }
            else
            {
                presentCategoryIds.Add(category.Id);
            }

            category.Title = name;

            return category;
        }

        private static void CreateCheckboxFieldValues(ConfiguratorField field)
        {
            var yesValue = new ConfiguratorFieldValue
            {
                Title = DeksmartDomainResource.CheckboxYesValue,
                NumericValue = 1,
                Order = 1
            };
            field.ConfiguratorFieldValues.Add(yesValue);

            var noValue = new ConfiguratorFieldValue
            {
                Title = DeksmartDomainResource.CheckboxNoValue,
                NumericValue = 0,
                Order = 2
            };
            field.ConfiguratorFieldValues.Add(noValue);
        }

        private static void EnsureCheckboxValues(Configurator configurator, PresentIds presentIds)
        {
            // Ensure each checkbox field has exactly two values and add them to presentIds
            foreach (var category in configurator.ConfiguratorFieldCategories)
            {
                foreach (var field in category.ConfiguratorFields.Where(f => f.ComponentType == Enum.ComponentType.Checkbox))
                {
                    // Get non-deleted values
                    var activeValues = field.ConfiguratorFieldValues.Where(v => !v.IsDeleted).ToList();

                    // If we don't have exactly 2 values, recreate them
                    if (activeValues.Count != 2)
                    {
                        // Mark existing values as deleted (don't remove them to preserve references)
                        foreach (var value in activeValues)
                        {
                            value.IsDeleted = true;
                        }

                        // Create new Yes/No values
                        CreateCheckboxFieldValues(field);
                    }

                    // Add all non-deleted values with IDs to presentIds.ValuesIds
                    foreach (var value in field.ConfiguratorFieldValues.Where(v => !v.IsDeleted && v.Id > 0))
                    {
                        presentIds.ValuesIds.Add(value.Id);
                    }
                }
            }
        }

        private static ConfiguratorFieldValue GetOrCreateFieldValue(ConfiguratorField field, string name, HashSet<int> presentValuesIds)
        {
            var value = field.ConfiguratorFieldValues
                .FirstOrDefault(fv => fv.Title == name && !fv.IsDeleted);

            if (value == null)
            {
                value = new ConfiguratorFieldValue();
                field.ConfiguratorFieldValues.Add(value);
            }
            else
            {
                presentValuesIds.Add(value.Id);
            }

            value.Title = name;

            return value;
        }

        private static ConfiguratorComposition GetOrCreateComposition(Configurator configurator, string name, HashSet<int> presentCompositionIds)
        {
            var composition = configurator.ConfiguratorCompositions
                .FirstOrDefault(c => c.Title == name && !c.IsDeleted);

            if (composition == null)
            {
                composition = new ConfiguratorComposition { ConfiguratorId = configurator.Id };
                configurator.ConfiguratorCompositions.Add(composition);
            }
            else
            {
                presentCompositionIds.Add(composition.Id);
            }

            composition.Title = name;

            return composition;
        }

        private static ConfiguratorProduct GetOrCreateItem(ConfiguratorComposition composition, string name, HashSet<int> presentItemIds)
        {
            var item = composition.ConfiguratorProducts
                .FirstOrDefault(i => i.Title == name && !i.IsDeleted);

            if (item == null)
            {
                item = new ConfiguratorProduct();
                composition.ConfiguratorProducts.Add(item);
            }
            else
            {
                presentItemIds.Add(item.Id);
            }

            item.Title = name;

            return item;
        }

        // Helper to reduce validation boilerplate
        private static bool AddValidationAndReturnIfError(ValidationResult validationResult, ValidationResult mainValidation)
        {
            if (validationResult.HasErrors)
            {
                mainValidation.AddError(validationResult.GetErrors());
                return true;
            }
            return false;
        }

        // Overload for tuple-based validations
        private static bool AddValidationAndReturnIfError<T>( (bool isValid, T value, ValidationResult validationResult) result, ValidationResult mainValidation)
        {
            if (result.validationResult.HasErrors)
            {
                mainValidation.AddError(result.validationResult.GetErrors());
                return true;
            }
            return false;
        }
    }
}