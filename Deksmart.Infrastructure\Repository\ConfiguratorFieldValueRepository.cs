using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Base;
using Deksmart.Infrastructure.Repository.Interface;

namespace Deksmart.Infrastructure.Repository
{
    public class ConfiguratorFieldValueRepository : IntIdDaoBase<ConfiguratorFieldValue>, IConfiguratorFieldValueRepository
    {
        public ConfiguratorFieldValueRepository(ConfiguratorContext context) : base(context)
        {
        }
    }
}
