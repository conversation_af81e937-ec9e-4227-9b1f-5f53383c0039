﻿namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Encapsulates the outcome of a validation operation, tracking error messages and providing methods to add, aggregate, and query errors.
    /// Used throughout the application and domain layers to collect, report, and communicate validation status for business logic and API responses.
    /// Central to error handling and validation reporting in the Deksmart domain.
    /// </summary>
    public class ValidationResult
    {
        private List<string> _errors { get; set; } = new List<string>();

        /// <summary>
        /// Indicates whether the validation has any errors
        /// </summary>
        public bool HasErrors => _errors.Any();

        /// <summary>
        /// Indicates whether the validation is successful (no errors)
        /// </summary>
        public bool IsValid => !HasErrors;

        /// <summary>
        /// Adds an error message to the validation result
        /// </summary>
        /// <param name="error">The error message to add</param>
        public void AddError(string error)
        {
            _errors.Add(error);
        }

        /// <summary>
        /// Adds multiple error messages to the validation result
        /// </summary>
        /// <param name="errors">The error messages to add</param>
        public void AddErrors(IEnumerable<string> errors)
        {
            _errors.AddRange(errors);
        }

        /// <summary>
        /// Gets all error messages as a comma-separated string
        /// </summary>
        /// <returns>A comma-separated string of all error messages</returns>
        public string GetErrors()
        {
            return string.Join(", ", _errors);
        }

        /// <summary>
        /// Gets all error messages as a read-only list
        /// </summary>
        /// <returns>A read-only list of all error messages</returns>
        public IReadOnlyList<string> GetErrorList()
        {
            return _errors.AsReadOnly();
        }
    }
}
