using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Factory;
using Microsoft.Extensions.Logging;

namespace Deksmart.Ui.Shared.Services
{
    public class ChildServiceManager : IChildServiceManager
    {
        public void InitializeChildServices(
            ConfiguratorDto result,
            List<IConfiguratorGridService> childServices,
            IConfiguratorGridServiceFactory serviceFactory,
            IConfiguratorGridService parentService,
            EventHandler? validationStateChangedHandler,
            ILogger logger)
        {
            // Clear existing child services if the configurator ID has changed
            if (result.ChildUserConfigurators != null)
            {
                // Dispose and clear existing child services
                DisposeChildServices(childServices, validationStateChangedHandler);

                logger.LogInformation("Initializing child services for configurator ID: {ConfiguratorId}", result.Id);

                // Create new child services
                var orderedConfigurators = result.ChildUserConfigurators.OrderBy(c => c.TabOrder ?? int.MaxValue);
                foreach (var childConfigurator in orderedConfigurators)
                {
                    var childService = serviceFactory.CreateService();
                    childService.MainConfiguratorGridService = parentService; // Set the parent service
                    childService.ConfiguratorWrapper = ConfiguratorWrapperFactory.Create(childConfigurator);
                    
                    // Set TabOrder from the DTO to maintain consistent ordering
                    if (childService.ConfiguratorWrapper != null)
                    {
                        childService.ConfiguratorWrapper.TabOrder = childConfigurator.TabOrder ?? 1;
                    }
                    
                    if (validationStateChangedHandler != null)
                    {
                        childService.ValidationStateChanged += validationStateChangedHandler;
                    }
                    
                    childServices.Add(childService);

                    logger.LogInformation("Added child service for configurator ID: {ChildConfiguratorId}", childConfigurator.Id);
                }
            }
        }

        public void DisposeChildServices(List<IConfiguratorGridService> childServices, EventHandler? validationStateChangedHandler)
        {
            foreach (var childService in childServices)
            {
                if (validationStateChangedHandler != null)
                {
                    childService.ValidationStateChanged -= validationStateChangedHandler;
                }
                childService.Dispose();
            }
            childServices.Clear();
        }
    }
}