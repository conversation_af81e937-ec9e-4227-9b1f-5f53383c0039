using Deksmart.Domain.Entity.Business;

namespace Deksmart.Domain.Service
{
    public interface IFieldValueMatcher
    {
        /// <summary>
        /// Matches and calculates values for fields with expressions, using the provided matched fields as parameters.
        /// </summary>
        /// <param name="clientFieldValues">The client-provided direct value fields, some of which may have expressions.</param>
        /// <param name="matchedFields">The already matched fields to use as parameters for expression evaluation.</param>
        /// <returns>
        /// A list of <see cref="ValueIdent"/> representing the calculated values for fields with expressions.
        /// </returns>
        List<ValueIdent> MatchExpressionFields(IEnumerable<DirectValue> clientFieldValues, IEnumerable<ValueIdent> matchedFields);

        /// <summary>
        /// Matches direct value fields from client input to database fields, validates their ranges, and returns the matched values.
        /// </summary>
        /// <param name="clientFieldValues">A dictionary of client field IDs and their values.</param>
        /// <param name="dbFields">The collection of direct value fields from the database.</param>
        /// <param name="validation">The validation result to add errors to if values are out of range.</param>
        /// <returns>
        /// A list of <see cref="ValueIdent"/> representing the matched and validated direct values.
        /// </returns>
        List<ValueIdent> MatchFieldDirectValues(Dictionary<int, decimal?> clientFieldValues, IEnumerable<DirectValue> dbFields, ValidationResult validation);

        /// <summary>
        /// Matches multiple choice fields from client input to database field groups, validates the selected values, and returns the matched values.
        /// </summary>
        /// <param name="clientFieldValues">A dictionary of client field IDs and their values.</param>
        /// <param name="dbFieldValues">The collection of multiple choice value groups from the database.</param>
        /// <param name="validation">The validation result to add errors to if values are invalid.</param>
        /// <returns>
        /// A list of <see cref="ValueIdent"/> representing the matched and validated multiple choice values.
        /// </returns>
        List<ValueIdent> MatchFieldMultipleChoiceValues(Dictionary<int, decimal?> clientFieldValues, IEnumerable<MultipleChoiceValue> dbFieldValues, ValidationResult validation);
    }

    public class FieldValueMatcherService : IFieldValueMatcher
    {
        private readonly IConfiguratorExpressionCalculator _calculator;
        private readonly IConfiguratorValidationService _validationService;

        public FieldValueMatcherService(IConfiguratorExpressionCalculator calculator, IConfiguratorValidationService validationService)
        {
            _calculator = calculator;
            _validationService = validationService;
        }

        public List<ValueIdent> MatchFieldMultipleChoiceValues(Dictionary<int, decimal?> clientFieldValues, IEnumerable<MultipleChoiceValue> dbFieldValues, ValidationResult validation)
        {
            var result = new List<ValueIdent>();

            foreach (var dbFieldValuesGroup in dbFieldValues)
            {
                var fieldId = dbFieldValuesGroup.Id;

                if (clientFieldValues.TryGetValue(fieldId, out decimal? value) && value.HasValue)
                {
                    // FieldId exists, but Value does not exist or is different from default
                    _validationService.ValidateMultipleChoiceValueHasValidValue(dbFieldValuesGroup, fieldId, value.Value, validation);

                    result.Add(new ValueIdent() { Ident = dbFieldValuesGroup.Ident, Value = value }); // FieldId and Value exists
                }
                else
                {
                    result.Add(new ValueIdent() { Ident = dbFieldValuesGroup.Ident, Value = dbFieldValuesGroup.DefaultValue });// FieldId does not exist in client values
                }
            }

            return result;
        }

        public List<ValueIdent> MatchFieldDirectValues(Dictionary<int, decimal?> clientFieldValues, IEnumerable<DirectValue> dbFields, ValidationResult validation)
        {
            var result = new List<ValueIdent>();
            foreach (var field in dbFields.Where(d => d.Expression == null))
            {
                if (clientFieldValues.TryGetValue(field.Id, out var value) && value.HasValue)
                {
                    // FieldId exists, but Value is out of range
                    _validationService.ValidateDirectValueIsWithinBounds(field, value.Value, validation);

                    result.Add(new ValueIdent { Ident = field.Ident, Value = value }); // FieldId exists and Value is within range
                }
                else
                {
                    result.Add(new ValueIdent { Ident = field.Ident, Value = field.DefaultValue }); // FieldId does not exist in client values
                }
            }

            return result;
        }

        public List<ValueIdent> MatchExpressionFields(IEnumerable<DirectValue> clientFieldValues, IEnumerable<ValueIdent> matchedFields)
        {
            //last calculate fields with expression and add them to matched fields
            return clientFieldValues.Where(d => d.Expression != null)
                .Select(d => new ValueIdent
                {
                    Ident = d.Ident,
                    Value = _calculator.GetValue(d.Expression!.Id, d.Expression.Expression, matchedFields)
                }).ToList();
        }
    }
}