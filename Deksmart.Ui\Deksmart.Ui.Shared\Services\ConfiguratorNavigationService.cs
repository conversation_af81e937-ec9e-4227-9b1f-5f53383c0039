using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Deksmart.Ui.Shared.Resources;
using Microsoft.JSInterop;

namespace Deksmart.Ui.Shared.Services
{
    public class ConfiguratorNavigationService
    {
        private readonly NavigationManager _navigationManager;
        private readonly ILogger<ConfiguratorNavigationService> _logger;
        private readonly IJSRuntime _jsRuntime;
        private IConfiguratorGridService? _activeService;
        private double _savedScrollPosition;

        public ConfiguratorNavigationService(
            NavigationManager navigationManager,
            ILogger<ConfiguratorNavigationService> logger,
            IJSRuntime jsRuntime)
        {
            _navigationManager = navigationManager;
            _logger = logger;
            _jsRuntime = jsRuntime;
        }

        public void SetActiveService(IConfiguratorGridService service)
        {
            _activeService = service;
            _logger.LogInformation("Set active configurator service for ID: {ConfiguratorId}", service.ConfiguratorWrapper?.Id);
        }

        public async Task NavigateToConfigurator(int id)
        {
            if (_activeService != null)
            {
                _logger.LogInformation("Navigating to configurator ID: {ConfiguratorId} from preset view", id);
                _savedScrollPosition = await _jsRuntime.InvokeAsync<double>("eval", "window.scrollY");
                _navigationManager.NavigateTo($"/{UiSharedResource.Deksmart}/{id}", false);
            }
        }

        public IConfiguratorGridService? GetActiveService()
        {
            return _activeService;
        }

        public double GetSavedScrollPosition()
        {
            return _savedScrollPosition;
        }

        public void ClearActiveService()
        {
            _activeService = null;
        }
    }
}