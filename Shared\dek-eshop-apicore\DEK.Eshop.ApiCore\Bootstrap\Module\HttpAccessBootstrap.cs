using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class HttpAccessBootstrap
{
    /// <summary>
    ///     Setup middleware
    /// </summary>
    public static void UseHttpLoggerBootstrap(this WebApplication app)
    {
        var logger = app.Services.GetService<Loging.Logger>();
        app.Use(async (context, next) => {
            //before
            await next.Invoke();
            //after
            logger?.DispatchHttpAccessEvent(context);
        });
    }
}
