namespace Deksmart.Domain.Entity.Business.Eshop
{
    /// <summary>
    /// Aggregates pricing and unit information for a specific e-shop product, identified by its product code.
    /// Used for business logic and data transfer related to product price calculations, display, and order processing.
    /// </summary>
    public class ProductPriceData
    {
        public string ProductCode { get; set; }
        public EshopProductPricing Pricing { get; set; }
        public EshopProductUnit Unit { get; set; }
        public decimal? ConvertedAmount { get; set; }
    }
} 