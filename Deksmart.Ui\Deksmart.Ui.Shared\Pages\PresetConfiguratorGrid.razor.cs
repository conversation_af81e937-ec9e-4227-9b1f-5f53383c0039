﻿using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components;

namespace Deksmart.Ui.Shared.Pages
{
    public partial class PresetConfiguratorGrid : ComponentBase
    {
        [Parameter]
        public int Id { get; set; }

        [Parameter]
        public string Code { get; set; } = default!;

        [Inject]
        public IConfiguratorGridService ConfiguratorGridService { get; set; } = default!;

        [Inject]
        public ConfiguratorNavigationService NavigationService { get; set; } = default!;

        protected override async Task OnInitializedAsync()
        {
            await ConfiguratorGridService.GetDataForPresetAsync(Id, Code);
            NavigationService.SetActiveService(ConfiguratorGridService);
        }
    }
}
