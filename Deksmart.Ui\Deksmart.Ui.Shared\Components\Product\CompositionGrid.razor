﻿@using Deksmart.Ui.Shared.Components
@using Deksmart.Ui.Shared.Components.Utility
@using Deksmart.Ui.Shared.Resources
@using Microsoft.AspNetCore.Components
@inherits ComponentBase

<div class="accordion-item product-listing-item @(IsCollapsed ? "" : "active")">
    <div class="accordion-header product-listing-header" @onclick="ToggleCollapse">
        <div class="accordion-button">
            <h4 class="accordion-title">@UiSharedResource.ProductListing@(IsSummaryView ? " - " + UiSharedResource.Summary : "")</h4>
            <span class="arrow @(IsCollapsed ? "collapsed" : "")">&#9660;</span>
        </div>
    </div>
    <div class="accordion-content product-listing-content">
        @if (!IsCollapsed)
        {
            <div class="composition-grid">
                <div class="composition-table">
                    <div class="composition-header">
                        <div class="composition-header-cell">@UiSharedResource.ProductNumber</div>
                        <div class="composition-header-cell">@UiSharedResource.ProductName</div>
                        <div class="composition-header-cell" style="grid-column: 3 / 5;">@UiSharedResource.QuantityPerStructure</div>
                        <div class="composition-header-cell" style="grid-column: 5 / 7;">@UiSharedResource.PriceVAT</div>
                    </div>
                @for (int i = 0; i < ConfiguratorGridService.ConfiguratorWrapper!.ConfiguratorCompositions!.Count; i++)
                {
                    var composition = ConfiguratorGridService.ConfiguratorWrapper.ConfiguratorCompositions[i];
                    var bgClass = i % 2 == 0 ? "composition-white" : "composition-lightgray";

                    <!-- Desktop Layout - Composition Title Row -->
                    <div class="composition-row desktop-only @bgClass">
                        <div class="composition-property" data-label="ProductNumber">
                            @if (!IsSummaryView && composition.IsMultipleProducts && composition.SelectedProducts.Count() < composition.Products.Count())
                            {
                                <button class="button" @onclick="async () => await AddProductIntoComposition(composition.Id)">@UiSharedResource.Next</button>
                            }
                        </div>
                        <div class="composition-property" style="grid-column: 2 / 7;">
                            <span>@composition.Title</span>
                        </div>
                    </div>

                    @for (var j = 0; j < composition.SelectedProducts.Count; j++)
                    {
                        var product = composition.SelectedProducts[j];
                        var index = j;

                        if (product == null)
                            continue;

                        <!-- Desktop Layout -->
                        <div class="composition-row desktop-only @bgClass @(product!.HasValidationError ? "composition-error" : "")">
                            <div class="composition-property" data-label="ProductNumber">
                                <span>@product.ProductCode</span>
                                @if (product.HasValidationError)
                                {
                                    <i class="fas fa-exclamation-triangle text-warning ms-1" title="@product.ValidationError"></i>
                                }
                                @if (product.Detail is null)
                                {
                                    <button class="button" @onclick="async () => await LoadProductDetail(product)">@UiSharedResource.Preview</button>
                                }
                                else
                                {
                                    <button class="button" @onclick="() => CloseProductDetail(product)">@UiSharedResource.Close</button>
                                }
                                @if (!IsSummaryView && composition.SelectedProducts.Count() > 1)
                                {
                                    <button class="button" @onclick="() => RemoveProductFromComposition(composition.Id, index)">@UiSharedResource.Remove</button>
                                }
                            </div>
                            <div class="composition-property" data-label="ProductName">
                                @if (!IsSummaryView && composition.Products.Any())
                                {
                                    <InputSelect class="form-control"
                                                 Value="product.Id"
                                                 ValueChanged="@(EventCallback.Factory.Create<int>(this, async id => await OnSelectedItemChanged(id, composition, index)))"
                                                 ValueExpression="@( () => product.Id )">
                                        @foreach (var product in composition.Products)
                                        {
                                            <option value="@product.Id">@product.Title</option>
                                        }
                                    </InputSelect>
                                }
                                else
                                {
                                    <span>@product.Title</span>
                                }
                            </div>
                            <div class="composition-property" data-label="Quantity">
                                @if (composition.IsLoading)
                                {
                                    <SimpleSpinner />
                                }
                                else if (product != null)
                                {
                                    <span>@product.CalculatedAmount @product.ProductUnit</span>
                                }
                            </div>
                            <div class="composition-property" data-label="Package">
                                @if (composition.IsLoading)
                                {
                                    <SimpleSpinner />
                                }
                                else if (product != null)
                                {
                                    @if (!IsSummaryView)
                                    {
                                        <span>
                                            <TrimInputNumber min="0"
                                                             Value="@product.PackageQuantity"
                                                             ValueChanged="@((decimal value) => OnNumericPackageAmountChanged(value, product))"
                                                             class="@(product.IsPackageAmountDirty ? "bold-value" : "")"
                                                             placeholder="@product.PackageQuantityWatermark"
                                                             Suffix="@product.PackageUnit" />
                                        </span>
                                    }
                                    else
                                    {
                                        <span>@product.PackageQuantity @product.PackageUnit</span>
                                    }
                                }
                            </div>
                            <div class="composition-property" data-label="PricePerPackage">
                                @if (composition.IsLoading)
                                {
                                    <SimpleSpinner />
                                }
                                else if (product != null)
                                {
                                    <span>@product.PackagePrice @UiSharedResource.CurrencySymbol / @product.PackageUnit</span>
                                }
                            </div>
                            <div class="composition-property" data-label="TotalPrice">
                                @if (composition.IsLoading)
                                {
                                    <SimpleSpinner />
                                }
                                else if (product != null)
                                {
                                    @if (product.HasValidationError)
                                    {
                                        <span class="text-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            @UiSharedResource.PricingError
                                        </span>
                                    }
                                    else
                                    {
                                        <span>@product.TotalPrice @UiSharedResource.CurrencySymbol</span>
                                    }
                                }
                            </div>
                        </div>

                        <!-- Mobile Layout -->
                        <div class="composition-mobile mobile-only @bgClass @(product!.HasValidationError ? "composition-error" : "")">
                            <div class="composition-mobile-header">
                                <span class="composition-mobile-title">@composition.Title</span>
                                @if (product.HasValidationError)
                                {
                                    <i class="fas fa-exclamation-triangle text-warning ms-1" title="@product.ValidationError"></i>
                                }
                                @if (!IsSummaryView && composition.SelectedProducts.Count() > 1)
                                {
                                    <button class="button" @onclick="() => RemoveProductFromComposition(composition.Id, index)">@UiSharedResource.Remove</button>
                                }
                            </div>
                            <div class="composition-mobile-row">
                                <div class="composition-mobile-flex-row">
                                    <div class="composition-mobile-property" data-label="@UiSharedResource.ProductNumber">
                                        <div class="mobile-content">
                                            <div class="product-number-group">
                                                <span>@product!.ProductCode</span>
                                                @if (product.Detail is null)
                                                {
                                                    <button class="button" @onclick="async () => await LoadProductDetail(product)">@UiSharedResource.Preview</button>
                                                }
                                                else
                                                {
                                                    <button class="button" @onclick="() => CloseProductDetail(product)">@UiSharedResource.Close</button>
                                                }
                                                @if (!IsSummaryView && composition.IsMultipleProducts && composition.SelectedProducts.Count() < composition.Products.Count())
                                                {
                                                    <button class="button" @onclick="async () => await AddProductIntoComposition(composition.Id)">@UiSharedResource.Next</button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="composition-mobile-property" data-label="@UiSharedResource.ProductName">
                                        <div class="mobile-content">
                                            @if (!IsSummaryView && composition.Products.Any())
                                            {
                                                <InputSelect class="form-control"
                                                             Value="product.Id"
                                                             ValueChanged="@(EventCallback.Factory.Create<int>(this, async id => await OnSelectedItemChanged(id, composition, index)))"
                                                             ValueExpression="@( () => product.Id )">
                                                    @foreach (var product in composition.Products)
                                                    {
                                                        <option value="@product.Id">@product.Title</option>
                                                    }
                                                </InputSelect>
                                            }
                                            else
                                            {
                                                <span>@product.Title</span>
                                            }
                                        </div>
                                    </div>
                                    <div class="composition-mobile-property" data-label="@UiSharedResource.QuantityPerStructure">
                                        @if (product != null)
                                        {
                                            <div class="mobile-content">
                                                <div class="quantity-package-group">
                                                    <div class="quantity-group">
                                                        <span>@product.CalculatedAmount</span>
                                                        <span>@product.ProductUnit</span>
                                                    </div>
                                                    <div class="package-group">
                                                        @if (!IsSummaryView)
                                                        {
                                                            <TrimInputNumber min="0" max="100"
                                                                             Value="@product.PackageQuantity"
                                                                             ValueChanged="@((decimal value) => OnNumericPackageAmountChanged(value, product))"
                                                                             class="@(product.IsPackageAmountDirty ? "bold-value" : "")"
                                                                             placeholder="@product.PackageQuantityWatermark" />
                                                        }
                                                        else
                                                        {
                                                            <span>@product.PackageQuantity</span>
                                                        }
                                                        <span>@product.PackageUnit</span>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                    <div class="composition-mobile-property" data-label="@UiSharedResource.PriceVAT">
                                        @if (composition.IsLoading)
                                        {
                                            <div class="mobile-content">
                                                <div style="padding: 1rem;">
                                                    <SimpleSpinner />
                                                </div>
                                            </div>
                                        }
                                        else if (product != null)
                                        {
                                            <div class="mobile-content">
                                                @if (product.HasValidationError)
                                                {
                                                    <div class="text-danger">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                                        @UiSharedResource.PricingError
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="price-group">
                                                        <div class="price-per-package-group">
                                                            <span>@product.PackagePrice</span>
                                                            <span>@UiSharedResource.CurrencySymbol / @product.PackageUnit</span>
                                                        </div>
                                                        <div class="total-price-group">
                                                            <span>@product.TotalPrice</span>
                                                            <span>@UiSharedResource.CurrencySymbol</span>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (product!.Detail is not null)
                        {
                            <ProductDetail Detail="@product.Detail" />
                        }
                    }
                }
                </div>
            </div>
        }

        <div class="card mb-3">
            <div class="card-body">
                @* Show error summary if shopping cart is disabled due to product errors *@
                @if (!ConfiguratorGridService.ConfiguratorWrapper.CanAddToCart)
                {
                    <div class="alert alert-warning d-flex align-items-center mb-3" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>@UiSharedResource.PricingError:</strong> 
                            @UiSharedResource.ProductPricingErrorMessage
                        </div>
                    </div>
                }
                
                <div class="single-line-right">
                    <b>@UiSharedResource.FinalPriceNoVat</b>
                    <span>@ConfiguratorGridService.ConfiguratorWrapper.TotalPriceNoVat.ToString("0.##") @UiSharedResource.CurrencySymbol</span>
                </div>
                <div class="single-line-right">
                    <b>@UiSharedResource.VAT</b>
                    <span>@ConfiguratorGridService.ConfiguratorWrapper.TotalVat.ToString("0.##") @UiSharedResource.CurrencySymbol</span>
                </div>
                <div class="single-line-right">
                    <b>@UiSharedResource.FinalPrice</b>
                    <span>@ConfiguratorGridService.ConfiguratorWrapper.TotalPrice.ToString("0.##") @UiSharedResource.CurrencySymbol</span>
                </div>
                
                @if (!ConfiguratorGridService.ConfiguratorWrapper.CanAddToCart)
                {
                    <div class="text-muted mt-2">
                        <small><i class="fas fa-info-circle me-1"></i>@UiSharedResource.TotalsExcludeErrorsMessage</small>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
