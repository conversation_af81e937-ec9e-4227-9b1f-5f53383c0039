﻿using Deksmart.Ui.Shared.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Deksmart.Ui.Shared.Pages
{
    public partial class ConfiguratorGrid : ComponentBase
    {
        [Inject]
        public IConfiguratorGridService ConfiguratorGridService { get; set; } = default!;

        [Inject]
        public ConfiguratorNavigationService NavigationService { get; set; } = default!;

        [Inject]
        public IJSRuntime JSRuntime { get; set; } = default!;

        [Parameter]
        public int Id { get; set; }

        protected override async Task OnParametersSetAsync()
        {
            var activeService = NavigationService.GetActiveService();
            if (activeService != null && activeService.ConfiguratorWrapper?.Id == Id)
            {
                ConfiguratorGridService = activeService;
                NavigationService.ClearActiveService();
            }
            else if (ConfiguratorGridService.ConfiguratorWrapper == null || ConfiguratorGridService.ConfiguratorWrapper.Id != Id)
            {
                await ConfiguratorGridService.LoadDataAsync(Id);
            }
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                var savedScrollPosition = NavigationService.GetSavedScrollPosition();
                if (savedScrollPosition > 0)
                {
                    await JSRuntime.InvokeVoidAsync("window.scrollTo", 0, savedScrollPosition);
                }
            }
        }
    }
}
