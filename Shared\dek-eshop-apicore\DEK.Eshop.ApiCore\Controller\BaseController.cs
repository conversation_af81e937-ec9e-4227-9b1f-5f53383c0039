using DEK.Eshop.ApiCore.Validation;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore.Metadata.Internal;

namespace DEK.Eshop.ApiCore.Controller;

public abstract class BaseController : ControllerBase
{
    [Obsolete]
    protected BadRequestObjectResult CreateBadRequest<T>(string key) where T : IValidationMessageFactory
    {
        return BadRequest(new ValidationPayload(T.CreateMessage(key), HttpContext.TraceIdentifier));
    }

    protected BadRequestObjectResult CreateBadRequest(string message, string? code = null, string? property = null)
    {
        var error = new Error { Code = code, Message = message, Property = property };
        return BadRequest(new[] { error });
    }
}
