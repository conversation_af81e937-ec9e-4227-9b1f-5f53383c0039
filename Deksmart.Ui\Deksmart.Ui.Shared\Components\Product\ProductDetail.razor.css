﻿.composition-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 12px;
    font-size: 0.9rem;
    background-color: var(--light-color);
    border-radius: 4px;
    grid-column: 1 / -1;
    margin: 4px;
}

.composition-details-header {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--light-color);
}

.composition-details-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    color: var(--secondary-color);
}

.composition-image {
    grid-column: 1;
    text-align: center;
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    box-shadow: var(--shadow-sm);
}

    .composition-image img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        max-height: 240px;
        object-fit: contain;
    }

.composition-description {
    grid-column: 2;
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    box-shadow: var(--shadow-sm);
}

.tech-specs-table {
    grid-column: 1 / -1;
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 4px;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

    .tech-specs-table td {
        padding: 8px 12px;
        border-bottom: 1px solid var(--light-color);
        font-size: 0.9rem;
    }

        .tech-specs-table td:first-child {
            font-weight: 600;
            width: 40%;
            background-color: var(--light-color);
            color: var(--secondary-color);
        }

    .tech-specs-table tr:last-child td {
        border-bottom: none;
    }

@media (max-width: 768px) {
    .composition-details {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 8px;
        margin: 8px 0;
    }

    .composition-image {
        grid-column: 1;
        text-align: center;
        padding: 4px;
    }

    .composition-description {
        grid-column: 1;
        padding: 8px;
    }

    .tech-specs-table {
        grid-column: 1;
    }

    .composition-image img {
        max-width: 100%;
        height: auto;
        border-radius: 3px;
        max-height: 140px;
        object-fit: contain;
    }
}