using Deksmart.Ui.Shared.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Deksmart.Ui.Shared.Factory
{
    /// <summary>
    /// Implementation of the factory for creating ConfiguratorGridService instances.
    /// </summary>
    public class ConfiguratorGridServiceFactory : IConfiguratorGridServiceFactory
    {
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="ConfiguratorGridServiceFactory"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider used to resolve dependencies.</param>
        public ConfiguratorGridServiceFactory(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <inheritdoc/>
        public IConfiguratorGridService CreateService()
        {
            return ActivatorUtilities.CreateInstance<ConfiguratorGridService>(_serviceProvider);
        }
    }
} 