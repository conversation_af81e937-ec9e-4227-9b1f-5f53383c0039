﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Deksmart.Domain\Deksmart.Domain.csproj" />
        <ProjectReference Include="..\Shared\dek-eshop-apicore\DEK.Eshop.ApiCore\DEK.Eshop.ApiCore.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Resource\DeksmartInfrastructureResource.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>DeksmartInfrastructureResource.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Resource\DeksmartInfrastructureResource.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>DeksmartInfrastructureResource.Designer.cs</LastGenOutput>
        </EmbeddedResource>
        <EmbeddedResource Update="Resource\DeksmartInfrastructureResource.cs-CZ.resx">
            <DependentUpon>DeksmartInfrastructureResource.resx</DependentUpon>
        </EmbeddedResource>
        <EmbeddedResource Update="Resource\DeksmartInfrastructureResource.sk-SK.resx">
            <DependentUpon>DeksmartInfrastructureResource.resx</DependentUpon>
        </EmbeddedResource>
    </ItemGroup>
</Project>
