using System.Text;
using System.Text.Json;

namespace DEK.Eshop.ApiCore.Extension;

public static class ObjectExtension
{
    /// <summary>
    /// NamingPolicy is camelCase
    /// </summary>
    /// <returns>Serialized object to string</returns>
    public static string Dek_ToJson(this object source)
    {
        var serializeOptions = new JsonSerializerOptions {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        };
        return JsonSerializer.Serialize(source, serializeOptions);
    }

    /// <returns>Serialized object to json payload as StringContent</returns>
    public static StringContent Dek_ToHttpJsonPayload(this object source)
    {
        return new StringContent(source.Dek_ToJson(), Encoding.UTF8, "application/json");
    }

    [Obsolete("Use Dek_ToHttpJsonPayload", true)]
    public static StringContent _ToPayload(this object source)
    {
        return source.Dek_ToHttpJsonPayload();
    }

    [Obsolete("Use Dek_ToJson", true)]
    public static string _ToJson(this object source)
    {
        return source.Dek_ToJson();
    }
}
