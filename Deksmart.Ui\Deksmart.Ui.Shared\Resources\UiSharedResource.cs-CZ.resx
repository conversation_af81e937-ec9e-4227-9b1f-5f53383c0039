﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AnUnexpectedErrorOccurred" xml:space="preserve">
    <value>Došlo k neočekávané chybě: {0}</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Zavřít</value>
  </data>
  <data name="Configurators" xml:space="preserve">
    <value>Kalkulačky</value>
  </data>
  <data name="EnterCompositionNumber" xml:space="preserve">
    <value>Zadejte číslo skladby z katalogu:</value>
  </data>
  <data name="FinalPrice" xml:space="preserve">
    <value>Výsledná cena s DPH</value>
  </data>
  <data name="FinalPriceNoVat" xml:space="preserve">
    <value>Výsledná cena za konstrukci bez DPH</value>
  </data>
  <data name="Find" xml:space="preserve">
    <value>Hledat</value>
  </data>
  <data name="NoConfiguratorSelected" xml:space="preserve">
    <value>Není vybrán žádný kalkulátor</value>
  </data>
  <data name="Preview" xml:space="preserve">
    <value>Náhled</value>
  </data>
  <data name="PriceVAT" xml:space="preserve">
    <value>Cena s DPH</value>
  </data>
  <data name="ProductListing" xml:space="preserve">
    <value>Výpis zboží</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>Název položky</value>
  </data>
  <data name="ProductNumber" xml:space="preserve">
    <value>Číslo položky</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Souhrn</value>
  </data>
  <data name="QuantityPerStructure" xml:space="preserve">
    <value>Potřebné množství na konstrukci</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Uložit</value>
  </data>
  <data name="VAT" xml:space="preserve">
    <value>DPH</value>
  </data>
  <data name="DownloadConfigurationPdf" xml:space="preserve">
    <value>Stáhnout PDF</value>
  </data>
  <data name="JsonFormatError" xml:space="preserve">
    <value>Server vrátil data v neočekávaném formátu. Zkuste to prosím znovu nebo kontaktujte podporu, pokud problém přetrvává.</value>
  </data>
  <data name="ArgumentException" xml:space="preserve">
    <value>Došlo k problému s parametry požadavku. Zkontrolujte prosím zadané údaje a zkuste to znovu.</value>
  </data>
  <data name="InvalidOperationError" xml:space="preserve">
    <value>Operaci se nepodařilo dokončit. Zkuste to prosím znovu nebo kontaktujte podporu, pokud problém přetrvává.</value>
  </data>
  <data name="UnauthorizedAccessError" xml:space="preserve">
    <value>Nemáte oprávnění provést tuto akci. Zkontrolujte prosím své přihlašovací údaje a zkuste to znovu.</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>Další</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Odstranit</value>
  </data>
  <data name="FailedToGeneratePdf" xml:space="preserve">
    <value>Nepodařilo se vygenerovat PDF</value>
  </data>
  <data name="SendInquiryButtonText" xml:space="preserve">
    <value>Odeslat poptávku</value>
  </data>
  <data name="SendInquirySuccess" xml:space="preserve">
    <value>Poptávka byla úspěšně odeslána</value>
  </data>
  <data name="SendInquiryError" xml:space="preserve">
    <value>Nepodařilo se odeslat poptávku</value>
  </data>
  <data name="NameLabel" xml:space="preserve">
    <value>Jméno</value>
  </data>
  <data name="EmailLabel" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="PhoneLabel" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="CancelButtonText" xml:space="preserve">
    <value>Zrušit</value>
  </data>
  <data name="ValidationError" xml:space="preserve">
    <value>Při odesílání dat do {0} došlo k chybě validace: {1}</value>
  </data>
  <data name="ValidationErrorsInTab" xml:space="preserve">
    <value>Tato záložka obsahuje chyby validace</value>
  </data>
  <data name="LoadingApplication" xml:space="preserve">
    <value>Načítání aplikace...</value>
  </data>
  <data name="PageNotFound" xml:space="preserve">
    <value>Stránka nenalezena</value>
  </data>
  <data name="PageNotFoundMessage" xml:space="preserve">
    <value>Omlouváme se, na této adrese nic není.</value>
  </data>
  <data name="ChildConfiguratorOverview" xml:space="preserve">
    <value>Celkový přehled</value>
  </data>
  <data name="ConfiguratorName" xml:space="preserve">
    <value>Název</value>
  </data>
  <data name="Field" xml:space="preserve">
    <value>Pole</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Hodnota</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ano</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Ne</value>
  </data>
  <data name="SelectOption" xml:space="preserve">
    <value>Vyberte...</value>
  </data>
  <data name="SelectConfigurator" xml:space="preserve">
    <value>Vyberte kalkulačku</value>
  </data>
  <data name="MetaDescription_MainPage" xml:space="preserve">
    <value>DEKSMART – konfigurátor produktů. Navrhněte, upravte a spočítejte materiál i cenu rychle a přesně pro váš projekt.</value>
  </data>
  <data name="MetaDescription_ConfiguratorTemplate" xml:space="preserve">
    <value>DEKSMART – {0}. Spočítejte materiál a cenu pro váš projekt.</value>
  </data>
  <data name="BadRequestError" xml:space="preserve">
    <value>Požadavek byl neplatný. Zkontrolujte prosím své údaje a zkuste to znovu.</value>
  </data>
  <data name="CalculateProductPricesUrl" xml:space="preserve">
    <value>calculate-product-prices</value>
  </data>
  <data name="CategoryCollapsed" xml:space="preserve">
    <value>category-{0}-collapsed</value>
  </data>
  <data name="ConfiguratorsUrl" xml:space="preserve">
    <value>configurators</value>
  </data>
  <data name="Deksmart" xml:space="preserve">
    <value>deksmart</value>
  </data>
  <data name="DeksmartBrand" xml:space="preserve">
    <value>DEKSMART</value>
  </data>
  <data name="ForbiddenError" xml:space="preserve">
    <value>Nemáte oprávnění k přístupu k tomuto zdroji.</value>
  </data>
  <data name="GenerateConfigurationPdfUrl" xml:space="preserve">
    <value>view/{0}/pdf</value>
  </data>
  <data name="LoadConfiguratorUrl" xml:space="preserve">
    <value>{0}/configurator</value>
  </data>
  <data name="LoadPresetUrl" xml:space="preserve">
    <value>{0}/configurator/{1}</value>
  </data>
  <data name="NewTab" xml:space="preserve">
    <value>Nová karta</value>
  </data>
  <data name="ResourceNotFound" xml:space="preserve">
    <value>Požadovaný {0} nebyl nalezen.</value>
  </data>
  <data name="SavePresetUrl" xml:space="preserve">
    <value>{0}/save</value>
  </data>
  <data name="SendInquiryUrl" xml:space="preserve">
    <value>view/{0}/email</value>
  </data>
  <data name="ServerError" xml:space="preserve">
    <value>Došlo k chybě serveru. Zkuste to prosím znovu později nebo kontaktujte podporu, pokud problém přetrvává.</value>
  </data>
  <data name="UnknownError" xml:space="preserve">
    <value>Došlo k neznámé chybě. Zkuste to prosím znovu později.</value>
  </data>
  <data name="CurrencySymbol" xml:space="preserve">
    <value>Kč</value>
  </data>
  <data name="AddToAgendaButtonText" xml:space="preserve">
    <value>Přidat do agendy</value>
  </data>
  <data name="AddToCartButtonText" xml:space="preserve">
    <value>Přidat do košíku</value>
  </data>
  <data name="PricingError" xml:space="preserve">
    <value>Chyba ocenění</value>
  </data>
  <data name="ProductPricingErrorMessage" xml:space="preserve">
    <value>Některé produkty nebylo možné ocenit. Nákupní košík je zablokován dokud nebudou chyby vyřešeny.</value>
  </data>
  <data name="TotalsExcludeErrorsMessage" xml:space="preserve">
    <value>Celkové částky nezahrnují produkty s chybami ocenění</value>
  </data>
  <data name="ProcessCompositeConfiguratorUrl" xml:space="preserve">
    <value>{0}/composite</value>
  </data>
</root>