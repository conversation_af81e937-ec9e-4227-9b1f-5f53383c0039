﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Deksmart.Domain.Resource {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class DeksmartDomainResource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal DeksmartDomainResource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Deksmart.Domain.Resource.DeksmartDomainResource", typeof(DeksmartDomainResource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string CheckboxNoValue {
            get {
                return ResourceManager.GetString("CheckboxNoValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string CheckboxYesValue {
            get {
                return ResourceManager.GetString("CheckboxYesValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Invalid component type: {1}.
        /// </summary>
        internal static string ComponentTypeDoesNotExist {
            get {
                return ResourceManager.GetString("ComponentTypeDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Invalid collapse state: {1}.
        /// </summary>
        internal static string CollapseStateDoesNotExist {
            get {
                return ResourceManager.GetString("CollapseStateDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Invalid field position: {1}.
        /// </summary>
        internal static string FieldPositionDoesNotExist {
            get {
                return ResourceManager.GetString("FieldPositionDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculator with id {0} not found..
        /// </summary>
        internal static string ConfiguratorDoesNotExist {
            get {
                return ResourceManager.GetString("ConfiguratorDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configurator title is required..
        /// </summary>
        internal static string ConfiguratorTitleRequired {
            get {
                return ResourceManager.GetString("ConfiguratorTitleRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression {1} cannot be parsed, there could be missing ident or incorrect syntax..
        /// </summary>
        internal static string ExpressionDoesNotMatch {
            get {
                return ResourceManager.GetString("ExpressionDoesNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Expression cannot be empty in expression field {1}..
        /// </summary>
        internal static string ExpressionValueCannotBeEmpty {
            get {
                return ResourceManager.GetString("ExpressionValueCannotBeEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression {0} cannot be parsed, there could be missing ident, incorrect syntax or referencing other expression field, no nesting is allowed..
        /// </summary>
        internal static string FieldExpressionDoesNotMatch {
            get {
                return ResourceManager.GetString("FieldExpressionDoesNotMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FieldId: &apos;{0}&apos; has no value: {1}.
        /// </summary>
        internal static string FieldValueDoesNotExist {
            get {
                return ResourceManager.GetString("FieldValueDoesNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field id {0} value {1} out of range {2}-{3}.
        /// </summary>
        internal static string FieldValueOutOfRange {
            get {
                return ResourceManager.GetString("FieldValueOutOfRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Field with title &apos;{0}&apos; is missing identificator..
        /// </summary>
        internal static string IdentRequiredWhenTitlePresent {
            get {
                return ResourceManager.GetString("IdentRequiredWhenTitlePresent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ident &apos;{0}&apos; is too long. Maximum length is 10 characters..
        /// </summary>
        internal static string IdentTooLong {
            get {
                return ResourceManager.GetString("IdentTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If a category is defined, the order must also be filled..
        /// </summary>
        internal static string ImportCategoryOrderRequired {
            get {
                return ResourceManager.GetString("ImportCategoryOrderRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If a composition is defined, the order must also be filled..
        /// </summary>
        internal static string ImportCompositionsOrderRequired {
            get {
                return ResourceManager.GetString("ImportCompositionsOrderRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The first sheet defining the calculator has the wrong format.
        /// </summary>
        internal static string ImportConfiguratorError {
            get {
                return ResourceManager.GetString("ImportConfiguratorError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If a field is defined, the order must also be filled..
        /// </summary>
        internal static string ImportFieldOrderRequired {
            get {
                return ResourceManager.GetString("ImportFieldOrderRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If Ident is filled, ComponentType must also be filled..
        /// </summary>
        internal static string ImportFieldsComponentTypeRequired {
            get {
                return ResourceManager.GetString("ImportFieldsComponentTypeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The second sheet defining fields is incorrectly formatted or missing.
        /// </summary>
        internal static string ImportFieldsError {
            get {
                return ResourceManager.GetString("ImportFieldsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If ComponentType is Expression, an expression must be defined..
        /// </summary>
        internal static string ImportFieldsExpressionRequired {
            get {
                return ResourceManager.GetString("ImportFieldsExpressionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The first cell in the first data row (after the header) of the fields sheet must be filled..
        /// </summary>
        internal static string ImportFieldsFirstCellError {
            get {
                return ResourceManager.GetString("ImportFieldsFirstCellError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If Value Title is filled, Numeric Value must also be filled..
        /// </summary>
        internal static string ImportFieldsNumericValueRequired {
            get {
                return ResourceManager.GetString("ImportFieldsNumericValueRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If ComponentType is Product, a product must be defined..
        /// </summary>
        internal static string ImportFieldsProductRequired {
            get {
                return ResourceManager.GetString("ImportFieldsProductRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If Ident is filled, Title must also be filled..
        /// </summary>
        internal static string ImportFieldsTitleRequired {
            get {
                return ResourceManager.GetString("ImportFieldsTitleRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If Value Title is filled, Value Order must also be filled..
        /// </summary>
        internal static string ImportFieldsValueOrderRequired {
            get {
                return ResourceManager.GetString("ImportFieldsValueOrderRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: If ComponentType is SingleChoice, Tile, or Selectbox, at least one value must be defined..
        /// </summary>
        internal static string ImportFieldsValueRequired {
            get {
                return ResourceManager.GetString("ImportFieldsValueRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Product code must be filled..
        /// </summary>
        internal static string ImportProductCodeRequired {
            get {
                return ResourceManager.GetString("ImportProductCodeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}:  Product name must be filled..
        /// </summary>
        internal static string ImportProductNameRequired {
            get {
                return ResourceManager.GetString("ImportProductNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Product order must be filled..
        /// </summary>
        internal static string ImportProductOrderRequired {
            get {
                return ResourceManager.GetString("ImportProductOrderRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The third sheet defining the products is incorrectly formatted or missing..
        /// </summary>
        internal static string ImportProductsError {
            get {
                return ResourceManager.GetString("ImportProductsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The first cell in the first data row (after the header) of the products sheet must be filled..
        /// </summary>
        internal static string ImportProductsFirstCellError {
            get {
                return ResourceManager.GetString("ImportProductsFirstCellError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Product unit must be filled..
        /// </summary>
        internal static string ImportProductUnitRequired {
            get {
                return ResourceManager.GetString("ImportProductUnitRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Product volume must be filled..
        /// </summary>
        internal static string ImportProductVolumeRequired {
            get {
                return ResourceManager.GetString("ImportProductVolumeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product {1} has invalid amount: {2}.
        /// </summary>
        internal static string InvalidProductAmount {
            get {
                return ResourceManager.GetString("InvalidProductAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product {1} has invalid unit &apos;{2}&apos;. Expected either &apos;{3}&apos; or &apos;{4}&apos;.
        /// </summary>
        internal static string InvalidProductUnit {
            get {
                return ResourceManager.GetString("InvalidProductUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Value {1} should be boolean..
        /// </summary>
        internal static string IsNotBoolean {
            get {
                return ResourceManager.GetString("IsNotBoolean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Value {1} should be image url..
        /// </summary>
        internal static string IsNotImageUrl {
            get {
                return ResourceManager.GetString("IsNotImageUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Value {1} should be integer..
        /// </summary>
        internal static string IsNotInteger {
            get {
                return ResourceManager.GetString("IsNotInteger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Value {1} should be numeric..
        /// </summary>
        internal static string IsNotNumeric {
            get {
                return ResourceManager.GetString("IsNotNumeric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Markdown string {1} could not be converted, make sure it has correct syntax..
        /// </summary>
        internal static string MarkdownConversionError {
            get {
                return ResourceManager.GetString("MarkdownConversionError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Amount for product is missing and cannot be set from previous product..
        /// </summary>
        internal static string MissingAmountForProduct {
            get {
                return ResourceManager.GetString("MissingAmountForProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: A category must have at least one field..
        /// </summary>
        internal static string MissingFieldForCategory {
            get {
                return ResourceManager.GetString("MissingFieldForCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: A composition must have at least one product..
        /// </summary>
        internal static string MissingProductForComposition {
            get {
                return ResourceManager.GetString("MissingProductForComposition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row {0}: Visibility for product is missing and cannot be set from previous product..
        /// </summary>
        internal static string MissingVisibilityForProduct {
            get {
                return ResourceManager.GetString("MissingVisibilityForProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No enrichment data found for product {1}.
        /// </summary>
        internal static string NoEnrichmentDataFound {
            get {
                return ResourceManager.GetString("NoEnrichmentDataFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product {0} not found in selected products.
        /// </summary>
        internal static string ProductNotFoundInSelection {
            get {
                return ResourceManager.GetString("ProductNotFoundInSelection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to get product units: {1}.
        /// </summary>
        internal static string UnitsError {
            get {
                return ResourceManager.GetString("UnitsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression string cannot be null or empty.
        /// </summary>
        internal static string CalculatorExpressionEmpty {
            get {
                return ResourceManager.GetString("CalculatorExpressionEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression '{0}' evaluated to null.
        /// </summary>
        internal static string CalculatorExpressionEvaluatedNull {
            get {
                return ResourceManager.GetString("CalculatorExpressionEvaluatedNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot parse expression result '{0}' to decimal.
        /// </summary>
        internal static string CalculatorResultParseError {
            get {
                return ResourceManager.GetString("CalculatorResultParseError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expression evaluation failed: {0}.
        /// </summary>
        internal static string CalculatorEvaluationError {
            get {
                return ResourceManager.GetString("CalculatorEvaluationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid expression or parameters: {0}.
        /// </summary>
        internal static string CalculatorInvalidArgument {
            get {
                return ResourceManager.GetString("CalculatorInvalidArgument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected error during calculation: {0}.
        /// </summary>
        internal static string CalculatorUnexpectedError {
            get {
                return ResourceManager.GetString("CalculatorUnexpectedError", resourceCulture);
            }
        }
    }
}
