namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a logical grouping of products that fulfill a specific functional requirement within a configurator.
    /// Used to transfer, organize, and render related products serving the same role, supporting selection of alternatives or variants.
    /// Enables both single- and multi-product compositions in configuration workflows, allowing the UI and backend to manage grouped product selections.
    /// </summary>
    [Serializable]
    public class ConfiguratorCompositionDto
    {
        public int Id { get; set; }

        public string Title { get; set; } = null!;

        public int Order { get; set; }

        public bool IsMultipleProducts { get; set; }

        public List<ConfiguratorProductDto> ConfiguratorProducts { get; set; } = [];
    }
}
