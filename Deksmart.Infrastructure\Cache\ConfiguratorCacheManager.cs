using DEK.Eshop.ApiCore.Cache;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Infrastructure.Resource;
using Microsoft.Extensions.Logging;

namespace Deksmart.Infrastructure.Cache
{
    public static class ConfiguratorCacheConstants
    {
        public const int DefaultCacheTimeoutMinutes = 60;
        public const string CacheKeyPrefix = "C";
        public const string ListCacheKeyPrefix = "CL";
    }

    /// <summary>
    /// Provides caching functionality for configurator data with error handling and fallback to database.
    /// </summary>
    public interface IConfiguratorCacheManager
    {
        /// <summary>
        /// Gets a list from cache or loads it from database if not cached or cache errors occur.
        /// Uses list-specific cache key prefix.
        /// </summary>
        /// <typeparam name="T">Type of items in the list</typeparam>
        /// <typeparam name="TId">Type of the identifier</typeparam>
        /// <param name="id">Identifier for the cached list</param>
        /// <param name="func">Function to load data from database if cache miss or error</param>
        /// <param name="minutes">Cache expiration time in minutes</param>
        /// <returns>List of items, empty list if no data found</returns>
        Task<List<T>> GetOrAddListAsync<T, TId>(TId id, Func<TId, Task<List<T>>> func, int minutes = ConfiguratorCacheConstants.DefaultCacheTimeoutMinutes)
            where TId : IEquatable<TId>;

        /// <summary>
        /// Gets a single item from cache or loads it from database if not cached or cache errors occur.
        /// </summary>
        /// <typeparam name="T">Type of the item</typeparam>
        /// <typeparam name="TId">Type of the identifier</typeparam>
        /// <param name="id">Identifier for the cached item</param>
        /// <param name="func">Function to load data from database if cache miss or error</param>
        /// <param name="minutes">Cache expiration time in minutes</param>
        /// <returns>The item or null/default if not found</returns>
        Task<T?> GetOrAddAsync<T, TId>(TId id, Func<TId, Task<T>> func, int minutes = ConfiguratorCacheConstants.DefaultCacheTimeoutMinutes)
            where TId : IEquatable<TId>;

        /// <summary>
        /// Gets multiple items from cache individually, loads missing items in bulk from database.
        /// Optimized for bulk operations - fails fast if cache errors occur to avoid multiple failed requests.
        /// </summary>
        /// <typeparam name="T">Type of items in the list</typeparam>
        /// <typeparam name="TId">Type of the identifier</typeparam>
        /// <param name="ids">List of identifiers to retrieve</param>
        /// <param name="func">Function to load missing items from database in bulk</param>
        /// <param name="minutes">Cache expiration time in minutes</param>
        /// <returns>List of items in original order, empty list if no data found</returns>
        Task<List<T>> GetOrAddListByItemAndSaveAsync<T, TId>(List<TId> ids, Func<List<TId>, Task<List<T>>> func, int minutes = ConfiguratorCacheConstants.DefaultCacheTimeoutMinutes)
            where TId : IEquatable<TId>;
    }

    public class ConfiguratorCacheManager(
        CacheManager cacheManager,
        ILogger<ConfiguratorCacheManager> logger) : IConfiguratorCacheManager
    {
        private readonly CacheManager _cacheManager = cacheManager;
        private readonly ILogger<ConfiguratorCacheManager> _logger = logger;

        public async Task<List<T>> GetOrAddListAsync<T, TId>(TId id, Func<TId, Task<List<T>>> func, int minutes = ConfiguratorCacheConstants.DefaultCacheTimeoutMinutes)
            where TId : IEquatable<TId>
        {
            var key = GenerateListCacheKey<T, TId>(id);
            var (result, hasError) = await TryGetFromCacheAsync<List<T>>(key);

            if (result == null || hasError)
            {
                result = await func(id);

                if (result == null)
                {
                    _logger.LogWarning("No data loaded from db");
                    return [];
                }

                await TrySaveToCacheAsync(key, result, minutes, hasError);
            }

            return result;
        }

        public async Task<List<T>> GetOrAddListByItemAndSaveAsync<T, TId>(List<TId> ids, Func<List<TId>, Task<List<T>>> func, int minutes = ConfiguratorCacheConstants.DefaultCacheTimeoutMinutes)
            where TId : IEquatable<TId>
        {
            if (ids == null || ids.Count == 0)
            {
                return [];
            }
            // TODO: [Performance Optimization]
            // Consider implementing Redis-specific optimizations when usage patterns are better understood:
            // - Add hit counting for frequently accessed collections
            // - Cache entire collections that are frequently accessed together
            // - Implement Redis hash structure for related items
            // - Use Redis pipelines for bulk operations

            var result = new T[ids.Count];
            var missingIds = new List<TId>();
            var idToIndexMap = new Dictionary<TId, int>();
            var cacheHasErrors = false;

            // Track original order and check cache for each ID
            for (int i = 0; i < ids.Count; i++)
            {
                var id = ids[i];
                idToIndexMap[id] = i;

                var key = GenerateItemCacheKey<T, TId>(id);
                var (cachedResult, hasError) = await TryGetFromCacheAsync<T>(key);

                if (hasError)
                {
                    // Error already logged in TryGetFromCacheAsync
                    cacheHasErrors = true;
                    // If cache is down, no point checking remaining items - add all to missing
                    AddRemainingIdsAsMissing(ids, i, missingIds, idToIndexMap);
                    break;
                }

                if (cachedResult != null)
                {
                    result[i] = cachedResult;
                }
                else
                {
                    missingIds.Add(id);
                }
            }

            // Load missing items in bulk
            if (missingIds.Count > 0)
            {
                var loadedItems = await func(missingIds);
                if (loadedItems == null)
                {
                    _logger.LogWarning("No data loaded from db");
                    return [];
                }

                // Validate that all requested items were loaded
                if (loadedItems.Count < missingIds.Count)
                {
                    _logger.LogError("Not all requested items were loaded from database. Expected {ExpectedCount} items but got {ActualCount}",
                        missingIds.Count, loadedItems.Count);
                    throw new InvalidOperationException(string.Format(DeksmartInfrastructureResource.FailedToLoadAllRequestedItems, missingIds.Count, loadedItems.Count));
                }


                for (int i = 0; i < missingIds.Count; i++)
                {
                    var id = missingIds[i];
                    var key = GenerateItemCacheKey<T, TId>(id);
                    
                    await TrySaveToCacheAsync(key, loadedItems[i], minutes, cacheHasErrors);
                    
                    // Insert at original position
                    var originalIndex = idToIndexMap[id];
                    result[originalIndex] = loadedItems[i];
                }
            }

            return [..result];
        }

        public async Task<T?> GetOrAddAsync<T, TId>(TId id, Func<TId, Task<T>> func, int minutes = ConfiguratorCacheConstants.DefaultCacheTimeoutMinutes)
            where TId : IEquatable<TId>
        {
            var key = GenerateItemCacheKey<T, TId>(id);
            var (result, hasError) = await TryGetFromCacheAsync<T>(key);

            if (result == null || hasError)
            {
                result = await func(id);

                if (result == null)
                {
                    _logger.LogWarning("No data loaded from db");
                    return default;
                }

                await TrySaveToCacheAsync(key, result, minutes, hasError);
            }

            return result;
        }

        private static string GenerateItemCacheKey<T, TId>(TId id) => $"{ConfiguratorCacheConstants.CacheKeyPrefix}:{GetShortTypeName<T>()}:{id}";

        private static string GenerateListCacheKey<T, TId>(TId id) => $"{ConfiguratorCacheConstants.ListCacheKeyPrefix}:{GetShortTypeName<T>()}:{id}";

        private static string GetShortTypeName<T>()
        {
            var type = typeof(T);
            
            // Use abbreviations for common types to save Redis memory
            if (type == typeof(ConfiguratorProduct)) return "CP";
            if (type == typeof(ConfiguratorField)) return "CF";
            if (type == typeof(ConfiguratorComposition)) return "CC";
            if (type == typeof(ConfiguratorPreset)) return "CPS";
            if (type == typeof(ConfiguratorFieldCategory)) return "CFC";
            if (type == typeof(ConfiguratorFieldValue)) return "CFV";
            if (type == typeof(EshopProduct)) return "EP";
            if (type == typeof(Configurator)) return "CON";
            if (type == typeof(ChildConfigurator)) return "CHC";
            
            // Fallback to type name for unknown types
            return type.Name;
        }

        private async Task<(T? result, bool hasError)> TryGetFromCacheAsync<T>(string key)
        {
            var (result, error) = await _cacheManager.GetAsync<T>(key);
            
            if (error != null)
            {
                _logger.LogError(error, "Error while trying to get cache");
                return (default, true);
            }
            
            return (result, false);
        }

        private async Task TrySaveToCacheAsync<T>(string key, T value, int minutes, bool cacheHasErrors)
        {
            if (!cacheHasErrors)
            {
                var (isSuccess, error) = await _cacheManager.SaveWithMinutesAsync(key, value, minutes);
                if (!isSuccess && error != null)
                {
                    _logger.LogError(error, "Error while trying to save to cache");
                }
            }
        }

        private static void AddRemainingIdsAsMissing<TId>(List<TId> ids, int startIndex, List<TId> missingIds, Dictionary<TId, int> idToIndexMap)
            where TId : IEquatable<TId>
        {
            for (int j = startIndex; j < ids.Count; j++)
            {
                var remainingId = ids[j];
                idToIndexMap[remainingId] = j;
                missingIds.Add(remainingId);
            }
        }
    }
}