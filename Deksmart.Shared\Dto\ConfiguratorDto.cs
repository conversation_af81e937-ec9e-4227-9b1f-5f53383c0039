namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing the complete definition and state of a configurator, including its metadata, compositions, field categories, child configurators, and calculated totals.
    /// Serves as the primary DTO for transferring configurator structure and state between backend, API, and UI.
    /// Supports dynamic UI generation, configuration workflows, and persistence by encapsulating all relevant configurator data and relationships.
    /// </summary>
    [Serializable]
    public class ConfiguratorDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = null!;
        public string Url { get; set; } = null!;
        public string? Description { get; set; }
        public string? EndDescription { get; set; }
        public decimal TotalPriceNoVat { get; set; }
        public decimal TotalPriceVat { get; set; }
        public decimal TotalVat { get; set; }
        /// <summary>
        /// Indicates that this configurator is a composite (can contain multiple sub-configurators).
        /// </summary>
        public bool IsComposite { get; set; }
        /// <summary>
        /// Indicates that this configurator was originally designed to have products (before filtering).
        /// </summary>
        public bool HasProducts { get; set; }
        public string? TabTitle { get; set; }
        public int? TabOrder { get; set; }
        public bool IsValid { get; set; }
        /// <summary>
        /// Indicates whether products can be added to shopping cart.
        /// False when any product has validation errors (pricing failures, missing data, etc.).
        /// Separate from IsValid which controls other form functionality.
        /// </summary>
        public bool CanAddToCart { get; set; } = true;
        public List<ConfiguratorCompositionDto> ConfiguratorCompositions { get; set; } = [];

        public List<ConfiguratorFieldCategoryDto> ConfiguratorFieldCategories { get; set; } = [];

        public List<ConfiguratorDto> ChildUserConfigurators { get; set; } = [];

        public List<ChildConfiguratorDto> ChildConfigurators { get; set; } = [];

        public string? MetaDescription { get; set; }
    }
}
