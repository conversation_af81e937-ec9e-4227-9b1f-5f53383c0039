using MailKit.Security;
using MailKit.Net.Smtp;
using MimeKit;
using Microsoft.IdentityModel.Tokens;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Config.Dto;
using Microsoft.Extensions.Configuration;

namespace DEK.Eshop.ApiCore.Mail;

public class MailManager
{
    private readonly IConfiguration config;

    public MailManager(IConfiguration config)
    {
        this.config = config;
    }

    public async Task SendMail(MailBuilder mailBuilder)
    {
        var configMail = ConfigFactory.Create<Config.Dto.Mail>(this.config, "ApiCore:Mail");
        var message = mailBuilder.Build();

        if (message.From.Count == 0) {
            message.From.Add(new MailboxAddress(configMail.NameFrom, configMail.MailFrom));
        }

        using var client = new SmtpClient();
        try {
            client.Timeout = 5000;
            client.Connect(configMail.Host, configMail.Port, SecureSocketOptions.Auto);
            if(configMail.Password != null) {
                client.Authenticate(configMail.MailFrom, configMail.Password);
            }

            await client.SendAsync(message);
        } finally {
            client.Disconnect(true);
        }
    }
}
