using DEK.Eshop.ApiCore.Database;
using Npgsql;
using System.Collections.Immutable;
using Deksmart.Infrastructure.Context;
using Deksmart.Domain.SavedConfiguration.Entity;
using DEK.Eshop.ApiCore.Dto;
using DEK.Eshop.ApiCore.Indentity;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository;

public class StateRepository
{
    private readonly PgsqlContextFactory<DeksmartPgsqlContext> _factory;

    public StateRepository(PgsqlContextFactory<DeksmartPgsqlContext> factory)
    {
        _factory = factory;
    }

    public virtual async Task<Pagination<CustomerState.Select>> GetCustomerStatePagination(ApplicationUser user, string query, SortBy sortBy, int offset, int limit)
    {
        var context = _factory.CreateContext();

        var totalCount = await context.TotalCount!.FromSqlRaw(@"
            SELECT
                count(*)::int as count
            FROM deksmart.dtcz_kalkulacky_state as user_state
            INNER JOIN deksmart.dtcz_kalkulacky_firma as customer_state on (user_state.hash = customer_state.hash)
            WHERE customer_state.email = @email
            AND customer_state.nazev LIKE @query",
            new NpgsqlParameter("email", user.UserEmail),
            new NpgsqlParameter("query", "%" + query + "%")
        ).FirstAsync();

        var orderBy = SortBy.ASC == sortBy ? "ASC" : "DESC";
        var list = await context.CustomerStateSelect!.FromSqlRaw($@"
            SELECT
                user_state.id as state_id,
                user_state.kalk_id as configurator_id,
                user_state.hash as hash,
                customer_state.email as email,
                customer_state.nazev as title,
                customer_state.timestamp as date_created
            FROM deksmart.dtcz_kalkulacky_state as user_state
            INNER JOIN deksmart.dtcz_kalkulacky_firma as customer_state on (user_state.hash = customer_state.hash)
            WHERE customer_state.email = @email
            AND customer_state.nazev LIKE @query
            ORDER BY customer_state.timestamp {orderBy}
            OFFSET @offset
            LIMIT @limit",
            new NpgsqlParameter("email", user.UserEmail),
            new NpgsqlParameter("query", "%"+query+"%"),
            new NpgsqlParameter("offset", offset),
            new NpgsqlParameter("limit", limit)
        ).AsNoTracking().ToListAsync();

        return new Pagination<CustomerState.Select>() {
            TotalCount = totalCount.Count,
            Items = list.ToImmutableList(),
        };

        //AND customer_state.nazev LIKE @query
        //ORDER BY customer_state.timestamp @sortBy
    }

    public virtual async Task<bool> DeleteCustomerState(ApplicationUser user, long hash)
    {
        var context = _factory.CreateContext();
        var stateDelete = await context.CustomerStateDelete.SingleOrDefaultAsync(s => s.Hash == hash && s.Email == user.UserEmail);
        if (stateDelete is null) {
            return false;
        }

        context.CustomerStateDelete.Remove(stateDelete);
        await context.SaveChangesAsync();

        return true;
    }


    #pragma warning disable CS8618
    /// <summary>
    /// Pouze pro testy
    /// </summary>
    public StateRepository()
    {
    }
    #pragma warning restore CS8618
}
