<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.3" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Deksmart.Ui.Web.Client\Deksmart.Ui.Web.Client.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\**\*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
