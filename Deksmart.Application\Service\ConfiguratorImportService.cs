using System.Data;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Db.Interface;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Deksmart.Infrastructure.Repository.Interface;
using Deksmart.Infrastructure.Repository.Interface.Base;
using ExcelDataReader;
using Microsoft.AspNetCore.Http;
using Deksmart.Application.Resource;

namespace Deksmart.Application.Service
{
    public interface IConfiguratorImportService
    {
        /// <summary>
        /// Imports and processes a configurator from the provided Excel file. Validates the file structure, parses configurator data, maps fields and products, updates or creates the configurator in the database, and returns validation results.
        /// </summary>
        /// <param name="file">The Excel file containing configurator data to import.</param>
        /// <returns>
        /// A tuple indicating whether the import was successful and a <see cref="ValidationResult"/> containing any validation errors.
        /// </returns>
        Task<(bool success, ValidationResult validation)> ParseConfiguratorImportsAsync(IFormFile file);
    }

    public class ConfiguratorImportService : IConfiguratorImportService
    {
        private readonly IImportProcessingService _importProcessingService;
        private readonly IImportValidationService _importValidationService;
        private readonly IConfiguratorImportParser _configuratorImportParser;
        private readonly IValidationService _validationService;

        public ConfiguratorImportService(
            IImportProcessingService importProcessingService,
            IImportValidationService importValidationService,
            IConfiguratorImportParser configuratorImportParser,
            IValidationService validationService)         
        {
            _importProcessingService = importProcessingService;
            _importValidationService = importValidationService;
            _configuratorImportParser = configuratorImportParser;
            _validationService = validationService;
        }

        public async Task<(bool success, ValidationResult validation)> ParseConfiguratorImportsAsync(IFormFile file)
        {
            var validation = _validationService.CreateValidation();

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);
            stream.Position = 0;

            var presentIds = new PresentIds();
            var expressionCache = new Dictionary<string, ConfiguratorExpression>();

            try
            {
                await _importProcessingService.BeginTransactionAsync();

                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    var result = reader.AsDataSet();

                    // Validate the import structure
                    var importValidation = _importValidationService.ValidateImport(result);
                    if (await _importProcessingService.HandleValidationFailureAsync(importValidation, validation))
                        return (false, validation);

                    var configuratorRow = result.Tables[0].Rows[1];
                    var emptyValidation = _importValidationService.ValidateCellEmpty(configuratorRow[0], DeksmartApplicationResource.ConfiguratorIdRequired);
                    if (await _importProcessingService.HandleValidationFailureAsync(emptyValidation, validation))
                        return (false, validation);
                    var (isValidInt, configuratorId, intValidation) = _importValidationService.ValidateCellInteger(configuratorRow[0]?.ToString(), DeksmartApplicationResource.ConfiguratorIdInvalid);
                    if (await _importProcessingService.HandleValidationFailureAsync(intValidation, validation, !isValidInt))
                        return (false, validation);
                    var existingConfigurator = await _importProcessingService.GetConfiguratorAsync(configuratorId);

                    // Parse configurator and map fields/products
                    var parseResult = _configuratorImportParser.ParseConfigurator(
                        configuratorId, existingConfigurator, configuratorRow, validation);
                    if (validation.HasErrors)
                        return (false, validation);

                    var configurator = parseResult.configurator;
                    var isUpdated = parseResult.isUpdated;
                    
                    // Capture original composite state before modification
                    var wasComposite = isUpdated && existingConfigurator != null && existingConfigurator.IsComposite;
                    configurator.IsComposite = false; // Explicit normal configurator type
                    expressionCache = InitializeExpressionCache(configurator);

                    _configuratorImportParser.MapFields(configurator, result, presentIds, expressionCache, validation);
                    if (validation.HasErrors)
                        return (false, validation);

                    _configuratorImportParser.MapProducts(configurator, result, presentIds, expressionCache, validation);
                    if (validation.HasErrors)
                        return (false, validation);

                    SetDeleteOnNotPresentItems(configurator, presentIds);

                    // Handle composite→normal conversion: cleanup child relationships
                    if (wasComposite)
                    {
                        _importProcessingService.CleanupCompositeConfiguratorData(configurator);
                    }

                    var expressionSyntaxValidation = _importValidationService.ValidateExpressionSyntax(configurator, expressionCache);
                    if (await _importProcessingService.HandleValidationFailureAsync(expressionSyntaxValidation, validation))
                        return (false, validation);

                    var expressionFieldsValidation = _importValidationService.ValidateExpressionFields(configurator);
                    if (await _importProcessingService.HandleValidationFailureAsync(expressionFieldsValidation, validation))
                        return (false, validation);

                    await _importProcessingService.SaveConfiguratorAsync(configurator, isUpdated);
                }
                await _importProcessingService.CommitTransactionAsync();
                return (true, validation);
            }
            catch (Exception ex)
            {
                validation.AddError(ex.Message);
                await _importProcessingService.RollbackTransactionAsync();
                return (false, validation);
            }
        }


        private static Dictionary<string, ConfiguratorExpression> InitializeExpressionCache(Configurator configurator)
        {
            var configuratorExpressions = new List<ConfiguratorExpression?>();

            var compositions = configurator.ConfiguratorCompositions.Where(d => !d.IsDeleted);
            configuratorExpressions.AddRange(compositions.Select(d => d.Visibility));

            var items = compositions.SelectMany(d => d.ConfiguratorProducts.Where(d => !d.IsDeleted));
            configuratorExpressions.AddRange(items.Select(d => d.Quantity));
            configuratorExpressions.AddRange(items.Select(d => d.Visibility));

            var categories = configurator.ConfiguratorFieldCategories.Where(d => !d.IsDeleted);
            configuratorExpressions.AddRange(categories.Select(d => d.Visibility));

            var fields = categories.SelectMany(d => d.ConfiguratorFields.Where(d => !d.IsDeleted));
            configuratorExpressions.AddRange(fields.Select(d => d.Visibility));
            configuratorExpressions.AddRange(fields.Select(d => d.Expression));

            // Make sure to include validation expressions
            var validations = fields.Select(d => d.Validation).Where(v => v != null);
            configuratorExpressions.AddRange(validations);

            var fieldValues = fields.SelectMany(d => d.ConfiguratorFieldValues.Where(d => !d.IsDeleted));
            configuratorExpressions.AddRange(fieldValues.Select(d => d.Visibility));

            var expressionCache = new Dictionary<string, ConfiguratorExpression>();

            foreach (var expression in configuratorExpressions
                .Where(d => d != null)
                .Cast<ConfiguratorExpression>()
                .DistinctBy(d => d.Id))
            {
                expressionCache[expression.Expression] = expression;
            }

            return expressionCache;
        }

        private void SetDeleteOnNotPresentItems(Configurator configurator, PresentIds presentIds)
        {
            _importProcessingService.SetDeleteOnNotPresentItems(configurator, presentIds);
        }
    }
}