using DEK.Eshop.ApiCore.Extension;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;

namespace DEK.Eshop.ApiCore.Loging;

public class ConsoleAdapter : IUncatchExceptionHandlerEvent, IHttpAccessEvent, IExceptionHandlerEvent
{

    private readonly IHttpContextAccessor httpContextAccessor;

    public ConsoleAdapter(IHttpContextAccessor httpContextAccessor)
    {
        this.httpContextAccessor = httpContextAccessor;
    }
    public void WriteUncatchException(object? sender, ExceptionHandlerEventArgs args)
    {
        var httpContext = this.httpContextAccessor.HttpContext;
        var exception = args.Exception;
        var path = httpContext?.Request.Path.ToString();
        var query = httpContext?.Request.QueryString.ToString();

        var payload = new Dictionary<string, string> {
            { "type", "exception" },
            { "time", DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss") },
            { "exception", exception.ToString() },
            { "path", path },
            { "query", query },
        };

        var json = payload.Dek_ToJson();

        json = json.Replace("\\u002B", "+")
            .Replace("\\u0026", "&")
            .Replace("\\u003E", ">")
            .Replace("\\u003C", "<")
            .Replace("\\u0022", "?");

       Console.Error.WriteLineAsync(json);
    }

    public void WriteHttpAccess(object? sender, HttpAccessHandlerEventArgs args)
    {
        var json = new Dictionary<string, string> {
            { "type", args.Type },
            { "method", args.Method },
            { "status", args.Status },
            { "path", args.Path },
            { "query", args.Query },
            { "time", args.Time },
            { "clientip", args.ClientIp },
            { "referrer", args.Referrer },
            { "agent", args.Agent }
        }
        .Dek_ToJson()
        .Replace("\\u002B", "+")
        .Replace("\\u0026", "&")
        .Replace("\\u003E", ">")
        .Replace("\\u003C", "<")
        .Replace("\\u0022", "?");

        Console.Out.WriteLineAsync(json);
    }

    public void WriteException(object? sender, ExceptionHandlerEventArgs args)
    {
        var httpContext = this.httpContextAccessor.HttpContext;
        var exception = args.Exception;
        var path = httpContext?.Request.Path.ToString();
        var query = httpContext?.Request.QueryString.ToString();

        var payload = new Dictionary<string, string> {
            { "type", "exception" },
            { "time", DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss") },
            { "exception", exception.ToString() },
            { "path", path },
            { "query", query },
        };

        var json = payload.Dek_ToJson();

        json = json.Replace("\\u002B", "+")
            .Replace("\\u0026", "&")
            .Replace("\\u003E", ">")
            .Replace("\\u003C", "<")
            .Replace("\\u0022", "?");

        Console.Error.WriteLineAsync(json);
    }
}
