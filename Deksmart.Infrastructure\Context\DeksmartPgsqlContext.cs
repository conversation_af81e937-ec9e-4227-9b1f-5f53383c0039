
using Deksmart.Domain.SavedConfiguration.Entity;
using DEK.Eshop.ApiCore.Extension;
using DEK.Eshop.ApiCore.Entity;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Context;

public class DeksmartPgsqlContext : DbContext
{
    public DbSet<CustomerState.Select> CustomerStateSelect { get; set; }

    public DbSet<CustomerState.Delete> CustomerStateDelete { get; set; }

    public DbSet<TotalCount> TotalCount { get; set; }

    public DeksmartPgsqlContext(DbContextOptions<DeksmartPgsqlContext> options) : base(options)
    {
    }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        //modelBuilder.Entity<ArticleNews>(entity =>
        //{
        //    entity.Property(e => e.Data)
        //        .HasColumnType("varchar")
        //        .HasConversion(
        //            v => v!.ToString(),
        //            v => v.ToString().Dek_ToObject<ArticleNewsData>()!
        //        );
        //});

        //modelBuilder.Entity<ArticleContent>(entity => {
        //    entity.HasNoKey();
        //    entity.Property(e => e.Data)
        //        .HasColumnType("varchar")
        //        .HasConversion(
        //            v => v!.ToString(),
        //            v => v!.ToString().Dek_ToObject<ArticleContentData>()!
        //        );
        //});
    }

}
