namespace DEK.Eshop.ApiCore.Config.Dto;

public class Logger
{
    public IEnumerable<string> DefaultMailTo { get; init; } = new[] { "<EMAIL>" };

    public bool MailUncatchExceptionEnabled { get; set; } = true;

    public IEnumerable<string> MailUncatchExceptionTo { get; init; } = new[] { "<EMAIL>" };

    public int MailUncatchExceptionMinutesInterval { get; set; } = 30;

    public IEnumerable<string> MailExceptionTo { get; init; } = new[] { "<EMAIL>" };

    public IEnumerable<string> MailStdLogTo { get; init; } = new[] { "<EMAIL>" };

    public string MailSubjectAffix {  get; init; } = string.Empty;
}
