using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Mail;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using StackExchange.Profiling.Internal;
using System.Text;

namespace DEK.Eshop.ApiCore.Loging;

public class MailAdapter : IUncatchExceptionHandlerEvent, IExceptionHandlerEvent
{
    private readonly IHostEnvironment env;
    private readonly IConfiguration config;
    private readonly IHttpContextAccessor httpContextAccessor;
    private readonly MailManager mailManager;
    private DateTime _lastMailSent = DateTime.MinValue;

    public MailAdapter(IHostEnvironment env, IConfiguration config, IHttpContextAccessor httpContextAccessor, MailManager mailManager)
    {
        this.env = env;
        this.config = config;
        this.httpContextAccessor = httpContextAccessor;
        this.mailManager = mailManager;
    }

    public async void WriteUncatchException(object? sender, ExceptionHandlerEventArgs args)
    {
        var configLogger = ConfigFactory.Create<Config.Dto.Logger>(this.config, "ApiCore:Logger");
        if (configLogger.MailUncatchExceptionEnabled == false) {
            return;
        }

        // check if mail was sent in last x minutes
        var minutesInterval = DateTime.Now.Subtract(this._lastMailSent);
        if (minutesInterval.TotalMinutes < configLogger.MailUncatchExceptionMinutesInterval) {
            return;
        }

        var httpContext = this.httpContextAccessor.HttpContext;
        var exception = args.Exception;

        // create mail subject
        var builder = new MailBuilder();
        builder.SetSubject($"Uncatch exception: {this.env.ApplicationName}, {this.env.EnvironmentName} {configLogger.MailSubjectAffix}");

        // override default mailTo if set in config
        var mailTo = configLogger.MailUncatchExceptionTo.Any() ? configLogger.MailUncatchExceptionTo : configLogger.DefaultMailTo;
        foreach (var to in mailTo) {
            builder.AddTo(to);
        }

        builder.SetTextBody(await this.CreateMailBody(exception, httpContext));

        await this.mailManager.SendMail(builder);
        this._lastMailSent = DateTime.Now;
    }

    public async void WriteException(object? sender, ExceptionHandlerEventArgs args)
    {
        var configLogger = ConfigFactory.Create<Config.Dto.Logger>(this.config, "ApiCore:Logger");
        if (configLogger.MailUncatchExceptionEnabled == false) {
            return;
        }

        // check if mail was sent in last x minutes
        var minutesInterval = DateTime.Now.Subtract(this._lastMailSent);
        if (minutesInterval.TotalMinutes < configLogger.MailUncatchExceptionMinutesInterval) {
            return;
        }

        var httpContext = this.httpContextAccessor.HttpContext;
        var exception = args.Exception;

        // create mail subject
        var builder = new MailBuilder();
        builder.SetSubject($"Exception: {this.env.ApplicationName}, {this.env.EnvironmentName} {configLogger.MailSubjectAffix}");

        // override default mailTo if set
        var mailTo = configLogger.MailExceptionTo.Any() ? configLogger.MailExceptionTo : configLogger.DefaultMailTo;
        foreach (var to in mailTo) {
            builder.AddTo(to);
        }

        builder.SetTextBody(await this.CreateMailBody(exception, httpContext));

        await this.mailManager.SendMail(builder);
        this._lastMailSent = DateTime.Now;
    }

    private async Task<string> CreateMailBody(Exception exception, HttpContext? httpContext)
    {
        string? stringBody = null;
        var path = httpContext?.Request.Path.ToString();
        var query = httpContext?.Request.QueryString.ToString();
        var token = httpContext?.Request.Headers.Authorization.ToString();

        if (httpContext is not null) {
            try {
                httpContext.Request.Body.Seek(0, SeekOrigin.Begin);
                using var reader = new StreamReader(httpContext.Request.Body, leaveOpen: true);
                stringBody = await reader.ReadToEndAsync();
            } catch (Exception e) {
                stringBody = $"Error reading request body: {e.Message}";
            }
        }

        var sb = new StringBuilder();
        sb.AppendLine($"Time: {DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss")}");

        if (!query.IsNullOrWhiteSpace()) {
            sb.AppendLine($"--------------------------------------------");
            sb.AppendLine($"Query: {query}");
        }

        if (!path.IsNullOrWhiteSpace()) {
            sb.AppendLine($"--------------------------------------------");
            sb.AppendLine($"Path: {path}");
        }

        if (!token.IsNullOrWhiteSpace()) {
            sb.AppendLine($"--------------------------------------------");
            sb.AppendLine($"Token: {token}");
        }

        sb.AppendLine($"--------------------------------------------");
        sb.AppendLine($"Exception:");
        sb.AppendLine($"----------");
        sb.AppendLine(exception.ToString());

        if (!stringBody.IsNullOrWhiteSpace()) {
            sb.AppendLine($"--------------------------------------------");
            sb.AppendLine($"Body:");
            sb.AppendLine($"-----");
            sb.AppendLine(stringBody);
        }

        return sb.ToString();
    }
}
