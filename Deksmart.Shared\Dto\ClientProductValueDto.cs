namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing the selection and quantity of a specific product within a configurator.
    /// Used to transfer user-selected product choices and their amounts between client, API, and persistence layers.
    /// Supports both UI workflows and preset persistence for product selections, enabling restoration and validation of user product choices.
    /// </summary>
    public class ClientProductValueDto
    {
        public int ProductId { get; set; }
        public decimal? Amount { get; set; }
    }
}