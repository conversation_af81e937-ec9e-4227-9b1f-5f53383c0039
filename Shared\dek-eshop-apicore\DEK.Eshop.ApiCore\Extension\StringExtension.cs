using System.Text.Json;

namespace DEK.Eshop.ApiCore.Extension;

public static class StringExtension
{
    [Obsolete("Use Dek_ToObject", true)]
    public static T? ToObjectDek<T>(this string source)
    {
        return source.Dek_ToObject<T>();
    }

    /// <summary>
    /// Deserialize string to object. NamingPolicy is camelCase
    /// </summary>
    /// <inheritdoc cref="JsonSerializer.Deserialize" />
    public static T? Dek_ToObject<T>(this string source)
    {
        var serializeOptions = new JsonSerializerOptions {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        };
        return JsonSerializer.Deserialize<T>(source, serializeOptions);
    }

    public static string Dek_Capitalize(this string source)
    {
        switch (source) {
            case null:
                throw new ArgumentNullException(nameof(source));
            case "":
                return source;
            default:
                var value = source.ToLower();
                return char.ToUpper(value.First()) + value.Substring(1);
        }
    }

    public static bool Dek_IsNullOrEmpty(this string? value)
    {
        return string.IsNullOrEmpty(value);
    }

}
