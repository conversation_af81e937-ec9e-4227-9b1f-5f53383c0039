appsettings.json

```json
 {
    "Mssql": {
        "User": "ESHOP",
        "Password": "ES2007",
        "Database": "TEST_ESHOP", // Na vývoji se mění podle claimu: "instance" v JWT
        "Host": "apps-e.dek.cz\\ESHOP2"
    },
    "Pgsql": {
        "Default": "dek_cz", // Nastavení pro produkci. Na vývoji se mění podle claimu: "eshopId" v JWT
        "dek_cz": {
            "Host": "postgresqltest.dek.cz",
            "Port": 5432,
            "Database": "eshop_dek_cz",
            "Username": "eshop_dek_cz",
            "Password": "...",
            "Pooling": true
        },
        "dek_sk": {
            ...
        },
        "argos_cz": {
            ...
        }

    },
}
```