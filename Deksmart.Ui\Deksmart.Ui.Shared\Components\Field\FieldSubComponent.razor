﻿@using Deksmart.Ui.Model
@using Deksmart.Ui.Shared.Components
@using Deksmart.Ui.Shared.Components.Utility
@using Deksmart.Ui.Shared.Resources

<div class="filter-component">
    @switch (Field)
    {
        case SliderFieldWrapper sliderField:
            <input id="@sliderField.Ident"
                   @onfocus="OnFocused"
                   @onblur="OnBlur"
                   type="range"
                   value="@(Convert.ToInt32(sliderField.Value))"
                   step="1"
                   class="@(sliderField.HasValidationError && sliderField.IsDirty ? "has-validation-error" : "")"
                   @oninput="@(e => OnInputSlider(Convert.ToDecimal(Convert.ToInt32(e.Value)), sliderField))"
                   @onchange="@(async e => await OnNumericInputChanged(Convert.ToDecimal(Convert.ToInt32(e.Value)), sliderField))"
                   min="@(Convert.ToInt32(sliderField.Min))"
                   max="@(Convert.ToInt32(sliderField.Max))" />

            <label class="field-suffix">@Convert.ToInt32(sliderField.Value)</label>
            break;
        case NumericFieldWrapper numericField:
            <TrimInputNumber Value="@numericField.Value"
                            ValueChanged="@((decimal value) => OnNumericInputChanged(value, numericField))"
                            Min="@numericField.Min"
                            Max="@numericField.Max"
                            Class="@(numericField.HasValidationError && numericField.IsDirty ? "has-validation-error" : "")" />
            break;
        case CheckboxFieldWrapper checkboxField:
            <div class="checkbox-container">
                <InputCheckbox id="@checkboxField.Ident"
                              @onfocus="OnFocused"
                              @onblur="OnBlur"
                              Value="checkboxField.Value"
                              class="@(checkboxField.HasValidationError && checkboxField.IsDirty ? "has-validation-error" : "")"
                              ValueChanged="@((bool value) => OnCheckBoxValueChanged(value, checkboxField))"
                              ValueExpression="@( () => checkboxField.Value )" />
                <label for="@checkboxField.Ident">@((MarkupString)checkboxField.Title)</label>
            </div>
            break;
        case SelectBoxFieldWrapper selectBoxField:
            <InputSelect id="@selectBoxField.Ident"
                        class="@(selectBoxField.HasValidationError && selectBoxField.IsDirty ? "has-validation-error" : "")"
                        @key="GetUniqueKey(selectBoxField)"
                        Value="selectBoxField.Value"
                        @onfocus="OnFocused"
                        @onblur="OnBlur"
                        ValueChanged="@((int? id) => OnSelectBoxValueChanged(id, selectBoxField))"
                        ValueExpression="@( () => selectBoxField.Value )">
                @if (selectBoxField.Value == null)
                {
                    <option value="">@UiSharedResource.SelectOption</option>
                }
                @foreach (var value in selectBoxField.FieldValues)
                {
                    <option value="@value.Id">@((MarkupString)value.Title)</option>
                }
            </InputSelect>
            break;
        case ExpressionFieldWrapper expressionField:
            <div id="@expressionField.Ident" class="@(expressionField.HasValidationError ? "has-validation-error" : "")">
                <label>@expressionField.Text</label>
            </div>
            break;
    }
    @if (!string.IsNullOrEmpty(Field.Suffix))
    {
        <label class="field-suffix"> @((MarkupString)Field.Suffix)</label>
    }
</div>
