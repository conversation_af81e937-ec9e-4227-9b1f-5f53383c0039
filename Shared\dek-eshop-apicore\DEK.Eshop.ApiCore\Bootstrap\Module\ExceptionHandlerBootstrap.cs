using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using DEK.Eshop.ApiCore.Indentity;
using DEK.Eshop.ApiCore.Validation;
using DEK.Eshop.ApiCore.Extension;
using System.Net.Http;

namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class ExceptionHandlerBootstrap
{
    public static void UseExceptionHandlerBootstrap(this WebApplication app)
    {
        app.UseExceptionHandler(applicationBuilder => applicationBuilder.Run(async httpContext => {
            var looger = applicationBuilder.ApplicationServices.GetService<Loging.Logger>();

            var exception = httpContext.Features.Get<IExceptionHandlerPathFeature>()?.Error;
            if (exception is null) {
                return;
            }

            // AggregateException E.g.: if controller action is not async and throws exception(s).
            if (exception is AggregateException) {
                var agg = (AggregateException)exception;
                foreach (var e in agg.InnerExceptions) {
                    if (e is ForbidenException) {
                        RespondWithCode(exception, httpContext, 403);
                        return;
                    }
                    if (e is UserException) {
                        RespondWithCode(exception, httpContext, 401);
                        return;
                    }
                }
            }

            // Return HTTP 403. E.g.: user has valid token but is not logged in (guest).
            if (exception is ForbidenException) {
                RespondWithCode(exception, httpContext, 403);
                return;
            }

            // Returns HTTP 401 during the creation of the user. E.g.: missing mandatory fields.
            if (exception is UserException) {
                RespondWithCode(exception, httpContext, 401);
                return;
            }

            // return http 500 response
            var isProduction = app.Environment.EnvironmentName == "Production";
            var payload = isProduction
                ? "{\"status\": \"error\", \"message\": \"Internal server errror.\"}"
                : exception.ToString(); //exception.Message;

            httpContext.Response.StatusCode = 500;
            httpContext.Response.ContentType = isProduction ? "application/json" : "text/plain";
            await httpContext.Response.WriteAsync(payload);

            // log exception
            if (exception is not null) {
                looger?.DispatchUncatchExceptionEvent(exception);
            }
        }));
    }

    public static async void RespondWithCode(Exception exception, HttpContext httpContext, int code)
    {
        httpContext.Response.StatusCode = code;
        httpContext.Response.ContentType = "application/json";
        var error = new Error { Message = exception.Message };
        var list = new List<Error> { error };
        await httpContext.Response.WriteAsync(list.Dek_ToJson());
    }
}
