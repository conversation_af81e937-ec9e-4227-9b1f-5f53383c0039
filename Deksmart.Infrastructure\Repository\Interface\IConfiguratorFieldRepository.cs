﻿using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for accessing and managing <see cref="ConfiguratorField"/> entities, including retrieval of direct and multiple choice field values for a configurator.
    /// </summary>
    public interface IConfiguratorFieldRepository : IIntIdRepositoryBase<ConfiguratorField>
    {
        /// <summary>
        /// Retrieves all direct value fields (fields without selectable values) for a given configurator.
        /// </summary>
        /// <param name="configuratorId">The unique identifier of the configurator.</param>
        /// <returns>A list of <see cref="DirectValue"/> objects representing direct value fields.</returns>
        Task<List<DirectValue>> GetFieldDirectValuesForConfiguratorAsync(int configuratorId);

        /// <summary>
        /// Retrieves all multiple choice value fields (fields with selectable values) for a given configurator.
        /// </summary>
        /// <param name="configuratorId">The unique identifier of the configurator.</param>
        /// <returns>A list of <see cref="MultipleChoiceValue"/> objects representing multiple choice fields.</returns>
        Task<List<MultipleChoiceValue>> GetFieldMultipleChoiceValuesIdForConfiguratorAsync(int configuratorId);
    }
}
