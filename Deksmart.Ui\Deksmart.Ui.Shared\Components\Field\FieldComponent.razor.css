﻿.tile-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 0.5rem;
}

.tile {
    flex: 1 1 calc(33.333% - 12px);
    min-width: 100px;
    max-width: 200px;
    box-sizing: border-box;
    padding: 12px;
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: white;
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.tile:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.tile.selected-tile {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(192, 12, 24, 0.05);
}

.tile.selected-tile::after {
    content: "✓";
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.tile-image {
    width: 100%;
    height: auto;
    display: block;
    margin-bottom: 10px;
    border-radius: var(--border-radius-sm);
}

.filter-row {
    display: grid;
    grid-template-columns: minmax(120px, 1fr) auto auto;
    align-items: center;
    gap: 12px;
    padding: 0;
    width: 100%;
    background-color: white;
    margin-bottom: 0.1rem;
    min-height: 34px; /* Match the height of the selectbox */
}

.filter-name {
    text-align: left;
    justify-self: start;
    display: flex;
    align-items: center;
    min-height: 34px; /* Match the height of the selectbox */
}

.filter-name label {
    font-weight: 500;
    color: var(--secondary-color);
    margin: 0;
    display: inline-block;
    font-size: 1rem;
    white-space: nowrap;
    line-height: 34px; /* Match the height of the selectbox */
}

.filter-info {
    justify-self: end;
}

.validation-error {
    color: var(--danger-color);
    font-size: 1rem;
    margin-top: 0;
    margin-bottom: 0.25rem;
    padding: 3px 10px;
    border-left: 3px solid var(--danger-color);
    background-color: rgba(192, 12, 24, 0.05);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
    animation: fadeIn 0.3s ease-in-out;
    width: 100%;
}

.field-with-error .filter-row {
    border-color: var(--danger-color);
}

.field-with-error .filter-name label {
    color: var(--danger-color);
}

.field-with-error input,
.field-with-error select {
    border-color: var(--danger-color);
    background-color: rgba(192, 12, 24, 0.05);
}

.checkbox-field {
    grid-template-columns: 1fr auto;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.single-choice-filter-item {
    display: block;
    width: 100%;
}

/* Radio button styles */
::deep input[type="radio"] {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    cursor: pointer;
    accent-color: #6c757d;
}

::deep input[type="radio"]:checked {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Tooltip container */
.info-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: var(--light-color);
    color: var(--secondary-color);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s ease-in-out;
    z-index: 2;
    box-shadow: var(--shadow-sm);
}

.info-button:hover {
    background-color: var(--secondary-color);
    color: white;
}

.tooltip {
    position: fixed;
    display: none;
    width: max-content;
    max-width: 300px;
    min-width: 200px;
    background-color: var(--secondary-color);
    color: white;
    text-align: left;
    padding: 10px 14px;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-md);
    z-index: 99999;
    transform: translateX(-50%);
    pointer-events: none;
    white-space: normal;
    word-wrap: break-word;
    opacity: 1;
    visibility: visible;
    font-size: 0.9rem;
    line-height: 1.4;
}

.tooltip::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent var(--secondary-color) transparent;
}

/* New styles for field container */
.field-container {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 100%;
}

.field-container > * {
    flex: 0 0 auto;
}