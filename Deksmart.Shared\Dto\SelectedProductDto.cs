﻿namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a product selected by the user in the configurator, including its code, selected amount, and unit.
    /// Used to transfer selected product data between client, API, and business logic for price calculation, state management, and workflow continuity.
    /// Serves as the bridge between user selection and backend processing.
    /// </summary>
    public class SelectedProductDto
    {
        public string ProductCode { get; set; } = null!;
        public decimal Amount { get; set; }
        public string Unit { get; set; } = null!;
    }
}
    