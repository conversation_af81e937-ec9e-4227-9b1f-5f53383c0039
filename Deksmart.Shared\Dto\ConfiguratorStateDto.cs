namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing the full state of a configurator instance, including user-entered field values, selected products, category UI states, and child configurator states.
    /// Used to transfer, persist, and restore the user's configuration progress between client, API, and backend.
    /// Supports workflow continuity, preset saving/loading, and multi-level configuration scenarios by encapsulating all relevant state data for a configurator session.
    /// </summary>
    public class ConfiguratorStateDto
    {
        public ConfiguratorStateDto(){}

        public ConfiguratorStateDto(int id, List<ClientFieldValueDto> fieldValues, List<ClientProductValueDto> selectedProducts)
        {
            ConfiguratorId = id;
            FieldValues = fieldValues;
            SelectedProducts = selectedProducts;
        }

        public int ConfiguratorId { get; set; }
        public List<ClientFieldValueDto> FieldValues { get; set; } = new();
        public List<ClientProductValueDto> SelectedProducts { get; set; } = new();
        public List<CategoryStateDto> CategoryStates { get; set; } = new();
        public List<ChildConfiguratorStateDto> ChildConfiguratorStates { get; set; } = new();
        public string? TabTitle { get; set; }
        public int? TabOrder { get; set; }
    }

    public class EmailConfiguratorStateDto : ConfiguratorStateDto
    {
        public EmailConfiguratorStateDto() : base() { }

        public EmailConfiguratorStateDto(int id, List<ClientFieldValueDto> fieldValues, List<ClientProductValueDto> selectedProducts)
            : base(id, fieldValues, selectedProducts) { }

        public ContactInfoDto? ContactInfo { get; set; }
    }

    public class ContactInfoDto
    {
        public string? Email { get; set; }
        public string? Name { get; set; }
        public string? Phone { get; set; }
    }

    public class ChildConfiguratorStateDto : ConfiguratorStateDto
    {
        public ChildConfiguratorStateDto(){}
        public ChildConfiguratorStateDto(int id, List<ClientFieldValueDto> fieldValues, List<ClientProductValueDto> selectedProducts, int? tabOrder, string? tabTitle)
            : base(id, fieldValues, selectedProducts)
        {
            TabOrder = tabOrder;
            TabTitle = tabTitle;
        }
    }

    public class CompositeConfiguratorStateDto
    {
        public CompositeConfiguratorStateDto() { }

        public CompositeConfiguratorStateDto(int mainConfiguratorId)
        {
            MainConfiguratorId = mainConfiguratorId;
        }

        /// <summary>
        /// The ID of the main composite configurator
        /// </summary>
        public int MainConfiguratorId { get; set; }

        /// <summary>
        /// The active child configurator state that has changes and needs processing.
        /// This child will be processed through the normal configurator processing route.
        /// </summary>
        public ConfiguratorStateDto? ActiveChildState { get; set; }

        /// <summary>
        /// Product states from other (non-active) child configurators for aggregation.
        /// These children have already been processed and just need their products included.
        /// </summary>
        public List<ChildProductStateDto>? OtherChildProducts { get; set; }
    }

    public class CompositeConfiguratorResponseDto
    {
        /// <summary>
        /// The processed active child configurator with updated calculations and validation.
        /// Used to update the UI state of the specific child tab that changed.
        /// </summary>
        public ConfiguratorDto? ProcessedActiveChild { get; set; }

        /// <summary>
        /// The main composite configurator with aggregated products summary from all children.
        /// Contains the complete product summary for display in the overview.
        /// </summary>
        public ConfiguratorDto? CompositeConfiguratorSummary { get; set; }
    }
}