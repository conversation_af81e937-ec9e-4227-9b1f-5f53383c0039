Write-Host "Starting Firefox debugging setup..." -ForegroundColor Green

# Kill any existing dotnet processes
Write-Host "Stopping any existing dotnet processes..." -ForegroundColor Yellow
Get-Process -Name dotnet -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Clean the Web project
Write-Host "Cleaning Web project..." -ForegroundColor Yellow
Push-Location .\Deksmart.Ui\Deksmart.Ui.Web
Remove-Item -Path "bin" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "obj" -Recurse -Force -ErrorAction SilentlyContinue
Pop-Location

# Build the API project
Write-Host "Building API project..." -ForegroundColor Yellow
dotnet build .\Deksmart.Api\Deksmart.Api.csproj

# Build the Web project
Write-Host "Building Web project..." -ForegroundColor Yellow
dotnet build .\Deksmart.Ui\Deksmart.Ui.Web\Deksmart.Ui.Web.csproj

# Create the runtimeconfig.json file
Write-Host "Creating runtimeconfig.json file..." -ForegroundColor Yellow
$runtimeConfigContent = @"
{
  "runtimeOptions": {
    "tfm": "net9.0",
    "framework": {
      "name": "Microsoft.AspNetCore.App",
      "version": "9.0.0"
    },
    "configProperties": {
      "System.GC.Server": true,
      "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false
    }
  }
}
"@

$runtimeConfigPath = ".\Deksmart.Ui\Deksmart.Ui.Web\bin\Debug\net9.0\Deksmart.Ui.Web.runtimeconfig.json"
New-Item -Path $runtimeConfigPath -Force -Value $runtimeConfigContent | Out-Null

# Start the API in the background
Write-Host "Starting API..." -ForegroundColor Green
$apiProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--project", ".\Deksmart.Api\Deksmart.Api.csproj", "--urls=https://localhost:7276;http://localhost:5144" -PassThru -NoNewWindow

# Start the Web application in the background
Write-Host "Starting Web application..." -ForegroundColor Green
$webProcess = Start-Process -FilePath "dotnet" -ArgumentList "run", "--project", ".\Deksmart.Ui\Deksmart.Ui.Web\Deksmart.Ui.Web.csproj", "--urls=https://localhost:7209;http://localhost:5213" -PassThru -NoNewWindow

# Wait for the applications to start
Write-Host "Waiting for applications to start..." -ForegroundColor Yellow
$maxWaitTime = 30 # seconds
$startTime = Get-Date
$apiStarted = $false
$webStarted = $false

while (((Get-Date) - $startTime).TotalSeconds -lt $maxWaitTime) {
    # Check if API is ready
    try {
        $apiResponse = Invoke-WebRequest -Uri "https://localhost:7276/deksmart/doc" -Method Head -TimeoutSec 1 -SkipCertificateCheck -ErrorAction SilentlyContinue
        if ($apiResponse.StatusCode -eq 200) {
            $apiStarted = $true
            Write-Host "API is ready!" -ForegroundColor Green
        }
    } catch {
        # API not ready yet
    }

    # Check if Web is ready
    try {
        $webResponse = Invoke-WebRequest -Uri "https://localhost:7209" -Method Head -TimeoutSec 1 -SkipCertificateCheck -ErrorAction SilentlyContinue
        if ($webResponse.StatusCode -eq 200) {
            $webStarted = $true
            Write-Host "Web application is ready!" -ForegroundColor Green
        }
    } catch {
        # Web not ready yet
    }

    if ($apiStarted -and $webStarted) {
        break
    }

    Start-Sleep -Seconds 1
}

if (-not ($apiStarted -and $webStarted)) {
    Write-Host "Timeout waiting for applications to start. Starting Firefox anyway..." -ForegroundColor Yellow
}

# Open Firefox
Write-Host "Opening Firefox..." -ForegroundColor Green
Start-Process "firefox" -ArgumentList "https://localhost:7209"

Write-Host "Press any key to stop the applications..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Stop the processes
Write-Host "Stopping applications..." -ForegroundColor Yellow
Stop-Process -Id $apiProcess.Id -Force -ErrorAction SilentlyContinue
Stop-Process -Id $webProcess.Id -Force -ErrorAction SilentlyContinue

Write-Host "Done!" -ForegroundColor Green
