namespace Deksmart.Domain.Entity.Business.Eshop
{
    /// <summary>
    /// Represents the result of unit conversion for a product, containing the converted amount in the target unit.
    /// The API returns these objects with quantityOutput property.
    /// </summary>
    public class EshopProductUnitConversion
    {
        public decimal QuantityOutput { get; set; }
        public string? ProductCode { get; set; }
    }
}