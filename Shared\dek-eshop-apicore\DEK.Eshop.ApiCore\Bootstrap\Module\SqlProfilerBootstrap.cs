using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Config.Dto;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DEK.Eshop.ApiCore.Bootstrap.Module;

public static class SqlProfilerBootstrap
{
    /// <exception cref="ConfigException"></exception>
    public static void AddSqlProfilerBootstrap(this IServiceCollection services, IConfiguration configuration)
    {
        var routeConfig = ConfigFactory.Create<Route>(configuration, "ApiCore:Route");
        var sqpProfilerConfig = ConfigFactory.Create<SqlProfiler>(configuration, "ApiCore:SqlProfiler");

        if (!sqpProfilerConfig.Enabled) {
            return;
        }

        services.AddMiniProfiler(options => {
            options.RouteBasePath = "/" + routeConfig.BasePath + "/profiler";
            options.SqlFormatter = new StackExchange.Profiling.SqlFormatters.InlineFormatter();
        }).AddEntityFramework();
    }

    /// <exception cref="ConfigException"></exception>
    public static void UseSqlProfilerBootstrap(this WebApplication app)
    {
        var sqpProfilerConfig = ConfigFactory.Create<SqlProfiler>(app.Configuration, "ApiCore:SqlProfiler");

        if (!sqpProfilerConfig.Enabled) {
            return;
        }

        app.UseMiniProfiler();
    }
}
