using Microsoft.AspNetCore.Mvc;
using System.Text.RegularExpressions;
using System.Text.Json.Serialization;

namespace DEK.Eshop.ApiCore.Validation;

/// <summary>
/// On invalid model state, returns a list of errors.
/// </summary>
[Obsolete]
public class ValidationPayload
{
    [JsonPropertyName("errors")]
    public IList<ValidationMessage> ErrorList { get; set; } = new List<ValidationMessage>();

    public string? TraceId { get; } = null;

    public ValidationPayload(ActionContext context, Dictionary<string, ValidationMessage> errorsDictionary)
    {
        TraceId = context.HttpContext.TraceIdentifier;
        FillErrorList(context, errorsDictionary);
    }

    public ValidationPayload(IList<ValidationMessage> errorList, string traceId)
    {
        ErrorList = errorList;
        TraceId = traceId;
    }

    public ValidationPayload(ValidationMessage error, string traceId)
    {
        ErrorList = new List<ValidationMessage> { error };
        TraceId = traceId;
    }

    
    public ValidationPayload(ValidationMessage error)
    {
        ErrorList = new List<ValidationMessage> { error };
    }

    //Deselealization constructor
    [JsonConstructor]
    public ValidationPayload()
    {
    }

    private void FillErrorList(ActionContext context, Dictionary<string, ValidationMessage> errorDictionary)
    {
        foreach (var keyModelStatePair in context.ModelState)
        {
            string path = keyModelStatePair.Key;
            path = !path.StartsWith("$.") ? "$." + path : path; // add $. if missing (invalid state on multipart/form-data returns path without $.)
            path = "$." + Char.ToLowerInvariant(path[2]) + path.Substring(3); // invalid state on multipart/form-data returns PascalCase

            var modelErrors = keyModelStatePair.Value.Errors;

            foreach (var modelError in modelErrors)
            {
                string errorKey = modelError.ErrorMessage;

                if (errorDictionary.ContainsKey(errorKey)) {
                    var errorMessage = errorDictionary[errorKey] with { Path = path };
                    ErrorList.Add(errorMessage);
                } else {
                    ErrorList.Add(new ValidationMessage("0", errorKey, path));
                }
            }
        }
    }

    private string? GetIndex(string path)
    {
        string pattern = @"(?<=\[)\d+(?=\])";
        Match match = Regex.Match(path, pattern);
        if (match.Success)
            return match.Value;

        return null;
        // pattern je skoro to samé jako \[(\d+)\]. Je použita Zero Width Assertion(positive lookahead and negative lookbehind).
        // Jde o to, aby hranaté závorky nebyly v "match stringu" a nebylo potřeba si explicitně vyžádat match v závorkách.
        // Více: https://developpaper.com/detailed-explanation-of-zero-width-assertion-of-regular-expressions/
        // Příklad: http://regexstorm.net/tester?p=%28%3f%3c%3d%5c%5b%29%5cd%2b%28%3f%3d%5c%5d%29&i=ProductEshopList%5b5%5d.Code&o=ixncs
        // a http://regexstorm.net/tester?p=%5c%5b%28%5cd%2b%29%5c%5d&i=ProductEshopList%5b5%5d.Code&o=ixncs
    }
}
