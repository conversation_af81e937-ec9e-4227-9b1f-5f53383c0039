﻿using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for accessing and managing <see cref="ConfiguratorComposition"/> entities, including retrieval of compositions for a specific configurator.
    /// </summary>
    public interface IConfiguratorCompositionRepository : IIntIdRepositoryBase<ConfiguratorComposition>
    {
        /// <summary>
        /// Retrieves all compositions for a given configurator, including related products, visibility, and quantities.
        /// </summary>
        /// <param name="configuratorId">The unique identifier of the configurator.</param>
        /// <returns>A list of <see cref="ConfiguratorComposition"/> entities with related data.</returns>
        Task<List<ConfiguratorComposition>> GetCompositionsForConfiguratorAsync(int configuratorId);
    }
}
