﻿using Deksmart.Domain.Entity.Db.Interface;

namespace Deksmart.Infrastructure.Repository.Interface.Base
{
    /// <summary>
    /// Generic repository interface for entities with a typed identifier, providing basic CRUD operations.
    /// </summary>
    /// <typeparam name="TEntity">The entity type.</typeparam>
    /// <typeparam name="TId">The type of the entity identifier.</typeparam>
    public interface IIdRepositoryBase<TEntity, TId> : IRepositoryBase
        where TEntity : class, IIdEntity<TId>
        where TId : struct
    {
        /// <summary>
        /// Retrieves an entity by its identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the entity.</param>
        /// <returns>The entity if found; otherwise, null.</returns>
        Task<IIdEntity<TId>?> GetEntityByIdAsync(TId id);

        /// <summary>
        /// Inserts a new entity into the database.
        /// </summary>
        /// <param name="entity">The entity to insert.</param>
        Task InsertAsync(TEntity entity);

        /// <summary>
        /// Marks the specified entity as updated in the context.
        /// </summary>
        /// <param name="entity">The entity to update.</param>
        void SetUpdated(TEntity entity);
    }
}
