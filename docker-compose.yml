version: '3.8'

services:
    api:
        build: 
            context: .
            dockerfile: Deksmart.Api/Dockerfile
        environment:
            - ApiCore__Cache__Redis__Host=redis:6379
            - ApiCore__Cache__Redis__ReplaceRedisWithDevNullAdapter=false
        ports:
            - "5001:8080"
        networks:
            - deksmart

    web:
        build:
            context: .
            dockerfile: Deksmart.Ui/Deksmart.Ui.Web/Dockerfile
        environment:
            - ASPNETCORE_ENVIRONMENT=LocalhostDocker
        ports:
            - "5000:8080"
        networks:
            - deksmart

    redis:
        image: docker-registry.dek.cz/redis:6.2
        ports:
            - "127.0.0.1:6379:6379" # allow access from host
        networks:
            - deksmart

    playwright:
        image: mcr.microsoft.com/playwright:v1.52.0-noble
        command: >
          /bin/sh -c "npx -y playwright@1.52.0 run-server --port 8080 --host 0.0.0.0"
        ports:
          - "127.0.0.1:5002:8080"
        networks:
          - deksmart
        working_dir: /home/<USER>
        user: pwuser
        init: true

networks:
    deksmart:
        driver: bridge
