using Deksmart.Ui.Model;
using Deksmart.Ui.Shared.Services;
using Deksmart.Ui.Shared.Resources;
using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class MetaDescriptionServiceTest
    {
        private readonly MetaDescriptionService _service;
        private const int MaxLength = 140;

        public MetaDescriptionServiceTest()
        {
            _service = new MetaDescriptionService();
        }

        [Fact]
        public void GetMetaDescription_NullConfigurator_ReturnsMainPageDescription()
        {
            var result = _service.GetMetaDescription(null);

            Assert.Equal(UiSharedResource.MetaDescription_MainPage, result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithMetaDescription_ReturnsConfiguratorMetaDescription()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = "Custom meta description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            Assert.Equal("Custom meta description", result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithLongMetaDescription_TruncatesAtMaxLength()
        {
            var longDescription = new string('a', MaxLength + 50);
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = longDescription,
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            Assert.Equal(MaxLength, result.Length);
            Assert.Equal(longDescription.Substring(0, MaxLength), result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithEmptyMetaDescription_UsesTemplate()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = "",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            var expectedTemplate = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, "Test Configurator");
            Assert.Equal(expectedTemplate, result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithWhitespaceMetaDescription_UsesTemplate()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = "   ",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            var expectedTemplate = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, "Test Configurator");
            Assert.Equal(expectedTemplate, result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithNullMetaDescription_UsesTemplate()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = null,
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            var expectedTemplate = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, "Test Configurator");
            Assert.Equal(expectedTemplate, result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithLongTitleTemplate_UsesTitleFallback()
        {
            var longTitle = new string('T', MaxLength - 10);
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = longTitle,
                Description = "Test Description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            // This will trigger the template being too long and falling back to title
            var template = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, longTitle);
            
            var result = _service.GetMetaDescription(configurator);

            // If template is too long, should fall back to title (potentially truncated)
            if (template.Length > MaxLength)
            {
                var expectedFallback = longTitle.Length > MaxLength ? longTitle.Substring(0, MaxLength) : longTitle;
                Assert.Equal(expectedFallback, result);
            }
            else
            {
                Assert.Equal(template, result);
            }
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithVeryLongTitle_TruncatesTitleFallback()
        {
            var veryLongTitle = new string('T', MaxLength + 20);
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = veryLongTitle,
                Description = "Test Description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            // Should fall back to truncated title
            Assert.Equal(MaxLength, result.Length);
            Assert.Equal(veryLongTitle.Substring(0, MaxLength), result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithNullTitle_UsesEmptyTitle()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = null,
                Description = "Test Description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            var expectedTemplate = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, string.Empty);
            Assert.Equal(expectedTemplate, result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithExactMaxLengthMetaDescription_ReturnsFullDescription()
        {
            var exactLengthDescription = new string('a', MaxLength);
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = exactLengthDescription,
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            Assert.Equal(MaxLength, result.Length);
            Assert.Equal(exactLengthDescription, result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithOneCharOverMaxLengthMetaDescription_TruncatesOneChar()
        {
            var overLengthDescription = new string('a', MaxLength + 1);
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                Description = "Test Description",
                MetaDescription = overLengthDescription,
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            Assert.Equal(MaxLength, result.Length);
            Assert.Equal(overLengthDescription.Substring(0, MaxLength), result);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithShortTitle_UsesTemplateWithoutTruncation()
        {
            var shortTitle = "Short";
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = shortTitle,
                Description = "Test Description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            var expectedTemplate = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, shortTitle);
            Assert.Equal(expectedTemplate, result);
            Assert.True(result.Length <= MaxLength);
        }

        [Fact]
        public void GetMetaDescription_ConfiguratorWithEmptyTitle_UsesTemplateWithEmptyString()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "",
                Description = "Test Description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result = _service.GetMetaDescription(configurator);

            var expectedTemplate = string.Format(UiSharedResource.MetaDescription_ConfiguratorTemplate, "");
            Assert.Equal(expectedTemplate, result);
        }

        [Fact]
        public void MaxLengthConstant_HasExpectedValue()
        {
            // Test that our understanding of MaxLength is correct
            // This is important for the other tests
            Assert.Equal(140, MaxLength);
        }

        [Fact]
        public void GetMetaDescription_MultipleCallsWithSameInput_ReturnsSameResult()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Consistent Test",
                Description = "Test Description",
                MetaDescription = "Custom description",
                IsValid = true
            };
            var configurator = new ConfiguratorWrapper(configuratorDto);

            var result1 = _service.GetMetaDescription(configurator);
            var result2 = _service.GetMetaDescription(configurator);

            Assert.Equal(result1, result2);
        }

        [Fact]
        public void GetMetaDescription_ResultNeverExceedsMaxLength()
        {
            // Test various scenarios to ensure result never exceeds MaxLength
            var testCases = new[]
            {
                new ConfiguratorDto { Id = 1, Title = new string('A', 200), Description = "Test", IsValid = true },
                new ConfiguratorDto { Id = 2, Title = "Normal", MetaDescription = new string('B', 200), Description = "Test", IsValid = true },
                new ConfiguratorDto { Id = 3, Title = null, Description = "Test", IsValid = true },
                new ConfiguratorDto { Id = 4, Title = "", MetaDescription = "", Description = "Test", IsValid = true }
            };

            foreach (var dto in testCases)
            {
                var configurator = new ConfiguratorWrapper(dto);
                var result = _service.GetMetaDescription(configurator);
                
                Assert.True(result.Length <= MaxLength, 
                    $"Result length {result.Length} exceeds MaxLength {MaxLength} for configurator with title: '{dto.Title}' and meta: '{dto.MetaDescription}'");
            }
        }
    }
}