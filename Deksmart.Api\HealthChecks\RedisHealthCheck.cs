using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Deksmart.Api.HealthChecks
{
    public class RedisHealthCheck : IHealthCheck
    {
        private readonly IConfiguration _configuration;

        public RedisHealthCheck(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var redisHost = _configuration["ApiCore:Cache:Redis:Host"] ?? "localhost:6379";
                var redisEnabled = _configuration.GetValue<bool>("ApiCore:Cache:Redis:Enabled");
                var redisDisabled = _configuration.GetValue<bool>("ApiCore:Cache:Redis:ReplaceRedisWithDevNullAdapter");
                
                if (!redisEnabled || redisDisabled)
                {
                    return Task.FromResult(HealthCheckResult.Healthy("Redis cache is disabled"));
                }
                
                // In a real implementation, you would test the connection to Redis
                // For now, we'll just check if the host is configured
                if (string.IsNullOrEmpty(redisHost))
                {
                    return Task.FromResult(HealthCheckResult.Degraded("Redis host is not configured"));
                }
                
                return Task.FromResult(HealthCheckResult.Healthy("Redis cache is configured"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(HealthCheckResult.Degraded("Redis cache check failed", ex));
            }
        }
    }
}
