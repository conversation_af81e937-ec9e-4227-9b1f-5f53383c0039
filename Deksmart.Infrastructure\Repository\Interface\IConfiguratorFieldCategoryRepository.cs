﻿using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for accessing and managing <see cref="ConfiguratorFieldCategory"/> entities, including retrieval of field categories for a specific configurator.
    /// </summary>
    public interface IConfiguratorFieldCategoryRepository : IIntIdRepositoryBase<ConfiguratorFieldCategory>
    {
        /// <summary>
        /// Retrieves all field categories for a given configurator, including related fields, visibility, and expressions.
        /// </summary>
        /// <param name="configuratorId">The unique identifier of the configurator.</param>
        /// <returns>A list of <see cref="ConfiguratorFieldCategory"/> entities with related data.</returns>
        Task<List<ConfiguratorFieldCategory>> GetFieldCategoriesForConfiguratorAsync(int configuratorId);
    }
}
