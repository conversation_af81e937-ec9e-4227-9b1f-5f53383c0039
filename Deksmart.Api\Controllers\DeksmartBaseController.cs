using Microsoft.AspNetCore.Mvc;
using Deksmart.Shared.Dto;
using ValidationResult = FluentValidation.Results.ValidationResult;

namespace Deksmart.Api.Controllers
{
    /// <summary>
    /// Base controller for Deksmart controllers that provides consistent error handling methods.
    /// </summary>
    public abstract class DeksmartBaseController : ControllerBase
    {
        /// <summary>
        /// Creates a ValidationErrorResult with status code 422 (Unprocessable Entity)
        /// </summary>
        protected class ValidationErrorResult : ObjectResult
        {
            public ValidationErrorResult(object value) : base(value)
            {
                StatusCode = 422; // Unprocessable Entity - commonly used for validation errors
            }
        }

        /// <summary>
        /// Creates a BadRequest response for critical errors (400 Bad Request)
        /// </summary>
        /// <param name="message">Error message</param>
        /// <returns>BadRequestObjectResult with error message</returns>
        protected BadRequestObjectResult CreateErrorResponse(string message)
        {
            return BadRequest(ApiResponse<object>.CreateError(message));
        }

        /// <summary>
        /// Creates a validation error response (422 Unprocessable Entity) for a FluentValidation result
        /// </summary>
        /// <param name="validationResult">FluentValidation validation result</param>
        /// <returns>ValidationErrorResult with error messages</returns>
        protected IActionResult CreateValidationErrorResponse(ValidationResult validationResult)
        {
            var errorMessages = validationResult.Errors.Select(e => e.ErrorMessage).ToList();
            return new ValidationErrorResult(ApiResponse<object>.CreateValidationError(errorMessages));
        }

        /// <summary>
        /// Creates a validation error response (422 Unprocessable Entity) for a domain validation result
        /// </summary>
        /// <param name="validationResult">Domain validation result</param>
        /// <returns>ValidationErrorResult with error messages</returns>
        protected IActionResult CreateValidationErrorResponse(Deksmart.Domain.Entity.Business.ValidationResult validationResult)
        {
            return new ValidationErrorResult(ApiResponse<object>.CreateValidationError(validationResult.GetErrorList()));
        }

        /// <summary>
        /// Creates a successful response with data
        /// </summary>
        /// <typeparam name="T">The type of data being returned</typeparam>
        /// <param name="data">The data to return</param>
        /// <returns>OkObjectResult with data wrapped in ApiResponse</returns>
        protected OkObjectResult CreateSuccessResponse<T>(T data)
        {
            return Ok(ApiResponse<T>.CreateSuccess(data));
        }
    }
}
