﻿@using Deksmart.Shared.Dto
@using Deksmart.Ui.Shared.Resources
@using Deksmart.Ui.Shared.Services
@inject HttpService Http
@inject NavigationManager NavigationManager

<div class="nav-scrollable">
    <nav class="flex-column">
        @if (configurators != null)
        {
            @foreach (var configurator in configurators)
            {
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="@($"/{UiSharedResource.Deksmart}/{configurator.Id}")">
                        <span class="bi bi-thick-dot-nav-menu" aria-hidden="true"></span>
                        <span class="configurator-title">@configurator.Title</span>
                    </NavLink>
                </div>
            }
        }
    </nav>
</div>

@code {
    private List<ConfiguratorDto>? configurators;

    protected override async Task OnInitializedAsync()
    {
        configurators = await Http.GetAsync<List<ConfiguratorDto>>(UiSharedResource.ConfiguratorsUrl);
    }
}