using Deksmart.Shared.Dto;
using Deksmart.Shared.Enum;
using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Factory
{
    public static class ConfiguratorWrapperFactory
    {
        public static ConfiguratorWrapper Create(ConfiguratorDto configurator)
        {
            var wrapper = new ConfiguratorWrapper(configurator)
            {
                FieldCategories = configurator.ConfiguratorFieldCategories.OrderBy(d => d.Order).Select(Create).ToList(),
                ConfiguratorCompositions = configurator.ConfiguratorCompositions.OrderBy(d => d.Order).Select(Create).ToList(),
            };

            return wrapper;
        }

        public static ConfiguratorFieldCategoryWrapper Create(ConfiguratorFieldCategoryDto category)
        {
            var categoryWrapper = new ConfiguratorFieldCategoryWrapper(category);

            var allFields = category.ConfiguratorFields.Select(Create).ToList();

            categoryWrapper.Fields = GetFieldsByPosition(allFields, FieldPositionDto.Left);
            SetChildFields(categoryWrapper.Fields, GetFieldsByPosition(allFields, FieldPositionDto.LeftSuffix));

            categoryWrapper.RightFields = GetFieldsByPosition(allFields, FieldPositionDto.Right);
            SetChildFields(categoryWrapper.RightFields, GetFieldsByPosition(allFields, FieldPositionDto.RightSuffix));

            categoryWrapper.FullWidthFields = GetFieldsByPosition(allFields, FieldPositionDto.FullWidth);
            SetChildFields(categoryWrapper.FullWidthFields, GetFieldsByPosition(allFields, FieldPositionDto.FullWidthSuffix));

            return categoryWrapper;
        }

        public static ConfiguratorFieldWrapper Create(ConfiguratorFieldDto field)
        {
            ConfiguratorFieldWrapper result = field.ComponentType switch
            {
                ComponentTypeDto.Numeric => new NumericFieldWrapper(field) { Value = field.Value ?? 0 },
                ComponentTypeDto.Slider => new SliderFieldWrapper(field) { Value = field.Value ?? 0 },
                ComponentTypeDto.Checkbox => new CheckboxFieldWrapper(field),
                ComponentTypeDto.Selectbox => new SelectBoxFieldWrapper(field),
                ComponentTypeDto.SingleChoice => new SingleChoiceFieldWrapper(field),
                ComponentTypeDto.Tile => new TileFieldWrapper(field),
                ComponentTypeDto.Expression => new ExpressionFieldWrapper(field),
                ComponentTypeDto.Text => new TextFieldWrapper(field),
                ComponentTypeDto.Product => new ProductFieldWrapper(field),
                _ => new DefaultFieldWrapper(field)
            };

            result.FieldValues = field.ConfiguratorFieldValues.OrderBy(d => d.Order).Select(Create).ToList();

            return result;
        }

        public static ConfiguratorFieldValueWrapper Create(ConfiguratorFieldValueDto value)
        {
            return new ConfiguratorFieldValueWrapper(value);
        }

        public static ConfiguratorCompositionWrapper Create(ConfiguratorCompositionDto composition)
        {
            var wrapper = new ConfiguratorCompositionWrapper(composition);
            var products = composition.ConfiguratorProducts.OrderBy(d => d.Order).ToArray();

            for (int i = 0; i < products.Length; i++)
            {
                var product = products[i];
                var productWrapper = new ConfiguratorProductWrapper(product);
                wrapper.Products.Add(productWrapper);
            }

            wrapper.SelectedProducts = wrapper.Products.Where(d => d.IsSelected).ToList();

            return wrapper;
        }

        private static void SetChildFields(List<ConfiguratorFieldWrapper> fields, List<ConfiguratorFieldWrapper> childFields)
        {
            for (int i = 0; i < childFields.Count; i++)
            {
                var childField = childFields[i];
                var parentField = fields.FirstOrDefault(d => d.Order == childField.Order);
                if (parentField is not null)
                    parentField.ChildField = childField;
            }
        }

        private static List<ConfiguratorFieldWrapper> GetFieldsByPosition(List<ConfiguratorFieldWrapper> fields, FieldPositionDto position)
        {
            return fields.Where(d => d.Field.FieldPosition == position).OrderBy(d => d.Order).ToList();
        }
    }
}