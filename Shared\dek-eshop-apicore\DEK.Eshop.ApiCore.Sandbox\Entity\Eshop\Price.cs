namespace DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;

public class Price
{
    // Sales 'n Packages
    public decimal PriceVatSales { get; set; }

    public decimal PriceNoVatSales { get; set; }

    public decimal PriceVatSalesOrigin { get; set; }

    public decimal PriceNoVatSalesOrigin { get; set; }

    public decimal PriceVatPackage { get; set; }

    public decimal PriceNoVatPackage { get; set; }

    public decimal PriceVatPackageOrigin { get; set; }

    public decimal PriceNoVatPackageOrigin { get; set; }

    // Primary 'n Secondery
    public bool IsPackagePrimary { get; set; }

    public decimal PriceVatPrimary { get; set; }

    public decimal PriceNoVatPrimary { get; set; }

    public decimal PriceVatSecondery { get; set; }

    public decimal PriceNoVatSecondery { get; set; }

    public decimal PriceVatPrimaryOrigin { get; set; }

    public decimal PriceNoVatPrimaryOrigin { get; set; }

    // History
    public bool IsVisibleDiscountHistory { get; set; }

    public decimal PriceVatLowestHistory { get; set; }

    public decimal PriceNoVatLowestHistory { get; set; }

    //Tax, Vat, Discount
    
    /// <summary>
    /// Cena obsahuje zahrnutý recyklační poplatek.
    /// </summary>
    public bool HasTax { get; set; }

    public decimal Vat { get; set; }

    public int DiscountPercent { get; set; }

    public Price(ProductEshopPrice p)
    {
        PriceVatSales = p.PriceVatSales;
        PriceNoVatSales = p.PriceNoVatSales;
        PriceVatSalesOrigin = p.PriceVatSalesOrigin;
        PriceNoVatSalesOrigin = p.PriceNoVatSalesOrigin;
        PriceVatPackage = p.PriceVatPackage;
        PriceNoVatPackage = p.PriceNoVatPackage;
        PriceVatPackageOrigin = p.PriceVatPackageOrigin;
        PriceNoVatPackageOrigin = p.PriceNoVatPackageOrigin;

        IsPackagePrimary = p.PriceVatSales != p.PriceVatPackage;
        PriceVatPrimary = IsPackagePrimary ? p.PriceVatPackage : p.PriceVatSales;
        PriceNoVatPrimary = IsPackagePrimary ? p.PriceNoVatPackage : p.PriceNoVatSales;
        PriceVatSecondery = IsPackagePrimary ? p.PriceVatSales : p.PriceVatPackage;
        PriceNoVatSecondery = IsPackagePrimary ? p.PriceNoVatSales : p.PriceNoVatPackage;
        PriceVatPrimaryOrigin = IsPackagePrimary ? p.PriceVatPackageOrigin : p.PriceVatSalesOrigin;
        PriceNoVatPrimaryOrigin = IsPackagePrimary ? p.PriceNoVatPackageOrigin : p.PriceNoVatSalesOrigin;

        IsVisibleDiscountHistory = p.IsVisibleDiscountHistory;
        PriceVatLowestHistory = p.PriceVatLowestHistory;
        PriceNoVatLowestHistory = p.PriceNoVatLowestHistory;

        HasTax = p.HasTax;
        Vat = p.Vat;
        DiscountPercent = p.DiscountPercent;
    }
}

