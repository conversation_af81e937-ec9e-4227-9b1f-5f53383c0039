using Microsoft.Extensions.Logging;
using Deksmart.Application.Resource;
using Deksmart.Application.Service.Http;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Entity.Db;
using Deksmart.Shared.Dto;

namespace Deksmart.Application.Service
{
    /// <summary>
    /// Service responsible for gathering and enriching product data from external APIs.
    /// Handles pricing, units, and conversion data aggregation for product calculations.
    /// </summary>
    public class ProductEnrichmentService : IProductEnrichmentService
    {
        private readonly ILogger<ProductEnrichmentService> _logger;
        private readonly IEshopApiService _eshopApiService;

        public ProductEnrichmentService(
            ILogger<ProductEnrichmentService> logger,
            IEshopApiService eshopApiService)
        {
            _logger = logger;
            _eshopApiService = eshopApiService;
        }

        public async Task<(List<EshopProductPricing> pricing, List<EshopProductUnit> units, ValidationResult validation)> GetBasicEnrichmentDataAsync(List<string> productCodes)
        {
            var validation = new ValidationResult();
            
            // Get enrichment data for all products
            var pricingTask = _eshopApiService.GetEshopProductPricingAsync(productCodes);
            var unitsTask = _eshopApiService.GetEshopProductUnitsAsync(productCodes);

            await Task.WhenAll(pricingTask, unitsTask);

            var pricingResult = await pricingTask;
            var unitsResult = await unitsTask;

            if (pricingResult.Validation.HasErrors)
            {
                validation.AddError(string.Format(DeksmartApplicationResource.PricingError, pricingResult.Validation.GetErrors()));
                _logger.LogWarning("Failed to get product pricing: {Errors}", pricingResult.Validation.GetErrors());
                return (new List<EshopProductPricing>(), new List<EshopProductUnit>(), validation);
            }

            if (unitsResult.Validation.HasErrors)
            {
                validation.AddError(string.Format(DeksmartApplicationResource.UnitsError, unitsResult.Validation.GetErrors()));
                _logger.LogWarning("Failed to get product units: {Errors}", unitsResult.Validation.GetErrors());
                return (new List<EshopProductPricing>(), new List<EshopProductUnit>(), validation);
            }

            return (pricingResult.Data, unitsResult.Data, validation);
        }

        public async Task<IEnumerable<ProductPriceData>> GetProductEnrichmentDataAsync<T>(
            List<string> productCodes,
            List<T> products,
            ValidationResult validation) where T : class
        {
            var (pricingData, unitsData, enrichmentValidation) = await GetBasicEnrichmentDataAsync(productCodes);
            if (enrichmentValidation.HasErrors)
            {
                validation.AddError(enrichmentValidation.GetErrors());
                return Enumerable.Empty<ProductPriceData>();
            }

            // Get unit conversion data using common method
            var conversionResult = await GetUnitConversionsAsync(productCodes, products, unitsData, validation);
            if (validation.HasErrors)
            {
                return Enumerable.Empty<ProductPriceData>();
            }

            // Create lookup dictionaries for explicit matching
            var conversionLookup = conversionResult.ToLookup(x => x.ProductCode, x => x.QuantityOutput);
            
            // Create enrichment data - always using sales units
            return CreateProductPriceDataCollection(productCodes, pricingData, unitsData, conversionLookup);
        }

        public async Task<List<EshopProductUnitConversion>> GetUnitConversionsAsync<T>(
            List<string> productCodes,
            List<T> products,
            List<EshopProductUnit> unitsData,
            ValidationResult validation) where T : class
        {
            // Prepare unit conversion parameters
            var quantities = new List<decimal>();
            var unitInputs = new List<string>();
            var unitOutputs = new List<string>();
            
            for (int i = 0; i < productCodes.Count; i++)
            {
                var productCode = productCodes[i];
                var unitsInfo = unitsData[i];
                
                // Handle different product types - use index-based lookup to handle duplicate product codes
                decimal quantity;
                string productUnit;
                
                if (typeof(T) == typeof(SelectedProductDto))
                {
                    var selectedProduct = products.Cast<SelectedProductDto>().ElementAt(i);
                    quantity = selectedProduct.Amount;
                    productUnit = selectedProduct.Unit;
                }
                else if (typeof(T) == typeof(ConfiguratorProduct))
                {
                    var product = products.Cast<ConfiguratorProduct>().ElementAt(i);
                    quantity = product.CalculatedAmount;
                    productUnit = product.ProductUnit;
                }
                else
                {
                    throw new ArgumentException($"Unsupported product type: {typeof(T)}");
                }
                
                // Use original configurator unit as input, convert to sales unit as output
                quantities.Add(quantity);
                unitInputs.Add(productUnit);
                
                unitOutputs.Add(unitsInfo.UnitSales);
            }
            
            var conversionResult = await _eshopApiService.GetProductUnitConversionsAsync(productCodes, quantities, unitInputs, unitOutputs);
            
            if (conversionResult.Validation.HasErrors)
            {
                // Log the API failure but don't stop processing - fall back to legacy conversion
                _logger.LogWarning("Unit conversion API failed, falling back to legacy conversion logic: {Errors}", conversionResult.Validation.GetErrors());
                
                // Return empty list to trigger legacy conversion fallback
                return new List<EshopProductUnitConversion>();
            }

            return conversionResult.Data;
        }

        public IEnumerable<ProductPriceData> CreateProductPriceDataCollection(
            List<string> productCodes,
            List<EshopProductPricing> pricingData,
            List<EshopProductUnit> unitsData,
            ILookup<string?, decimal> conversionLookup)
        {
            return productCodes
                .Select((code, index) => new ProductPriceData
                {
                    ProductCode = code,
                    Pricing = pricingData[index],
                    Unit = unitsData[index],
                    // Use null if no conversion found for this product (triggers legacy conversion)
                    ConvertedAmount = conversionLookup.Contains(code) ? (decimal?)conversionLookup[code].FirstOrDefault() : null
                })
                .Where(x => x.Pricing != null && x.Unit != null);
        }
    }
}
