﻿namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Represents a product selected by the user in the configurator, encapsulating its code, selected amount, and unit.
    /// Used to transfer selected product data between client, API, and business logic for price calculation and state management.
    /// Serves as the bridge between user selection and backend processing.
    /// </summary>
    public class SelectedProduct
    {
        public string ProductCode { get; set; } = null!;
        public decimal Amount { get; set; }
        public string Unit { get; set; } = null!;
    }
}
