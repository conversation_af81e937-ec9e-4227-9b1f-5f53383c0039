using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Domain.Entity.Db;
using Deksmart.Shared.Dto;

namespace Deksmart.Application.Service
{
    /// <summary>
    /// Service responsible for gathering and enriching product data from external APIs.
    /// Handles pricing, units, and conversion data aggregation for product calculations.
    /// </summary>
    public interface IProductEnrichmentService
    {
        /// <summary>
        /// Gets basic pricing and units data for products
        /// </summary>
        /// <param name="productCodes">List of product codes to get data for</param>
        /// <returns>Tuple containing pricing data, units data, and validation result</returns>
        Task<(List<EshopProductPricing> pricing, List<EshopProductUnit> units, ValidationResult validation)> GetBasicEnrichmentDataAsync(List<string> productCodes);

        /// <summary>
        /// Gets enrichment data for products including pricing, units, and conversions
        /// </summary>
        /// <param name="productCodes">List of product codes</param>
        /// <param name="products">List of products (ConfiguratorProduct or SelectedProductDto)</param>
        /// <param name="validation">Validation result to add errors to</param>
        /// <returns>Collection of enriched product price data</returns>
        Task<IEnumerable<ProductPriceData>> GetProductEnrichmentDataAsync<T>(
            List<string> productCodes,
            List<T> products,
            ValidationResult validation) where T : class;

        /// <summary>
        /// Gets unit conversions for products
        /// </summary>
        /// <param name="productCodes">List of product codes</param>
        /// <param name="products">List of products (ConfiguratorProduct or SelectedProductDto)</param>
        /// <param name="unitsData">Units data for the products</param>
        /// <param name="validation">Validation result to add errors to</param>
        /// <returns>List of unit conversion results</returns>
        Task<List<EshopProductUnitConversion>> GetUnitConversionsAsync<T>(
            List<string> productCodes,
            List<T> products,
            List<EshopProductUnit> unitsData,
            ValidationResult validation) where T : class;

        /// <summary>
        /// Creates ProductPriceData collection from pricing, units, and conversion data
        /// </summary>
        /// <param name="productCodes">List of product codes</param>
        /// <param name="pricingData">Pricing data for products</param>
        /// <param name="unitsData">Units data for products</param>
        /// <param name="conversionLookup">Lookup for conversion data by product code</param>
        /// <returns>Collection of product price data</returns>
        IEnumerable<ProductPriceData> CreateProductPriceDataCollection(
            List<string> productCodes,
            List<EshopProductPricing> pricingData,
            List<EshopProductUnit> unitsData,
            ILookup<string?, decimal> conversionLookup);
    }
}
