# ConfigFactory
#### DEK.Eshop.ApiCore.Factory
---

Vytvá<PERSON><PERSON> config objekty z jednotlivých sekcí appsettings.json.

- metoda objektu: `public T Create<T>(string? section = null)`
- metoda třídy: `public static T Create<T>(IConfiguration configuration, string? section = null)`
- v obou případech může být název sekce přetížen (`string? section = null`), pokud není stejný jako generic `<T>`

ConfigFactory je zaregistrován jako singleton.

```cs
using DEK.Eshop.ApiCore.Entity.Config;
using DEK.Eshop.ApiCore.Factory;

public class Foo
{
    public Foo(ConfigFactory configFactory, IConfiguration configuration)
    {
        var mssql = configFactory.Create<Mssql>();
        var jwt = ConfigFactory.Create<Jwt>(configuration);
    }
}
```

`Mssql` a `Jwt` jsou exitující Entity a zároveň sekce v configu.