using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.Hosting;
using DEK.Eshop.ApiCore.Extension;
using DEK.Eshop.ApiCore.Testing;

namespace DEK.Eshop.ApiCore.Tests;

public class Abstract_Tests<TEntryPoint> : IClassFixture<WebApplicationFactory<TEntryPoint>> where TEntryPoint : class
{
    private readonly WebApplicationFactory<TEntryPoint> factory;
    protected readonly HttpClient client;
    protected readonly ITestOutputHelper output;
    public Abstract_Tests(WebApplicationFactory<TEntryPoint> factory, ITestOutputHelper output)
    {

        this.factory = factory.WithWebHostBuilder(SetupWebHostBuilder);
        this.output = output;
        this.client = this.CreateClient();
    }

    protected virtual void SetupWebHostBuilder(IWebHostBuilder builder)
    {
        builder.UseSetting("Environment", "Test");
    }

    /// <summary>
    /// Create HttpClient with Authorization header
    /// @todo : override user fields
    /// </summary>
    protected HttpClient CreateClient(Dictionary<string, string?>? userOverride = null)
    {
        var tester = new Tester();
        if (userOverride != null) {
            foreach (var kvp in userOverride) {
                tester.UserBase[kvp.Key] = kvp.Value;
            }
        }

        var client = this.factory.CreateClient();
        //this.output.WriteLine(tester.CreateJwtHeader());
        client.DefaultRequestHeaders.Add("Authorization", tester.CreateJwtHeader());
        return client;
    }

    protected async Task<bool> HasInternalServerError(HttpResponseMessage response)
    {
        var statusCode = (int)response.StatusCode;
        if (statusCode >= 500) {
            this.output.WriteLine(await response.Dek_ToStringAsync());
            return true;
        }
        return false;
    }
}
