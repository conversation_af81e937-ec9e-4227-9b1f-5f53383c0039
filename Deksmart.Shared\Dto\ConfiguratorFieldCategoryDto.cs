using Deksmart.Shared.Enum;

namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a top-level grouping of user-interactive fields within a configurator.
    /// Used to organize, categorize, and transfer field groups (filters, input elements) for dynamic UI generation.
    /// Supports category-level metadata, ordering, and collapse/expand state for both backend and UI workflows.
    /// </summary>
    [Serializable]
    public class ConfiguratorFieldCategoryDto
    {
        public int Id { get; set; }

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public int Order { get; set; }

        /// <summary>
        /// Represents the collapse/expand state of the category for UI and API.
        /// </summary>
        public CategoryCollapseStateDto CollapseState { get; set; }

        public List<ConfiguratorFieldDto> ConfiguratorFields { get; set; } = [];
    }
}
