using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Service;
using Moq;

namespace Deksmart.Domain.Tests
{
    public class ConfiguratorValidationServiceTest
    {
        private readonly Mock<IConfiguratorExistenceValidator> _existenceValidatorMock;
        private readonly ConfiguratorValidationService _service;

        public ConfiguratorValidationServiceTest()
        {
            _existenceValidatorMock = new Mock<IConfiguratorExistenceValidator>();

            _service = new ConfiguratorValidationService(_existenceValidatorMock.Object);
        }

        [Fact]
        public async Task ValidateConfiguratorExists_WhenConfiguratorExists_NoErrorsAdded()
        {
            // Arrange
            var configuratorId = 42;
            var validation = new ValidationResult();
            _existenceValidatorMock.Setup(x => x.DoesConfiguratorExistAsync(configuratorId))
                .ReturnsAsync(true);

            // Act
            await _service.ValidateConfiguratorExists(configuratorId, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public async Task ValidateConfiguratorExists_WhenConfiguratorDoesNotExist_AddsError()
        {
            // Arrange
            var configuratorId = 42;
            var validation = new ValidationResult();
            _existenceValidatorMock.Setup(x => x.DoesConfiguratorExistAsync(configuratorId))
                .ReturnsAsync(false);

            // Act
            await _service.ValidateConfiguratorExists(configuratorId, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(string.Format(DeksmartDomainResource.ConfiguratorDoesNotExist, configuratorId), validation.GetErrors());
        }

        [Fact]
        public void ValidateDirectValueIsWithinBounds_WhenValueIsWithinBounds_NoErrorsAdded()
        {
            // Arrange
            var field = new DirectValue
            {
                Id = 1,
                Min = 0,
                Max = 100,
                DefaultValue = 50
            };
            var value = 75m;
            var validation = new ValidationResult();

            // Act
            _service.ValidateDirectValueIsWithinBounds(field, value, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateDirectValueIsWithinBounds_WhenValueEqualsMin_NoErrorsAdded()
        {
            // Arrange
            var field = new DirectValue
            {
                Id = 1,
                Min = 0,
                Max = 100,
                DefaultValue = 50
            };
            var value = 0m;
            var validation = new ValidationResult();

            // Act
            _service.ValidateDirectValueIsWithinBounds(field, value, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateDirectValueIsWithinBounds_WhenValueEqualsMax_NoErrorsAdded()
        {
            // Arrange
            var field = new DirectValue
            {
                Id = 1,
                Min = 0,
                Max = 100,
                DefaultValue = 50
            };
            var value = 100m;
            var validation = new ValidationResult();

            // Act
            _service.ValidateDirectValueIsWithinBounds(field, value, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateDirectValueIsWithinBounds_WhenValueBelowMin_AddsError()
        {
            // Arrange
            var field = new DirectValue
            {
                Id = 1,
                Min = 0,
                Max = 100,
                DefaultValue = 50
            };
            var value = -10m;
            var validation = new ValidationResult();
            var expectedError = string.Format(DeksmartDomainResource.FieldValueOutOfRange, field.Id, value, field.Min, field.Max);

            // Act
            _service.ValidateDirectValueIsWithinBounds(field, value, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(expectedError, validation.GetErrors());
        }

        [Fact]
        public void ValidateDirectValueIsWithinBounds_WhenValueAboveMax_AddsError()
        {
            // Arrange
            var field = new DirectValue
            {
                Id = 1,
                Min = 0,
                Max = 100,
                DefaultValue = 50
            };
            var value = 150m;
            var validation = new ValidationResult();
            var expectedError = string.Format(DeksmartDomainResource.FieldValueOutOfRange, field.Id, value, field.Min, field.Max);

            // Act
            _service.ValidateDirectValueIsWithinBounds(field, value, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(expectedError, validation.GetErrors());
        }

        [Fact]
        public void ValidateMultipleChoiceValueHasValidValue_WhenValueInList_NoErrorsAdded()
        {
            // Arrange
            var field = new MultipleChoiceValue
            {
                Id = 1,
                Ident = "test-field",
                DefaultValue = 0,
                Values = new List<decimal> { 10, 20, 30 }
            };
            var value = 20m;
            var validation = new ValidationResult();

            // Act
            _service.ValidateMultipleChoiceValueHasValidValue(field, field.Id, value, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateMultipleChoiceValueHasValidValue_WhenValueIsDefaultValue_NoErrorsAdded()
        {
            // Arrange
            var field = new MultipleChoiceValue
            {
                Id = 1,
                Ident = "test-field",
                DefaultValue = 42,
                Values = new List<decimal> { 10, 20, 30 }
            };
            var value = 42m;
            var validation = new ValidationResult();

            // Act
            _service.ValidateMultipleChoiceValueHasValidValue(field, field.Id, value, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateMultipleChoiceValueHasValidValue_WhenValueNotValidChoice_AddsError()
        {
            // Arrange
            var field = new MultipleChoiceValue
            {
                Id = 1,
                Ident = "test-field",
                DefaultValue = 0,
                Values = new List<decimal> { 10, 20, 30 }
            };
            var value = 15m;
            var validation = new ValidationResult();
            var expectedError = string.Format(DeksmartDomainResource.FieldValueDoesNotExist, value, field.Id);

            // Act
            _service.ValidateMultipleChoiceValueHasValidValue(field, field.Id, value, validation);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(expectedError, validation.GetErrors());
        }

        [Fact]
        public async Task ValidateConfiguratorExistsAsync_WhenConfiguratorExists_ReturnsValidationWithoutErrors()
        {
            // Arrange
            var configuratorId = 42;
            _existenceValidatorMock.Setup(x => x.DoesConfiguratorExistAsync(configuratorId))
                .ReturnsAsync(true);

            // Act
            var validation = new ValidationResult();
            await _service.ValidateConfiguratorExistsAsync(configuratorId, validation);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public async Task ValidateConfiguratorExistsAsync_WhenConfiguratorDoesNotExist_ReturnsValidationWithError()
        {
            // Arrange
            var configuratorId = 42;
            _existenceValidatorMock.Setup(x => x.DoesConfiguratorExistAsync(configuratorId))
                .ReturnsAsync(false);

            var validation = new ValidationResult();

            // Act
            var result = new ValidationResult();
            await _service.ValidateConfiguratorExistsAsync(configuratorId, result);

            // Assert
            Assert.True(result.HasErrors);
            Assert.Contains(string.Format(DeksmartDomainResource.ConfiguratorDoesNotExist, configuratorId), result.GetErrors());
        }
    }
}