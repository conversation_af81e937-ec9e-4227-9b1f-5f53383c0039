using Deksmart.Application.Service;
using Deksmart.Domain.Entity.Db;

namespace Deksmart.Application.Tests.Service
{
    public class ConfiguratorViewGeneratorTest
    {
        private readonly IConfiguratorViewGenerator _generator;

        public ConfiguratorViewGeneratorTest()
        {
            _generator = new ConfiguratorViewGenerator();
        }

        [Fact]
        public void GenerateHtml_WithEmptyConfigurator_ReturnsValidHtml()
        {
            // Arrange
            var configurator = new Configurator
            {
                Title = "Test Configurator"
            };

            // Act
            var result = _generator.GenerateHtml(configurator);

            // Assert
            Assert.NotNull(result);
            Assert.Contains("<!DOCTYPE html>", result);
            Assert.Contains("Test Configurator", result);
        }

        [Fact]
        public void GenerateHtml_WithProducts_IncludesProductTable()
        {
            // Arrange
            var configurator = new Configurator
            {
                Title = "Test Configurator",
                ConfiguratorCompositions = new List<ConfiguratorComposition>
                {
                    new ConfiguratorComposition
                    {
                        Title = "Test Composition",
                        Order = 1,
                        ConfiguratorProducts = new List<ConfiguratorProduct>
                        {
                            new ConfiguratorProduct
                            {
                                Title = "Test Product",
                                ProductCode = "TP001",
                                IsSelected = true,
                                CalculatedAmount = 2,
                                ProductUnit = "pcs",
                                PriceVatPackage = 100,
                                PackageUnit = "pcs",
                                PriceVat = 200
                            }
                        }
                    }
                }
            };

            // Act
            var result = _generator.GenerateHtml(configurator);

            // Assert
            Assert.NotNull(result);
            Assert.Contains("Test Product", result);
            Assert.Contains("TP001", result);
            Assert.Contains("border-collapse: collapse", result);
        }
    }
}
