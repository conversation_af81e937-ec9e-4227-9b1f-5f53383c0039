using Deksmart.Infrastructure.Context;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Deksmart.Api.HealthChecks
{
    public class PostgresHealthCheck : IHealthCheck
    {
        private readonly ConfiguratorContext _dbContext;

        public PostgresHealthCheck(ConfiguratorContext dbContext)
        {
            _dbContext = dbContext;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // Try to connect to the database
                var canConnect = await _dbContext.Database.CanConnectAsync(cancellationToken);
                
                if (canConnect)
                {
                    return HealthCheckResult.Healthy("PostgreSQL database is healthy");
                }
                
                return HealthCheckResult.Unhealthy("Cannot connect to PostgreSQL database");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("PostgreSQL database is unhealthy", ex);
            }
        }
    }
}
