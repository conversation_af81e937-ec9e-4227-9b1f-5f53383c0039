# Unified Validation Strategy for Deksmart

This document outlines the unified validation strategy for the Deksmart application.

## Validation Types

The application uses three main types of validation:

### 1. Critical Validation (Return Error)
- **Purpose**: Prevent invalid operations that would cause system errors
- **Examples**: Invalid IDs, non-existent entities, permission issues
- **Response**: HTTP 400 Bad Request with clear error message
- **Implementation**: Use FluentValidation for input validation

### 2. Business Validation (Return ValidationResult)
- **Purpose**: Indicate business rule violations that users should fix
- **Examples**: Email format validation, required fields, data constraints
- **Response**: HTTP 422 Unprocessable Entity with validation errors
- **Implementation**: Use Domain.Entity.Business.ValidationResult consistently

### 3. Field Validation (Set IsValid property)
- **Purpose**: Guide users to complete configurator correctly
- **Examples**: Required fields in configurator, field constraints
- **Response**: Return data with validation errors in field properties
- **Implementation**: Continue using expression-based validation

## Implementation Details

### Service Method Return Types

All service methods that perform validation should return a consistent result type:

```csharp
// For methods that return data and can have validation errors
public async Task<(T? result, ValidationResult validation)> MethodNameAsync(...)
{
    var validation = new ValidationResult();
    
    // Perform validation
    if (validation.HasErrors)
    {
        return (default, validation);
    }
    
    // Process and return result
    return (result, validation);
}

// For methods that only perform operations without returning data
public async Task<ValidationResult> MethodNameAsync(...)
{
    var validation = new ValidationResult();
    
    // Perform validation and operations
    
    return validation;
}
```

### Domain ValidationResult

The `ValidationResult` class in the Domain layer should be the standard for all business validation:

```csharp
public class ValidationResult
{
    private List<string> _errors { get; set; } = new List<string>();

    public bool HasErrors => _errors.Any();
    public bool IsValid => !HasErrors;

    public void AddError(string error) => _errors.Add(error);
    public void AddErrors(IEnumerable<string> errors) => _errors.AddRange(errors);
    public string GetErrors() => string.Join(", ", _errors);
    public IReadOnlyList<string> GetErrorList() => _errors.AsReadOnly();
}
```

### Controller Response Handling

Controllers should use the `DeksmartBaseController` methods for consistent responses:

```csharp
// For critical errors (400 Bad Request)
return CreateErrorResponse(errorMessage);

// For validation errors (422 Unprocessable Entity)
return CreateValidationErrorResponse(validation);

// For successful responses
return CreateSuccessResponse(data);
```

### API Response Format

All API responses should use the `ApiResponse<T>` class for consistency:

```csharp
public class ApiResponse<T>
{
    public T? Data { get; set; }
    public bool Success { get; set; }
    public List<string>? ValidationErrors { get; set; }
    public string? ErrorMessage { get; set; }
}
```

## Service Implementation Guidelines

### Domain Services

Domain services should:

1. Accept a `ValidationResult` parameter for collecting validation errors
2. Add errors to the validation result instead of returning error strings
3. Focus on validating business rules

Example:

```csharp
public class ConfiguratorValidationService : IConfiguratorValidationService
{
    public void ValidateDirectValueIsWithinBounds(DirectValue field, decimal value, ValidationResult validation)
    {
        if (value < field.Min || value > field.Max)
            validation.AddError(string.Format(DeksmartDomainResource.FieldValueOutOfRange, field.Id, value, field.Min, field.Max));
    }
}
```

### Application Services

Application services should:

1. Create a `ValidationResult` instance at the beginning of methods
2. Pass the validation result to domain services
3. Return the validation result along with any data
4. Use the tuple pattern `(T? result, ValidationResult validation)` consistently

Example:

```csharp
public async Task<(ConfiguratorDto? configurator, ValidationResult validation)> GetFilteredConfiguratorAsync(int configuratorId, ConfiguratorStateDto configuratorState)
{
    var validation = new ValidationResult();
    
    // Validate configurator exists
    await _validationService.ValidateConfiguratorExists(configuratorId, validation);
    if (validation.HasErrors)
        return (null, validation);
    
    // Process configurator
    var configurator = await _cacheManager.GetOrAddAsync(configuratorId, _configuratorDao.GetCompleteConfiguratorNoTrackingAsync);
    
    // Return result with validation
    return (_mappingService.MapToDto(configurator), validation);
}
```

### HTTP Services

HTTP services should:

1. Use the `ApiResult<T>` pattern consistently
2. Convert external errors to `ValidationResult` format
3. Handle network and serialization errors appropriately

Example:

```csharp
public async Task<ApiResult<List<T>>> GetDataAsync<T>(string endpoint, List<string> codes)
{
    try
    {
        // API call logic
        
        if (!response.IsSuccessStatusCode)
        {
            var error = await response.Content.ReadAsStringAsync();
            return ApiResult<List<T>>.Error($"API returned {response.StatusCode}: {error}");
        }
        
        return ApiResult<List<T>>.Success(data);
    }
    catch (Exception ex)
    {
        return ApiResult<List<T>>.Error($"Error while fetching data from {endpoint}: {ex.Message}");
    }
}
```

## Migration Strategy

1. Update service interfaces to use the consistent return type pattern
2. Refactor service implementations to use ValidationResult consistently
3. Update controller actions to handle the new return types
4. Gradually replace string error returns with ValidationResult

## Guidelines

1. **Don't modify DEK.Eshop.ApiCore** - Leave this code as is and focus on the Deksmart namespace.

2. **Use FluentValidation for input validation** - Continue using FluentValidation for validating API inputs.

3. **Use domain validation for business rules** - Use the domain ValidationResult for business logic validation.

4. **Maintain expression-based field validation** - Keep the existing configurator field validation mechanism.

5. **Standardize controller responses** - Use the DeksmartBaseController methods for consistent responses.

6. **Document validation approach** - Add comments explaining the validation strategy to help future developers.
