# made according to https://github.com/dotnet/dotnet-docker/tree/main/samples/aspnetapp
# and https://playwright.dev/docs/docker

###############################################################
# Build stage
###############################################################

FROM docker-registry.dek.cz/dotnet/sdk:9.0 AS build-stage
WORKDIR /build
COPY . .

# Install Playwright in the build stage
RUN dotnet add Deksmart.Application/Deksmart.Application.csproj package Microsoft.Playwright
RUN dotnet restore Deksmart.Api/Deksmart.Api.csproj

# Build the application
RUN dotnet publish "Deksmart.Api/Deksmart.Api.csproj" -c Release -o /app

# Install Playwright CLI and browsers in the build stage
RUN dotnet tool install --global Microsoft.Playwright.CLI
ENV PATH="${PATH}:/root/.dotnet/tools"
RUN playwright install --with-deps chromium
RUN ls -la /root/.cache/ms-playwright/

# Make sure Node.js is installed and has proper permissions
RUN apt-get update && apt-get install -y --no-install-recommends nodejs && \
    chmod -R 777 /root/.cache/ms-playwright

###############################################################
# Final stage
###############################################################

FROM docker-registry.dek.cz/dotnet/aspnet:9.0-bookworm-slim-amd64

# Install dependencies for Playwright
RUN apt-get update && apt-get install -y --no-install-recommends \
    # General dependencies
    curl \
    wget \
    ca-certificates \
    nodejs \
    gnupg \
    # Install Chrome browser
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends google-chrome-stable \
    # Dependencies for Chromium
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    libxcb1 \
    libx11-6 \
    libxext6 \
    libwayland-client0 \
    # Fonts
    fonts-liberation \
    fonts-noto-color-emoji \
    fonts-freefont-ttf \
    # Tools
    xvfb \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY --from=build-stage /app ./
COPY --from=build-stage /root/.cache/ms-playwright /ms-playwright

# Set environment variable to tell Playwright where to find browsers
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# Create necessary directories with proper permissions
RUN mkdir -p /ms-playwright && \
    chmod -R 777 /ms-playwright && \
    mkdir -p /.playwright && \
    chmod -R 777 /.playwright && \
    mkdir -p /app/.playwright && \
    chmod -R 777 /app/.playwright && \
    mkdir -p /app/.playwright/node/linux-x64 && \
    chmod -R 777 /app/.playwright/node/linux-x64

# Copy node executable to the expected location
RUN which node > /dev/null && \
    cp $(which node) /app/.playwright/node/linux-x64/ && \
    chmod 777 /app/.playwright/node/linux-x64/node

# Create symbolic links to make browser paths consistent
RUN ln -s /ms-playwright/chromium-* /ms-playwright/chromium-1161 || true && \
    chmod -R 777 /ms-playwright/chromium-1161 || true

ENTRYPOINT ["dotnet", "Deksmart.Api.dll"]

#docker build -f .\Deksmart.Api\Dockerfile -t desksmart-api:debian . --no-cache --progress=plain
#docker run -it --rm -p 5001:8080 --name desksmart-api desksmart-api:debian
#http://localhost:5001/api/doc