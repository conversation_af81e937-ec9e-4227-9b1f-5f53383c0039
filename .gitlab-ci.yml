image: docker-registry.dek.cz/docker:latest

variables:
    IMAGE_DEKSMART_API: $CI_REGISTRY_IMAGE/api:$CI_COMMIT_REF_SLUG
    IMAGE_DEKSMART_WEB: $CI_REGISTRY_IMAGE/web:$CI_COMMIT_REF_SLUG
    GIT_SUBMODULE_STRATEGY: recursive
    GIT_SUBMODULE_FORCE_HTTPS: "true" # <PERSON><PERSON><PERSON> být pokud je submodule přidaný přes ssh

stages:
    - build
    - test
    - deploy

# Api
build-deksmart-api:
    tags:
        - docker
    stage: build
    script:
        - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
        - docker build -f ./Deksmart.Api/Build.Dockerfile -t $IMAGE_DEKSMART_API . --no-cache
        - docker push $IMAGE_DEKSMART_API
        - docker image rm $IMAGE_DEKSMART_API
    only:
        - master
        - production

# Web
build-deksmart-web:
    tags:
        - docker
    stage: build
    script:
        - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
        - docker build -f ./Deksmart.Ui/Deksmart.Ui.Web/Build.Dockerfile -t $IMAGE_DEKSMART_WEB . --no-cache
        - docker push $IMAGE_DEKSMART_WEB
        - docker image rm $IMAGE_DEKSMART_WEB
    only:
        - master
        - production

# Tests
test-deksmart:
    tags:
        - docker
    stage: test
    image: mcr.microsoft.com/dotnet/sdk:9.0
    variables:
        DOTNET_CLI_TELEMETRY_OPTOUT: "1"
        DOTNET_SKIP_FIRST_TIME_EXPERIENCE: "1"
    script:
        # Restore and build individual projects (excluding problematic MAUI Hybrid project)
        - dotnet restore Deksmart.Domain/Deksmart.Domain.csproj
        - dotnet restore Deksmart.Shared/Deksmart.Shared.csproj  
        - dotnet restore Shared/dek-eshop-apicore/DEK.Eshop.ApiCore/DEK.Eshop.ApiCore.csproj
        - dotnet restore Deksmart.Infrastructure/Deksmart.Infrastructure.csproj
        - dotnet restore Deksmart.Application/Deksmart.Application.csproj
        - dotnet restore Deksmart.Api/Deksmart.Api.csproj
        - dotnet restore Deksmart.Ui/Deksmart.Ui.Model/Deksmart.Ui.Model.csproj
        - dotnet restore Deksmart.Ui/Deksmart.Ui.Shared/Deksmart.Ui.Shared.csproj
        - dotnet restore Deksmart.Ui/Deksmart.Ui.Web.Client/Deksmart.Ui.Web.Client.csproj
        - dotnet restore Deksmart.Ui/Deksmart.Ui.Web/Deksmart.Ui.Web.csproj
        - dotnet restore Tests/Deksmart.Domain.Tests/Deksmart.Domain.Tests.csproj
        - dotnet restore Tests/Deksmart.Application.Tests/Deksmart.Application.Tests.csproj
        - dotnet restore Tests/Deksmart.Api.Tests/Deksmart.Api.Tests.csproj
        - dotnet restore Tests/Deksmart.Integration.Tests/Deksmart.Integration.Tests.csproj
        # Build test projects
        - dotnet build Tests/Deksmart.Domain.Tests/Deksmart.Domain.Tests.csproj --configuration Release --no-restore
        - dotnet build Tests/Deksmart.Application.Tests/Deksmart.Application.Tests.csproj --configuration Release --no-restore
        - dotnet build Tests/Deksmart.Api.Tests/Deksmart.Api.Tests.csproj --configuration Release --no-restore
        - dotnet build Tests/Deksmart.Integration.Tests/Deksmart.Integration.Tests.csproj --configuration Release --no-restore
        # Run all test projects
        - dotnet test Tests/Deksmart.Domain.Tests/Deksmart.Domain.Tests.csproj --configuration Release --no-build --logger "trx;LogFileName=domain_test_results.trx" --logger "console;verbosity=normal"
        - dotnet test Tests/Deksmart.Application.Tests/Deksmart.Application.Tests.csproj --configuration Release --no-build --logger "trx;LogFileName=application_test_results.trx" --logger "console;verbosity=normal"
        - dotnet test Tests/Deksmart.Api.Tests/Deksmart.Api.Tests.csproj --configuration Release --no-build --logger "trx;LogFileName=api_test_results.trx" --logger "console;verbosity=normal"
        - dotnet test Tests/Deksmart.Integration.Tests/Deksmart.Integration.Tests.csproj --configuration Release --no-build --logger "trx;LogFileName=integration_test_results.trx" --logger "console;verbosity=normal"
    artifacts:
        reports:
            junit: "Tests/*/TestResults/*.trx"
        paths:
            - "Tests/*/TestResults/*.trx"
        expire_in: 1 week
        when: always
    only:
        - master
        - production
        - merge_requests

rundeck-job:
    tags:
        - docker
    stage: deploy
    image: docker-registry.dek.cz/rundeck-cli
    variables:
        RD_URL: https://rundeck.dek.cz
    script:
        - /usr/bin/rd run --id 0a683e13-6d2b-4733-9ab0-8660e7f04cfa --follow
    only:
        - master
