@using Deksmart.Ui.Shared.Components.Utility
@using Deksmart.Ui.Shared.Resources

@if (ConfiguratorGridService.ConfiguratorWrapper != null)
{
    <div class="d-flex justify-content-end mt-3 mb-3">
        <button class="btn btn-primary me-2" @onclick="HandleDownloadClick" disabled="@(IsDownloadLoading || IsConfiguratorLoading)">
            @if (IsDownloadLoading)
            {
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            }
            else
            {
                <i class="fas fa-file-pdf"></i>
            }
            @UiSharedResource.DownloadConfigurationPdf
        </button>
        <button class="btn btn-success me-2" @onclick="HandleAddToAgendaClick" disabled="@(IsAddToAgendaLoading || IsConfiguratorLoading)">
            @if (IsAddToAgendaLoading)
            {
                <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            }
            else
            {
                <i class="fas fa-calendar-plus"></i>
            }
            @UiSharedResource.AddToAgendaButtonText
        </button>
        @if (ConfiguratorGridService.ConfiguratorWrapper.HasProducts)
        {
            <button class="btn btn-warning me-2" @onclick="HandleAddToCartClick" disabled="@(IsAddToCartLoading || IsConfiguratorLoading || ConfiguratorGridService.ConfiguratorWrapper?.CanAddToCart != true)">
                @if (IsAddToCartLoading)
                {
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                }
                else
                {
                    <i class="fas fa-shopping-cart"></i>
                }
                @UiSharedResource.AddToCartButtonText
            </button>
        }
        else
        {
            <button class="btn btn-primary" @onclick="HandleInquiryClick" disabled="@(IsInquiryLoading || IsConfiguratorLoading)">
                @if (IsInquiryLoading)
                {
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                }
                else
                {
                    <i class="fas fa-envelope"></i>
                }
                @UiSharedResource.SendInquiryButtonText
            </button>
        }
    </div>

    <InquiryForm IsVisible="@ShowInquiryForm"
        IsVisibleChanged="@((bool value) => ShowInquiryForm = value)"
        OnSubmit="@HandleInquirySubmit" />
}
