﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Represents a reusable expression evaluated by NCalc for dynamic logic in the configurator.
    /// Used for purposes such as visibility, product quantity calculation, validation, or expression-based components.
    /// </summary>
    [Table("configurator_expression", Schema = "dbo")]
    public partial class ConfiguratorExpression : IIntIdEntity
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("expression")]
        [StringLength(2000)]
        public string Expression { get; set; } = null!;
    }
}
