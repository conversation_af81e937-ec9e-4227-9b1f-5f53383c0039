using Microsoft.AspNetCore.Components;
using Deksmart.Ui.Shared.Services;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class HeaderStateServiceTest
    {
        private readonly HeaderStateService _service;

        public HeaderStateServiceTest()
        {
            _service = new HeaderStateService();
        }

        [Fact]
        public void Constructor_InitializesWithDefaultState()
        {
            Assert.Null(_service.Title);
            Assert.False(_service.ShowSaveButton);
            Assert.False(_service.SaveButtonDisabled);
            Assert.Equal(default(EventCallback), _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_WithTitle_UpdatesTitleProperty()
        {
            var title = "Test Header Title";

            _service.UpdateHeader(title);

            Assert.Equal(title, _service.Title);
        }

        [Fact]
        public void UpdateHeader_WithNullTitle_SetsTitleToNull()
        {
            _service.UpdateHeader("Initial Title");
            
            _service.UpdateHeader(null);

            Assert.Null(_service.Title);
        }

        [Fact]
        public void UpdateHeader_WithShowSaveButton_UpdatesShowSaveButtonProperty()
        {
            _service.UpdateHeader("Title", showSaveButton: true);

            Assert.True(_service.ShowSaveButton);
        }

        [Fact]
        public void UpdateHeader_WithoutShowSaveButtonParameter_DefaultsToFalse()
        {
            _service.UpdateHeader("Title");

            Assert.False(_service.ShowSaveButton);
        }

        [Fact]
        public void UpdateHeader_WithSaveButtonDisabled_UpdatesSaveButtonDisabledProperty()
        {
            _service.UpdateHeader("Title", saveButtonDisabled: true);

            Assert.True(_service.SaveButtonDisabled);
        }

        [Fact]
        public void UpdateHeader_WithoutSaveButtonDisabledParameter_DefaultsToFalse()
        {
            _service.UpdateHeader("Title");

            Assert.False(_service.SaveButtonDisabled);
        }

        [Fact]
        public void UpdateHeader_WithSaveCallback_UpdatesSaveCallbackProperty()
        {
            var callback = EventCallback.Factory.Create(this, () => { });

            _service.UpdateHeader("Title", saveCallback: callback);

            Assert.Equal(callback, _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_WithoutSaveCallbackParameter_DefaultsToDefault()
        {
            _service.UpdateHeader("Title");

            Assert.Equal(default(EventCallback), _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_WithAllParameters_UpdatesAllProperties()
        {
            var title = "Complete Header";
            var showSaveButton = true;
            var saveButtonDisabled = true;
            var callback = EventCallback.Factory.Create(this, () => { });

            _service.UpdateHeader(title, showSaveButton, saveButtonDisabled, callback);

            Assert.Equal(title, _service.Title);
            Assert.Equal(showSaveButton, _service.ShowSaveButton);
            Assert.Equal(saveButtonDisabled, _service.SaveButtonDisabled);
            Assert.Equal(callback, _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_RaisesOnChangeEvent()
        {
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.UpdateHeader("Test Title");

            Assert.True(eventRaised);
        }

        [Fact]
        public void UpdateHeader_MultipleSubscribers_AllGetNotified()
        {
            bool subscriber1Called = false;
            bool subscriber2Called = false;
            _service.OnChange += () => subscriber1Called = true;
            _service.OnChange += () => subscriber2Called = true;

            _service.UpdateHeader("Test Title");

            Assert.True(subscriber1Called);
            Assert.True(subscriber2Called);
        }

        [Fact]
        public void ClearHeader_ResetsAllProperties()
        {
            // First set some values
            var callback = EventCallback.Factory.Create(this, () => { });
            _service.UpdateHeader("Some Title", true, true, callback);

            _service.ClearHeader();

            Assert.Null(_service.Title);
            Assert.False(_service.ShowSaveButton);
            Assert.False(_service.SaveButtonDisabled);
            Assert.Equal(default(EventCallback), _service.SaveCallback);
        }

        [Fact]
        public void ClearHeader_RaisesOnChangeEvent()
        {
            bool eventRaised = false;
            _service.OnChange += () => eventRaised = true;

            _service.ClearHeader();

            Assert.True(eventRaised);
        }

        [Fact]
        public void ClearHeader_MultipleSubscribers_AllGetNotified()
        {
            bool subscriber1Called = false;
            bool subscriber2Called = false;
            _service.OnChange += () => subscriber1Called = true;
            _service.OnChange += () => subscriber2Called = true;

            _service.ClearHeader();

            Assert.True(subscriber1Called);
            Assert.True(subscriber2Called);
        }

        [Fact]
        public void ClearHeader_AfterUpdateHeader_ResetsState()
        {
            // Setup initial state
            var callback = EventCallback.Factory.Create(this, () => { });
            _service.UpdateHeader("Initial Title", true, true, callback);
            
            // Verify setup
            Assert.Equal("Initial Title", _service.Title);
            Assert.True(_service.ShowSaveButton);
            Assert.True(_service.SaveButtonDisabled);
            Assert.Equal(callback, _service.SaveCallback);

            _service.ClearHeader();

            Assert.Null(_service.Title);
            Assert.False(_service.ShowSaveButton);
            Assert.False(_service.SaveButtonDisabled);
            Assert.Equal(default(EventCallback), _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_OverwritesPreviousState()
        {
            var callback1 = EventCallback.Factory.Create(this, () => { });
            var callback2 = EventCallback.Factory.Create(this, () => { });

            _service.UpdateHeader("First Title", true, true, callback1);
            _service.UpdateHeader("Second Title", false, false, callback2);

            Assert.Equal("Second Title", _service.Title);
            Assert.False(_service.ShowSaveButton);
            Assert.False(_service.SaveButtonDisabled);
            Assert.Equal(callback2, _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_EventRaisedForEachCall()
        {
            int eventCount = 0;
            _service.OnChange += () => eventCount++;

            _service.UpdateHeader("Title 1");
            _service.UpdateHeader("Title 2");
            _service.UpdateHeader("Title 3");

            Assert.Equal(3, eventCount);
        }

        [Fact]
        public void ClearHeader_EventRaisedForEachCall()
        {
            int eventCount = 0;
            _service.OnChange += () => eventCount++;

            _service.ClearHeader();
            _service.ClearHeader();

            Assert.Equal(2, eventCount);
        }

        [Fact]
        public void UpdateHeader_EmptyString_SetsEmptyTitle()
        {
            _service.UpdateHeader("");

            Assert.Equal("", _service.Title);
        }

        [Fact]
        public void UpdateHeader_WhitespaceString_SetsWhitespaceTitle()
        {
            var whitespaceTitle = "   ";
            
            _service.UpdateHeader(whitespaceTitle);

            Assert.Equal(whitespaceTitle, _service.Title);
        }

        [Fact]
        public void EventSubscription_UnsubscribeWorks()
        {
            bool eventRaised = false;
            Action handler = () => eventRaised = true;
            
            _service.OnChange += handler;
            _service.UpdateHeader("Test");
            Assert.True(eventRaised);

            eventRaised = false;
            _service.OnChange -= handler;
            _service.UpdateHeader("Test 2");
            Assert.False(eventRaised);
        }

        [Fact]
        public void StateProperties_AreReadOnlyFromOutside()
        {
            // This test verifies that Title, ShowSaveButton, SaveButtonDisabled, and SaveCallback
            // have private setters and can only be modified through UpdateHeader/ClearHeader
            var titleProperty = typeof(HeaderStateService).GetProperty("Title");
            var showSaveButtonProperty = typeof(HeaderStateService).GetProperty("ShowSaveButton");
            var saveButtonDisabledProperty = typeof(HeaderStateService).GetProperty("SaveButtonDisabled");
            var saveCallbackProperty = typeof(HeaderStateService).GetProperty("SaveCallback");

            Assert.NotNull(titleProperty);
            Assert.NotNull(showSaveButtonProperty);
            Assert.NotNull(saveButtonDisabledProperty);
            Assert.NotNull(saveCallbackProperty);

            // Verify setters are private
            Assert.False(titleProperty.CanWrite && titleProperty.SetMethod?.IsPublic == true);
            Assert.False(showSaveButtonProperty.CanWrite && showSaveButtonProperty.SetMethod?.IsPublic == true);
            Assert.False(saveButtonDisabledProperty.CanWrite && saveButtonDisabledProperty.SetMethod?.IsPublic == true);
            Assert.False(saveCallbackProperty.CanWrite && saveCallbackProperty.SetMethod?.IsPublic == true);
        }

        [Fact]
        public void UpdateHeader_LongTitle_HandledCorrectly()
        {
            var longTitle = new string('A', 1000);

            _service.UpdateHeader(longTitle);

            Assert.Equal(longTitle, _service.Title);
        }

        [Fact]
        public void EventCallback_DefaultValue_IsHandledCorrectly()
        {
            _service.UpdateHeader("Title", true);

            Assert.Equal(default(EventCallback), _service.SaveCallback);
        }

        [Fact]
        public void UpdateHeader_SaveButtonDisabledWithShowSaveButtonFalse_BothStatesSet()
        {
            _service.UpdateHeader("Title", false, true);

            Assert.False(_service.ShowSaveButton);
            Assert.True(_service.SaveButtonDisabled);
        }

        [Fact]
        public void UpdateHeader_MultipleCallsWithDifferentDisabledStates_UpdatesCorrectly()
        {
            _service.UpdateHeader("Title", true, false);
            Assert.False(_service.SaveButtonDisabled);

            _service.UpdateHeader("Title", true, true);
            Assert.True(_service.SaveButtonDisabled);

            _service.UpdateHeader("Title", true, false);
            Assert.False(_service.SaveButtonDisabled);
        }

        [Fact]
        public void UpdateHeader_SameValueMultipleTimes_StillRaisesEvents()
        {
            int eventCount = 0;
            _service.OnChange += () => eventCount++;

            _service.UpdateHeader("Same Title");
            _service.UpdateHeader("Same Title");
            _service.UpdateHeader("Same Title");

            Assert.Equal(3, eventCount);
            Assert.Equal("Same Title", _service.Title);
        }

        [Fact]
        public void ConsecutiveClearHeader_BothRaiseEvents()
        {
            int eventCount = 0;
            _service.OnChange += () => eventCount++;

            // Setup some state first
            _service.UpdateHeader("Title");
            eventCount = 0; // Reset counter

            _service.ClearHeader();
            _service.ClearHeader();

            Assert.Equal(2, eventCount);
            Assert.Null(_service.Title);
        }
    }
}