#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM docker-registry.dek.cz/dotnet/aspnet:8.0-bookworm-slim-amd64 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM docker-registry.dek.cz/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["DEK.Eshop.ApiCore.Sandbox/DEK.Eshop.ApiCore.Sandbox.csproj", "DEK.Eshop.ApiCore.Sandbox/"]
RUN dotnet restore "DEK.Eshop.ApiCore.Sandbox/DEK.Eshop.ApiCore.Sandbox.csproj"
COPY . .
WORKDIR "/src/DEK.Eshop.ApiCore.Sandbox"
RUN dotnet build "DEK.Eshop.ApiCore.Sandbox.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "DEK.Eshop.ApiCore.Sandbox.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "DEK.Eshop.ApiCore.Sandbox.dll"]
