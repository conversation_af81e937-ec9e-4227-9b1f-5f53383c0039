using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Service;
using Moq;

namespace Deksmart.Domain.Tests
{
    public class FieldValueMatcherTest
    {
        private readonly Mock<IConfiguratorExpressionCalculator> _calculatorMock;
        private readonly Mock<IConfiguratorValidationService> _validationServiceMock;
        private readonly FieldValueMatcherService _sut;

        public FieldValueMatcherTest()
        {
            _calculatorMock = new Mock<IConfiguratorExpressionCalculator>();
            _validationServiceMock = new Mock<IConfiguratorValidationService>();
            _sut = new FieldValueMatcherService(_calculatorMock.Object, _validationServiceMock.Object);
        }

        [Fact]
        public void MatchExpressionFields_WithValidExpression_ReturnsCalculatedValue()
        {
            // Arrange
            var clientFieldValues = new List<DirectValue>
            {
                new() { Id = 1, Ident = "F1", Expression = new ConfiguratorExpression { Id = 1, Expression = "x + y" } }
            };

            var matchedFields = new List<ValueIdent>
            {
                new() { Ident = "x", Value = 5 },
                new() { Ident = "y", Value = 10 }
            };

            _calculatorMock.Setup(x => x.GetValue(1, "x + y", matchedFields))
                .Returns(15);

            // Act
            var result = _sut.MatchExpressionFields(clientFieldValues, matchedFields);

            // Assert
            Assert.Single(result);
            Assert.Equal("F1", result[0].Ident);
            Assert.Equal(15, result[0].Value);
        }

        [Fact]
        public void MatchFieldDirectValues_WithValidValue_ReturnsValue()
        {
            // Arrange
            var clientFieldValues = new Dictionary<int, decimal?>
            {
                { 1, 50 }
            };

            var dbFields = new List<DirectValue>
            {
                new() { Id = 1, Ident = "F1", Min = 0, Max = 100, DefaultValue = 0 }
            };

            var validation = new ValidationResult();

            // Act
            var result = _sut.MatchFieldDirectValues(clientFieldValues, dbFields, validation);

            // Assert
            Assert.Single(result);
            Assert.Equal("F1", result[0].Ident);
            Assert.Equal(50, result[0].Value);
            _validationServiceMock.Verify(x => x.ValidateDirectValueIsWithinBounds(dbFields[0], 50, validation), Times.Once);
        }

        [Fact]
        public void MatchFieldDirectValues_WithMissingValue_ReturnsDefaultValue()
        {
            // Arrange
            var clientFieldValues = new Dictionary<int, decimal?>();
            var dbFields = new List<DirectValue>
            {
                new() { Id = 1, Ident = "F1", Min = 0, Max = 100, DefaultValue = 42 }
            };

            var validation = new ValidationResult();

            // Act
            var result = _sut.MatchFieldDirectValues(clientFieldValues, dbFields, validation);

            // Assert
            Assert.Single(result);
            Assert.Equal("F1", result[0].Ident);
            Assert.Equal(42, result[0].Value);
            _validationServiceMock.Verify(x => x.ValidateDirectValueIsWithinBounds(It.IsAny<DirectValue>(), It.IsAny<decimal>(), It.IsAny<ValidationResult>()), Times.Never);
        }

        [Fact]
        public void MatchFieldMultipleChoiceValues_WithValidValue_ReturnsValue()
        {
            // Arrange
            var clientFieldValues = new Dictionary<int, decimal?>
            {
                { 1, 2 }
            };

            var dbFieldValues = new List<MultipleChoiceValue>
            {
                new() { Id = 1, Ident = "MC1", DefaultValue = 1, Values = new List<decimal> { 1, 2, 3 } }
            };

            var validation = new ValidationResult();

            // Act
            var result = _sut.MatchFieldMultipleChoiceValues(clientFieldValues, dbFieldValues, validation);

            // Assert
            Assert.Single(result);
            Assert.Equal("MC1", result[0].Ident);
            Assert.Equal(2, result[0].Value);
            _validationServiceMock.Verify(x => x.ValidateMultipleChoiceValueHasValidValue(dbFieldValues[0], 1, 2, validation), Times.Once);
        }

        [Fact]
        public void MatchFieldMultipleChoiceValues_WithMissingValue_ReturnsDefaultValue()
        {
            // Arrange
            var clientFieldValues = new Dictionary<int, decimal?>();
            var dbFieldValues = new List<MultipleChoiceValue>
            {
                new() { Id = 1, Ident = "MC1", DefaultValue = 42, Values = new List<decimal> { 1, 2, 3 } }
            };

            var validation = new ValidationResult();

            // Act
            var result = _sut.MatchFieldMultipleChoiceValues(clientFieldValues, dbFieldValues, validation);

            // Assert
            Assert.Single(result);
            Assert.Equal("MC1", result[0].Ident);
            Assert.Equal(42, result[0].Value);
            _validationServiceMock.Verify(x => x.ValidateMultipleChoiceValueHasValidValue(It.IsAny<MultipleChoiceValue>(), It.IsAny<int>(), It.IsAny<decimal>(), It.IsAny<ValidationResult>()), Times.Never);
        }

        [Fact]
        public void MatchExpressionFields_WithMultipleExpressions_ReturnsAllCalculatedValues()
        {
            // Arrange
            var clientFieldValues = new List<DirectValue>
            {
                new() { Id = 1, Ident = "F1", Expression = new ConfiguratorExpression { Id = 1, Expression = "x + y" } },
                new() { Id = 2, Ident = "F2", Expression = new ConfiguratorExpression { Id = 2, Expression = "x * y" } }
            };

            var matchedFields = new List<ValueIdent>
            {
                new() { Ident = "x", Value = 5 },
                new() { Ident = "y", Value = 10 }
            };

            _calculatorMock.Setup(x => x.GetValue(1, "x + y", matchedFields))
                .Returns(15);
            _calculatorMock.Setup(x => x.GetValue(2, "x * y", matchedFields))
                .Returns(50);

            // Act
            var result = _sut.MatchExpressionFields(clientFieldValues, matchedFields);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Contains(result, r => r.Ident == "F1" && r.Value == 15);
            Assert.Contains(result, r => r.Ident == "F2" && r.Value == 50);
        }

        [Fact]
        public void MatchFieldDirectValues_WithMultipleFields_ReturnsAllValues()
        {
            // Arrange
            var clientFieldValues = new Dictionary<int, decimal?>
            {
                { 1, 50 },
                { 2, 75 }
            };

            var dbFields = new List<DirectValue>
            {
                new() { Id = 1, Ident = "F1", Min = 0, Max = 100, DefaultValue = 0 },
                new() { Id = 2, Ident = "F2", Min = 0, Max = 100, DefaultValue = 0 }
            };

            var validation = new ValidationResult();

            // Act
            var result = _sut.MatchFieldDirectValues(clientFieldValues, dbFields, validation);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Contains(result, r => r.Ident == "F1" && r.Value == 50);
            Assert.Contains(result, r => r.Ident == "F2" && r.Value == 75);
            _validationServiceMock.Verify(x => x.ValidateDirectValueIsWithinBounds(It.IsAny<DirectValue>(), It.IsAny<decimal>(), validation), Times.Exactly(2));
        }
    }
}
