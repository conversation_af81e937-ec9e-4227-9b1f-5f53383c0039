﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="Microsoft.Playwright" Version="1.52.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Deksmart.Domain\Deksmart.Domain.csproj" />
    <ProjectReference Include="..\Deksmart.Infrastructure\Deksmart.Infrastructure.csproj" />
    <ProjectReference Include="..\Deksmart.Shared\Deksmart.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resource\DeksmartApplicationResource.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>DeksmartApplicationResource.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resource\DeksmartApplicationResource.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>DeksmartApplicationResource.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
</Project>
