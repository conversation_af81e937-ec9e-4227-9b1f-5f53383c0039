namespace Deksmart.Shared.Enum
{
    /// <summary>
    /// Represents the collapse/expand state of a configurator field category for data transfer and UI.
    /// </summary>
    public enum CategoryCollapseStateDto : short
    {
        /// <summary>
        /// The category cannot be collapsed or expanded.
        /// </summary>
        NotCollapsible = 0,
        /// <summary>
        /// The category is expanded (visible).
        /// </summary>
        Expanded = 1,
        /// <summary>
        /// The category is collapsed (hidden).
        /// </summary>
        Collapsed = 2
    }
} 