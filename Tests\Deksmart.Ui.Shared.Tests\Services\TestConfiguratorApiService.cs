using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Resources;
using Deksmart.Ui.Shared.Services;
using Microsoft.Extensions.Logging;

namespace Deksmart.Ui.Shared.Tests.Services
{
    /// <summary>
    /// Test implementation of ConfiguratorApiService that uses ITestHttpService for mocking.
    /// This class provides the same functionality as ConfiguratorApiService but accepts
    /// a mockable interface for HTTP operations, enabling comprehensive unit testing.
    /// Implements the same business logic including logging, error handling, and notification management.
    /// </summary>
    public class TestConfiguratorApiService : IConfiguratorApiService
    {
        private readonly ITestHttpService _httpService;
        private readonly ILogger<ConfiguratorApiService> _logger;
        private readonly NotificationService _notificationService;

        /// <summary>
        /// Initializes a new instance of TestConfiguratorApiService with the specified dependencies
        /// </summary>
        /// <param name="httpService">The HTTP service interface for making API calls</param>
        /// <param name="logger">Logger for recording service operations</param>
        /// <param name="notificationService">Service for displaying user notifications</param>
        public TestConfiguratorApiService(
            ITestHttpService httpService,
            ILogger<ConfiguratorApiService> logger,
            NotificationService notificationService)
        {
            _httpService = httpService;
            _logger = logger;
            _notificationService = notificationService;
        }

        /// <inheritdoc />
        public async Task<ConfiguratorDto?> GetConfiguratorAsync(int id, CancellationToken cancellationToken)
        {
            return await _httpService.GetAsync<ConfiguratorDto>(id.ToString(), cancellationToken);
        }

        /// <inheritdoc />
        public async Task<ConfiguratorDto?> GetFilteredConfiguratorAsync(int id, ConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            return await _httpService.PostAsync<ConfiguratorDto>(
                string.Format(UiSharedResource.LoadConfiguratorUrl, id), 
                state, 
                cancellationToken);
        }

        /// <inheritdoc />
        public async Task<CompositeConfiguratorResponseDto?> ProcessCompositeConfiguratorAsync(int id, CompositeConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            return await _httpService.PostAsync<CompositeConfiguratorResponseDto>(
                string.Format(UiSharedResource.ProcessCompositeConfiguratorUrl, id), 
                state, 
                cancellationToken);
        }

        /// <inheritdoc />
        public async Task<ConfiguratorDto?> GetConfiguratorForPresetAsync(int id, string preset, CancellationToken cancellationToken)
        {
            return await _httpService.GetAsync<ConfiguratorDto>(
                string.Format(UiSharedResource.LoadPresetUrl, id, preset), 
                cancellationToken);
        }

        /// <inheritdoc />
        public async Task<Guid?> SavePresetAsync(int configuratorId, ConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            return await _httpService.PostAsync<Guid>(
                string.Format(UiSharedResource.SavePresetUrl, configuratorId), 
                state, 
                cancellationToken);
        }

        /// <inheritdoc />
        public async Task<ProductDetailsDto?> LoadProductDetailAsync(string productCode, CancellationToken cancellationToken)
        {
            return await _httpService.GetAsync<ProductDetailsDto>($"product-detail/{productCode}", cancellationToken);
        }

        /// <inheritdoc />
        public async Task<ProductPriceCalculationResponseDto?> CalculateProductPricesAsync(ProductPriceCalculationRequestDto request, CancellationToken cancellationToken)
        {
            var result = await _httpService.PostAsync<ProductPriceCalculationResponseDto>(
                UiSharedResource.CalculateProductPricesUrl,
                request,
                cancellationToken);

            if (result != null)
            {
                _logger.LogInformation($"Calculated product prices for {request.CurrentProductCode}");
            }

            return result;
        }

        /// <inheritdoc />
        public async Task<(byte[]? pdfBytes, string fileName)> GenerateConfigurationPdfAsync(int configuratorId, ConfiguratorStateDto state, CancellationToken cancellationToken)
        {
            var (pdfBytes, fileName) = await _httpService.PostBinaryAsync(
                string.Format(UiSharedResource.GenerateConfigurationPdfUrl, configuratorId),
                state,
                cancellationToken);

            if (pdfBytes == null)
            {
                _notificationService.ShowError(UiSharedResource.FailedToGeneratePdf);
                return (null, "configuration.pdf");
            }

            return (pdfBytes, fileName ?? "configuration.pdf");
        }

        /// <inheritdoc />
        public async Task<bool> SendInquiryAsync(int configuratorId, EmailConfiguratorStateDto emailState, CancellationToken cancellationToken)
        {
            var success = await _httpService.PostAsync(
                string.Format(UiSharedResource.SendInquiryUrl, configuratorId),
                emailState,
                cancellationToken);

            if (success)
            {
                _notificationService.ShowSuccess(UiSharedResource.SendInquirySuccess);
            }

            return success;
        }
    }
}