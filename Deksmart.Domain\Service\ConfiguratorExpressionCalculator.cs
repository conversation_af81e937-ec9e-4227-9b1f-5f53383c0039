using System.Collections.Concurrent;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;

namespace Deksmart.Domain.Service
{
    public interface IConfiguratorExpressionCalculator
    {
        /// <summary>
        /// Calculates field values and filters out fields and categories that are not visible based on the provided parameter values.
        /// </summary>
        /// <param name="configurator">The configurator entity to process.</param>
        /// <param name="matchedFieldsWithValue">The parameter values used for calculation and filtering.</param>
        void CalculateAndFilterFields(Configurator configurator, IEnumerable<ValueIdent> matchedFieldsWithValue);

        /// <summary>
        /// Filters out compositions and products that are not visible based on the provided parameter values, and calculates product quantities.
        /// </summary>
        /// <param name="configurator">The configurator entity to process.</param>
        /// <param name="matchedFieldsWithValue">The parameter values used for filtering and calculation.</param>
        void FilterProducts(Configurator configurator, IEnumerable<ValueIdent> matchedFieldsWithValue);

        /// <summary>
        /// Evaluates an expression using the provided parameter values and caches the result by expression ID.
        /// </summary>
        /// <param name="id">The unique identifier for the expression (used for caching).</param>
        /// <param name="expression">The expression to evaluate.</param>
        /// <param name="valueIdents">The parameter values to use in the expression.</param>
        /// <returns>The result of the evaluated expression as a decimal.</returns>
        decimal GetValue(int id, string expression, IEnumerable<ValueIdent> valueIdents);

        /// <summary>
        /// Clears the internal cache of evaluated expressions.
        /// </summary>
        void ClearCache();
    }

    public class ConfiguratorExpressionCalculator : IConfiguratorExpressionCalculator
    {
        private readonly ICalculator _calculator;
        private readonly ConcurrentDictionary<int, decimal> _expressionCache = new();

        public ConfiguratorExpressionCalculator(ICalculator calculator)
        {
            _calculator = calculator;
        }

        public decimal GetValue(int id, string expression, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            if (!_expressionCache.TryGetValue(id, out var evaluatedValue))
            {
                var (result, validation) = _calculator.CalculateExpression(expression, matchedFieldsWithValue);
                evaluatedValue = result;
                _expressionCache[id] = evaluatedValue;
                
                // TODO: Consider how to handle validation errors from expression calculation
                // For now, we continue with the existing behavior (return 0 on error)
            }

            return evaluatedValue;
        }

        public void FilterProducts(Configurator configurator, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            _expressionCache.Clear();

            foreach (var composition in configurator.ConfiguratorCompositions.ToArray())
            {
                if (IsVisible(composition.Visibility, matchedFieldsWithValue))
                {
                    CalculateComposition(composition, matchedFieldsWithValue);

                    if (composition.ConfiguratorProducts.Count == 0)
                        configurator.ConfiguratorCompositions.Remove(composition);
                }
                else
                {
                    configurator.ConfiguratorCompositions.Remove(composition);
                }
            }
        }

        /// <summary>
        /// Calculates field values and filters out fields and categories that are not visible based on the provided parameter values.
        /// </summary>
        /// <param name="configurator">The configurator entity to process.</param>
        /// <param name="matchedFieldsWithValue">The parameter values used for calculation and filtering.</param>
        public void CalculateAndFilterFields(Configurator configurator, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            foreach (var category in configurator.ConfiguratorFieldCategories.ToArray())
            {
                if (IsVisible(category.Visibility, matchedFieldsWithValue))
                {
                    CalculateCategory(category, matchedFieldsWithValue);
                    if (!category.ConfiguratorFields.Any())
                        configurator.ConfiguratorFieldCategories.Remove(category);
                }
                else
                {
                    configurator.ConfiguratorFieldCategories.Remove(category);
                }
            }
        }

        /// <summary>
        /// Clears the internal cache of evaluated expressions.
        /// </summary>
        public void ClearCache()
        {
            _expressionCache.Clear();
        }

        private ConfiguratorComposition CalculateComposition(ConfiguratorComposition composition, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            foreach (var product in composition.ConfiguratorProducts.ToArray())
            {
                if (IsVisible(product.Visibility, matchedFieldsWithValue))
                {
                    if (product.Quantity != null)
                    {
                        var productAmount = GetValue(product.Quantity.Id, product.Quantity.Expression, matchedFieldsWithValue);
                        product.CalculatedAmount = productAmount * product.ProductVolume;
                    }
                }
                else
                {
                    composition.ConfiguratorProducts.Remove(product);
                }
            }

            return composition;
        }

        private void CalculateCategory(ConfiguratorFieldCategory category, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            foreach (var field in category.ConfiguratorFields.ToArray())
            {
                if (IsVisible(field.Visibility, matchedFieldsWithValue))
                {
                    var isMultipleChoice = field.ConfiguratorFieldValues.Count > 0;
                    CalculateField(field, matchedFieldsWithValue);

                    if (isMultipleChoice && !field.ConfiguratorFieldValues.Any())
                        category.ConfiguratorFields.Remove(field);
                }
                else
                {
                    category.ConfiguratorFields.Remove(field);
                }
            }
        }

        private void CalculateField(ConfiguratorField field, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            decimal value = 0;
            if (field.Expression != null)
                value = GetValue(field.Expression.Id, field.Expression.Expression, matchedFieldsWithValue);
            else
            {
                var matched = matchedFieldsWithValue.FirstOrDefault(f => f.Ident == field.Ident)?.Value;
                if(matched == null)
                {
                    value = field.DefaultValue;
                    foreach(var fieldValue in matchedFieldsWithValue.Where(f => f.Ident == field.Ident))
                    {
                        fieldValue.Value = value;
                    }
                }
                else
                {
                    value = matched.Value;
                }
            }

            field.Value = value;

            var isMultipleChoice = field.ConfiguratorFieldValues.Count > 0;

            if (!isMultipleChoice)
                return;

            foreach (var fieldValue in field.ConfiguratorFieldValues.ToArray())
            {
                if (!IsVisible(fieldValue.Visibility, matchedFieldsWithValue))
                    field.ConfiguratorFieldValues.Remove(fieldValue);
            }

            ChangeFilteredValueIfNotVisibible(field, matchedFieldsWithValue);
        }

        private void ChangeFilteredValueIfNotVisibible(ConfiguratorField field, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            if (!field.ConfiguratorFieldValues.Any(d => d.NumericValue == field.Value))
            {
                field.Value = field.DefaultValue;
                matchedFieldsWithValue.First(f => f.Ident == field.Ident).Value = field.Value;
            }
        }

        private bool IsVisible(ConfiguratorExpression? visibilityExpression, IEnumerable<ValueIdent> matchedFieldsWithValue)
        {
            if (visibilityExpression == null)
                return true;

            var visibility = GetValue(visibilityExpression.Id, visibilityExpression.Expression, matchedFieldsWithValue);
            return visibility > 0;
        }
    }
}
