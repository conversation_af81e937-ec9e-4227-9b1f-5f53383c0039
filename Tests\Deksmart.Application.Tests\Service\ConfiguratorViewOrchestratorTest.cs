using Deksmart.Application.Service;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Shared.Dto;
using Microsoft.Extensions.Logging;
using Moq;

namespace Deksmart.Application.Tests.Service
{
    public class ConfiguratorViewOrchestratorTest
    {
        private readonly Mock<IConfiguratorService> _configuratorServiceMock;
        private readonly Mock<IConfiguratorPresetService> _presetServiceMock;
        private readonly Mock<IConfiguratorViewGenerator> _viewGeneratorMock;
        private readonly Mock<ILogger<ConfiguratorViewOrchestrator>> _loggerMock;
        private readonly IConfiguratorViewOrchestrator _orchestrator;

        public ConfiguratorViewOrchestratorTest()
        {
            _configuratorServiceMock = new Mock<IConfiguratorService>();
            _presetServiceMock = new Mock<IConfiguratorPresetService>();
            _viewGeneratorMock = new Mock<IConfiguratorViewGenerator>();
            _loggerMock = new Mock<ILogger<ConfiguratorViewOrchestrator>>();

            _orchestrator = new ConfiguratorViewOrchestrator(
                _configuratorServiceMock.Object,
                _presetServiceMock.Object,
                _viewGeneratorMock.Object,
                _loggerMock.Object);
        }

        [Fact]
        public async Task GenerateHtmlFromStateAsync_WithValidState_ReturnsHtml()
        {
            // Arrange
            var configuratorId = 1;
            var fieldValues = new List<ClientFieldValueDto>
            {
                new ClientFieldValueDto { FieldId = 1, Value = 10 }
            };
            var selectedProducts = new List<ClientProductValueDto>
            {
                new ClientProductValueDto { ProductId = 1, Amount = 2 }
            };
            var state = new ConfiguratorStateDto
            {
                FieldValues = fieldValues,
                SelectedProducts = selectedProducts
            };
            var configurator = new Configurator { Title = "Test Configurator" };
            var expectedHtml = "<html>Test</html>";

            var validation = new ValidationResult();
            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .ReturnsAsync((configurator, validation));

            _viewGeneratorMock
                .Setup(x => x.GenerateHtml(configurator, null))
                .Returns(expectedHtml);

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromStateAsync(configuratorId, state);

            // Assert
            Assert.Equal(expectedHtml, html);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GenerateHtmlFromStateAsync_WithEmailState_ReturnsHtml()
        {
            // Arrange
            var configuratorId = 1;
            var fieldValues = new List<ClientFieldValueDto>
            {
                new ClientFieldValueDto { FieldId = 1, Value = 10 }
            };
            var selectedProducts = new List<ClientProductValueDto>
            {
                new ClientProductValueDto { ProductId = 1, Amount = 2 }
            };
            var contactInfo = new ContactInfoDto
            {
                Name = "John Doe",
                Email = "<EMAIL>"
            };
            var state = new EmailConfiguratorStateDto
            {
                FieldValues = fieldValues,
                SelectedProducts = selectedProducts,
                ContactInfo = contactInfo
            };
            var configurator = new Configurator { Title = "Test Configurator" };
            var expectedHtml = "<html>Test</html>";

            var validation = new ValidationResult();
            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .ReturnsAsync((configurator, validation));

            _viewGeneratorMock
                .Setup(x => x.GenerateHtml(configurator, contactInfo))
                .Returns(expectedHtml);

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromStateAsync(configuratorId, state);

            // Assert
            Assert.Equal(expectedHtml, html);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GenerateHtmlFromStateAsync_WhenConfiguratorServiceFails_ReturnsError()
        {
            // Arrange
            var configuratorId = 1;
            var state = new ConfiguratorStateDto();
            var errorMessage = "Configurator not found";

            var validation = new ValidationResult();
            validation.AddError(errorMessage);

            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .Returns(Task.FromResult<(Configurator? configurator, ValidationResult validation)>((null, validation)));

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromStateAsync(configuratorId, state);

            // Assert
            Assert.Null(html);
            Assert.True(error.HasErrors);
            Assert.Contains(errorMessage, error.GetErrors());
        }

        [Fact]
        public async Task GenerateHtmlFromPresetAsync_WithValidPreset_ReturnsHtml()
        {
            // Arrange
            var configuratorId = 1;
            var presetId = Guid.NewGuid();
            var state = new ConfiguratorStateDto
            {
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>()
            };
            var configurator = new Configurator { Title = "Test Configurator" };
            var expectedHtml = "<html>Test</html>";

            _presetServiceMock
                .Setup(x => x.GetConfiguratorStateAsync(presetId))
                .ReturnsAsync(state);

            var validation = new ValidationResult();
            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .ReturnsAsync((configurator, validation));

            _viewGeneratorMock
                .Setup(x => x.GenerateHtml(configurator, null))
                .Returns(expectedHtml);

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromPresetAsync(configuratorId, presetId);

            // Assert
            Assert.Equal(expectedHtml, html);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GenerateHtmlFromPresetAsync_WithEmailPreset_ReturnsHtml()
        {
            // Arrange
            var configuratorId = 1;
            var presetId = Guid.NewGuid();
            var contactInfo = new ContactInfoDto
            {
                Name = "John Doe",
                Email = "<EMAIL>"
            };
            var state = new EmailConfiguratorStateDto
            {
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>(),
                ContactInfo = contactInfo
            };
            var configurator = new Configurator { Title = "Test Configurator" };
            var expectedHtml = "<html>Test</html>";

            _presetServiceMock
                .Setup(x => x.GetConfiguratorStateAsync(presetId))
                .ReturnsAsync(state);

            var validation = new ValidationResult();
            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .ReturnsAsync((configurator, validation));

            _viewGeneratorMock
                .Setup(x => x.GenerateHtml(configurator, contactInfo))
                .Returns(expectedHtml);

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromPresetAsync(configuratorId, presetId);

            // Assert
            Assert.Equal(expectedHtml, html);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GenerateHtmlFromPresetAsync_WhenPresetDoesNotExist_ReturnsError()
        {
            // Arrange
            var configuratorId = 1;
            var presetId = Guid.NewGuid();

            _presetServiceMock
                .Setup(x => x.GetConfiguratorStateAsync(presetId))
                .ReturnsAsync((ConfiguratorStateDto?)null);

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromPresetAsync(configuratorId, presetId);

            // Assert
            Assert.Null(html);
            Assert.True(error.HasErrors);
            Assert.Contains($"Preset with ID {presetId} does not exist", error.GetErrors());
        }

        [Fact]
        public async Task GenerateHtmlFromPresetAsync_WhenConfiguratorServiceFails_ReturnsError()
        {
            // Arrange
            var configuratorId = 1;
            var presetId = Guid.NewGuid();
            var state = new ConfiguratorStateDto
            {
                FieldValues = new List<ClientFieldValueDto>(),
                SelectedProducts = new List<ClientProductValueDto>()
            };
            var errorMessage = "Configurator not found";

            _presetServiceMock
                .Setup(x => x.GetConfiguratorStateAsync(presetId))
                .ReturnsAsync(state);

            var validation = new ValidationResult();
            validation.AddError(errorMessage);

            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .Returns(Task.FromResult<(Configurator? configurator, ValidationResult validation)>((null, validation)));

            // Act
            var (html, error) = await _orchestrator.GenerateHtmlFromPresetAsync(configuratorId, presetId);

            // Assert
            Assert.Null(html);
            Assert.True(error.HasErrors);
            Assert.Contains(errorMessage, error.GetErrors());
        }

        [Fact]
        public async Task GeneratePdfFromStateAsync_WithValidState_ReturnsPdf()
        {
            // Arrange
            var configuratorId = 1;
            var fieldValues = new List<ClientFieldValueDto>
            {
                new ClientFieldValueDto { FieldId = 1, Value = 10 }
            };
            var selectedProducts = new List<ClientProductValueDto>
            {
                new ClientProductValueDto { ProductId = 1, Amount = 2 }
            };
            var state = new ConfiguratorStateDto
            {
                FieldValues = fieldValues,
                SelectedProducts = selectedProducts
            };
            var configurator = new Configurator { Title = "Test Configurator" };
            var html = "<html>Test</html>";
            var expectedPdf = new byte[] { 0x25, 0x50, 0x44, 0x46 };

            var validation = new ValidationResult();
            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .ReturnsAsync((configurator, validation));

            _viewGeneratorMock
                .Setup(x => x.GenerateHtml(configurator, null))
                .Returns(html);

            var pdfValidation = new ValidationResult();
            _viewGeneratorMock
                .Setup(x => x.GeneratePdfFromHtmlAsync(html))
                .ReturnsAsync((expectedPdf, pdfValidation));

            // Act
            var (pdf, title, error) = await _orchestrator.GeneratePdfFromStateAsync(configuratorId, state);

            // Assert
            Assert.Equal(expectedPdf, pdf);
            Assert.Equal("Test Configurator", title);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GeneratePdfFromPresetAsync_WithValidPreset_ReturnsPdf()
        {
            // Arrange
            var configuratorId = 1;
            var presetId = Guid.NewGuid();
            var state = new ConfiguratorStateDto
            {
                FieldValues = new List<ClientFieldValueDto>
                {
                    new ClientFieldValueDto { FieldId = 1, Value = 10.5m }
                },
                SelectedProducts = new List<ClientProductValueDto>
                {
                    new ClientProductValueDto { ProductId = 1, Amount = 2 }
                }
            };
            var configurator = new Configurator { Title = "Test Configurator" };
            var html = "<html>Test</html>";
            var expectedPdf = new byte[] { 0x25, 0x50, 0x44, 0x46 };

            _presetServiceMock
                .Setup(x => x.GetConfiguratorStateAsync(presetId))
                .ReturnsAsync(state);

            var validation = new ValidationResult();
            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .ReturnsAsync((configurator, validation));

            _viewGeneratorMock
                .Setup(x => x.GenerateHtml(configurator, null))
                .Returns(html);

            var pdfValidation = new ValidationResult();
            _viewGeneratorMock
                .Setup(x => x.GeneratePdfFromHtmlAsync(html))
                .ReturnsAsync((expectedPdf, pdfValidation));

            // Act
            var (pdf, title, error) = await _orchestrator.GeneratePdfFromPresetAsync(configuratorId, presetId);

            // Assert
            Assert.Equal(expectedPdf, pdf);
            Assert.Equal("Test Configurator", title);
            Assert.False(error.HasErrors);
        }

        [Fact]
        public async Task GeneratePdfFromStateAsync_WhenHtmlGenerationFails_ReturnsError()
        {
            // Arrange
            var configuratorId = 1;
            var state = new ConfiguratorStateDto();
            var errorMessage = "Failed to generate HTML";

            var validation = new ValidationResult();
            validation.AddError(errorMessage);

            _configuratorServiceMock
                .Setup(x => x.GetFilteredConfiguratorEntityAsync(configuratorId, state))
                .Returns(Task.FromResult<(Configurator? configurator, ValidationResult validation)>((null, validation)));

            // Act
            var (pdf, title, error) = await _orchestrator.GeneratePdfFromStateAsync(configuratorId, state);

            // Assert
            Assert.Null(pdf);
            Assert.Null(title);
            Assert.True(error.HasErrors);
            Assert.Contains(errorMessage, error.GetErrors());
        }
    }
}
