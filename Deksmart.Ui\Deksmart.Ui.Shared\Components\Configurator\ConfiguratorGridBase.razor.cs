﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Deksmart.Ui.Shared.Services;
using Microsoft.Extensions.Logging;
using Deksmart.Ui.Shared.Resources;
using Deksmart.Ui.Model;
using Deksmart.Shared.Enum;

namespace Deksmart.Ui.Shared.Components.Configurator
{
    public partial class ConfiguratorGridBase : ComponentBase, IDisposable
    {
        [Parameter]
        public int Id { get; set; }
        [Parameter]
        public IConfiguratorGridService ConfiguratorGridService { get; set; } = default!;
        [Parameter]
        public bool ShowSaveButton { get; set; } = true;
        [Parameter]
        public bool ShowPageTitle { get; set; } = true;
        [Parameter]
        public bool UpdateHeader { get; set; } = true;
        [Inject]
        protected NavigationManager NavigationManager { get; set; } = default!;
        [Inject]
        protected ILogger<ConfiguratorGridBase> Logger { get; set; } = default!;
        [Inject]
        protected IJSRuntime JSRuntime { get; set; } = default!;
        [Inject]
        protected HeaderStateService HeaderState { get; set; } = default!;

        protected override void OnInitialized()
        {
            base.OnInitialized();
            
            // Subscribe to loading state changes
            ConfiguratorGridService.LoadingStateChanged += OnLoadingStateChanged;
            
            if (UpdateHeader)
            {
                UpdateHeaderState();
            }
        }

        protected override void OnParametersSet()
        {
            base.OnParametersSet();
            
            // Unsubscribe and resubscribe to handle parameter changes
            ConfiguratorGridService.LoadingStateChanged -= OnLoadingStateChanged;
            ConfiguratorGridService.LoadingStateChanged += OnLoadingStateChanged;
            
            if (UpdateHeader)
            {
                UpdateHeaderState();
            }
        }

        private void OnLoadingStateChanged(object? sender, EventArgs e)
        {
            InvokeAsync(() =>
            {
                if (UpdateHeader)
                {
                    UpdateHeaderState();
                }
                StateHasChanged();
            });
        }

        private void UpdateHeaderState()
        {
            if (ConfiguratorGridService?.ConfiguratorWrapper != null)
            {
                var title = ConfiguratorGridService.ConfiguratorWrapper.Title;
                var saveCallback = EventCallback.Factory.Create(this, SaveLayout);
                HeaderState.UpdateHeader(title, ShowSaveButton, ConfiguratorGridService.IsLoading, saveCallback);
            }
        }

        public virtual async Task LoadDataAsync()
        {
            await ConfiguratorGridService.LoadDataAsync(Id);
            if (UpdateHeader)
            {
                UpdateHeaderState();
            }
            StateHasChanged();
        }

        protected async Task HandleDataLoaded()
        {
            Logger.LogInformation("HandleDataLoaded");
            await LoadDataAsync();
        }

        protected async Task HandleIdWrapperValueChange((int id, IdFieldWrapper selectBoxField) data)
        {
            Logger.LogInformation($"HandleIdWrapperValueChange: {data.id}, {data.selectBoxField}");
            await ConfiguratorGridService.ChangeIdWrapperValue(data.selectBoxField, data.id);
        }

        protected async Task SaveLayout()
        {
            var presetId = await ConfiguratorGridService.SavePreset();

            if (presetId != null)
            {
                var newUrl = UiSharedResource.Deksmart + "/" + string.Format(UiSharedResource.LoadPresetUrl, Id, presetId);
                var completeUrl = NavigationManager.Uri + newUrl;
                // Copy newUrl to clipboard
                await JSRuntime.InvokeVoidAsync("copyToClipboard", completeUrl);
                NavigationManager.NavigateTo(newUrl);
            }
        }

        protected void ToggleCategoryCollapse(ConfiguratorFieldCategoryWrapper category)
        {
            category.CollapseState = category.IsCollapsed
                ? CategoryCollapseStateDto.Expanded
                : CategoryCollapseStateDto.Collapsed;
        }

        /// <summary>
        /// Checks if a category has exactly one field of type IdFieldWrapper and returns the selected value's title
        /// </summary>
        /// <param name="category">The category to check</param>
        /// <param name="selectedValue">Output parameter that will contain the selected value's title if found</param>
        /// <returns>True if the category has exactly one IdFieldWrapper field with a selected value, false otherwise</returns>
        protected bool HasSingleIdFieldWrapper(ConfiguratorFieldCategoryWrapper category, out string? selectedValue)
        {
            selectedValue = null;

            // Get all fields from the category (including all positions)
            var allFields = category.AllFields;

            // Filter to only IdFieldWrapper fields
            var idFields = allFields.OfType<IdFieldWrapper>().ToList();

            // Check if there's exactly one IdFieldWrapper field
            if (idFields.Count == 1)
            {
                var idField = idFields[0];

                // Check if the field has a selected value
                if (idField.SelectedWrapper != null)
                {
                    // Return the title of the selected value
                    selectedValue = idField.SelectedWrapper.Title;
                    return true;
                }
            }

            return false;
        }

        void IDisposable.Dispose()
        {
            // Unsubscribe from loading state changes
            ConfiguratorGridService.LoadingStateChanged -= OnLoadingStateChanged;
            
            // Only clear the header state if we were updating it
            if (UpdateHeader)
            {
                HeaderState.ClearHeader();
            }
        }
    }
}
