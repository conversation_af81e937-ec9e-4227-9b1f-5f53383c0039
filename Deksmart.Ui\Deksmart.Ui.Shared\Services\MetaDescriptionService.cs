using Deksmart.Ui.Model;
using Deksmart.Ui.Shared.Resources;

namespace Deksmart.Ui.Shared.Services
{
    public class MetaDescriptionService
    {
        private const int MaxLength = 140;

        public MetaDescriptionService()
        {
        }

        public string GetMetaDescription(ConfiguratorWrapper? configurator)
        {
            if (configurator == null)
            {
                return UiSharedResource.MetaDescription_MainPage;
            }

            // Use the configurator's own meta description if present and not empty
            if (!string.IsNullOrWhiteSpace(configurator.MetaDescription))
            {
                return configurator.MetaDescription.Length > MaxLength
                    ? configurator.MetaDescription.Substring(0, MaxLength)
                    : configurator.MetaDescription;
            }

            var title = configurator.Title ?? string.Empty;
            var template = UiSharedResource.MetaDescription_ConfiguratorTemplate;
            var candidate = string.Format(template, title);

            if (candidate.Length <= MaxLength)
            {
                return candidate;
            }

            // Fallback: just use the title, truncated if needed
            var fallback = title.Length > MaxLength ? title.Substring(0, <PERSON><PERSON>ength) : title;
            return fallback;
        }
    }
} 