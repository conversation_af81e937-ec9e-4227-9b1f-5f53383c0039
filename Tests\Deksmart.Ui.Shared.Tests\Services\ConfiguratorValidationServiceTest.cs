using Moq;
using Deksmart.Ui.Shared.Services;
using Deksmart.Shared.Dto;
using Deksmart.Shared.Enum;
using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Tests.Services
{
    public class ConfiguratorValidationServiceTest
    {
        private readonly ConfiguratorValidationService _service;

        public ConfiguratorValidationServiceTest()
        {
            _service = new ConfiguratorValidationService();
        }

        private ConfiguratorWrapper CreateTestConfiguratorWrapper(bool isValid = true)
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Test Configurator",
                IsValid = isValid,
                Url = "test-url"
            };

            var wrapper = new ConfiguratorWrapper(configuratorDto);

            // Add field categories with fields
            var category1 = new ConfiguratorFieldCategoryWrapper(new ConfiguratorFieldCategoryDto
            {
                Id = 1,
                Title = "Category 1",
                CollapseState = CategoryCollapseStateDto.Expanded
            });

            var category2 = new ConfiguratorFieldCategoryWrapper(new ConfiguratorFieldCategoryDto
            {
                Id = 2,
                Title = "Category 2",
                CollapseState = CategoryCollapseStateDto.Expanded
            });

            // Add valid field to category 1
            var validField = new NumericFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 1,
                Title = "Valid Field",
                Ident = "valid_field"
            });

            // Add field with validation error to category 2
            var invalidField = new NumericFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 2,
                Title = "Invalid Field",
                Ident = "invalid_field",
                ValidationError = "This field is required"
            });
            invalidField.IsDirty = true;

            category1.Fields.Add(validField);
            category2.Fields.Add(invalidField);

            wrapper.FieldCategories.Add(category1);
            wrapper.FieldCategories.Add(category2);

            return wrapper;
        }

        private Mock<IConfiguratorGridService> CreateMockChildService(bool isValid = true, bool hasValidationError = false)
        {
            var mockService = new Mock<IConfiguratorGridService>();
            
            if (isValid)
            {
                var validWrapper = CreateTestConfiguratorWrapper(true);
                if (!hasValidationError)
                {
                    // Remove validation errors for valid child
                    foreach (var category in validWrapper.FieldCategories)
                    {
                        foreach (var field in category.Fields)
                        {
                            field.Field.ValidationError = null;
                        }
                    }
                }
                mockService.Setup(s => s.ConfiguratorWrapper).Returns(validWrapper);
            }
            else
            {
                var invalidWrapper = CreateTestConfiguratorWrapper(false);
                mockService.Setup(s => s.ConfiguratorWrapper).Returns(invalidWrapper);
            }

            mockService.Setup(s => s.FindFirstInvalidCategoryId()).Returns(hasValidationError ? "category-2" : null);

            return mockService;
        }

        [Fact]
        public void SetAllFieldsDirty_WithNullWrapper_ReturnsFalse()
        {
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.SetAllFieldsDirty(null, childServices);

            Assert.False(result);
        }

        [Fact]
        public void SetAllFieldsDirty_WithValidWrapper_SetsAllFieldsDirtyAndReturnsIsValid()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            var childServices = new List<IConfiguratorGridService>();

            // Initially fields should not be dirty
            Assert.False(wrapper.FieldCategories[0].Fields[0].IsDirty);

            var result = _service.SetAllFieldsDirty(wrapper, childServices);

            // Should set all fields as dirty
            Assert.True(wrapper.FieldCategories[0].Fields[0].IsDirty);
            Assert.True(wrapper.FieldCategories[1].Fields[0].IsDirty);
            
            // Should return the wrapper's IsValid state
            Assert.True(result);
        }

        [Fact]
        public void SetAllFieldsDirty_WithInvalidWrapper_ReturnsInvalidState()
        {
            var wrapper = CreateTestConfiguratorWrapper(false);
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.SetAllFieldsDirty(wrapper, childServices);

            Assert.False(result);
        }

        [Fact]
        public void SetAllFieldsDirty_WithChildServices_SetsChildServicesPropertiesAndCallsSetAllFieldsDirty()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            var mockChild1 = CreateMockChildService(true);
            var mockChild2 = CreateMockChildService(false);

            var childServices = new List<IConfiguratorGridService>
            {
                mockChild1.Object,
                mockChild2.Object
            };

            _service.SetAllFieldsDirty(wrapper, childServices);

            // Verify ShowValidationCheckboxes was set for all children
            mockChild1.VerifySet(s => s.ShowValidationCheckboxes = true, Times.Once);
            mockChild2.VerifySet(s => s.ShowValidationCheckboxes = true, Times.Once);

            // Verify SetAllFieldsDirty was called for all children
            mockChild1.Verify(s => s.SetAllFieldsDirty(), Times.Once);
            mockChild2.Verify(s => s.SetAllFieldsDirty(), Times.Once);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_WithNullWrapper_ReturnsNull()
        {
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.FindFirstInvalidCategoryId(null, childServices);

            Assert.Null(result);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_WithNoValidationErrors_ReturnsNull()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            // Clear validation errors and make fields not dirty
            foreach (var category in wrapper.FieldCategories)
            {
                foreach (var field in category.Fields)
                {
                    field.Field.ValidationError = null;
                    field.IsDirty = false;
                }
            }

            var childServices = new List<IConfiguratorGridService>();

            var result = _service.FindFirstInvalidCategoryId(wrapper, childServices);

            Assert.Null(result);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_WithValidationErrorInMainConfigurator_ReturnsCorrectCategoryId()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);

            var childServices = new List<IConfiguratorGridService>();

            var result = _service.FindFirstInvalidCategoryId(wrapper, childServices);

            Assert.Equal("category-2", result);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_WithValidationErrorOnlyInChildConfigurator_ReturnsChildCategoryId()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            // Clear validation errors in main configurator
            foreach (var category in wrapper.FieldCategories)
            {
                foreach (var field in category.Fields)
                {
                    field.Field.ValidationError = null;
                    field.IsDirty = false;
                }
            }

            var mockChild = CreateMockChildService(false, true);
            var childServices = new List<IConfiguratorGridService>
            {
                mockChild.Object
            };

            var result = _service.FindFirstInvalidCategoryId(wrapper, childServices);

            Assert.Equal("category-2", result);
            mockChild.Verify(s => s.FindFirstInvalidCategoryId(), Times.Once);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_WithMultipleErrors_ReturnsFirstError()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            
            // Add another field with validation error to category 1 (should come first)
            var earlyErrorField = new NumericFieldWrapper(new ConfiguratorFieldDto
            {
                Id = 3,
                Title = "Early Error Field",
                Ident = "early_error_field",
                ValidationError = "Early error"
            });
            earlyErrorField.IsDirty = true;
            wrapper.FieldCategories[0].Fields.Add(earlyErrorField);

            var childServices = new List<IConfiguratorGridService>();

            var result = _service.FindFirstInvalidCategoryId(wrapper, childServices);

            // Should return category-1 since it's the first category with errors
            Assert.Equal("category-1", result);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_WithValidationErrorButNotDirty_ReturnsNull()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            // Set validation error but make field not dirty
            foreach (var category in wrapper.FieldCategories)
            {
                foreach (var field in category.Fields)
                {
                    if (field.HasValidationError)
                    {
                        field.IsDirty = false;
                    }
                }
            }

            var childServices = new List<IConfiguratorGridService>();

            var result = _service.FindFirstInvalidCategoryId(wrapper, childServices);

            Assert.Null(result);
        }

        [Fact]
        public void UpdateParentValidationState_WithNullWrapper_ReturnsFalse()
        {
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.UpdateParentValidationState(null, childServices);

            Assert.False(result);
        }

        [Fact]
        public void UpdateParentValidationState_WithAllValidChildren_UpdatesParentToValidAndReturnsTrue()
        {
            var wrapper = CreateTestConfiguratorWrapper(false); // Start as invalid
            var mockChild1 = CreateMockChildService(true);
            var mockChild2 = CreateMockChildService(true);

            var childServices = new List<IConfiguratorGridService>
            {
                mockChild1.Object,
                mockChild2.Object
            };

            var result = _service.UpdateParentValidationState(wrapper, childServices);

            Assert.True(result);
            Assert.True(wrapper.IsValid); // Should be updated to valid
        }

        [Fact]
        public void UpdateParentValidationState_WithSomeInvalidChildren_UpdatesParentToInvalidAndReturnsFalse()
        {
            var wrapper = CreateTestConfiguratorWrapper(true); // Start as valid
            var mockChild1 = CreateMockChildService(true);
            var mockChild2 = CreateMockChildService(false); // Invalid child

            var childServices = new List<IConfiguratorGridService>
            {
                mockChild1.Object,
                mockChild2.Object
            };

            var result = _service.UpdateParentValidationState(wrapper, childServices);

            Assert.False(result);
            Assert.False(wrapper.IsValid); // Should be updated to invalid
        }

        [Fact]
        public void UpdateParentValidationState_WithEmptyChildServices_UpdatesParentToValidAndReturnsTrue()
        {
            var wrapper = CreateTestConfiguratorWrapper(false); // Start as invalid
            var childServices = new List<IConfiguratorGridService>();

            var result = _service.UpdateParentValidationState(wrapper, childServices);

            Assert.True(result);
            Assert.True(wrapper.IsValid); // Should be updated to valid (no children means all children are valid)
        }

        [Fact]
        public void UpdateParentValidationState_WithChildServicesWithNullWrappers_IgnoresThoseChildren()
        {
            var wrapper = CreateTestConfiguratorWrapper(false); // Start as invalid
            var mockValidChild = CreateMockChildService(true);
            var mockNullWrapperChild = new Mock<IConfiguratorGridService>();
            mockNullWrapperChild.Setup(s => s.ConfiguratorWrapper).Returns((ConfiguratorWrapper?)null);

            var childServices = new List<IConfiguratorGridService>
            {
                mockValidChild.Object,
                mockNullWrapperChild.Object
            };

            var result = _service.UpdateParentValidationState(wrapper, childServices);

            Assert.True(result);
            Assert.True(wrapper.IsValid); // Should be valid since the only non-null child is valid
        }

        [Fact]
        public void UpdateParentValidationState_WithMixedValidInvalidAndNullChildren_ReturnsCorrectState()
        {
            var wrapper = CreateTestConfiguratorWrapper(true); // Start as valid
            var mockValidChild = CreateMockChildService(true);
            var mockInvalidChild = CreateMockChildService(false);
            var mockNullWrapperChild = new Mock<IConfiguratorGridService>();
            mockNullWrapperChild.Setup(s => s.ConfiguratorWrapper).Returns((ConfiguratorWrapper?)null);

            var childServices = new List<IConfiguratorGridService>
            {
                mockValidChild.Object,
                mockInvalidChild.Object,
                mockNullWrapperChild.Object
            };

            var result = _service.UpdateParentValidationState(wrapper, childServices);

            Assert.False(result);
            Assert.False(wrapper.IsValid); // Should be invalid due to mockInvalidChild
        }

        [Fact]
        public void SetAllFieldsDirty_WithEmptyFieldCategories_ReturnsWrapperIsValidState()
        {
            var configuratorDto = new ConfiguratorDto
            {
                Id = 1,
                Title = "Empty Configurator",
                IsValid = true,
                Url = "test-url"
            };

            var wrapper = new ConfiguratorWrapper(configuratorDto);
            // Don't add any field categories

            var childServices = new List<IConfiguratorGridService>();

            var result = _service.SetAllFieldsDirty(wrapper, childServices);

            Assert.True(result);
        }

        [Fact]
        public void FindFirstInvalidCategoryId_ChecksChildrenOnlyAfterMainConfiguratorHasNoErrors()
        {
            var wrapper = CreateTestConfiguratorWrapper(true);
            // Clear validation errors in main configurator
            foreach (var category in wrapper.FieldCategories)
            {
                foreach (var field in category.Fields)
                {
                    field.Field.ValidationError = null;
                    field.IsDirty = false;
                }
            }

            var mockChild1 = CreateMockChildService(true, false); // No errors
            var mockChild2 = CreateMockChildService(false, true); // Has errors

            var childServices = new List<IConfiguratorGridService>
            {
                mockChild1.Object,
                mockChild2.Object
            };

            var result = _service.FindFirstInvalidCategoryId(wrapper, childServices);

            // Should find error in second child
            Assert.Equal("category-2", result);
            
            // Should have checked first child first
            mockChild1.Verify(s => s.FindFirstInvalidCategoryId(), Times.Once);
            mockChild2.Verify(s => s.FindFirstInvalidCategoryId(), Times.Once);
        }
    }
}