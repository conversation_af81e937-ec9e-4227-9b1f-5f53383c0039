using System.Net.Http.Json;

namespace DEK.Eshop.ApiCore.Extension;

public static class HttpResponseMessageExtension
{
    /// <summary>
    /// Deserializace HttpResponseMessage do objektu.
    /// </summary>
    public static Task<T?> Dek_ToObjectAsync<T>(this HttpResponseMessage source)
    {
        return source.Content.ReadFromJsonAsync<T>();
    }

    /// <summary>
    /// HttpResponseMessage do stringu.
    /// </summary>
    public static Task<string> Dek_ToStringAsync(this HttpResponseMessage source)
    {
        return source.Content.ReadAsStringAsync();
    }

    [Obsolete("Use Dek_ToObjectAsync", true)]
    public static Task<T?> _ToObject_<T>(this HttpResponseMessage source)
    {
        return source.Dek_ToObjectAsync<T>();
    }

    [Obsolete("Use Dek_ToStringAsync", true)]
    public static string _ToString(this HttpResponseMessage source)
    {
        return source.Content.ReadAsStringAsync().Result;
    }
}
