﻿using DEK.Eshop.ApiCore.Sandbox.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using DEK.Eshop.ApiCore.Sandbox.Entity;
using DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;
using Cart.Entity.Cart.Get;
using DEK.Eshop.ApiCore.Database;
using DEK.Eshop.ApiCore.Extension;
using DEK.Eshop.ApiCore.Indentity;

namespace DEK.Eshop.ApiCore.Sandbox.Repository;

public class MssqlRepository
{
    private readonly MssqlContextFactory<MssqlContext> _factory;

    private MssqlContext? _context;

    private readonly IServiceProvider _serviceProvider;

    public MssqlRepository(MssqlContextFactory<MssqlContext> factory, IServiceProvider serviceProvider)
    {
        _factory = factory;
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// Získání pobočky eshopu
    /// </summary>
    public virtual async Task<Branch?> GetBranchAsync(string code)
    {
        using (var context = _factory.CreateContext()) {
            var list = await context.Branch!.FromSqlRaw("EXEC [proc].czES_Pobocka_Detail @IN_IdPobocky = @IN_IdPobocky_", new SqlParameter("IN_IdPobocky_", code)).ToListAsync();
            return list.Count > 0 ? list.First() : null;
        }
    }

    /// <summary>
    /// Zda existuje pobočka
    /// </summary>
    public virtual async Task<bool> IsBranchExists(string branchCode)
    {
        using(var cmd = _factory.CreateCommand()) {
            var builder = new ProcedureQueryBuilder("[proc].czES_Pobocka_Detail");
            builder.AddParam("@IN_IdPobocky", branchCode);

            return await cmd.ExecuteFirstColumnAndRowAsync(builder) != null;
        }
    }

    /// <summary>
    /// Položka eshopu
    /// </summary>
    public virtual async Task<ProductBranchEshop?> GetProductEshop_(string code)
    {
        using (var context = _factory.CreateContext()) {
            var builder = new ProcedureQueryBuilder("[proc].czES_Polozka_Jednotky");

            builder.AddParam("@IN_Polozka", code);
            builder.AddParam("@IN_PobockaVydej", "P100");

            var list = await context.ProductBranchEshop!.GetListAsync(builder);
            return list.FirstOrDefault();
        }
    }

    public virtual async Task<ProductEshopDetail?> GetProductEshopDetail_(string code)
    {
        using (var context = _factory.CreateContext()) {
            var builder = new ProcedureQueryBuilder("[proc].czES_Polozka_Detail");
            builder.AddParam("IN_Polozka", code);

            var list = await context.ProductEshopDetail!.GetListAsync(builder);
            context.ChangeTracker.Clear();
            return list.FirstOrDefault();
        }
    }

    public virtual async Task<ProductEshopUnit?> GetProductEshopUnit_(string code, ApplicationUser user)
    {
        using (var context = _factory.CreateContext()) {
            var builder = new ProcedureQueryBuilder("[proc].czES_Polozka_Jednotky");
            builder.AddParam("IN_Polozka", code);
            builder.AddParam("IN_PobockaVydej", user.BranchCode ?? "");

            var list = await context.ProductEshopUnit!.GetListAsync(builder);
            return list.FirstOrDefault();
        }
    }

    public virtual async Task<ProductEshopPrice?> GetProductEshopPrice_(string code, ApplicationUser user)
    {
        using (var context = _factory.CreateContext()) {
            var builder = new ProcedureQueryBuilder("[proc].czES_Polozka_Ceny");
            builder.AddParam("IN_Polozka", code);
            builder.AddParam("@IN_OcenUroven", user.PriceLevelEshop);
            builder.AddParam("@IN_PobockaVydej", user.BranchCode ?? "");
            builder.AddParam("@IN_PobockaFirma", user.BranchHomeCode ?? "");
            builder.AddParam("@IN_FirmaID", user.CompanyId);

            var list = await context.ProductEshopPrice!.GetListAsync(builder);
            return list.FirstOrDefault();
        }
    }

}
