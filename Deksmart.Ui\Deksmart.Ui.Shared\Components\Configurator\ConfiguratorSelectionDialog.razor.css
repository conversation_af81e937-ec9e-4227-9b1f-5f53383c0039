.configurator-selection-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.configurator-selection-dialog.visible {
    display: flex;
}

.dialog-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.configurator-list {
    margin: 20px 0;
}

.configurator-item {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.configurator-item:hover {
    background-color: #f5f5f5;
}

.configurator-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.configurator-description {
    color: #666;
    font-size: 0.9em;
}

.cancel-button {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.cancel-button:hover {
    background-color: #e0e0e0;
} 