using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Shared.Services
{
    public interface IConfiguratorApiService
    {
        /// <summary>
        /// Gets a configurator without applying field filters
        /// </summary>
        Task<ConfiguratorDto?> GetConfiguratorAsync(int id, CancellationToken cancellationToken);

        /// <summary>
        /// Gets a filtered configurator by applying the provided state
        /// </summary>
        Task<ConfiguratorDto?> GetFilteredConfiguratorAsync(int id, ConfiguratorStateDto state, CancellationToken cancellationToken);

        /// <summary>
        /// Processes a composite configurator with active child processing and product aggregation.
        /// Returns both the processed active child and the composite summary with aggregated products.
        /// </summary>
        Task<CompositeConfiguratorResponseDto?> ProcessCompositeConfiguratorAsync(int id, CompositeConfiguratorStateDto state, CancellationToken cancellationToken);

        /// <summary>
        /// Loads a configurator with a preset applied
        /// </summary>
        Task<ConfiguratorDto?> GetConfiguratorForPresetAsync(int id, string preset, CancellationToken cancellationToken);

        /// <summary>
        /// Saves the current configurator state as a preset
        /// </summary>
        Task<Guid?> SavePresetAsync(int configuratorId, ConfiguratorStateDto state, CancellationToken cancellationToken);

        /// <summary>
        /// Loads product details for the specified product
        /// </summary>
        Task<ProductDetailsDto?> LoadProductDetailAsync(string productCode, CancellationToken cancellationToken);

        /// <summary>
        /// Calculates product prices for the given request
        /// </summary>
        Task<ProductPriceCalculationResponseDto?> CalculateProductPricesAsync(ProductPriceCalculationRequestDto request, CancellationToken cancellationToken);

        /// <summary>
        /// Generates a PDF for the configurator
        /// </summary>
        Task<(byte[]? pdfBytes, string fileName)> GenerateConfigurationPdfAsync(int configuratorId, ConfiguratorStateDto state, CancellationToken cancellationToken);

        /// <summary>
        /// Sends an inquiry email
        /// </summary>
        Task<bool> SendInquiryAsync(int configuratorId, EmailConfiguratorStateDto emailState, CancellationToken cancellationToken);
    }
}