using Deksmart.Domain.Entity.Db;

namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Encapsulates the result of a price calculation for a single product, including net price, VAT, gross price, package information, and validation results.
    /// Used in the business logic to apply calculated prices to products, validate unit and amount correctness, and transfer pricing data for API/UI consumption.
    /// Constructed during the price calculation process for each product in a configurator.
    /// </summary>
    public class ProductPriceCalculation
    {
        public decimal PriceNoVat { get; set; }
        public decimal PriceVat { get; set; }
        public decimal Vat { get; set; }
        public decimal PriceVatPackage { get; set; }
        public decimal PackageQuantity { get; set; }
        public decimal? UserPackageQuantity { get; set; }
        public string? PackageUnit { get; set; }
        public ValidationResult Validation { get; set; } = new();

        public ProductPriceCalculation(decimal priceNoVat, decimal priceVat, decimal vat, decimal priceVatPackage, decimal packageQuantity, decimal? userPackageQuantity, string? packageUnit)
        {
            PriceNoVat = priceNoVat;
            PriceVat = priceVat;
            Vat = vat;
            PriceVatPackage = priceVatPackage;
            PackageQuantity = packageQuantity;
            UserPackageQuantity = userPackageQuantity;
            PackageUnit = packageUnit;
        }

        public void ApplyTo(ConfiguratorProduct product)
        {
            product.PriceNoVat = PriceNoVat;
            product.PriceVat = PriceVat;
            product.Vat = Vat;
            product.PriceVatPackage = PriceVatPackage;
            product.PackageQuantity = PackageQuantity;
            product.PackageUnit = PackageUnit;
            product.UserPackageQuantity = UserPackageQuantity;
        }
    }
} 