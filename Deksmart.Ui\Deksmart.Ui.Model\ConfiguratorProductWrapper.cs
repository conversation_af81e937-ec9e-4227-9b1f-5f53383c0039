﻿using Deksmart.Shared.Dto;

namespace Deksmart.Ui.Model
{
    public class ConfiguratorProductWrapper
    {
        private ConfiguratorProductDto _data { get; set; }

        public ConfiguratorProductWrapper(ConfiguratorProductDto configuratorProduct)
        {
            _data = configuratorProduct;
            PackageQuantity = _data.UserPackageQuantity ?? _data.PackageQuantity;
        }

        public int Id => _data.Id;

        public string ProductCode => _data.ProductCode;

        public string Title => _data.Title;

        public bool IsSelected => _data.IsSelected;

        public string CalculatedAmount => _data.CalculatedAmount.ToString("0.#####");

        public decimal CalculatedAmountDecimal => _data.CalculatedAmount;

        public string ProductUnit => _data.ProductUnit ?? string.Empty;

        public decimal PackageQuantity { get; set; }

        public string PackageQuantityWatermark => _data.PackageQuantity.ToString();

        public string PackageUnit => _data.PackageUnit ?? "0";

        public decimal PackagePrice => _data.PriceVatPackage;

        public decimal TotalPrice => _data.PriceVat;

        public bool IsPackageAmountDirty => PackageQuantity != _data.PackageQuantity;

        public ConfiguratorProductDetailWrapper? Detail { get; set; }

        public string? ValidationError => _data.ValidationError;

        public bool HasValidationError => _data.HasValidationError;

        public int Order => _data.Order;

        public void UpdatePrices(decimal priceVat)
        {
            _data.PriceVat = priceVat;
        }
    }
}
