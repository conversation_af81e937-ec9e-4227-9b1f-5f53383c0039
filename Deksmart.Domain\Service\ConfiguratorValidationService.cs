﻿using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Resource;

namespace Deksmart.Domain.Service
{
    /// <summary>
    /// Provides validation methods for configurator existence and field value constraints.
    /// </summary>
    public interface IConfiguratorValidationService
    {
        /// <summary>
        /// Validates that a configurator with the specified ID exists, adding errors to the provided validation result.
        /// </summary>
        /// <param name="configuratorId">The configurator ID to validate.</param>
        /// <param name="validation">The validation result to add errors to.</param>
        Task ValidateConfiguratorExists(int configuratorId, ValidationResult validation);

        /// <summary>
        /// Validates that the provided value is within the allowed bounds for the specified direct value field, adding errors to the provided validation result.
        /// </summary>
        /// <param name="field">The field to validate.</param>
        /// <param name="value">The value to validate.</param>
        /// <param name="validation">The validation result to add errors to.</param>
        void ValidateDirectValueIsWithinBounds(DirectValue field, decimal value, ValidationResult validation);

        /// <summary>
        /// Validates that the provided value is a valid option for the specified multiple choice field, adding errors to the provided validation result.
        /// </summary>
        /// <param name="dbFieldValuesGroup">The field values group.</param>
        /// <param name="fieldId">The field ID.</param>
        /// <param name="value">The value to validate.</param>
        /// <param name="result">The validation result to add errors to.</param>
        void ValidateMultipleChoiceValueHasValidValue(MultipleChoiceValue dbFieldValuesGroup, int fieldId, decimal value, ValidationResult result);

        /// <summary>
        /// Validates that a configurator exists
        /// </summary>
        /// <param name="configuratorId">The configurator ID to validate</param>
        /// <returns>A validation result</returns>
        Task ValidateConfiguratorExistsAsync(int configuratorId, ValidationResult validation);
    }

    public interface IConfiguratorExistenceValidator
    {
        Task<bool> DoesConfiguratorExistAsync(int configuratorId);
    }

    public class ConfiguratorValidationService : IConfiguratorValidationService
    {
        private readonly IConfiguratorExistenceValidator _existenceValidator;

        public ConfiguratorValidationService(IConfiguratorExistenceValidator existenceValidator)
        {
            _existenceValidator = existenceValidator;
        }

        public void ValidateMultipleChoiceValueHasValidValue(MultipleChoiceValue dbFieldValuesGroup, int fieldId, decimal value, ValidationResult validation)
        {
            if (!dbFieldValuesGroup.Values.Contains(value) && dbFieldValuesGroup.DefaultValue != value)
                validation.AddError(string.Format(DeksmartDomainResource.FieldValueDoesNotExist, value, fieldId));
        }

        public void ValidateDirectValueIsWithinBounds(DirectValue field, decimal value, ValidationResult validation)
        {
            if (value < field.Min || value > field.Max)
                validation.AddError(string.Format(DeksmartDomainResource.FieldValueOutOfRange, field.Id, value, field.Min, field.Max));
        }

        public async Task ValidateConfiguratorExists(int configuratorId, ValidationResult validation)
        {
            if (!await _existenceValidator.DoesConfiguratorExistAsync(configuratorId))
                validation.AddError(string.Format(DeksmartDomainResource.ConfiguratorDoesNotExist, configuratorId));
        }

        public async Task ValidateConfiguratorExistsAsync(int configuratorId, ValidationResult validation)
        {
            if (!await _existenceValidator.DoesConfiguratorExistAsync(configuratorId))
                validation.AddError(string.Format(DeksmartDomainResource.ConfiguratorDoesNotExist, configuratorId));
        }
    }
}
