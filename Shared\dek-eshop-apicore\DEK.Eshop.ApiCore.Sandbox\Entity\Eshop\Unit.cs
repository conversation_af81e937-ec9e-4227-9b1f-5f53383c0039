namespace DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;

public class Unit
{
    public string UnitSales { get; set; } = null!;

    public string UnitPackage { get; set; } = null!;

    public string UnitPrimary { get; set; } = null!;

    public string UnitSecondary { get; set; } = null!;

    public bool IsPackagePrimary { get; set; }

    public decimal UnitsInPackage { get; set; }

    public decimal MinimumUnitsSales { get; set; }

    public decimal MinimumUnitsPrimary { get; set; }

    public Unit (ProductEshopUnit u)
    {
        var isPrimaryPackage = u.IsOnlyPackage;

        UnitSales = u.UnitSales;
        UnitPrimary = isPrimaryPackage ? u.UnitPackage : u.UnitSales;
        UnitPackage = u.UnitPackage;
        UnitSecondary = isPrimaryPackage ? u.UnitSales : u.UnitPackage;
        IsPackagePrimary = isPrimaryPackage;
        UnitsInPackage = u.SalesUnitsInPackage;
        MinimumUnitsSales = isPrimaryPackage && u.MinimumUnitsSales < u.SalesUnitsInPackage ? u.SalesUnitsInPackage : u.MinimumUnitsSales; // Override v contextu. Pokud je minimum 0 tak 1.
        MinimumUnitsPrimary = isPrimaryPackage ? MinimumUnitsSales / UnitsInPackage : MinimumUnitsSales;
    }
}
