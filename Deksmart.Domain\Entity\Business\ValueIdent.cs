namespace Deksmart.Domain.Entity.Business
{
    /// <summary>
    /// Represents a field identifier and its associated value, used as a generic container for passing matched field values in the configurator.
    /// Central to expression evaluation, field matching, and dynamic filtering, enabling identifier-based value propagation throughout business logic.
    /// </summary>
    public record ValueIdent
    {
        public string Ident { get; set; } = null!;
        public decimal? Value { get; set; }
    }
}
