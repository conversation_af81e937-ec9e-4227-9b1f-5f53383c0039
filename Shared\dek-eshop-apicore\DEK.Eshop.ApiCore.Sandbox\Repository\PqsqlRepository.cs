﻿namespace DEK.Eshop.ApiCore.Sandbox.Repository;

using DEK.Eshop.ApiCore.Database;
using DEK.Eshop.ApiCore.Sandbox.Context;
using System.Data;

public class PqsqlRepository
{
    private readonly PgsqlContextFactory<PgsqlContext> _factory;

    public PqsqlRepository(PgsqlContextFactory<PgsqlContext> factory)
    {
        _factory = factory;
    }

    public virtual async Task<string> GetPqsqlVersionAsync()
    {
        using (var cmd = _factory.CreateCommand()) {
            cmd.CommandType = CommandType.Text;
            cmd.CommandText = "SELECT version()";

            var result = "";
            var reader = await cmd.ExecuteReaderAsync();
            while (reader.Read()) {
                result = reader[0].ToString()!;
            }

            return result;
        }
    }
}