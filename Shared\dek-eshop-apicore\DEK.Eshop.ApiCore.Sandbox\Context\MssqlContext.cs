﻿namespace DEK.Eshop.ApiCore.Sandbox.Context;

using Cart.Entity.Cart.Get;
using DEK.Eshop.ApiCore.Sandbox.Entity;
using DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;
using Microsoft.EntityFrameworkCore;

public class MssqlContext : DbContext 
{
    public DbSet<Branch>? Branch { get; set; }

    public DbSet<ProductBranchEshop>? ProductBranchEshop { get; set; }

    public DbSet<ProductEshopDetail>? ProductEshopDetail { get; set; }

    public DbSet<ProductEshopUnit>? ProductEshopUnit { get; set; }

    public DbSet<ProductEshopPrice>? ProductEshopPrice { get; set; }

    public MssqlContext(DbContextOptions<MssqlContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Při vytváření entity se pouští setter u property
        modelBuilder.UsePropertyAccessMode(PropertyAccessMode.Property);

        //modelBuilder.Entity<ProductEshopUnit>(entity => {
            // Minimalní množství k objednání(default 1) + konvert na decimal z double
            //entity.Property(e => e.MinimumUnitsSales)
            //    .HasConversion(v => Decimal.ToDouble(v), v => v == 0 ? 1 : (decimal)v);
        //});

        modelBuilder.Entity<ProductEshopPrice>(entity => {

            // Cena: konvert na decimal z double
            entity.Property(e => e.PriceNoVatSales)
                .HasConversion<double>();

            // Cena: konvert na decimal z double
            entity.Property(e => e.PriceNoVatSalesOrigin)
                .HasConversion<double>();

            // Cena: konvert na decimal z double
            entity.Property(e => e.PriceNoVatPackage)
                .HasConversion<double>();

            // Cena: konvert na decimal z double
            entity.Property(e => e.PriceNoVatPackageOrigin)
                .HasConversion<double>();

            // Cena: konvert na bool z int
            entity.Property(e => e.IsVisibleDiscountHistory)
                .HasConversion<int>();

            // Cena: konvert na bool z decimal
            entity.Property(e => e.HasTax)
                 .HasConversion(v => Convert.ToDouble(v), v => v > 0 ? true : false);

            // Cena: konvert na bool z int
            entity.Property(e => e.DiscountPercent)
                .HasConversion<decimal>();
        });
    }
}
