using System.Net.Mime;

namespace DEK.Eshop.ApiCore.File;

public static class FileTypeChecker {

    // https://en.wikipedia.org/wiki/List_of_file_signatures
    public static readonly Dictionary<string, byte[][]> Signatures = new () {
        {"image/jpeg",
            new byte[][] {
                new byte[] { 0xFF, 0xD8, 0xFF, 0xDB },
                new byte[] { 0xFF, 0xD8, 0xFF, 0xE0 },
                new byte[] { 0xFF, 0xD8, 0xFF, 0xEE },
                new byte[] { 0xFF, 0xD8, 0xFF, 0xE1 },
            }
        },
        { "image/png",
            new byte[][] {
                new byte[] { 0x89, 0x50, 0x4E, 0x47 },
            }
        },
        { "image/gif",
            new byte[][] {
                new byte[] { 0x47, 0x49, 0x46, 0x38 },
            }
        },
        // for now just checking first 4 bytes
        { "image/webp",
            new byte[][] {
                new byte[] { 0x52, 0x49, 0x46, 0x46, 0x00, 0x00, 0x00, 0x00, 0x57, 0x45, 0x42, 0x50 },
            } 
        },
    };

    /// <summary>
    /// Check first 4 bytes of file against known signatures
    /// </summary>
    public static bool CheckType(byte[] bytesToTest, string mimeType)
    {
        if (bytesToTest.Length == 0) {
            return false;
        }

        if (!Signatures.ContainsKey(mimeType)) {
            return false;
        }

        var firstFourBytes = 4;
        var result = Signatures[mimeType].FirstOrDefault(signature => {
            return bytesToTest.Take(firstFourBytes).SequenceEqual(signature.Take(firstFourBytes));
        });

        return result is not null;
    }

}
