using System.Collections;

namespace DEK.Eshop.ApiCore.Data;

[Obsolete]
// Use Tupple instead
public record Result<T>
{
    /// <summary>
    /// Success with value
    /// </summary>
    public bool IsSuccess { get { return this.isSuccess && IsEmptyValue() == false; } }
    private readonly bool isSuccess;

    /// <summary>
    /// Success without value
    /// </summary>
    public bool IsEmpty { get { return this.isSuccess && IsEmptyValue(); } }

    /// <summary>
    /// Just if error list is not empty. Success is possible even with errors.
    /// </summary>
    public bool HasError { get { return ErrorList.Any(); } }

    public T? Value { get; init; }

    public List<Exception> ErrorList { get; init; } = new();

    private Result(bool isSuccess, T? value, Exception? error)
    {
        this.isSuccess = isSuccess;
        Value = value;
        if (error is not null) {
            ErrorList = new List<Exception> { error };
        }
    }

    private Result(List<Exception> errorList)
    {
        this.isSuccess = false;
        Value = default;
        ErrorList = errorList;
    }

    public void AddError(Exception error)
    {
        ErrorList.Add(error);
    }

    public static Result<T> Success(T? value = default)
    {
        return new Result<T>(isSuccess: true, value: value, error: default);
    }

    public static Result<T> Empty()
    {
        return new Result<T>(isSuccess: true, value: default, error: default);
    }

    public static Result<T> Fail(Exception error)
    {
        return new Result<T>(isSuccess: false, value: default, error: error);
    }

    public static Result<T> Fail(List<Exception> errorList)
    {
        return new Result<T>(errorList);

    }

    private bool IsEmptyValue() {

        if (Value is null){
            return true;
        }

        if (Value is string){
            return string.IsNullOrEmpty(Value as string);
        }

        if (Value is IEnumerable) {
            return (Value as IEnumerable<object>)!.Any() == false;
        }

        return false;
    }

    //Deprecated from 11.7.2024

    [Obsolete]
    public bool IsSuccessAndNotEmpty { get { return IsSuccess; } }

    [Obsolete]
    public bool IsSuccessAndEmpty { get { return IsSuccess && Value is null; } }
}
