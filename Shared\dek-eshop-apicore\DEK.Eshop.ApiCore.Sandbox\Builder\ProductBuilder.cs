using DEK.Eshop.ApiCore.Sandbox.Entity.Eshop;
using DEK.Eshop.ApiCore.Sandbox.Repository;
using System.Collections.Concurrent;
using DEK.Eshop.ApiCore.Indentity;

namespace DEK.Eshop.ApiCore.Sandbox.Builder;

public enum ProductEshopPart
{
    Detail,
    Unit,
    Price,
}

public class ProductBuilder
{

    private readonly MssqlRepository _mssqlRepository;

    private readonly ApplicationUser _user;

    public List<string> ProductCodes = new List<string>();

    public List<ProductEshopPart> ProductParts = new();

    public ProductBuilder(MssqlRepository mssqlRepository, ApplicationUser user)
    {
        _mssqlRepository = mssqlRepository;
        _user = user;
    }

    public async Task<ConcurrentBag<ProductEshop?>> Build()
    {
        var reuslts = new ConcurrentBag<ProductEshop?>();
        var options = new ParallelOptions() { MaxDegreeOfParallelism = 4 };
        await Parallel.ForEachAsync(ProductCodes, options, async (code, token) => {
            reuslts.Add(await this.Create_(code, ProductParts));
        });
        return reuslts;
    }

    public async Task<ProductEshop?> Create_(string code, List<ProductEshopPart> parts)
    {
        var tasks = new List<Task>();
        Task<ProductEshopDetail?>? taskDetail = null;
        Task<ProductEshopUnit?>? taskUnit = null;
        Task<ProductEshopPrice?>? taskPrice = null;

        var product = new ProductEshop(code);

        foreach (var part in parts) {
            switch (part) {
                case ProductEshopPart.Detail:
                    taskDetail = _mssqlRepository.GetProductEshopDetail_(code);
                    tasks.Add(taskDetail);
                    break;
                case ProductEshopPart.Unit:
                    taskUnit = _mssqlRepository.GetProductEshopUnit_(code, this._user);
                    tasks.Add(taskUnit);
                    break;
                case ProductEshopPart.Price:
                    taskPrice = _mssqlRepository.GetProductEshopPrice_(code, this._user);
                    tasks.Add(taskPrice);
                    break;
                default:
                    throw new ArgumentException("Unexpected value in enum ProductEshopPart: " + part.ToString());
            }
        }

        var detail = taskDetail != null ? await taskDetail : null;
        product = detail != null ? this.Fill(product, detail) : product;

        var unit = taskUnit != null ? await taskUnit : null;
        product = unit != null ? this.Fill(product, unit) : product;

        var price = taskPrice != null ? await taskPrice : null;
        product = price != null ? this.Fill(product, price) : product;

        return product;
    }

    private ProductEshop Fill(ProductEshop productEshop, ProductEshopDetail productEshopDetail)
    {
        productEshop.Code = productEshopDetail.Code;
        productEshop.Title = productEshopDetail.Title;
        productEshop.Slug = productEshopDetail.Slug;
        productEshop.Image = productEshopDetail.Image;
        return productEshop;
    }

    private ProductEshop Fill(ProductEshop productEshop, ProductEshopUnit productEshopUnit)
    {
        productEshop.Unit = new Unit(productEshopUnit);
        return productEshop;
    }

    private ProductEshop Fill(ProductEshop product, ProductEshopPrice price)
    {
        product.Price = new Price(price);
        return product;
    }
}
