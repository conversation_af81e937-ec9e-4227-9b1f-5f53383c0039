:root {
    /* Color Variables - Context7 Theme */
    --primary-color: #c00c18; /* Red for primary actions */
    --primary-dark: #a00914; /* Darker red for hover states */
    --secondary-color: #333333; /* Dark gray for headers and text */
    --success-color: #28a745; /* Green for success states */
    --danger-color: #c00c18; /* Red for errors/danger */
    --warning-color: #ffc107; /* Amber for warnings */
    --info-color: #17a2b8; /* Teal for info */
    --light-color: #f8f9fa; /* Light gray for backgrounds */
    --light-border: #e9ecef; /* Light gray for borders */
    --dark-color: #333333; /* Dark gray for text */
    --header-bg: #333333; /* Dark background for header */
    --header-text: #ffffff; /* White text for header */
    --body-bg: #ffffff; /* White background for body */
    --card-bg: #ffffff; /* White background for cards */
    --active-bg: #f1f1f1; /* Light gray for active states */
    --hover-bg: #f8f8f8; /* Very light gray for hover states */
    --disabled-bg: #e9ecef; /* Light gray for disabled elements */
    --disabled-text: #6c757d; /* Medium gray for disabled text */

    /* Category state colors */
    --category-active-bg: #c00c18; /* Red background for active category */
    --category-active-text: #ffffff; /* White text for active category */
    --category-completed-bg: #f8f8f8; /* Light gray for completed category */
    --category-completed-border: #28a745; /* Green border for completed category */
    --category-error-bg: #fff5f5; /* Very light red for error category */
    --category-error-border: #c00c18; /* Red border for error category */
    --category-disabled-bg: #f5f5f5; /* Light gray for disabled category */
    --category-disabled-border: #cccccc; /* Gray border for disabled category */
    --category-pink-bg: #ffebee; /* Light pink background */

    /* Loading Spinner Variables */
    --loading-spinner-size: 40px;
    --loading-spinner-border-width: 4px;
    --loading-spinner-color: var(--primary-color);
    --loading-spinner-bg-color: var(--light-color);
    --loading-spinner-min-height: 200px;
    --loading-spinner-overlay-bg: rgba(255, 255, 255, 0.8);

    /* Loading Progress Variables */
    --loading-progress-size: 8rem;
    --loading-progress-stroke-width: 0.6rem;
    --loading-progress-color: var(--primary-color);
    --loading-progress-bg-color: var(--light-color);
    --loading-progress-text-color: var(--secondary-color);

    /* Spacing Variables */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;

    /* Border Radius */
    --border-radius-sm: 2px;
    --border-radius-md: 4px;
    --border-radius-lg: 8px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.08);
    --shadow-md: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.12);
}

/* Base Styles */
html, body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
    background-color: var(--body-bg);
    color: var(--dark-color);
    line-height: 1.5;
}

/* Responsive Typography */
html {
    font-size: 16px;
}

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-weight: 500;
    line-height: 1.2;
    color: var(--secondary-color);
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
    color: var(--primary-dark);
}

/* Container */
.container {
    width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

/* Buttons */
.button, .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease-in-out;
    margin: var(--spacing-xs);
}

/* Primary button - red background with white text */
.button, .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.button:hover, .btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

/* Secondary button - white background with dark border */
.btn-secondary {
    background-color: white;
    border-color: var(--light-border);
    color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--hover-bg);
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* Outline button - transparent with colored border */
.btn-outline {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Icon button */
.btn-icon {
    padding: 0.5rem;
    border-radius: 50%;
    min-width: 2.5rem;
    min-height: 2.5rem;
}

/* Touch-friendly button sizes for mobile */
@media (max-width: 768px) {
    .button, .btn {
        padding: 0.75rem 1rem;
        min-height: 44px; /* Minimum touch target size */
    }
}

.button:active, .btn:active {
    transform: translateY(1px);
}

.button:disabled, .btn:disabled {
    background-color: var(--disabled-bg);
    border-color: var(--disabled-bg);
    color: var(--disabled-text);
    cursor: not-allowed;
    opacity: 0.8;
}

/* Form Elements */
.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--dark-color);
    background-color: white;
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 2px rgba(192, 12, 24, 0.1);
}

/* Select styles */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

/* Checkbox and radio styles */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.form-check-input {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
    appearance: none;
    border: 1px solid var(--light-border);
    background-color: white;
}

.form-check-input[type="checkbox"] {
    border-radius: 2px;
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M10.97 4.97a.75.75 0 0 1 1.07 1.05l-3.99 4.99a.75.75 0 0 1-1.08.02L4.324 8.384a.75.75 0 1 1 1.06-1.06l2.094 2.093 3.473-4.425a.267.267 0 0 1 .02-.022z'/%3E%3C/svg%3E");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.75rem;
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(192, 12, 24, 0.1);
    outline: none;
}

.form-check-label {
    font-size: 0.9rem;
    color: var(--dark-color);
}

/* Touch-friendly form elements for mobile */
@media (max-width: 768px) {
    .form-control {
        padding: 0.75rem;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    select.form-control {
        height: 44px; /* Minimum touch target size */
    }

    .form-check-input {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* Cards */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--card-bg);
    background-clip: border-box;
    border: 1px solid var(--light-border);
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.card-header {
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--light-border);
    font-weight: 500;
}

.card-body {
    flex: 1 1 auto;
    padding: 1rem;
}

.card-footer {
    padding: 0.75rem 1rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--light-border);
}

/* Card variations */
.card-primary {
    border-top: 3px solid var(--primary-color);
}

.card-secondary {
    border-top: 3px solid var(--secondary-color);
}

/* Grid System */
.grid {
    display: grid;
    gap: var(--spacing-md);
}

@media (min-width: 576px) {
    .grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

/* Loading Spinner */
/* Styles moved to LoadingSpinner.razor.css */

/* Error UI */
#blazor-error-ui {
    background: var(--secondary-color);
    color: white;
    bottom: 0;
    box-shadow: var(--shadow-lg);
    display: none;
    left: 0;
    padding: var(--spacing-md);
    position: fixed;
    width: 100%;
    z-index: 1000;
    border-top: 4px solid var(--primary-color);
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: var(--spacing-md);
    top: var(--spacing-sm);
}

/* Platform-specific optimizations */
@media (hover: none) {
    /* Touch device optimizations */
    .button:hover {
        transform: none;
        box-shadow: none;
    }

    /* Prevent text selection on tap */
    * {
        -webkit-tap-highlight-color: transparent;
    }
}

/* Print styles */
@media print {
    .button,
    .form-control,
    .card {
        border: 1px solid #000;
    }

    .loading-container,
    #blazor-error-ui {
        display: none;
    }
}

h1:focus {
    outline: none;
}

a, .btn-link {
    color: #0071c1;
}

.btn-primary {
    background-color: white;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-secondary {
    background-color: white;
    border-color: var(--secondary-color);
    color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.content {
    padding-top: 1.1rem;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.loading-progress {
    position: relative;
    display: block;
    width: var(--loading-progress-size);
    height: var(--loading-progress-size);
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: var(--loading-progress-bg-color);
        stroke-width: var(--loading-progress-stroke-width);
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: var(--loading-progress-color);
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
    color: var(--loading-progress-text-color);
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

code {
    color: #c02d76;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
    color: var(--bs-secondary-color);
    text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
    text-align: start;
}

/* Alert Styling */
.alert {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border: 1px solid transparent;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* TrimInputNumber Styling */
input[type="number"] {
    width: 100px;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid #ced4da;
    border-radius: var(--border-radius-sm);
    font-size: 1rem;
}

input[type="number"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Page Header */
.page-header {
    background-color: white;
    color: var(--secondary-color);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--primary-color);
}

.page-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--secondary-color);
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 4.5rem; /* Increased to position below the header */
    right: 1rem;
    z-index: 1001; /* Increased to be above other elements but below the header */
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
}

.notification {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    padding: 1rem;
    animation: slideIn 0.3s ease-out;
    border: 1px solid var(--light-color);
    width: 100%;
    max-width: 400px;
}

.notification.error {
    border-left: 4px solid var(--primary-color);
    background-color: rgba(255, 235, 235, 0.95); /* Light red background */
}

.notification.success {
    border-left: 4px solid var(--success-color);
    background-color: rgba(235, 255, 235, 0.95); /* Light green background */
}

.notification-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 1rem;
}

.notification-message {
    flex: 1;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.5rem;
    padding: 0;
    line-height: 1;
}

.notification-close:hover {
    color: var(--text-color);
}

@keyframes slideIn {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    70% {
        transform: translateX(-10px);
        opacity: 1;
    }
    85% {
        transform: translateX(5px);
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Navigation */
.nav-link {
    color: var(--secondary-color);
    padding: 0.75rem 1rem;
    text-decoration: none;
    transition: all 0.2s ease-in-out;
    border-bottom: 2px solid transparent;
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-link:hover {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    font-weight: 600;
}

/* Tabs */
.nav-tabs {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    border-bottom: 1px solid var(--light-border);
}

.nav-tabs .nav-item {
    margin-bottom: -1px;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: var(--border-radius-sm);
    border-top-right-radius: var(--border-radius-sm);
    padding: 0.5rem 1rem;
    margin-right: 0.25rem;
}

.nav-tabs .nav-link:hover {
    border-color: var(--light-border) var(--light-border) transparent;
    background-color: var(--hover-bg);
}

.nav-tabs .nav-link.active {
    color: var(--secondary-color);
    background-color: var(--card-bg);
    border-color: var(--light-border) var(--light-border) var(--card-bg);
    border-bottom: 2px solid var(--primary-color);
}

/* Tab content */
.tab-content {
    padding: 1rem 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}