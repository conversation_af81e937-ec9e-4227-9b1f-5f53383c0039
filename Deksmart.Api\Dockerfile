# made according to https://github.com/dotnet/dotnet-docker/tree/main/samples/aspnetapp

###############################################################
# Test and Build stage
###############################################################

FROM docker-registry.dek.cz/dotnet/sdk:9.0 AS build-stage
COPY . .

RUN dotnet publish "Deksmart.Api/Deksmart.Api.csproj" -c Release -o /app

###############################################################
# Final stage
###############################################################

FROM docker-registry.dek.cz/dotnet/aspnet:9.0-bookworm-slim-amd64
WORKDIR /app
COPY --from=build-stage /app ./
RUN chmod +x /app/.playwright/node/linux-x64/node
ENTRYPOINT ["dotnet", "Deksmart.Api.dll"]

#docker build -f .\Deksmart.Api\Build.Dockerfile -t desksmart-api:debian . --no-cache --progress=plain
#docker run -it --rm -p 5001:8080 --name desksmart-api desksmart-api:debian
#http://localhost:5001/api/doc