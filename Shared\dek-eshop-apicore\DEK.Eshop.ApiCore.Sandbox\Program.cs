using DEK.Eshop.ApiCore.Sandbox.Context;
using DEK.Eshop.ApiCore.Sandbox.Repository;
using DEK.Eshop.ApiCore.Database;
using DEK.Eshop.ApiCore.Bootstrap;
using DEK.Eshop.ApiCore.Loging;
using DEK.Eshop.ApiCore.Mail;
using DEK.Eshop.ApiCore.Config;
using ConfigService = DEK.Eshop.ApiCore.Sandbox.Config.ConfigService; //overrides origin ConfigService

var builder = WebApplication.CreateBuilder(args);


builder.Services.AddCoreBootstrap(builder);
builder.Services.AddScoped<IConfigService, ConfigService>();

// App Repository
builder.Services.AddScoped<MssqlRepository>();
builder.Services.AddScoped<PqsqlRepository>();

// App Factory
builder.Services.AddScoped<MssqlContextFactory<MssqlContext>>();
builder.Services.AddScoped<PgsqlContextFactory<PgsqlContext>>();

var app = builder.Build();

app.UseCoreBootstrap();

app.Run();

public partial class Program { }