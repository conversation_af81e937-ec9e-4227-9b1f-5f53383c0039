using Deksmart.Shared.Enum;

namespace Deksmart.Shared.Dto
{
    /// <summary>
    /// Data Transfer Object representing a single user-editable field within a configurator, including its metadata, type, position, constraints, and possible values.
    /// Used to transfer, render, and validate field definitions and user input between backend, API, and UI.
    /// Supports dynamic UI generation and configuration workflows by encapsulating all relevant field data and relationships.
    /// </summary>
    [Serializable]
    public class ConfiguratorFieldDto
    {
        public int Id { get; set; }

        public string Title { get; set; } = null!;

        public string? Description { get; set; }

        public string? Suffix { get; set; }

        public int Order { get; set; }

        public ComponentTypeDto ComponentType { get; set; }

        public FieldPositionDto FieldPosition { get; set; }

        public string Ident { get; set; } = null!;

        public int? Max { get; set; }

        public int? Min { get; set; }

        public string? JavaScriptAfterRender { get; set; }

        public string? JavaScriptOnFocus { get; set; }

        public string? JavaScriptOnBlur { get; set; }

        public decimal? Value { get; set; }

        public string? ValidationError { get; set; }

        public ProductDetailsDto? ProductDetailsDto { get; set; }

        public List<ConfiguratorFieldValueDto> ConfiguratorFieldValues { get; set; } = [];
    }
}
