using Microsoft.AspNetCore.Mvc;
using Deksmart.Application.Service;
using Deksmart.Api.Validator;
using Deksmart.Shared.Dto;
using DEK.Eshop.ApiCore.Mail;

namespace Deksmart.Api.Controllers
{
    /// <summary>
    /// Controller for generating configurator views in HTML and PDF formats
    /// </summary>
    [ApiController]
    [Route("api/view")]
    public class ConfiguratorViewController : DeksmartBaseController
    {
        private readonly IConfiguratorViewOrchestrator _viewOrchestrator;
        private readonly IConfiguratorRouteValidator _configuratorRouteValidator;
        private readonly IPresetRouteValidator _presetRouteValidator;
        private readonly IEmailConfiguratorStateValidator _emailConfiguratorStateValidator;
        private readonly MailManager _mailManager;
        private readonly IConfiguration _configuration;

        public ConfiguratorViewController(
            IConfiguratorViewOrchestrator viewOrchestrator,
            IConfiguratorRouteValidator configuratorRouteValidator,
            IPresetRouteValidator presetRouteValidator,
            IEmailConfiguratorStateValidator emailConfiguratorStateValidator,
            MailManager mailManager,
            IConfiguration configuration)
        {
            _viewOrchestrator = viewOrchestrator;
            _configuratorRouteValidator = configuratorRouteValidator;
            _presetRouteValidator = presetRouteValidator;
            _emailConfiguratorStateValidator = emailConfiguratorStateValidator;
            _mailManager = mailManager;
            _configuration = configuration;
        }

        /// <summary>
        /// Generates HTML view from the current configurator state
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="configuratorState">The current state of the configurator</param>
        /// <returns>HTML content with configuration and products if any are selected</returns>
        [HttpPost("{id}/html")]
        public async Task<IActionResult> GenerateHtmlFromState([FromRoute] string id, [FromBody] ConfiguratorStateDto configuratorState)
        {
            var configuratorValidation = await _configuratorRouteValidator.ValidateAsync(id);
            if (!configuratorValidation.IsValid)
            {
                return CreateValidationErrorResponse(configuratorValidation);
            }

            var configuratorId = int.Parse(id);
            var (html, validation) = await _viewOrchestrator.GenerateHtmlFromStateAsync(configuratorId, configuratorState);
            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            return Content(html!, "text/html");
        }

        /// <summary>
        /// Generates HTML view from a saved preset
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="presetId">The ID of the preset to use</param>
        /// <returns>HTML content with configuration and products if any are selected</returns>
        [HttpGet("{id}/html/{presetId}")]
        public async Task<IActionResult> GenerateHtmlFromPreset([FromRoute] string id, [FromRoute] string presetId)
        {
            var configuratorValidation = await _configuratorRouteValidator.ValidateAsync(id);
            if (!configuratorValidation.IsValid)
            {
                return CreateValidationErrorResponse(configuratorValidation);
            }

            var presetValidation = await _presetRouteValidator.ValidateAsync(presetId);
            if (!presetValidation.IsValid)
            {
                return CreateValidationErrorResponse(presetValidation);
            }

            var configuratorId = int.Parse(id);
            var parsedPresetId = Guid.Parse(presetId);
            var (html, validation) = await _viewOrchestrator.GenerateHtmlFromPresetAsync(configuratorId, parsedPresetId);
            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            return Content(html!, "text/html");
        }

        /// <summary>
        /// Generates PDF document from the current configurator state
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="configuratorState">The current state of the configurator</param>
        /// <returns>PDF document with configuration and products if any are selected</returns>
        [HttpPost("{id}/pdf")]
        public async Task<IActionResult> GeneratePdfFromState([FromRoute] string id, [FromBody] ConfiguratorStateDto configuratorState)
        {
            var configuratorValidation = await _configuratorRouteValidator.ValidateAsync(id);
            if (!configuratorValidation.IsValid)
            {
                return CreateValidationErrorResponse(configuratorValidation);
            }

            var configuratorId = int.Parse(id);
            var (pdfBytes, title, validation) = await _viewOrchestrator.GeneratePdfFromStateAsync(configuratorId, configuratorState);
            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            return File(pdfBytes!, "application/pdf", GetPdfFileName(title));
        }

        /// <summary>
        /// Generates PDF document from a saved preset
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="presetId">The ID of the preset to use</param>
        /// <returns>PDF document with configuration and products if any are selected</returns>
        [HttpGet("{id}/pdf/{presetId}")]
        public async Task<IActionResult> GeneratePdfFromPreset([FromRoute] string id, [FromRoute] string presetId)
        {
            var configuratorValidation = await _configuratorRouteValidator.ValidateAsync(id);
            if (!configuratorValidation.IsValid)
            {
                return CreateValidationErrorResponse(configuratorValidation);
            }

            var presetValidation = await _presetRouteValidator.ValidateAsync(presetId);
            if (!presetValidation.IsValid)
            {
                return CreateValidationErrorResponse(presetValidation);
            }

            var configuratorId = int.Parse(id);
            var parsedPresetId = Guid.Parse(presetId);
            var (pdfBytes, title, validation) = await _viewOrchestrator.GeneratePdfFromPresetAsync(configuratorId, parsedPresetId);
            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            return File(pdfBytes!, "application/pdf", GetPdfFileName(title));
        }

        /// <summary>
        /// Sends an email with the configurator state as HTML content
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="configuratorState">The current state of the configurator</param>
        /// <returns>Ok if email was sent successfully</returns>
        [HttpPost("{id}/email")]
        public async Task<IActionResult> SendEmailFromState([FromRoute] string id, [FromBody] EmailConfiguratorStateDto configuratorState)
        {
            var configuratorValidation = await _configuratorRouteValidator.ValidateAsync(id);
            if (!configuratorValidation.IsValid)
            {
                return CreateValidationErrorResponse(configuratorValidation);
            }

            var emailStateValidation = await _emailConfiguratorStateValidator.ValidateAsync(configuratorState);
            if (!emailStateValidation.IsValid)
            {
                return CreateValidationErrorResponse(emailStateValidation);
            }

            var configuratorId = int.Parse(id);
            var (html, validation) = await _viewOrchestrator.GenerateHtmlFromStateAsync(configuratorId, configuratorState);
            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            var subject = $"Configurator State - ID: {id}";
            if (configuratorState.ContactInfo != null)
            {
                if (!string.IsNullOrEmpty(configuratorState.ContactInfo.Name))
                {
                    subject += $" - {configuratorState.ContactInfo.Name}";
                }
                if (!string.IsNullOrEmpty(configuratorState.ContactInfo.Email))
                {
                    subject += $" ({configuratorState.ContactInfo.Email})";
                }
            }

            var defaultEmailTo = _configuration["ApiCore:Mail:DefaultEmailTo"] ?? "<EMAIL>";
            var mailBuilder = new MailBuilder()
                .AddTo(defaultEmailTo)
                .SetSubject(subject)
                .SetHtmlBody(html!);

            try
            {
                await _mailManager.SendMail(mailBuilder);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(ex.Message);
            }

            return CreateSuccessResponse(true);
        }

        /// <summary>
        /// Sends an email with the configurator preset as HTML content
        /// </summary>
        /// <param name="id">The configurator ID</param>
        /// <param name="presetId">The ID of the preset to use</param>
        /// <returns>Ok if email was sent successfully</returns>
        [HttpGet("{id}/email/{presetId}")]
        public async Task<IActionResult> SendEmailFromPreset([FromRoute] string id, [FromRoute] string presetId)
        {
            var configuratorValidation = await _configuratorRouteValidator.ValidateAsync(id);
            if (!configuratorValidation.IsValid)
            {
                return CreateValidationErrorResponse(configuratorValidation);
            }

            var presetValidation = await _presetRouteValidator.ValidateAsync(presetId);
            if (!presetValidation.IsValid)
            {
                return CreateValidationErrorResponse(presetValidation);
            }

            var configuratorId = int.Parse(id);
            var parsedPresetId = Guid.Parse(presetId);
            var (html, validation) = await _viewOrchestrator.GenerateHtmlFromPresetAsync(configuratorId, parsedPresetId);
            if (validation.HasErrors)
            {
                return CreateValidationErrorResponse(validation);
            }

            var defaultEmailTo = _configuration["ApiCore:Mail:DefaultEmailTo"] ?? "<EMAIL>";
            var mailBuilder = new MailBuilder()
                .AddTo(defaultEmailTo)
                .SetSubject($"Configurator Preset - ID: {id}, Preset: {presetId}")
                .SetHtmlBody(html!);

            try
            {
                await _mailManager.SendMail(mailBuilder);
            }
            catch (Exception ex)
            {
                return CreateErrorResponse(ex.Message);
            }

            return CreateSuccessResponse(true);
        }

        /// <summary>
        /// Creates a standardized PDF file name from the configurator title
        /// </summary>
        /// <param name="title">The configurator title</param>
        /// <returns>A file name suitable for PDF download</returns>
        private static string GetPdfFileName(string? title)
        {
            return $"{title?.Replace(" ", "_") ?? "configurator"}.pdf";
        }
    }
}
