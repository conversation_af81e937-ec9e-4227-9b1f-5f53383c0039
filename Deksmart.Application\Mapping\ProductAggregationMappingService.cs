using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Business.Eshop;
using Deksmart.Shared.Dto;

namespace Deksmart.Application.Mapping
{
    /// <summary>
    /// Service responsible for complex product aggregation mapping operations.
    /// Handles business logic related to product aggregation, unit conversions, and composition creation.
    /// This is different from simple DTO mapping - it contains business rules and calculations.
    /// </summary>
    public interface IProductAggregationMappingService
    {
        /// <summary>
        /// Creates an aggregated product by summing amounts from multiple products with the same ProductCode.
        /// Implements UserAmount priority logic and package quantity calculations.
        /// </summary>
        /// <param name="products">List of products with the same ProductCode to aggregate</param>
        /// <param name="productOrder">Order for the new aggregated product</param>
        /// <param name="unitData">Unit data for package calculations</param>
        /// <returns>Aggregated ConfiguratorProduct with calculated amounts</returns>
        ConfiguratorProduct CreateAggregatedProduct(List<ConfiguratorProduct> products, int productOrder, EshopProductUnit unitData);

        /// <summary>
        /// Creates ProductAggregationData from a selected product and child state information
        /// </summary>
        /// <param name="selectedProduct">The product to create aggregation data for</param>
        /// <param name="childProductState">The child product state containing ordering information</param>
        /// <param name="productId">ID for the new product</param>
        /// <returns>ProductAggregationData with complete ordering information</returns>
        ProductAggregationData CreateProductAggregationData(
            ProductForAggregationDto selectedProduct, 
            ChildProductStateDto childProductState, 
            int productId);

        /// <summary>
        /// Converts a product to sales units using conversion data
        /// </summary>
        /// <param name="originalProduct">Original product to convert</param>
        /// <param name="conversion">Unit conversion data</param>
        /// <param name="unitInfo">Unit information for sales and package units</param>
        /// <returns>New ConfiguratorProduct with converted units and amounts</returns>
        ConfiguratorProduct ConvertToSalesUnits(
            ConfiguratorProduct originalProduct, 
            EshopProductUnitConversion conversion, 
            EshopProductUnit unitInfo);

        /// <summary>
        /// Creates a basic ConfiguratorProduct from ProductForAggregationDto
        /// </summary>
        /// <param name="selectedProduct">The DTO to convert</param>
        /// <param name="productId">ID for the new product</param>
        /// <returns>ConfiguratorProduct entity</returns>
        ConfiguratorProduct CreateConfiguratorProductFromDto(ProductForAggregationDto selectedProduct, int productId);
    }

    public class ProductAggregationMappingService : IProductAggregationMappingService
    {
        public ConfiguratorProduct CreateAggregatedProduct(List<ConfiguratorProduct> products, int productOrder, EshopProductUnit unitData)
        {
            if (!products.Any())
                throw new ArgumentException("Products list cannot be empty", nameof(products));

            var firstProduct = products.First();
            
            // Sum calculated amounts (always sum ALL original amounts)
            var totalCalculatedAmount = products.Sum(p => p.CalculatedAmount);
            
            // Calculate PackageQuantity: Pure system recommendation for ALL products (ignoring user input)
            var calculatedPackageQuantity = totalCalculatedAmount > 0 ? Math.Ceiling(totalCalculatedAmount / unitData.UnitsInPackage) : 0;
            
            // Check if any product has user amount set
            var productsWithUserAmount = products.Where(p => p.UserAmount.HasValue).ToList();
            var productsWithoutUserAmount = products.Where(p => !p.UserAmount.HasValue).ToList();
            
            decimal? finalUserAmount = null;
            
            // UserAmount Priority Logic: if user sets ANY part of a product, calculate total packages
            if (productsWithUserAmount.Any())
            {
                // Sum user amounts (already in package units)
                var totalUserPackages = productsWithUserAmount.Sum(p => p.UserAmount!.Value);
                
                // Calculate packages needed for products without user amounts
                // Use actual UnitsInPackage from unit data
                var calculatedAmountWithoutUserAmount = productsWithoutUserAmount.Sum(p => p.CalculatedAmount);
                var calculatedPackages = calculatedAmountWithoutUserAmount > 0 ? Math.Ceiling(calculatedAmountWithoutUserAmount / unitData.UnitsInPackage) : 0;
                
                // Total packages = calculated packages + user packages
                finalUserAmount = calculatedPackages + totalUserPackages;
            }

            return new ConfiguratorProduct
            {
                Id = productOrder,
                ProductCode = firstProduct.ProductCode,
                Title = firstProduct.Title,
                CalculatedAmount = totalCalculatedAmount,
                UserAmount = finalUserAmount,
                ProductUnit = firstProduct.ProductUnit,
                Order = productOrder,
                IsSelected = true,
                // Pricing fields will be calculated later
                PriceNoVat = 0,
                PriceVat = 0,
                Vat = 0,
                PriceVatPackage = 0,
                PackageQuantity = calculatedPackageQuantity,  // Pure system recommendation
                PackageUnit = firstProduct.PackageUnit
            };
        }

        public ProductAggregationData CreateProductAggregationData(
            ProductForAggregationDto selectedProduct, 
            ChildProductStateDto childProductState, 
            int productId)
        {
            // Create a ConfiguratorProduct for aggregation with all needed data
            var product = CreateConfiguratorProductFromDto(selectedProduct, productId);

            // Return as ProductAggregationData to keep all ordering info organized
            return new ProductAggregationData
            {
                Product = product,
                CompositionTitle = selectedProduct.CompositionTitle ?? "Unknown",
                ConfiguratorId = childProductState.ConfiguratorId,
                TabOrder = childProductState.TabOrder,
                CompositionOrder = selectedProduct.CompositionOrder,
                ProductOrder = selectedProduct.ProductOrder
            };
        }

        public ConfiguratorProduct ConvertToSalesUnits(
            ConfiguratorProduct originalProduct, 
            EshopProductUnitConversion conversion, 
            EshopProductUnit unitInfo)
        {
            // Create converted product with sales amount and sales unit
            return new ConfiguratorProduct
            {
                Id = originalProduct.Id,
                ProductCode = originalProduct.ProductCode,
                Title = originalProduct.Title,
                CalculatedAmount = conversion.QuantityOutput, // Converted to sales units
                UserAmount = originalProduct.UserAmount,
                ProductUnit = unitInfo.UnitSales, // Sales unit
                Order = originalProduct.Order,
                IsSelected = true,
                PackageUnit = unitInfo.UnitPackage // Correct package unit
            };
        }

        public ConfiguratorProduct CreateConfiguratorProductFromDto(ProductForAggregationDto selectedProduct, int productId)
        {
            return new ConfiguratorProduct
            {
                Id = productId,
                ProductCode = selectedProduct.ProductCode,
                Title = selectedProduct.Title,
                CalculatedAmount = selectedProduct.CalculatedAmount,
                UserAmount = selectedProduct.UserAmount,
                ProductUnit = selectedProduct.ProductUnit,
                IsSelected = true,
                Order = productId,
                // Will be calculated later by pricing service
                PriceNoVat = 0,
                PriceVat = 0,
                Vat = 0,
                PriceVatPackage = 0,
                PackageQuantity = 0,
                PackageUnit = selectedProduct.ProductUnit
            };
        }
    }
}