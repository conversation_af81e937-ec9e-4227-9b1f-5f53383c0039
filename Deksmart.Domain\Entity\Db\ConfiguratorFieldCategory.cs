﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;
using Microsoft.EntityFrameworkCore;
using Deksmart.Domain.Enum;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// Top-level grouping component for fields within a configurator.
    /// Used to organize and categorize user-interactive fields (filters, input elements) in the configurator UI.
    /// </summary>
    [Table("configurator_field_category", Schema = "dbo")]
    [Index("ConfiguratorId", Name = "idx_configurator_field_category_configurator_id")]
    [Index("VisibilityId", Name = "idx_configurator_field_category_visibility_id")]
    public partial class ConfiguratorFieldCategory : IDeletableEntity
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// KALK_ID
        /// </summary>
        [Column("configurator_id")]
        public int ConfiguratorId { get; set; }

        [Column("title")]
        [StringLength(100)]
        public string Title { get; set; } = null!;

        [Column("description")]
        [StringLength(2000)]
        public string? Description { get; set; }

        [Column("order")]
        public int Order { get; set; }

        [Column("is_deleted")]
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Represents the collapse/expand state of the category.
        /// </summary>
        [Column("collapse_state")]
        public CategoryCollapseState CollapseState { get; set; }

        [Column("visibility_id")]
        public int? VisibilityId { get; set; }

        public virtual ICollection<ConfiguratorField> ConfiguratorFields { get; set; } = new List<ConfiguratorField>();

        [ForeignKey("VisibilityId")]
        public virtual ConfiguratorExpression? Visibility { get; set; }

        //Business logic
        [NotMapped]
        public bool IsVisible { get; set; }
    }
}
