using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using System.Data;
using System.Data.Common;
using DEK.Eshop.ApiCore.Config.Dto;
using DEK.Eshop.ApiCore.Config;
using DEK.Eshop.ApiCore.Indentity;
using Microsoft.Extensions.Configuration;

namespace DEK.Eshop.ApiCore.Database;

public class MssqlContextFactory<TContext> where TContext : DbContext
{
    private PooledDbContextFactory<TContext> _factory;

    /// <summary>
    ///     https://learn.microsoft.com/en-us/ef/core/dbcontext-configuration
    ///     https://learn.microsoft.com/en-us/ef/core/performance/advanced-performance-topic
    ///     https://github.com/dotnet/EntityFramework.Docs/blob/main/samples/core/Benchmarks/ContextPooling.cs
    /// </summary>
    /// <exception cref="ConfigException"></exception>
    public MssqlContextFactory(IConfiguration config, IHostEnvironment env, IConfigService serviceConfig)
    {
        var configMssql = serviceConfig.GetMssql();
        this._factory = CreateFactory(configMssql);
    }

    private MssqlContextFactory(PooledDbContextFactory<TContext> contextFactory)
    {
        this._factory = contextFactory;
    }

    public static MssqlContextFactory<TContext> Create(Mssql config)
    {
        return new MssqlContextFactory<TContext>(CreateFactory(config));
    }

    public static PooledDbContextFactory<TContext> CreateFactory(Mssql config)
    {
        var conStr = new SqlConnectionStringBuilder
        {
            UserID = config.User,
            Password = config.Password,
            InitialCatalog = config.Database,
            DataSource = config.Host,
            Pooling = config.Pooling, // Pooling of connections
            // https://learn.microsoft.com/en-us/answers/questions/663116/a-connection-was-successfully-established-with-the-1.html
            //Encrypt = false,
            TrustServerCertificate = true,

        };
        var options = new DbContextOptionsBuilder<TContext>()
            .UseSqlServer(conStr.ConnectionString)
            .UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking)
            .Options;

        return new PooledDbContextFactory<TContext>(options); // Pooling of DbContexts
    }

    public TContext CreateContext()
    {
        return _factory.CreateDbContext();
    }

    /// <summary>
    /// Po execute je potřeba spojení opět "ručně" zavřít. Dát DbCommand do using v Repository nestačí.
    /// Musí se zavolat Dispose() nebo Close() na DbCommand.Connection. Vyjímka je ExecuteFirstColumnAndRowAsync(), která
    /// už to dělá v sobě.
    /// </summary>
    public DbCommand CreateCommand()
    {
        var cmd = CreateContext().Database.GetDbConnection().CreateCommand();

        if (cmd.Connection?.State != ConnectionState.Open)
            cmd.Connection?.Open();

        return cmd;
    }
}
