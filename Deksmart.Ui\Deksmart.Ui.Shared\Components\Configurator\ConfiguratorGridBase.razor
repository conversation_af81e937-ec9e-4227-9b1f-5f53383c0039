@using Deksmart.Ui.Shared.Components.Field
@using Deksmart.Ui.Shared.Components.Product
@inherits ComponentBase

@if (ConfiguratorGridService.ConfiguratorWrapper != null)
{
    <ConfiguratorHeader ConfiguratorGridService="@ConfiguratorGridService" ShowSaveButton="@ShowSaveButton"
        ShowPageTitle="@ShowPageTitle" />

    @foreach (var category in ConfiguratorGridService.ConfiguratorWrapper.FieldCategories!)
    {
        // Check if any fields in this category have validation errors
        bool hasErrors = false;
        if (category.AllFields != null && category.AllFields.Any())
        {
            hasErrors = category.AllFields.Any(f => f != null && f.HasValidationError);
        }

        // Only show validation class if this is a child configurator and ShowValidationCheckboxes is true
        bool showValidationClass = ConfiguratorGridService.ShowValidationCheckboxes &&
                                  ConfiguratorGridService.MainConfiguratorGridService != null;

        string validationClass = showValidationClass ? (hasErrors ? "has-error" : "completed") : "";

        <div id="<EMAIL>" class="accordion-item @(category.IsCollapsed ? "" : "active")">
            @if (category.IsCollapsible)
            {
                <div class="accordion-header @validationClass" @onclick="() => ToggleCategoryCollapse(category)">
                    <div class="accordion-button">
                        <h4 class="accordion-title">
                            @category.Title
                            @if (category.IsCollapsed && HasSingleIdFieldWrapper(category, out var selectedValue) && !string.IsNullOrEmpty(selectedValue))
                            {
                                <span class="selected-value-title">: @selectedValue</span>
                            }
                        </h4>
                        <span class="arrow @(category.IsCollapsed ? "collapsed" : "")">&#9660;</span>
                    </div>
                </div>
            }
            else
            {
                <div class="accordion-header @validationClass">
                    <h4 class="accordion-title">@category.Title</h4>
                </div>
            }

            @if (!category.IsCollapsed || !category.IsCollapsible)
            {
                <div class="accordion-content">
                    <div class="filter-grid-container">
                        @if (category.FullWidthFields.Any())
                        {
                            <div class="filter-grid-full-width">
                                @foreach (var filter in category.FullWidthFields)
                                {
                                    <FieldComponent Field="@filter" OnDataLoaded="HandleDataLoaded"
                                        OnIdWrapperValueChange="HandleIdWrapperValueChange" />
                                }
                            </div>
                        }
                        @if (category.Fields.Any())
                        {
                            <div class="filter-grid">
                                @foreach (var filter in category.Fields)
                                {
                                    <FieldComponent Field="@filter" OnDataLoaded="HandleDataLoaded"
                                        OnIdWrapperValueChange="HandleIdWrapperValueChange" />
                                }
                            </div>
                        }
                        @if (category.RightFields.Any())
                        {
                            <div class="filter-grid right-field">
                                @foreach (var filter in category.RightFields)
                                {
                                    <FieldComponent Field="@filter" OnDataLoaded="HandleDataLoaded"
                                        OnIdWrapperValueChange="HandleIdWrapperValueChange" />
                                }
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    }

    @if (!string.IsNullOrEmpty(ConfiguratorGridService.ConfiguratorWrapper.EndDescription))
    {
        <div class="card mb-3">
            <div class="card-body">
                @((MarkupString)ConfiguratorGridService.ConfiguratorWrapper.EndDescription)
            </div>
        </div>
    }

    @if (ConfiguratorGridService.ConfiguratorWrapper.ConfiguratorCompositions?.Any() ?? false)
    {
        <CompositionGrid OnDataLoaded="HandleDataLoaded" ConfiguratorGridService="ConfiguratorGridService" />
    }
}

<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function () {
            console.log('Text copied to clipboard');
        }).catch(function (error) {
            console.error('Error copying text to clipboard: ', error);
        });
    };
</script>