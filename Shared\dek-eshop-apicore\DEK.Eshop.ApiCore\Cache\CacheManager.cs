using DEK.Eshop.ApiCore.Data;
using Microsoft.Extensions.Caching.Distributed;
using StackExchange.Redis;
using System.Text.Json;

namespace DEK.Eshop.ApiCore.Cache;

/// <summary>
/// Cache wrapper over IDistributedCache interface
/// https://docs.microsoft.com/en-us/aspnet/core/performance/caching/distributed
/// </summary>
public class CacheManager
{
    private readonly IDistributedCache cacheDistributed;

    private RedisConnectionException? connectionException;

    private DateTime lastConnectionAttempt = DateTime.MinValue;

    public CacheManager(IDistributedCache cacheDistributed)
    {
        this.cacheDistributed = cacheDistributed;
    }

    /// <summary>
    /// Get value from cache.
    /// </summary>
    public async Task<(T?, Exception? error)> GetAsync<T>(string key)
    {
        //Skip connection attempts if last attempt was less than 60 seconds ago
        //Time interval between connection attempts
        if(lastConnectionAttempt != DateTime.MinValue) {
            var interval = DateTime.Now.Subtract(this.lastConnectionAttempt);
            if (interval.TotalSeconds < 60) {
                return (default, this.connectionException);
            }
        }

        try {
            var value = await cacheDistributed.GetStringAsync(this.CreateKey<T>(key));

            this.lastConnectionAttempt = DateTime.MinValue;
            this.connectionException = null;

            if (string.IsNullOrEmpty(value)) {
                return default;
            }

            var output = Deserialize<T>(value);

            if (output is null) {
                return default;
            }

            return (output, default);
        } catch (RedisConnectionException e) {
            this.connectionException = e;
            this.lastConnectionAttempt = DateTime.Now;
            return (default, e);
        } catch (Exception e) {
            return (default, e);
        }
    }

    /// <summary>
    /// Save to cache with absolute expiration in seconds.
    /// </summary>
    [Obsolete]
    public async Task<(bool isSuccess, Exception? error)> SaveWithSecondsAsyncBackground<T>(string key, T value, int seconds)
    {
        var (isSuccess, error) = await this.SaveWithSecondsAsync(key, value, seconds);
        return (isSuccess, error);
    }

    /// <summary>
    /// Save to cache with absolute expiration in seconds.
    /// </summary>
    public async Task<(bool isSuccess, Exception? error)> SaveWithSecondsAsync<T>(string key, T value, int seconds)
    {
        try {
            await SaveWithAbosluteExpirationAsync<T>(this.CreateKey<T>(key), value, TimeSpan.FromSeconds(seconds));
            return (true, default);
        } catch (Exception e) {
            return (false, e);
        }
    }
    /// <summary>
    /// Save to cache with absolute expiration in minutes.
    /// </summary>
    [Obsolete]
    public async Task<(bool isSuccess, Exception? error)> SaveWithMinutesAsyncBackground<T>(string key, T value, int minutes)
    {
        var (isSuccess, error) = await this.SaveWithMinutesAsync(key, value, minutes);
        return (isSuccess, error);
    }

    /// <summary>
    /// Save to cache with absolute expiration in minutes.
    /// </summary>
    public async Task<(bool isSuccess, Exception? error)> SaveWithMinutesAsync<T>(string key, T value, int minutes)
    {
        try {
            await SaveWithAbosluteExpirationAsync<T>(this.CreateKey<T>(key), value, TimeSpan.FromMinutes(minutes));
            return (true, default);
        } catch (Exception e) {
            return (false, e);
        }
    }

    // TODO: better condition for deserialization
    private T? Deserialize<T>(string value)
    {
        try {
            return JsonSerializer.Deserialize<T>(value);
        } catch (JsonException) {
            return (T)Convert.ChangeType(value, typeof(T));
        }
    }

    private Task SaveWithAbosluteExpirationAsync<T>(string key, T value, TimeSpan time)
    {
        return cacheDistributed.SetStringAsync(
            key,
            JsonSerializer.Serialize(value),
            new DistributedCacheEntryOptions {
                AbsoluteExpirationRelativeToNow = time,
            }
       );
    }

    private string CreateKey<T>(string key)
    {
        return key + "-" + typeof(T).Name;
    }
}
