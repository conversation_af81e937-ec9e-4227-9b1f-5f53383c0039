# made according to https://github.com/dotnet/dotnet-docker/tree/main/samples/aspnetapp

###############################################################
# Test and Build stage
###############################################################

FROM docker-registry.dek.cz/dotnet/sdk:9.0 AS build-stage
COPY . .

# RUN dotnet test -c Release
# RUN dotnet publish "Deksmart.Ui/Deksmart.Ui.Web/Deksmart.Ui.Web.csproj" -c Release -o /app --no-restore
RUN dotnet publish "Deksmart.Ui/Deksmart.Ui.Web/Deksmart.Ui.Web.csproj" -c Release -o /app

###############################################################
# Final stage
###############################################################

FROM docker-registry.dek.cz/dotnet/aspnet:9.0-bookworm-slim-amd64
WORKDIR /app
COPY --from=build-stage /app ./
ENTRYPOINT ["dotnet", "Deksmart.Ui.Web.dll"]

#docker build -f .\Deksmart.Ui\Deksmart.Ui.Web\Build.Dockerfile -t desksmart-ui-web:debian . --no-cache --progress=plain
#docker run -it --rm -p 5000:8080 --name desksmart-ui-web desksmart-ui-web:debian
#http://localhost:5000/deksmart
