using Deksmart.Application.Service;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Microsoft.AspNetCore.Http;
using Moq;
using System.Data;
using System.Text;
using Deksmart.Domain.Enum;

namespace Deksmart.Application.Tests.Service
{
    public class ConfiguratorImportServiceTest
    {
        private readonly Mock<IImportProcessingService> _importProcessingServiceMock;
        private readonly Mock<IImportValidationService> _importValidationServiceMock;
        private readonly Mock<IConfiguratorImportParser> _configuratorImportParserMock;
        private readonly Mock<IValidationService> _validationServiceMock;
        private readonly IConfiguratorImportService _importService;

        public ConfiguratorImportServiceTest()
        {
            // Register encoding provider for Windows-1252 support
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            _importProcessingServiceMock = new Mock<IImportProcessingService>();
            _importValidationServiceMock = new Mock<IImportValidationService>();
            _configuratorImportParserMock = new Mock<IConfiguratorImportParser>();
            _validationServiceMock = new Mock<IValidationService>();
            
            _validationServiceMock.Setup(x => x.CreateValidation()).Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateCellEmpty(It.IsAny<object>(), It.IsAny<string>())).Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger(It.IsAny<string>(), It.IsAny<string>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellInteger(It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, 1, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<ComponentType>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, ComponentType.Text, new ValidationResult()));
            _importValidationServiceMock.Setup(x => x.ValidateCellEnum<CategoryCollapseState>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns((true, CategoryCollapseState.NotCollapsible, new ValidationResult()));

            _importService = new ConfiguratorImportService(
                _importProcessingServiceMock.Object,
                _importValidationServiceMock.Object,
                _configuratorImportParserMock.Object,
                _validationServiceMock.Object);

            // ImportProcessingService mocks
            _importProcessingServiceMock.Setup(x => x.GetConfiguratorAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => CreateInitializedConfigurator(id));
            _importProcessingServiceMock.Setup(x => x.BeginTransactionAsync()).Returns(Task.CompletedTask);
            _importProcessingServiceMock.Setup(x => x.CommitTransactionAsync()).Returns(Task.CompletedTask);
            _importProcessingServiceMock.Setup(x => x.RollbackTransactionAsync()).Returns(Task.CompletedTask);
            _importProcessingServiceMock.Setup(x => x.SaveConfiguratorAsync(It.IsAny<Configurator>(), It.IsAny<bool>())).Returns(Task.CompletedTask);
            _importProcessingServiceMock.Setup(x => x.HandleValidationFailureAsync(It.IsAny<ValidationResult>(), It.IsAny<ValidationResult>(), It.IsAny<bool>()))
                .ReturnsAsync((ValidationResult validationResult, ValidationResult mainValidation, bool forceFail) => 
                {
                    if (validationResult.HasErrors || forceFail)
                    {
                        mainValidation.AddError(validationResult.GetErrors());
                        return true;
                    }
                    return false;
                });
            _importProcessingServiceMock.Setup(x => x.SetDeleteOnNotPresentItems(It.IsAny<Configurator>(), It.IsAny<PresentIds>()));
            _importProcessingServiceMock.Setup(x => x.CleanupCompositeConfiguratorData(It.IsAny<Configurator>()));

            // Default validation/parser mocks (can be overridden in tests)
            _importValidationServiceMock.Setup(x => x.ValidateImport(It.IsAny<DataSet>()))
                .Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateExpressionSyntax(It.IsAny<Configurator>(), It.IsAny<Dictionary<string, ConfiguratorExpression>>()))
                .Returns(new ValidationResult());
            _importValidationServiceMock.Setup(x => x.ValidateExpressionFields(It.IsAny<Configurator>()))
                .Returns(new ValidationResult());
            _configuratorImportParserMock.Setup(x => x.ParseConfigurator(It.IsAny<int>(), It.IsAny<Configurator>(), It.IsAny<DataRow>(), It.IsAny<ValidationResult>()))
                .Returns((int id, Configurator? c, DataRow row, ValidationResult v) => (c ?? CreateInitializedConfigurator(id), false));
            _configuratorImportParserMock.Setup(x => x.MapFields(It.IsAny<Configurator>(), It.IsAny<DataSet>(), It.IsAny<PresentIds>(), It.IsAny<Dictionary<string, ConfiguratorExpression>>(), It.IsAny<ValidationResult>()))
                .Callback((Configurator c, DataSet d, PresentIds p, Dictionary<string, ConfiguratorExpression> e, ValidationResult v) => { });
            _configuratorImportParserMock.Setup(x => x.MapProducts(It.IsAny<Configurator>(), It.IsAny<DataSet>(), It.IsAny<PresentIds>(), It.IsAny<Dictionary<string, ConfiguratorExpression>>(), It.IsAny<ValidationResult>()))
                .Callback((Configurator c, DataSet d, PresentIds p, Dictionary<string, ConfiguratorExpression> e, ValidationResult v) => { });
        }

        [Fact]
        public async Task ParseConfiguratorImportsAsync_WithValidExcel_ReturnsSuccess()
        {
            // Arrange
            var filePath = Path.Combine("TestFiles", "ImportDocumentation.xlsx");
            var file = CreateFormFile(filePath);

            // Act
            var result = await _importService.ParseConfiguratorImportsAsync(file);

            // Assert
            Assert.False(result.validation.HasErrors);
        }

        [Fact]
        public async Task ParseConfiguratorImportsAsync_WithInvalidExcel_ReturnsError()
        {
            // Arrange
            var filePath = Path.Combine("TestFiles", "ImportDocumentation.xlsx");
            var file = CreateFormFile(filePath);
            var errorMessage = "Invalid Excel format";
            var validation = new ValidationResult();
            validation.AddError(errorMessage);
            _importValidationServiceMock.Setup(x => x.ValidateImport(It.IsAny<DataSet>())).Returns(validation);

            // Act
            var result = await _importService.ParseConfiguratorImportsAsync(file);

            // Assert
            Assert.True(result.validation.HasErrors);
            Assert.Contains(errorMessage, result.validation.GetErrors());
        }

        [Fact]
        public async Task ParseConfiguratorImportsAsync_WithInvalidExpressions_ReturnsError()
        {
            // Arrange
            var filePath = Path.Combine("TestFiles", "ImportDocumentation.xlsx");
            var file = CreateFormFile(filePath);
            var errorMessage = "Invalid expression syntax";
            var validImportValidation = new ValidationResult();
            var invalidExpressionValidation = new ValidationResult();
            invalidExpressionValidation.AddError(errorMessage);
            _importValidationServiceMock.Setup(x => x.ValidateImport(It.IsAny<DataSet>())).Returns(validImportValidation);
            _importValidationServiceMock.Setup(x => x.ValidateExpressionSyntax(It.IsAny<Configurator>(), It.IsAny<Dictionary<string, ConfiguratorExpression>>())).Returns(invalidExpressionValidation);
            _importValidationServiceMock.Setup(x => x.ValidateExpressionFields(It.IsAny<Configurator>())).Returns(new ValidationResult());

            // Act
            var result = await _importService.ParseConfiguratorImportsAsync(file);

            // Assert
            Assert.True(result.validation.HasErrors);
            Assert.Contains(errorMessage, result.validation.GetErrors());
        }

        private static FormFile CreateFormFile(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var stream = new MemoryStream(File.ReadAllBytes(filePath));
            return new FormFile(stream, 0, stream.Length, "file", fileInfo.Name);
        }

        // Helper to create a fully initialized Configurator
        Configurator CreateInitializedConfigurator(int id)
        {
            var dummyExpression = new ConfiguratorExpression { Id = 1, Expression = "1" };
            var fieldValue = new ConfiguratorFieldValue
            {
                Id = 1,
                Title = "Value",
                Visibility = dummyExpression
            };
            var field = new ConfiguratorField
            {
                Id = 1,
                Title = "Field",
                Ident = "F1",
                ConfiguratorFieldValues = new List<ConfiguratorFieldValue> { fieldValue },
                Expression = dummyExpression,
                Visibility = dummyExpression,
                Validation = dummyExpression
            };
            var category = new ConfiguratorFieldCategory
            {
                Id = 1,
                Title = "Category",
                ConfiguratorFields = new List<ConfiguratorField> { field },
                Visibility = dummyExpression
            };
            var product = new ConfiguratorProduct
            {
                Id = 1,
                ProductCode = "P1",
                Title = "Product",
                ProductUnit = "unit",
                ProductVolume = 1,
                Visibility = dummyExpression,
                Quantity = dummyExpression
            };
            var composition = new ConfiguratorComposition
            {
                Id = 1,
                Title = "Composition",
                ConfiguratorProducts = new List<ConfiguratorProduct> { product },
                Visibility = dummyExpression
            };
            var childConfigurator = new ChildConfigurator
            {
                CompositeId = id,
                ConfiguratorId = 2,
                DisplayOrder = 1,
                Configurator = new Configurator { Id = 2, Title = "Child", ConfiguratorCompositions = new List<ConfiguratorComposition>(), ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory>(), ChildConfigurators = new List<ChildConfigurator>(), ChildUserConfigurators = new List<Configurator>() }
            };
            return new Configurator
            {
                Id = id,
                Title = "Configurator",
                ConfiguratorCompositions = new List<ConfiguratorComposition> { composition },
                ConfiguratorFieldCategories = new List<ConfiguratorFieldCategory> { category },
                ChildConfigurators = new List<ChildConfigurator> { childConfigurator },
                ChildUserConfigurators = new List<Configurator>(),
            };
        }
    }
}
