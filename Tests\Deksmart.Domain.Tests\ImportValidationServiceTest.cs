using System.Data;
using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Entity.Db;
using Deksmart.Domain.Enum;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Moq;

namespace Deksmart.Domain.Tests
{
    public class ImportValidationServiceTest
    {
        private readonly ImportValidationService _sut;
        private readonly Mock<IValidationService> _validationServiceMock;

        public ImportValidationServiceTest()
        {
            _validationServiceMock = new Mock<IValidationService>();
            _validationServiceMock.Setup(x => x.CreateValidation()).Returns(new ValidationResult());

            _sut = new ImportValidationService(_validationServiceMock.Object);
        }

        // Tests for the new validation methods

        [Fact]
        public void ValidateCellEmpty_WithEmptyValue_ReturnsValidationWithError()
        {
            // Arrange
            var error = "Test error message";

            // Act
            var validation = _sut.ValidateCellEmpty("", error);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(error, validation.GetErrors());
        }

        [Fact]
        public void ValidateCellEmpty_WithWhitespace_ReturnsValidationWithError()
        {
            // Arrange
            var error = "Test error message";

            // Act
            var validation = _sut.ValidateCellEmpty("   ", error);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(error, validation.GetErrors());
        }

        [Fact]
        public void ValidateCellEmpty_WithValidValue_ReturnsValidationWithoutErrors()
        {
            // Arrange
            var error = "Test error message";

            // Act
            var validation = _sut.ValidateCellEmpty("valid value", error);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Theory]
        [InlineData("42", true, 42)]
        [InlineData("3.14", true, 3.14)]
        [InlineData("3,14", true, 3.14)]
        [InlineData("1234.56", true, 1234.56)]
        [InlineData("1234,56", true, 1234.56)]
        [InlineData("invalid", false, 0)]
        [InlineData("", false, 0)]
        [InlineData(null, false, 0)]
        public void ValidateCellNumeric_WithVariousInputs_ReturnsExpectedResult(string? value, bool expectedIsValid, decimal expectedNumeric)
        {
            // Arrange - Set Czech culture to ensure consistent decimal parsing behavior
            var originalCulture = System.Globalization.CultureInfo.CurrentCulture;
            System.Globalization.CultureInfo.CurrentCulture = new System.Globalization.CultureInfo("cs-CZ");
            
            try
            {
                var error = DeksmartDomainResource.IsNotNumeric;
                var rowNumber = 1;

                // Act
                var (isValid, numeric, validation) = _sut.ValidateCellNumeric(value, rowNumber);

                // Assert
                Assert.Equal(expectedIsValid, isValid);
                Assert.Equal(expectedNumeric, numeric);
                if (!expectedIsValid && !string.IsNullOrEmpty(value))
                {
                    Assert.True(validation.HasErrors);
                    Assert.Contains(string.Format(error, rowNumber, value), validation.GetErrors());
                }
                else
                {
                    Assert.False(validation.HasErrors);
                }
            }
            finally
            {
                // Restore original culture
                System.Globalization.CultureInfo.CurrentCulture = originalCulture;
            }
        }

        [Theory]
        [InlineData("42", true, 42)]
        [InlineData("invalid", false, 0)]
        [InlineData("3.14", false, 0)]
        [InlineData("", false, 0)]
        [InlineData(null, false, 0)]
        public void ValidateCellInteger_WithVariousInputs_ReturnsExpectedResult(string? value, bool expectedIsValid, int expectedInteger)
        {
            // Arrange
            var error = DeksmartDomainResource.IsNotInteger;
            var rowNumber = 1;

            // Act
            var (isValid, integer, validation) = _sut.ValidateCellInteger(value, rowNumber);

            // Assert
            Assert.Equal(expectedIsValid, isValid);
            Assert.Equal(expectedInteger, integer);
            if (!expectedIsValid && !string.IsNullOrEmpty(value))
            {
                Assert.True(validation.HasErrors);
                Assert.Contains(string.Format(error, rowNumber, value), validation.GetErrors());
            }
            else
            {
                Assert.False(validation.HasErrors);
            }
        }

        [Theory]
        [InlineData("true", true, true)]
        [InlineData("false", true, false)]
        [InlineData("1", true, true)]
        [InlineData("0", true, false)]
        [InlineData("invalid", false, false)]
        [InlineData("", false, false)]
        [InlineData(null, false, false)]
        public void ValidateCellBool_WithVariousInputs_ReturnsExpectedResult(string? value, bool expectedIsValid, bool expectedBoolean)
        {
            // Arrange
            var rowNumber = 1;

            // Act
            var (isCellNull, boolean, validation) = _sut.ValidateCellBool(value, rowNumber);

            // Assert
            Assert.Equal(expectedIsValid, isCellNull);
            Assert.Equal(expectedBoolean, boolean);
            if (!expectedIsValid && !string.IsNullOrEmpty(value))
            {
                Assert.True(validation.HasErrors);
                Assert.Contains(string.Format(DeksmartDomainResource.IsNotBoolean, rowNumber, value), validation.GetErrors());
            }
            else
            {
                Assert.False(validation.HasErrors);
            }
        }

        [Theory]
        [InlineData("Left", true, FieldPosition.Left)]
        [InlineData("Right", true, FieldPosition.Right)]
        [InlineData("invalid", false, default(FieldPosition))]
        [InlineData("", false, default(FieldPosition))]
        [InlineData(null, false, default(FieldPosition))]
        public void ValidateCellEnum_WithVariousInputs_ReturnsExpectedResult(string? value, bool expectedIsValid, FieldPosition expectedEnum)
        {
            // Arrange
            var error = "Invalid field position: {0}";
            var rowNumber = 1;

            // Act
            var (isValid, enumValue, validation) = _sut.ValidateCellEnum<FieldPosition>(value, error, rowNumber);

            // Assert
            Assert.Equal(expectedIsValid, isValid);
            Assert.Equal(expectedEnum, enumValue);
            if (!expectedIsValid && !string.IsNullOrEmpty(value))
            {
                Assert.True(validation.HasErrors);
                Assert.Contains(string.Format(error, rowNumber, value), validation.GetErrors());
            }
            else
            {
                Assert.False(validation.HasErrors);
            }
        }

        [Fact]
        public void ValidateImport_WithValidDataSet_ReturnsValidationWithoutErrors()
        {
            // Arrange
            var dataSet = new DataSet();

            // Configurator table needs at least 2 rows and 3 columns
            var configuratorTable = dataSet.Tables.Add("Configurator");
            configuratorTable.Columns.Add("Id");
            configuratorTable.Columns.Add("Title");
            configuratorTable.Columns.Add("Url");
            configuratorTable.Rows.Add(1, "Test", "test");
            configuratorTable.Rows.Add(2, "Test2", "test2");

            // Fields table needs at least 2 rows and 22 columns
            var fieldsTable = dataSet.Tables.Add("Fields");
            for (int i = 0; i < 22; i++)
            {
                fieldsTable.Columns.Add($"Column{i}");
            }
            fieldsTable.Rows.Add(Enumerable.Repeat("value", 22).ToArray());
            fieldsTable.Rows.Add(Enumerable.Repeat("value", 22).ToArray());

            // Products table needs at least 2 rows and 11 columns
            var itemsTable = dataSet.Tables.Add("Products");
            for (int i = 0; i < 11; i++)
            {
                itemsTable.Columns.Add($"Column{i}");
            }
            itemsTable.Rows.Add(Enumerable.Repeat("value", 11).ToArray());
            itemsTable.Rows.Add(Enumerable.Repeat("value", 11).ToArray());

            // Act
            var validation = _sut.ValidateImport(dataSet);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateExpressionField_WithExpressionFieldAndEmptyValue_ReturnsValidationWithError()
        {
            // Arrange
            var currentField = new ConfiguratorField
            {
                ComponentType = ComponentType.Expression,
                Title = "Test Field"
            };

            // Act
            var validation = _sut.ValidateExpressionField("", currentField, 1);

            // Assert
            Assert.True(validation.HasErrors);
            Assert.Contains(string.Format(DeksmartDomainResource.ExpressionValueCannotBeEmpty, 1, "Test Field"), validation.GetErrors());
        }

        [Fact]
        public void ValidateExpressionField_WithExpressionFieldAndValidValue_ReturnsValidationWithoutErrors()
        {
            // Arrange
            var currentField = new ConfiguratorField
            {
                ComponentType = ComponentType.Expression,
                Title = "Test Field"
            };

            // Act
            var validation = _sut.ValidateExpressionField("x + y", currentField, 1);

            // Assert
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ValidateExpressionField_WithNonExpressionField_ReturnsValidationWithoutErrors()
        {
            // Arrange
            var currentField = new ConfiguratorField
            {
                ComponentType = ComponentType.Numeric,
                Title = "Test Field"
            };

            // Act
            var validation = _sut.ValidateExpressionField("", currentField, 1);

            // Assert
            Assert.False(validation.HasErrors);
        }
    }
}