﻿using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Repository.Interface.Base;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for accessing and managing <see cref="ConfiguratorPreset"/> entities, including retrieval by ID, catalog association, and update operations.
    /// </summary>
    public interface IConfiguratorPresetRepository : IIdRepositoryBase<ConfiguratorPreset, Guid>
    {
        /// <summary>
        /// Retrieves a configurator preset by its unique identifier, including field combinations and child presets.
        /// </summary>
        /// <param name="presetId">The unique identifier of the preset.</param>
        /// <returns>The <see cref="ConfiguratorPreset"/> entity, or null if not found.</returns>
        Task<ConfiguratorPreset?> GetFieldPresetByIdAsync(Guid presetId);

        /// <summary>
        /// Retrieves all product combinations associated with a given preset.
        /// </summary>
        /// <param name="presetId">The unique identifier of the preset.</param>
        /// <returns>A list of <see cref="ConfiguratorProductCombination"/> entities for the preset.</returns>
        Task<List<ConfiguratorProductCombination>> GetProductPresetByIdAsync(Guid presetId);

        /// <summary>
        /// Retrieves a configurator preset by its catalog identifier.
        /// </summary>
        /// <param name="catalogId">The catalog identifier associated with the preset.</param>
        /// <returns>The <see cref="ConfiguratorPreset"/> entity, or null if not found.</returns>
        Task<ConfiguratorPreset?> GetPresetIdByCatalogId(string catalogId);

        /// <summary>
        /// Associates a catalog identifier with a configurator preset.
        /// </summary>
        /// <param name="presetId">The unique identifier of the preset.</param>
        /// <param name="catalogId">The catalog identifier to associate.</param>
        /// <returns>True if the association was successful; otherwise, false.</returns>
        Task<bool> SetCatalogIdToPresetAsync(Guid presetId, string catalogId);

        /// <summary>
        /// Updates the specified configurator preset in the database.
        /// </summary>
        /// <param name="preset">The <see cref="ConfiguratorPreset"/> entity to update.</param>
        Task UpdateAsync(ConfiguratorPreset preset);
    }
}
