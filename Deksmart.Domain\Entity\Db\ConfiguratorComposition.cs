﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Deksmart.Domain.Entity.Db.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Domain.Entity.Db
{
    /// <summary>
    /// CZ_ESHOP_KONFIGURATOR_SKLADBY
    /// Represents a logical grouping of products that fulfill a specific functional requirement within a configuration.
    /// Organizes related products serving the same role or purpose, allowing selection from available alternatives or variants for that requirement.
    /// </summary>
    [Table("configurator_composition", Schema = "dbo")]
    [Index("ConfiguratorId", Name = "idx_configurator_composition_configurator_id")]
    public partial class ConfiguratorComposition : IDeletableEntity
    {
        /// <summary>
        /// SKL_ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// KALK_ID
        /// </summary>
        [Column("configurator_id")]
        public int ConfiguratorId { get; set; }

        /// <summary>
        /// SKL_NAZEV
        /// </summary>
        [Column("title")]
        [StringLength(200)]
        public string Title { get; set; } = null!;

        /// <summary>
        /// PORADI
        /// </summary>
        [Column("order")]
        public int Order { get; set; }

        [Column("visibility_id")]
        public int? VisibilityId { get; set; }

        /// <summary>
        /// Indicates that composition can contain multiple products.
        /// </summary>
        [Column("is_multiple_products")]
        public bool IsMultipleProducts { get; set; }

        [Column("is_deleted")]
        public bool IsDeleted { get; set; }

        public virtual ICollection<ConfiguratorProduct> ConfiguratorProducts { get; set; } = new List<ConfiguratorProduct>();

        [ForeignKey("VisibilityId")]
        public virtual ConfiguratorExpression? Visibility { get; set; }
    }
}
