using DEK.Eshop.ApiCore.Database;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Data.Common;

namespace DEK.Eshop.ApiCore.Extension;

public static class DbCommandExtension
{
    public static async Task<object?> ExecuteFirstColumnAndRowAsync(this DbCommand source, ProcedureQueryBuilder builder)
    {
        source.CommandType = CommandType.StoredProcedure;
        source.CommandText = builder.ProcedureName;
        source.Parameters.Clear();

        foreach (var kvp in builder.Parameters)
            source.Parameters.Add(new SqlParameter(kvp.Value.ParameterName[..^1], kvp.Value.Value)); // [..^1] remove last char from string

        var rows = await source.ExecuteScalarAsync();

        source.Connection?.Dispose();

        return rows;
    }

    /// <summary>
    /// Execute without return value
    /// </summary>
    public static async Task Dek_ExecuteAsync(this DbCommand source, ProcedureQueryBuilder builder)
    {
        source.CommandType = CommandType.StoredProcedure;
        source.CommandText = builder.ProcedureName;
        source.Parameters.Clear();

        foreach (var kvp in builder.Parameters)
            source.Parameters.Add(new SqlParameter(kvp.Value.ParameterName[..^1], kvp.Value.Value)); // [..^1] remove last char from string

        var rows = await source.ExecuteNonQueryAsync();

        source.Connection?.Dispose();
    }
}
