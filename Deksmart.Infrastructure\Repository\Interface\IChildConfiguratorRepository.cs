using Deksmart.Domain.Entity.Db;

namespace Deksmart.Infrastructure.Repository.Interface
{
    /// <summary>
    /// Repository interface for managing <see cref="ChildConfigurator"/> entities, including update, removal, and retrieval by parent configurator.
    /// </summary>
    public interface IChildConfiguratorRepository
    {
        /// <summary>
        /// Marks the specified child configurator entity as updated in the context.
        /// </summary>
        /// <param name="entity">The <see cref="ChildConfigurator"/> entity to update.</param>
        void SetUpdated(ChildConfigurator entity);

        /// <summary>
        /// Removes the specified child configurator entity from the context.
        /// </summary>
        /// <param name="entity">The <see cref="ChildConfigurator"/> entity to remove.</param>
        void Remove(ChildConfigurator entity);

        /// <summary>
        /// Removes a range of child configurator entities from the context.
        /// </summary>
        /// <param name="entities">The collection of <see cref="ChildConfigurator"/> entities to remove.</param>
        void RemoveRange(IEnumerable<ChildConfigurator> entities);

        /// <summary>
        /// Retrieves all child configurators associated with a given parent configurator.
        /// </summary>
        /// <param name="configuratorId">The unique identifier of the parent configurator.</param>
        /// <returns>A list of <see cref="ChildConfigurator"/> entities.</returns>
        Task<List<ChildConfigurator>> GetChildConfiguratorsByConfiguratorId(int configuratorId);

        /// <summary>
        /// Retrieves a child configurator relationship by the child configurator ID.
        /// </summary>
        /// <param name="configuratorId">The ID of the child configurator.</param>
        /// <returns>The <see cref="ChildConfigurator"/> entity representing the relationship, or null if not found.</returns>
        Task<ChildConfigurator?> GetByConfiguratorIdAsync(int configuratorId);
    }
}