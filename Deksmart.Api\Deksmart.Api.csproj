<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>c11055d7-0647-43ad-bd4d-a7485a05a4b5</UserSecretsId>
        <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\Comments.xml</DocumentationFile>
        <NoWarn>$(NoWarn);1591</NoWarn><!-- Vypnut<PERSON> zelených warningů vynucujících si komentáře -->
        <PreserveCompilationContext>true</PreserveCompilationContext>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <RuntimeIdentifiers>linux-musl-x64;linux-x64</RuntimeIdentifiers>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.3" />
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
        <PackageReference Include="Polly" Version="8.5.2" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Deksmart.Api.Tests" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Deksmart.Application\Deksmart.Application.csproj" />
      <ProjectReference Include="..\Deksmart.Infrastructure\Deksmart.Infrastructure.csproj" />
      <ProjectReference Include="..\Shared\dek-eshop-apicore\DEK.Eshop.ApiCore\DEK.Eshop.ApiCore.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Resource\DeksmartApiResource.Designer.cs">
        <DependentUpon>DeksmartApiResource.resx</DependentUpon>
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <EmbeddedResource Update="Resource\DeksmartApiResource.resx">
        <LastGenOutput>DeksmartApiResource.Designer.cs</LastGenOutput>
        <Generator>ResXFileCodeGenerator</Generator>
      </EmbeddedResource>
    </ItemGroup>

</Project>
