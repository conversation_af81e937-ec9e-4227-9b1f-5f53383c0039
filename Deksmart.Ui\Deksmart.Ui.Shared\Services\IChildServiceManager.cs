using Deksmart.Shared.Dto;
using Deksmart.Ui.Shared.Factory;
using Microsoft.Extensions.Logging;

namespace Deksmart.Ui.Shared.Services
{
    public interface IChildServiceManager
    {
        /// <summary>
        /// Initializes child services for a composite configurator
        /// </summary>
        /// <param name="result">The configurator DTO containing child configurations</param>
        /// <param name="childServices">The list to populate with child services</param>
        /// <param name="serviceFactory">Factory for creating child services</param>
        /// <param name="parentService">The parent service reference</param>
        /// <param name="validationStateChangedHandler">Event handler for validation state changes</param>
        /// <param name="logger">Logger for debugging</param>
        void InitializeChildServices(
            ConfiguratorDto result,
            List<IConfiguratorGridService> childServices,
            IConfiguratorGridServiceFactory serviceFactory,
            IConfiguratorGridService parentService,
            EventHandler? validationStateChangedHandler,
            ILogger logger);

        /// <summary>
        /// Disposes all child services and clears the collection
        /// </summary>
        /// <param name="childServices">The child services to dispose</param>
        /// <param name="validationStateChangedHandler">Event handler to unsubscribe</param>
        void DisposeChildServices(List<IConfiguratorGridService> childServices, EventHandler? validationStateChangedHandler);
    }
}