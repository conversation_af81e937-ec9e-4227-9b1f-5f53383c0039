using DEK.Eshop.ApiCore.Indentity;
using JwtConfig = DEK.Eshop.ApiCore.Config.Dto.Jwt;
using PgsqlConfig = DEK.Eshop.ApiCore.Config.Dto.Pgsql;
using MssqlConfig = DEK.Eshop.ApiCore.Config.Dto.Mssql;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using DEK.Eshop.ApiCore.Config;

namespace DEK.Eshop.ApiCore.Testing;

public class Tester
{
    public Dictionary<string, string?> UserBase { get; set; } = new Dictionary<string, string?>()
    {
        ["iss"] = "web.eshop",
        ["eshopId"] = "dek_cz",
        ["cartId"] = "e5018cde4f7f6f92207d963636327a6c064499bd",
        ["userEmail"] = "<EMAIL>",
        ["branchCode"] = "P100",
        ["instance"] = "TEST_ESHOP",
    };

    public string TestConfig { get; set; } = "appsettings.Test.json";

    public Claim[] CreateUserClaims(string[]? roles = null)
    {
        var claims = new List<Claim>();
        foreach (var kvp in UserBase) {
            if (kvp.Value is string) {
                claims.Add(new Claim(kvp.Key, kvp.Value));
            }
        }

        roles ??= new[] { "eshop" };

        foreach (var role in roles) {
            claims.Add(new Claim("roles", role));
        }

        return claims.ToArray();
    }

    public JwtConfig CreateJwtConfig()
    {
        var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").AddJsonFile(TestConfig).Build();
        return ConfigFactory.Create<JwtConfig>(config, "Jwt");
    }

    public MssqlConfig CreateMssqlConfig()
    {
        var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").AddJsonFile(TestConfig).Build();
        return ConfigFactory.Create<MssqlConfig>(config, "Mssql");
    }

    public PgsqlConfig CreatePgsqlConfig()
    {
        var config = new ConfigurationBuilder().AddJsonFile("appsettings.json").AddJsonFile(TestConfig).Build();
        return ConfigFactory.Create<PgsqlConfig>(config, "Pgsql:dek_cz");
    }

    /// <summary>
    /// https://weblog.west-wind.com/posts/2021/Mar/09/Role-based-JWT-Tokens-in-ASPNET-Core
    /// </summary>
    public JwtSecurityToken CreateJwtToken()
    {
        var jwt = CreateJwtConfig();
        var index = 0;
        var symmetricSecurityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwt.SymmetricKeys[index]));
        var claims = CreateUserClaims();
        var signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);

        return new JwtSecurityToken(
            //issuer: jwt.Issuers[index],
            //audience: jwt.Audiences[index],
            claims: claims,
            signingCredentials: signingCredentials
        );
    }

    public string CreateJwtHeader()
    {
        return "Bearer " + new JwtSecurityTokenHandler().WriteToken(CreateJwtToken());
    }
}
