using Deksmart.Shared.Dto;
using Deksmart.Ui.Model;

namespace Deksmart.Ui.Shared.Services
{
    public interface IConfiguratorStateService
    {
        /// <summary>
        /// Builds a configurator state DTO from the wrapper (handles both single and composite configurators)
        /// </summary>
        T GetConfiguratorState<T>(ConfiguratorWrapper configuratorWrapper, List<IConfiguratorGridService> childServices) where T : ConfiguratorStateDto, new();

        /// <summary>
        /// Gets field values from a configurator wrapper
        /// </summary>
        List<ClientFieldValueDto> GetFieldValues(ConfiguratorWrapper configuratorWrapper);

        /// <summary>
        /// Gets selected products from a configurator wrapper
        /// </summary>
        List<ClientProductValueDto> GetSelectedProducts(ConfiguratorWrapper configuratorWrapper);

        /// <summary>
        /// Gets category states from a configurator wrapper
        /// </summary>
        List<CategoryStateDto> GetCategoryStates(ConfiguratorWrapper configuratorWrapper);

        /// <summary>
        /// Saves the current field states for restoration after reload
        /// </summary>
        Dictionary<string, bool> SaveFieldStates(ConfiguratorWrapper? configuratorWrapper);

        /// <summary>
        /// Restores field states after reload
        /// </summary>
        void RestoreFieldStates(ConfiguratorWrapper? configuratorWrapper, Dictionary<string, bool> fieldStates);

        /// <summary>
        /// Converts a field wrapper to a field value DTO
        /// </summary>
        ClientFieldValueDto GetFieldValueDataContract(ConfiguratorFieldWrapper wrapper);

        /// <summary>
        /// Builds a composite configurator state DTO with the current configurator as the active child
        /// and gets other children's products from the main configurator service
        /// </summary>
        CompositeConfiguratorStateDto GetCompositeConfiguratorState(ConfiguratorWrapper activeChildWrapper, IConfiguratorGridService mainConfiguratorService);
    }
}