using Deksmart.Domain.Entity.Db;
using Deksmart.Infrastructure.Context;
using Deksmart.Infrastructure.Repository.Interface;
using Microsoft.EntityFrameworkCore;

namespace Deksmart.Infrastructure.Repository
{
    public class ChildConfiguratorRepository : IChildConfiguratorRepository
    {
        private readonly ConfiguratorContext _context;

        public ChildConfiguratorRepository(ConfiguratorContext context)
        {
            _context = context;
        }

        /// <inheritdoc/>
        public void SetUpdated(ChildConfigurator entity)
        {
            _context.Set<ChildConfigurator>().Update(entity);
        }

        /// <inheritdoc/>
        public void Remove(ChildConfigurator entity)
        {
            _context.Set<ChildConfigurator>().Remove(entity);
        }

        /// <inheritdoc/>
        public void RemoveRange(IEnumerable<ChildConfigurator> entities)
        {
            _context.Set<ChildConfigurator>().RemoveRange(entities);
        }

        /// <inheritdoc/>
        public async Task<List<ChildConfigurator>> GetChildConfiguratorsByConfiguratorId(int configuratorId)
        {
            return await _context.Set<ChildConfigurator>().Where(c => c.CompositeId == configuratorId).ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<ChildConfigurator?> GetByConfiguratorIdAsync(int configuratorId)
        {
            return await _context.Set<ChildConfigurator>()
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.ConfiguratorId == configuratorId);
        }
    }
} 