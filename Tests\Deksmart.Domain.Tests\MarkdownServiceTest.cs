using Deksmart.Domain.Entity.Business;
using Deksmart.Domain.Resource;
using Deksmart.Domain.Service;
using Deksmart.Domain.Service.Base;
using Moq;

namespace Deksmart.Domain.Tests
{
    public class MarkdownServiceTest
    {
        private readonly IMarkdownService _sut;
        private readonly Mock<IValidationService> _validationServiceMock;

        public MarkdownServiceTest()
        {
            _validationServiceMock = new Mock<IValidationService>();
            _validationServiceMock.Setup(x => x.CreateValidation()).Returns(new ValidationResult());

            _sut = new MarkdownService(_validationServiceMock.Object);
        }

        [Theory]
        [InlineData("# Heading", "<h1 id=\"heading\">Heading</h1>\n")]
        [InlineData("**Bold** text", "<p><strong>Bold</strong> text</p>\n")]
        [InlineData("*Italic* text", "<p><em>Italic</em> text</p>\n")]
        [InlineData("[Link](https://example.com)", "<p><a href=\"https://example.com\">Link</a></p>\n")]
        [InlineData("1. First item\n2. Second item", "<ol>\n<li>First item</li>\n<li>Second item</li>\n</ol>\n")]
        public void ConvertToHtml_WithValidation_ReturnsExpectedHtml(string markdown, string expectedHtml)
        {
            // Act
            var (html, validation) = _sut.ConvertToHtml(markdown, 1);

            // Assert
            Assert.Equal(expectedHtml, html);
            Assert.False(validation.HasErrors);
        }

        [Fact]
        public void ConvertToHtml_WithValidation_NullInput_ReturnsEmptyStringAndAddsError()
        {
            // Arrange
            string? nullMarkdown = null;

            // Act
            var (html, validation) = _sut.ConvertToHtml(nullMarkdown!, 1);

            // Assert
            Assert.Empty(html);
            Assert.True(validation.HasErrors);
            var rowNumber = 1;
            Assert.Contains(string.Format(DeksmartDomainResource.MarkdownConversionError, rowNumber, nullMarkdown), validation.GetErrors());
        }

        [Fact]
        public void ConvertToHtml_WithValidation_EmptyInput_ReturnsEmptyString()
        {
            // Arrange
            var emptyMarkdown = string.Empty;

            // Act
            var (html, validation) = _sut.ConvertToHtml(emptyMarkdown, 1);

            // Assert
            Assert.Empty(html);
            Assert.False(validation.HasErrors);
        }
    }
}